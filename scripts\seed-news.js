// Script to seed the Sanity database with sample news articles and categories
const { createClient } = require('@sanity/client');
require('dotenv').config({ path: '.env.local' });

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Sample categories
const categories = [
  {
    _type: 'category',
    title: 'Royal Announcements',
    slug: { _type: 'slug', current: 'royal-announcements' },
    description: 'Official announcements from the Royal Court of Adukrom Kingdom',
    color: '#002366', // Royal blue
    order: 1,
  },
  {
    _type: 'category',
    title: 'Events',
    slug: { _type: 'slug', current: 'events' },
    description: 'Updates about upcoming and past royal events',
    color: '#8B0000', // Dark red
    order: 2,
  },
  {
    _type: 'category',
    title: 'Culture',
    slug: { _type: 'slug', current: 'culture' },
    description: 'Articles about the rich cultural heritage of Adukrom Kingdom',
    color: '#006400', // Dark green
    order: 3,
  },
  {
    _type: 'category',
    title: 'Community',
    slug: { _type: 'slug', current: 'community' },
    description: 'News about community initiatives and developments',
    color: '#FFD700', // Gold
    order: 4,
  },
];

// Sample news articles
const newsArticles = [
  {
    _type: 'news',
    title: 'International Dignitaries Confirm Attendance at Royal Events',
    slug: { _type: 'slug', current: 'international-dignitaries-confirm-attendance' },
    excerpt: 'Representatives from over 20 countries will attend the historic ceremonies of King Allen Ellison, marking a new era for Adukrom Kingdom.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The Royal Court of Adukrom Kingdom is pleased to announce that representatives from over 20 countries have confirmed their attendance at the upcoming coronation ceremony of King Allen Ellison.',
            _key: 'intro-span',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'paragraph1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'This historic event, scheduled for August 29, 2025, will mark a significant milestone in the kingdom\'s history and strengthen international diplomatic relations.',
            _key: 'p1-span',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'heading1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Global Recognition',
            _key: 'h1-span',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'paragraph2',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The attendance of international dignitaries underscores the growing global recognition of Adukrom Kingdom and its cultural significance. Delegations from North America, Europe, Asia, and various African nations will participate in the ceremonies.',
            _key: 'p2-span',
          },
        ],
      },
    ],
    publishedAt: new Date().toISOString(),
    status: 'published',
    featured: true,
  },
  {
    _type: 'news',
    title: 'Sacred Crown Jewels Unveiled for Upcoming Ceremony',
    slug: { _type: 'slug', current: 'sacred-crown-jewels-unveiled' },
    excerpt: 'The historic crown jewels, kept in sacred storage for decades, have been revealed in preparation for the ceremony.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'For the first time in decades, the sacred crown jewels of Adukrom Kingdom have been unveiled from their protected storage in preparation for the upcoming coronation ceremony.',
            _key: 'intro-span',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'paragraph1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'These precious artifacts, which include the royal crown, scepter, and ceremonial sword, represent the kingdom\'s sovereignty and rich cultural heritage.',
            _key: 'p1-span',
          },
        ],
      },
    ],
    publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    status: 'published',
    featured: false,
  },
  {
    _type: 'news',
    title: 'Traditional Artisans Create Ceremonial Garments',
    slug: { _type: 'slug', current: 'traditional-artisans-create-ceremonial-garments' },
    excerpt: 'Master weavers and craftspeople have been working for months to create the elaborate ceremonial attire.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'A team of master weavers and craftspeople from Adukrom have been working diligently for months to create the elaborate ceremonial garments that will be worn during the upcoming royal events.',
            _key: 'intro-span',
          },
        ],
      },
    ],
    publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
    status: 'published',
    featured: false,
  },
  {
    _type: 'news',
    title: 'Exclusive: King Allen Ellison Shares Vision for Adukrom Kingdom',
    slug: { _type: 'slug', current: 'king-allen-ellison-shares-vision' },
    excerpt: 'In a rare interview, King Allen Ellison discusses his plans for cultural preservation and economic growth.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'In an exclusive interview, King Allen Ellison shared his comprehensive vision for the future of Adukrom Kingdom, emphasizing cultural preservation, economic development, and strengthening connections with the African diaspora.',
            _key: 'intro-span',
          },
        ],
      },
    ],
    publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    status: 'published',
    featured: true,
  },
  {
    _type: 'news',
    title: 'The Legacy of Adukrom Kingdom: A Historical Timeline',
    slug: { _type: 'slug', current: 'adukrom-kingdom-historical-timeline' },
    excerpt: 'Exploring the rich history and traditions of Adukrom Kingdom as it prepares for its first major event in 25 years.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'As Adukrom Kingdom prepares for its first major royal event in 25 years, we explore the rich history and traditions that have shaped this important cultural center in Ghana\'s Eastern Region.',
            _key: 'intro-span',
          },
        ],
      },
    ],
    publishedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
    status: 'published',
    featured: false,
  },
];

// Main function to seed the database
async function seedDatabase() {
  try {
    console.log('Starting to seed the news database...');
    
    // Create categories
    console.log('Creating categories...');
    const createdCategories = [];
    for (const category of categories) {
      // Check if category already exists
      const existingCategory = await client.fetch(
        '*[_type == "category" && slug.current == $slug][0]',
        { slug: category.slug.current }
      );
      
      if (!existingCategory) {
        const createdCategory = await client.create(category);
        createdCategories.push(createdCategory);
        console.log(`Category created: ${category.title}`);
      } else {
        createdCategories.push(existingCategory);
        console.log(`Category already exists: ${category.title}`);
      }
    }
    
    // Create news articles
    console.log('Creating news articles...');
    for (const article of newsArticles) {
      // Check if article already exists
      const existingArticle = await client.fetch(
        '*[_type == "news" && slug.current == $slug][0]',
        { slug: article.slug.current }
      );
      
      if (!existingArticle) {
        // Assign a category to the article
        const categoryIndex = Math.floor(Math.random() * createdCategories.length);
        const category = createdCategories[categoryIndex];
        
        article.category = {
          _type: 'reference',
          _ref: category._id,
        };
        
        // Create the article
        const createdArticle = await client.create(article);
        console.log(`News article created: ${article.title}`);
      } else {
        console.log(`News article already exists: ${article.title}`);
      }
    }
    
    console.log('News database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding news database:', error);
  }
}

// Run the seed function
seedDatabase();
