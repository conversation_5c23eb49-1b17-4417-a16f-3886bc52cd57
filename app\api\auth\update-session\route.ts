import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import fs from 'node:fs';
import path from 'node:path';
import { getWriteClient } from '@/lib/sanity.client';

// Path to the users file
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// POST /api/auth/update-session - Force update the session
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'No active session' },
        { status: 401 }
      );
    }

    // Get the user ID from the session
    const userId = session.user.id;
    const userEmail = session.user.email;

    // Try to get user from Sanity first
    try {
      // Get Sanity client
      const client = getWriteClient();

      console.log('Searching for user in Sanity with email:', userEmail);

      // Try to find the user by email with cache disabled
      const sanityUser = await client.fetch(
        `*[_type == "adminUser" && email == $email][0]{_id, name, email, username, role, createdAt, updatedAt, lastLogin}`,
        { email: userEmail },
        { cache: 'no-cache' }
      );

      console.log('Sanity user search result:', sanityUser);

      if (sanityUser) {
        console.log('User found in Sanity, returning Sanity data');

        return NextResponse.json({
          success: true,
          message: 'Session data retrieved from Sanity',
          user: {
            id: userId || sanityUser._id,
            name: sanityUser.name,
            email: sanityUser.email,
            role: sanityUser.role,
            createdAt: sanityUser.createdAt,
            updatedAt: sanityUser.updatedAt,
            lastLogin: sanityUser.lastLogin || new Date().toISOString()
          }
        });
      }
    } catch (sanityError) {
      console.error('Error fetching user from Sanity:', sanityError);
    }

    // If Sanity fetch fails, fall back to JSON file
    try {
      // Read the users file
      const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

      // Find the user by ID or email
      let user = usersData.find((u: any) => u.id === userId);

      // If not found by ID, try by email
      if (!user && userEmail) {
        user = usersData.find((u: any) => u.email === userEmail);
      }

      // If user found in JSON file, return that data
      if (user) {
        console.log('User found in JSON file, returning JSON data');
        return NextResponse.json({
          success: true,
          message: 'Session data retrieved from JSON file',
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLogin: user.lastLogin || new Date().toISOString()
          }
        });
      }
    } catch (jsonError) {
      console.error('Error reading JSON file:', jsonError);
    }

    // If all else fails, return session data
    console.log('User not found in Sanity or JSON file, using session data');
    return NextResponse.json({
      success: true,
      message: 'Session data retrieved from session',
      user: {
        id: session.user.id || 'unknown',
        name: session.user.name || 'Unknown User',
        email: session.user.email || '<EMAIL>',
        role: (session.user as any).role || 'admin',
        lastLogin: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating session:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update session' },
      { status: 500 }
    );
  }
}
