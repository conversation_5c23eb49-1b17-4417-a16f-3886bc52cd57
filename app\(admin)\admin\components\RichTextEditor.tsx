'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Link as LinkIcon,
  Image as ImageIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Heading1,
  Heading2,
  Heading3,
  Quote,
} from 'lucide-react';

interface RichTextEditorProps {
  value: any[];
  onChange: (value: any[]) => void;
  label?: string;
  placeholder?: string;
}

export default function RichTextEditor({
  value,
  onChange,
  label = 'Content',
  placeholder = 'Write your content here...',
}: RichTextEditorProps) {
  // For simplicity, we'll use a textarea for now
  // In a real implementation, you would use a proper rich text editor like TipTap, Slate, or Draft.js
  const [content, setContent] = useState('');

  // Convert Portable Text to plain text for the textarea
  useEffect(() => {
    try {
      console.log('RichTextEditor value:', value);
      console.log('Value type:', typeof value);
      console.log('Is array:', Array.isArray(value));

      // Handle different input formats
      if (!value) {
        // If value is null or undefined, set empty content
        console.log('Value is empty, setting empty content');
        setContent('');
        return;
      }

      if (typeof value === 'string') {
        // If value is already a string, use it directly
        console.log('Value is string, using directly');
        setContent(value);
        return;
      }

      if (Array.isArray(value)) {
        // Process Portable Text format
        console.log('Value is array, processing as Portable Text');

        if (value.length === 0) {
          console.log('Empty array, setting empty content');
          setContent('');
          return;
        }

        const plainText = value
          .filter(block => {
            // Check if block is valid
            const isValid = block && (block._type === 'block' || block.children);
            if (!isValid) {
              console.log('Filtering out invalid block:', block);
            }
            return isValid;
          })
          .map(block => {
            if (block.children && Array.isArray(block.children)) {
              return block.children
                .map((child: any) => {
                  if (!child) {
                    console.log('Null child in block');
                    return '';
                  }
                  if (typeof child === 'string') {
                    return child;
                  }
                  return (child.text) || '';
                })
                .join('');
            }
            // If block has no children but has text property
            if (block.text) {
              return block.text;
            }
            // If block is just a string
            if (typeof block === 'string') {
              return block;
            }
            console.log('Could not extract text from block:', block);
            return '';
          })
          .join('\n\n');

        console.log('Converted to plain text:', plainText.substring(0, 100) + (plainText.length > 100 ? '...' : ''));
        setContent(plainText);
        return;
      }

      // Fallback for unknown formats
      console.warn('RichTextEditor received value in unknown format:', value);
      setContent('');
    } catch (error) {
      console.error('Error converting Portable Text to plain text:', error);
      setContent('');
    }
  }, [value]);

  // Convert plain text to Portable Text when the content changes
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    try {
      const newContent = e.target.value;
      setContent(newContent);

      console.log('Converting text to Portable Text format');

      // If content is empty, return an empty array
      if (!newContent.trim()) {
        console.log('Content is empty, returning empty array');
        onChange([]);
        return;
      }

      // Generate a timestamp to ensure unique keys
      const timestamp = Date.now();

      // Convert to Portable Text format
      const portableText = newContent
        .split('\n\n')
        .filter(Boolean)
        .map((paragraph, index) => ({
          _type: 'block',
          _key: `block-${timestamp}-${index}-${Math.random().toString(36).substring(2, 9)}`,
          style: 'normal',
          markDefs: [],
          children: [
            {
              _type: 'span',
              _key: `span-${timestamp}-${index}-${Math.random().toString(36).substring(2, 9)}`,
              text: paragraph,
              marks: []
            }
          ]
        }));

      // If no paragraphs were created (e.g., only whitespace), create a default empty paragraph
      if (portableText.length === 0) {
        console.log('No paragraphs created, adding default paragraph');
        portableText.push({
          _type: 'block',
          _key: `block-${timestamp}-default-${Math.random().toString(36).substring(2, 9)}`,
          style: 'normal',
          markDefs: [],
          children: [
            {
              _type: 'span',
              _key: `span-${timestamp}-default-${Math.random().toString(36).substring(2, 9)}`,
              text: '',
              marks: []
            }
          ]
        });
      }

      console.log(`Created ${portableText.length} blocks of Portable Text`);
      onChange(portableText);
    } catch (error) {
      console.error('Error converting to Portable Text:', error);
      // Return a minimal valid Portable Text object in case of error
      const timestamp = Date.now();
      const errorBlock = [{
        _type: 'block',
        _key: `error-${timestamp}`,
        style: 'normal',
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: `error-text-${timestamp}`,
            text: e.target.value || '',
            marks: []
          }
        ]
      }];
      console.log('Returning error fallback block');
      onChange(errorBlock);
    }
  };

  return (
    <div className="space-y-2">
      {label && <Label htmlFor="content">{label}</Label>}

      <Card className="p-2 border border-input">
        <div className="flex flex-wrap gap-1 mb-2 p-1 bg-muted rounded-md">
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Bold className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Italic className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Underline className="h-4 w-4" />
          </Button>
          <div className="w-px h-8 bg-border mx-1" />
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Heading1 className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Heading2 className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Heading3 className="h-4 w-4" />
          </Button>
          <div className="w-px h-8 bg-border mx-1" />
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <List className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <ListOrdered className="h-4 w-4" />
          </Button>
          <div className="w-px h-8 bg-border mx-1" />
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <AlignRight className="h-4 w-4" />
          </Button>
          <div className="w-px h-8 bg-border mx-1" />
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <LinkIcon className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <ImageIcon className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" type="button" className="h-8 px-2">
            <Quote className="h-4 w-4" />
          </Button>
        </div>

        <Textarea
          id="content"
          value={content}
          onChange={handleContentChange}
          placeholder={placeholder}
          className="min-h-[300px] resize-y"
        />

        <div className="mt-2 text-xs text-muted-foreground">
          <p>This is a simplified editor. For a full-featured editor, we recommend integrating a dedicated rich text editor library.</p>
          <p className="mt-1">Tip: Use double line breaks to create new paragraphs.</p>
        </div>
      </Card>
    </div>
  );
}
