import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';

// GET /api/sanity-test - Test Sanity connection
export async function GET(request: NextRequest) {
  try {
    // Get Sanity client
    const client = getWriteClient();
    
    console.log('Sanity client created successfully');
    console.log('Sanity config:', {
      projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
      dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
      apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
      token: process.env.SANITY_API_TOKEN ? 'Set' : 'Not set'
    });
    
    // Try to fetch all admin users
    const users = await client.fetch(`*[_type == "adminUser"]`);
    
    console.log('Sanity users found:', users.length);
    
    // Return success
    return NextResponse.json({
      success: true,
      message: 'Sanity connection test successful',
      users: users.map((user: any) => ({
        _id: user._id,
        name: user.name,
        email: user.email,
        username: user.username,
        role: user.role
      }))
    });
  } catch (error) {
    console.error('Error testing Sanity connection:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to test Sanity connection', error: String(error) },
      { status: 500 }
    );
  }
}

// POST /api/sanity-test - Test Sanity update
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { id, name } = body;
    
    // Validate required fields
    if (!id || !name) {
      return NextResponse.json(
        { success: false, message: 'ID and name are required' },
        { status: 400 }
      );
    }
    
    // Get Sanity client
    const client = getWriteClient();
    
    console.log('Sanity client created successfully');
    console.log('Attempting to update document:', id);
    console.log('New name:', name);
    
    // Try to update the document
    const result = await client
      .patch(id)
      .set({
        name: name,
        updatedAt: new Date().toISOString()
      })
      .commit();
    
    console.log('Update result:', result);
    
    // Return success
    return NextResponse.json({
      success: true,
      message: 'Sanity update test successful',
      result
    });
  } catch (error) {
    console.error('Error testing Sanity update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to test Sanity update', error: String(error) },
      { status: 500 }
    );
  }
}
