'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/Header';
import RSVP from '@/components/RSVP';

export default function RsvpPage() {
  return (
    <div className="min-h-screen bg-ivory">
      <Header />

      {/* Hero Section */}
      <section className="royal-gradient py-20 relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              className="text-4xl md:text-5xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              RSVP for Royal Events
            </motion.h1>
            <motion.p
              className="text-xl text-royalGold mb-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Reserve your place at upcoming ceremonies and celebrations
            </motion.p>
          </div>
        </div>
      </section>

      {/* RSVP Form Section */}
      <RSVP />
    </div>
  );
}
