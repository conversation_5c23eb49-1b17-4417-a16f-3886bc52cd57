-----> Building on the Heroku-24 stack
-----> Deleting 1 files matching .slugignore patterns.
-----> Using buildpack: heroku/nodejs
-----> Node.js app detected
       
-----> Creating runtime environment
       
       NPM_CONFIG_LOGLEVEL=error
       NODE_VERBOSE=false
       NODE_ENV=production
       NODE_MODULES_CACHE=true
       
-----> Installing binaries
       engines.node (package.json):   20.x
       engines.npm (package.json):    10.x
       
       Resolving node version 20.x...
       Downloading and installing node 20.19.2...
       Bootstrapping npm 10.x (replacing 10.8.2)...
       npm 10.9.2 installed
       
-----> Restoring cache
       - node_modules
       
-----> Installing dependencies
       Installing node modules (package.json)
       
       up to date in 2s
       npm notice
       npm notice New major version of npm available! 10.9.2 -> 11.4.0
       npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.0
       npm notice To update run: npm install -g npm@11.4.0
       npm notice
       
-----> Build
       Detected both "build" and "heroku-postbuild" scripts
       Running heroku-postbuild
       
       > kingdom2@0.1.0 heroku-postbuild
       > node heroku-build.js
       
       Starting custom Heroku build process for Kingdom Adukrom website...
       This script ensures all dependencies are properly installed and configured.
       Creating .env file with Sanity configuration...
       .env.local file created successfully
       Checking Node.js version...
       Current Node.js version: v20.19.2
       Creating next-env.d.ts file...
       Checking for CSS import issues...
       Installing missing dependencies...
       @sendgrid/mail is already installed
       twilio is already installed
       Updating Next.js configuration for production...
       Fixing Node.js protocol imports...
       Building Next.js application...
       Running: npm run build
       
       > kingdom2@0.1.0 build
       > next build
       
       ⚠ No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache
       Attention: Next.js now collects completely anonymous telemetry regarding usage.
       This information is used to shape Next.js' roadmap and prioritize features.
       You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
       https://nextjs.org/telemetry
       
          ▲ Next.js 15.3.2
          - Environments: .env.local, .env
       
          Creating an optimized production build ...
 ⚠ Compiled with warnings in 58s
./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js
Critical dependency: the request of a dependency is an expression
Import trace for requested module:
./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js
./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/index.js
./node_modules/@opentelemetry/instrumentation/build/esm/platform/index.js
./node_modules/@opentelemetry/instrumentation/build/esm/index.js
./node_modules/@sentry/node/build/cjs/otel/instrument.js
./node_modules/@sentry/node/build/cjs/index.js
./node_modules/@sentry/nextjs/build/cjs/index.server.js
./lib/errorHandling.ts
./app/api/partners/route.ts
          Skipping validation of types
          Skipping linting
          Collecting page data ...
          Generating static pages (0/121) ...
          Generating static pages (30/121) 
          Generating static pages (60/121) 
          Generating static pages (90/121) 
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /partners couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B_type+%3D%3D+%22strategicPartner%22%5D+%7C+order%28order+asc%29+%7B%0A++++++_id%2C%0A++++++name%2C%0A++++++slug%2C%0A++++++description%2C%0A++++++website%2C%0A++++++logo%2C%0A++++++partnershipType%2C%0A++++++featured%2C%0A++++++order%2C%0A++++++startDate%2C%0A++++++active%0A++++%7D%0A++&returnQuery=false&perspective=published /partners. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[_type == "strategicPartner"] | order(order ...: Error: Dynamic server usage: Route /partners couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B_type+%3D%3D+%22strategicPartner%22%5D+%7C+order%28order+asc%29+%7B%0A++++++_id%2C%0A++++++name%2C%0A++++++slug%2C%0A++++++description%2C%0A++++++website%2C%0A++++++logo%2C%0A++++++partnershipType%2C%0A++++++featured%2C%0A++++++order%2C%0A++++++startDate%2C%0A++++++active%0A++++%7D%0A++&returnQuery=false&perspective=published /partners. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /partners couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B_type+%3D%3D+%22strategicPartner%22%5D+%7C+order%28order+asc%29+%7B%0A++++++_id%2C%0A++++++name%2C%0A++++++slug%2C%0A++++++description%2C%0A++++++website%2C%0A++++++logo%2C%0A++++++partnershipType%2C%0A++++++featured%2C%0A++++++order%2C%0A++++++startDate%2C%0A++++++active%0A++++%7D%0A++&returnQuery=false&perspective=published /partners. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-power-of-a-resource-based-economy-a-vision-for-africas-future%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-launches-grow-together-project%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22ellison-outreach-foundation-sponsors-three-schools-in-ghana%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22his-majesty-prince-allen-ellison-to-be-enstooled-as-king%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22traditional-artisans-create-ceremonial-garments%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22the-legacy-of-adukrom-kingdom-a-historical-timeline%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22sacred-crown-jewels-unveiled-for-upcoming-ceremony%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22exclusive-king-allen-ellison-shares-vision-for-adukrom-kingdom%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
Sanity query error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
Error in sanityFetch: 
    *[(_type == "post" || _type == "news") && slu...: Error: Dynamic server usage: Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at h (.next/server/chunks/4243.js:19:12685)
    at <unknown> (.next/server/chunks/4243.js:1:31556)
    at <unknown> (.next/server/chunks/4243.js:19:6575)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859)
    at e.startActiveSpan (.next/server/chunks/4243.js:1:10550)
    at e.startActiveSpan (.next/server/chunks/4243.js:12:13289)
    at <unknown> (.next/server/chunks/4243.js:19:6083)
    at e.with (.next/server/chunks/4243.js:1:771)
    at e.with (.next/server/chunks/4243.js:1:1859) {
  description: "Route /news/[slug] couldn't be rendered statically because it used no-store fetch https://n32kgamt.api.sanity.io/v2025-05-09/data/query/production?query=%0A++++*%5B%28_type+%3D%3D+%22post%22+%7C%7C+_type+%3D%3D+%22news%22%29+%26%26+slug.current+%3D%3D+%24slug%5D%5B0%5D+%7B%0A++++++_id%2C%0A++++++title%2C%0A++++++slug%2C%0A++++++excerpt%2C%0A++++++mainImage%2C%0A++++++body%2C%0A++++++publishedAt%2C%0A++++++category-%3E%7B%0A++++++++title%2C%0A++++++++slug%0A++++++%7D%2C%0A++++++author-%3E%7B%0A++++++++name%2C%0A++++++++image%2C%0A++++++++bio%0A++++++%7D%2C%0A++++++featured%2C%0A++++++status%0A++++%7D%0A++&%24slug=%22prince-allen-ellisons-vision-for-transforming-africas-infrastructure%22&returnQuery=false&perspective=published /news/[slug]. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE',
  isNetworkError: true,
  request: [Object]
}
        ✓ Generating static pages (121/121)
          Finalizing page optimization ...
          Collecting build traces ...
 ⚠ Failed to copy traced files for /tmp/build_326e424e/.next/server/app/(users)/page.js [Error: ENOENT: no such file or directory, copyfile '/tmp/build_326e424e/.next/server/app/(users)/page_client-reference-manifest.js' -> '/tmp/build_326e424e/.next/standalone/.next/server/app/(users)/page_client-reference-manifest.js'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'copyfile',
  path: '/tmp/build_326e424e/.next/server/app/(users)/page_client-reference-manifest.js',
  dest: '/tmp/build_326e424e/.next/standalone/.next/server/app/(users)/page_client-reference-manifest.js'
}
       
       Route (app)                                                                                                           Size  First Load JS  Revalidate  Expire
       ┌ ○ /                                                                                                              23.9 kB         231 kB          1m      1y
       ├ ○ /_not-found                                                                                                      990 B         103 kB
       ├ ƒ /[slug]                                                                                                        4.75 kB         274 kB
       ├ ○ /admin                                                                                                         7.62 kB         135 kB          1m      1y
       ├ ○ /admin/banking                                                                                                 9.56 kB         143 kB          1m      1y
       ├ ○ /admin/categories                                                                                              6.03 kB         158 kB          1m      1y
       ├ ○ /admin/events                                                                                                  3.28 kB         204 kB          1m      1y
       ├ ƒ /admin/events/[id]                                                                                             3.63 kB         207 kB
       ├ ƒ /admin/events/[id]/edit                                                                                        22.5 kB         237 kB
       ├ ○ /admin/events/new                                                                                                18 kB         172 kB          1m      1y
       ├ ○ /admin/gallery                                                                                                 9.32 kB         227 kB          1m      1y
       ├ ƒ /admin/gallery/edit/[id]                                                                                       8.73 kB         180 kB
       ├ ○ /admin/gallery/upload                                                                                          10.6 kB         151 kB          1m      1y
       ├ ○ /admin/login                                                                                                   6.68 kB         141 kB          1m      1y
       ├ ○ /admin/login/super                                                                                             5.96 kB         135 kB          1m      1y
       ├ ○ /admin/logout                                                                                                  3.78 kB         126 kB          1m      1y
       ├ ○ /admin/news                                                                                                    9.31 kB         160 kB          1m      1y
       ├ ○ /admin/news/create                                                                                              5.9 kB         140 kB          1m      1y
       ├ ƒ /admin/news/edit/[id]                                                                                          11.4 kB         207 kB
       ├ ○ /admin/pages                                                                                                   9.26 kB         179 kB          1m      1y
       ├ ƒ /admin/pages/[id]                                                                                              15.8 kB         202 kB
       ├ ƒ /admin/pages/edit/[id]                                                                                         15.2 kB         204 kB
       ├ ƒ /admin/pages/sections/[id]/[index]                                                                             6.73 kB         155 kB
       ├ ƒ /admin/pages/sections/[id]/new                                                                                 8.09 kB         194 kB
       ├ ○ /admin/partners                                                                                                5.79 kB         220 kB          1m      1y
       ├ ƒ /admin/partners/edit/[id]                                                                                      5.53 kB         220 kB
       ├ ○ /admin/profile                                                                                                  6.1 kB         135 kB          1m      1y
       ├ ○ /admin/rsvp                                                                                                    12.4 kB         215 kB          1m      1y
       ├ ƒ /admin/rsvp/[id]                                                                                               6.48 kB         147 kB
       ├ ○ /admin/sanity-test                                                                                             5.04 kB         124 kB          1m      1y
       ├ ○ /admin/sanity-update-test                                                                                      4.86 kB         124 kB          1m      1y
       ├ ○ /admin/settings                                                                                                22.4 kB         201 kB          1m      1y
       ├ ○ /admin/store                                                                                                   12.6 kB         163 kB          1m      1y
       ├ ○ /admin/store/categories                                                                                        6.12 kB         155 kB          1m      1y
       ├ ○ /admin/store/products                                                                                            10 kB         177 kB          1m      1y
       ├ ○ /admin/super                                                                                                   5.67 kB         119 kB          1m      1y
       ├ ○ /admin/users                                                                                                   10.6 kB         172 kB          1m      1y
       ├ ƒ /api/admin/login                                                                                                 204 B         103 kB
       ├ ƒ /api/admin/logout                                                                                                204 B         103 kB
       ├ ƒ /api/admin/users                                                                                                 477 B         159 kB
       ├ ƒ /api/admin/users/[id]                                                                                            477 B         159 kB
       ├ ƒ /api/auth/[...nextauth]                                                                                          477 B         159 kB
       ├ ƒ /api/auth/check-role                                                                                             477 B         159 kB
       ├ ƒ /api/auth/direct-login                                                                                           477 B         159 kB
       ├ ƒ /api/auth/forgot-password                                                                                        204 B         103 kB
       ├ ƒ /api/auth/get-latest-user                                                                                        204 B         103 kB
       ├ ƒ /api/auth/logout                                                                                                 204 B         103 kB
       ├ ƒ /api/auth/register                                                                                               477 B         159 kB
       ├ ƒ /api/auth/reset-password                                                                                         204 B         103 kB
       ├ ƒ /api/auth/session-update                                                                                         477 B         159 kB
       ├ ƒ /api/auth/update-login                                                                                           204 B         103 kB
       ├ ƒ /api/auth/update-session                                                                                         477 B         159 kB
       ├ ƒ /api/auth/verify-token                                                                                           204 B         103 kB
       ├ ƒ /api/auth/verify-user                                                                                            204 B         103 kB
       ├ ƒ /api/categories                                                                                                  477 B         159 kB
       ├ ƒ /api/categories/[id]                                                                                             477 B         159 kB
       ├ ƒ /api/dashboard                                                                                                   477 B         159 kB
       ├ ƒ /api/email                                                                                                       477 B         159 kB
       ├ ƒ /api/events                                                                                                      204 B         103 kB
       ├ ƒ /api/events/[id]                                                                                                 204 B         103 kB
       ├ ƒ /api/events/[id]/image                                                                                           204 B         103 kB
       ├ ƒ /api/exit-preview                                                                                                204 B         103 kB
       ├ ƒ /api/gallery                                                                                                     477 B         159 kB
       ├ ƒ /api/gallery/[id]                                                                                                477 B         159 kB
       ├ ƒ /api/gallery/cleanup                                                                                             477 B         159 kB
       ├ ƒ /api/gallery/delete-batch                                                                                        477 B         159 kB
       ├ ƒ /api/gallery/duplicates                                                                                          477 B         159 kB
       ├ ƒ /api/gallery/from-article                                                                                        477 B         159 kB
       ├ ƒ /api/gallery/upload                                                                                              477 B         159 kB
       ├ ƒ /api/news                                                                                                        204 B         103 kB
       ├ ƒ /api/news/[id]                                                                                                   477 B         159 kB
       ├ ƒ /api/news/search                                                                                                 477 B         159 kB
       ├ ƒ /api/newsletter/subscribe                                                                                        477 B         159 kB
       ├ ƒ /api/pages/[id]/sections/[index]                                                                                 477 B         159 kB
       ├ ƒ /api/partners                                                                                                    477 B         159 kB
       ├ ƒ /api/partners/[id]                                                                                               477 B         159 kB
       ├ ƒ /api/preview                                                                                                     204 B         103 kB
       ├ ƒ /api/preview/disable                                                                                             204 B         103 kB
       ├ ƒ /api/preview/enable                                                                                              204 B         103 kB
       ├ ƒ /api/rsvp                                                                                                        477 B         159 kB
       ├ ƒ /api/rsvp/[id]                                                                                                   477 B         159 kB
       ├ ƒ /api/sanity                                                                                                      477 B         159 kB
       ├ ƒ /api/sanity-test                                                                                                 477 B         159 kB
       ├ ƒ /api/sanity/fix-keys                                                                                           11.9 kB         171 kB
       ├ ƒ /api/settings/update                                                                                             477 B         159 kB
       ├ ƒ /api/store/categories                                                                                            477 B         159 kB
       ├ ƒ /api/store/orders                                                                                                477 B         159 kB
       ├ ƒ /api/store/orders/[id]                                                                                           477 B         159 kB
       ├ ƒ /api/store/products                                                                                              477 B         159 kB
       ├ ƒ /api/store/products/[id]                                                                                         477 B         159 kB
       ├ ƒ /api/store/settings                                                                                              477 B         159 kB
       ├ ƒ /api/upload                                                                                                      477 B         159 kB
       ├ ƒ /api/users                                                                                                       477 B         159 kB
       ├ ƒ /api/users/[id]                                                                                                  477 B         159 kB
       ├ ƒ /api/users/current                                                                                               477 B         159 kB
       ├ ƒ /api/users/direct-update                                                                                         477 B         159 kB
       ├ ƒ /api/users/get-user                                                                                              477 B         159 kB
       ├ ƒ /api/users/invite                                                                                                477 B         159 kB
       ├ ƒ /api/users/password                                                                                              477 B         159 kB
       ├ ƒ /api/users/sync                                                                                                  477 B         159 kB
       ├ ƒ /api/users/sync-password                                                                                         477 B         159 kB
       ├ ○ /events                                                                                                        8.68 kB         229 kB          1m      1y
       ├ ○ /forgot-password                                                                                               5.12 kB         128 kB          1m      1y
       ├ ○ /gallery                                                                                                       4.11 kB         237 kB          1m      1y
       ├ ○ /manifest.webmanifest                                                                                            204 B         103 kB
       ├ ○ /news                                                                                                          7.66 kB         213 kB          1m      1y
       ├ ● /news/[slug]                                                                                                   1.64 kB         238 kB
       ├   ├ /news/his-majesty-king-allen-ellison-to-be-crowned-in-historic-ceremony
       ├   ├ /news/the-ellison-outreach-foundation-inc-launches-grow-together-project-in-ghana-to-combat-food-insecurity
       ├   ├ /news/prince-allen-ellison-honored-as-global-heforshe-hero-for-womens-leadership-advocacy
       ├   └ [+11 more paths]
       ├ ● /news/category/[slug]                                                                                            756 B         234 kB          1m      1y
       ├   ├ /news/category/general                                                                                                                       1m      1y
       ├   ├ /news/category/events                                                                                                                        1m      1y
       ├   ├ /news/category/royal-family                                                                                                                  1m      1y
       ├   └ [+8 more paths]
       ├ ○ /news/preview                                                                                                   1.5 kB         234 kB          1m      1y
       ├ ƒ /partners                                                                                                        756 B         234 kB
       ├ ○ /privacy                                                                                                       3.71 kB         147 kB          1m      1y
       ├ ○ /reset-password                                                                                                 5.9 kB         129 kB          1m      1y
       ├ ○ /robots.txt                                                                                                      204 B         103 kB
       ├ ○ /secret                                                                                                        8.45 kB         191 kB          1m      1y
       ├ ○ /sitemap.xml                                                                                                     204 B         103 kB
       ├ ○ /store                                                                                                         6.08 kB         204 kB          1m      1y
       ├ ƒ /store/[slug]                                                                                                  6.82 kB         205 kB
       ├ ○ /store/cart                                                                                                     5.6 kB         203 kB          1m      1y
       ├ ○ /studio/[[...tool]]                                                                                             1.4 MB         1.6 MB
       ├ ○ /unauthorized                                                                                                  4.13 kB         123 kB          1m      1y
       └ ○ /uplift-ghana                                                                                                    189 B         111 kB          1m      1y
       + First Load JS shared by all                                                                                       102 kB
         ├ chunks/1684-d19a61e1eef08a30.js                                                                                46.2 kB
         ├ chunks/4bd1b696-ef24e22af2dd4969.js                                                                            53.2 kB
         └ other shared chunks (total)                                                                                    2.93 kB
       
       
       ƒ Middleware                                                                                                       53.5 kB
       
       ○  (Static)   prerendered as static content
       ●  (SSG)      prerendered as static HTML (uses generateStaticParams)
       ƒ  (Dynamic)  server-rendered on demand
       
       Custom build process completed successfully!
       
-----> Caching build
       - node_modules
       
-----> Pruning devDependencies
       
       up to date in 2s
       
-----> Build succeeded!
-----> Discovering process types
       Procfile declares types -> web
-----> Compressing...
       Done: 496.5M
-----> Launching...
 !     Warning: Your slug size (496 MB) exceeds our soft limit (300 MB) which may affect boot time.
       Released v111
       https://adukingdom-62986aa3239a.herokuapp.com/ deployed to Heroku
