import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';
import { createClient } from '@sanity/client';

// GET /api/rsvp/[id] - Get a specific RSVP
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Missing RSVP ID' },
        { status: 400 }
      );
    }
    
    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error creating write client:', error);
      
      // Fallback to direct client creation
      const token = process.env.SANITY_API_TOKEN;
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Server configuration error: Missing Sanity API token' },
          { status: 500 }
        );
      }
      
      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }
    
    // Fetch the RSVP document
    const rsvp = await client.fetch(`*[_type == "rsvp" && _id == $id][0]`, { id });
    
    if (!rsvp) {
      return NextResponse.json(
        { success: false, message: 'RSVP not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: rsvp
    });
  } catch (error) {
    console.error('Error fetching RSVP:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch RSVP',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/rsvp/[id] - Delete an RSVP
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Missing RSVP ID' },
        { status: 400 }
      );
    }
    
    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error creating write client:', error);
      
      // Fallback to direct client creation
      const token = process.env.SANITY_API_TOKEN;
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Server configuration error: Missing Sanity API token' },
          { status: 500 }
        );
      }
      
      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }
    
    // Check if the RSVP exists
    const rsvp = await client.fetch(`*[_type == "rsvp" && _id == $id][0]`, { id });
    
    if (!rsvp) {
      return NextResponse.json(
        { success: false, message: 'RSVP not found' },
        { status: 404 }
      );
    }
    
    // Delete the RSVP document
    // Use delete instead of transaction to avoid permission issues
    await client.delete(id);
    
    return NextResponse.json({
      success: true,
      message: 'RSVP deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting RSVP:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete RSVP submission',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
