import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getUserById, updateUser } from '@/lib/users';
import { getWriteClient } from '@/lib/sanity.client';
import bcrypt from 'bcryptjs';

// PUT /api/users/password - Update user password
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Get user ID from session
    const userId = session.user.id;

    // Parse request body
    const body = await request.json();
    const { currentPassword, newPassword } = body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { success: false, message: 'Current password and new password are required' },
        { status: 400 }
      );
    }

    // Get user from database
    const user = getUserById(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { success: false, message: 'Current password is incorrect' },
        { status: 400 }
      );
    }

    try {
      // Update user password in local database
      const updatedUser = await updateUser(userId, { password: newPassword });

      // Also update password in Sanity
      try {
        // Get Sanity client
        const client = getWriteClient();

        // Find user in Sanity by email
        const sanityUser = await client.fetch(
          `*[_type == "adminUser" && email == $email][0]`,
          { email: updatedUser.email }
        );

        if (sanityUser) {
          console.log(`Updating password in Sanity for user: ${updatedUser.email} (${sanityUser._id})`);

          // Get the hashed password from the updated local user
          const localUser = getUserById(userId);

          if (!localUser || !localUser.password) {
            console.error('Failed to get hashed password from local user');
            throw new Error('Failed to get hashed password');
          }

          console.log('Updating Sanity user password with hash from local user');

          // Update password in Sanity
          await client
            .patch(sanityUser._id)
            .set({
              hashedPassword: localUser.password, // Use the hashed password from local user
              updatedAt: new Date().toISOString()
            })
            .commit();

          console.log(`Password updated in Sanity for user ID: ${sanityUser._id}`);

          console.log('Password updated in Sanity successfully');
        } else {
          console.warn(`User not found in Sanity: ${updatedUser.email}`);
        }
      } catch (sanityError) {
        console.error('Error updating password in Sanity:', sanityError);
        // Don't fail the request if Sanity update fails
      }

      return NextResponse.json({
        success: true,
        message: 'Password updated successfully',
      });
    } catch (error: any) {
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to update password' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating password:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update password' },
      { status: 500 }
    );
  }
}
