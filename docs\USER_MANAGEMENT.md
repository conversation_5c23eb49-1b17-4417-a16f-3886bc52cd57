# User Management System

This document explains the user management system in the Kingdom Adukrom application, including the migration from local JSON storage to Sanity CMS.

## Overview

The application has been updated to use Sanity CMS as the primary source of truth for user data, replacing the previous dual-storage system that used both local JSON files and Sanity CMS. This change improves data consistency, security, and reliability.

## Key Components

1. **User Transaction Utility** (`lib/user-transaction.ts`)
   - Provides transaction-like operations for user management
   - Ensures operations are either completed successfully in both systems or rolled back
   - <PERSON><PERSON> create, update, and delete operations with proper error handling

2. **Authentication System** (`lib/auth.ts`)
   - Uses Sanity CMS as the primary authentication source
   - Falls back to local storage for backward compatibility
   - Updates both systems when users log in

3. **API Routes**
   - `/api/admin/users` - Manages admin users using the transaction utility
   - `/api/admin/users/[id]` - Manages individual admin users

4. **Migration Script** (`scripts/migrate-users-to-sanity.ts`)
   - Migrates all users from local JSON to Sanity CMS
   - Creates a backup of the local JSON file
   - Provides detailed logging of the migration process

## Migration Process

To migrate users from local JSON to Sanity CMS, follow these steps:

1. **Backup your data**
   ```bash
   cp data/users.json data/users.json.backup
   ```

2. **Run the migration script**
   ```bash
   npx ts-node scripts/migrate-users-to-sanity.ts
   ```

3. **Verify the migration**
   - Check the Sanity Studio to ensure all users were migrated correctly
   - Test user authentication to confirm it works with Sanity CMS

4. **Update your code** (already done in this PR)
   - Use the transaction utility for all user operations
   - Treat Sanity CMS as the source of truth

## User Management Best Practices

1. **Always use the transaction utility**
   - Import and use functions from `lib/user-transaction.ts` for all user operations
   - Never modify users directly in either system

2. **Protect super admin users**
   - Set `isProtected: true` for super admin users to prevent accidental deletion
   - The system ensures at least one super admin always exists

3. **Handle errors gracefully**
   - The transaction utility provides detailed error messages
   - Always check for and handle errors in your code

4. **Maintain backward compatibility**
   - The system still supports local JSON for backward compatibility
   - This allows for a gradual transition to Sanity CMS

## Security Considerations

1. **Password Storage**
   - Passwords are hashed using bcrypt before storage
   - Never store or transmit plain-text passwords

2. **User Permissions**
   - Use the role-based permission system to control access
   - Super admin users have full access to all features
   - Regular admin users have limited access based on their role

3. **API Security**
   - All user management API routes require authentication
   - Only super admin users can manage other users

## Troubleshooting

If you encounter issues with user management, check the following:

1. **Authentication Failures**
   - Verify that the user exists in Sanity CMS
   - Check that the password is correct
   - Ensure the user is active (`isActive: true`)

2. **Synchronization Issues**
   - If users appear in one system but not the other, run the migration script again
   - Check the server logs for errors during user operations

3. **Permission Errors**
   - Verify that the user has the correct role
   - Check that the user is authenticated before accessing protected routes

## Future Improvements

1. **Complete Removal of Local JSON**
   - Once all systems are fully migrated to Sanity CMS, the local JSON storage can be removed
   - This will simplify the codebase and improve performance

2. **Enhanced User Profiles**
   - Add additional user profile fields in Sanity CMS
   - Implement user profile images and other metadata

3. **Audit Logging**
   - Implement comprehensive audit logging for all user operations
   - Track who made changes to user accounts and when

## Conclusion

The migration to Sanity CMS as the primary source for user data improves the reliability and consistency of the user management system. By using the transaction utility and following the best practices outlined in this document, you can ensure that user data remains synchronized and secure.
