import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin } from '@/lib/auth-utils';
import { createUserTransaction } from '@/lib/user-transaction';

// GET /api/admin/users - Get all admin users
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to manage users' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Fetch all admin users
    const users = await client.fetch(`
      *[_type == "adminUser"] | order(name asc) {
        _id,
        username,
        name,
        email,
        role,
        image,
        createdAt,
        lastLogin,
        isActive,
        permissions
      }
    `);

    return NextResponse.json({
      success: true,
      users,
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch admin users',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create a new admin user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to create users' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.email || !body.name || !body.role || !body.password) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate username from email if not provided
    const username = body.username || body.email.split('@')[0];

    try {
      // Use the transaction utility to create the user in both systems
      const result = await createUserTransaction({
        name: body.name,
        email: body.email,
        username: username,
        password: body.password,
        role: body.role,
        permissions: body.permissions || {},
        isActive: true,
        isProtected: body.isProtected || false
      });

      return NextResponse.json({
        success: true,
        message: 'User created successfully',
        user: result.user,
      });
    } catch (transactionError) {
      console.error('Transaction error creating user:', transactionError);
      return NextResponse.json(
        {
          success: false,
          message: transactionError instanceof Error ? transactionError.message : 'Failed to create user'
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create admin user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
