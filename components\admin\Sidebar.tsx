'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import {
  Calendar,
  ImageIcon,
  LayoutDashboard,
  Mail,
  MessageSquare,
  Settings,
  FileText,
  ChevronDown,
  ChevronRight,
  ShoppingBag,
  CreditCard,
  Users,
  Shield
} from '@/components/icons';
import { isSuperAdmin } from '@/lib/auth';

interface SidebarItemProps {
  icon: React.ReactNode;
  title: string;
  href: string;
  isActive: boolean;
  hasSubmenu?: boolean;
  isSubmenuOpen?: boolean;
  toggleSubmenu?: () => void;
}

function SidebarItem({
  icon,
  title,
  href,
  isActive,
  hasSubmenu = false,
  isSubmenuOpen = false,
  toggleSubmenu,
}: SidebarItemProps) {
  return (
    <Link
      href={href}
      className={cn(
        'flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors',
        isActive
          ? 'bg-royalBlue text-white'
          : 'text-gray-700 hover:bg-gray-100 hover:text-royalBlue'
      )}
      onClick={(e) => {
        if (hasSubmenu && toggleSubmenu) {
          e.preventDefault();
          toggleSubmenu();
        }
      }}
    >
      <span className="mr-3">{icon}</span>
      <span className="flex-1">{title}</span>
      {hasSubmenu && (
        <span className="ml-auto">
          {isSubmenuOpen ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </span>
      )}
    </Link>
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const [contentSubmenuOpen, setContentSubmenuOpen] = useState(false);
  const [adminSubmenuOpen, setAdminSubmenuOpen] = useState(false);

  // Check if user is super admin
  const userIsSuperAdmin = isSuperAdmin(session);

  return (
    <div className="hidden w-64 flex-shrink-0 border-r bg-white md:flex md:flex-col">
      <div className="flex h-14 items-center border-b px-4">
        <Link href="/admin" className="flex items-center space-x-2">
          <div className="relative h-8 w-8">
            <Image
              src="/Website Images/ghanaian_presidential_palace.png"
              alt="Logo"
              fill
              className="object-contain"
            />
          </div>
          <span className="text-lg font-bold text-royalBlue">Admin Portal</span>
        </Link>
      </div>

      <div className="flex flex-1 flex-col overflow-y-auto p-4">
        <nav className="space-y-1">
          <SidebarItem
            icon={<LayoutDashboard className="h-5 w-5" />}
            title="Dashboard"
            href="/admin"
            isActive={pathname === '/admin'}
          />

          <SidebarItem
            icon={<Calendar className="h-5 w-5" />}
            title="Events"
            href="/admin/events"
            isActive={pathname.startsWith('/admin/events')}
          />

          <SidebarItem
            icon={<FileText className="h-5 w-5" />}
            title="News"
            href="/admin/news"
            isActive={pathname.startsWith('/admin/news')}
          />

          <SidebarItem
            icon={<ImageIcon className="h-5 w-5" />}
            title="Gallery"
            href="/admin/gallery"
            isActive={pathname.startsWith('/admin/gallery')}
          />

          <SidebarItem
            icon={<Mail className="h-5 w-5" />}
            title="RSVP Submissions"
            href="/admin/rsvp"
            isActive={pathname.startsWith('/admin/rsvp')}
          />

          <SidebarItem
            icon={<MessageSquare className="h-5 w-5" />}
            title="Content"
            href="#"
            isActive={pathname.startsWith('/admin/content')}
            hasSubmenu={true}
            isSubmenuOpen={contentSubmenuOpen}
            toggleSubmenu={() => setContentSubmenuOpen(!contentSubmenuOpen)}
          />

          {contentSubmenuOpen && (
            <div className="ml-6 space-y-1 pt-1">
              <SidebarItem
                icon={<FileText className="h-4 w-4" />}
                title="Sanity Studio"
                href="/admin/studio"
                isActive={pathname.startsWith('/admin/studio')}
              />
            </div>
          )}

          {userIsSuperAdmin && (
            <>
              <SidebarItem
                icon={<Shield className="h-5 w-5" />}
                title="Admin Controls"
                href="#"
                isActive={
                  pathname.startsWith('/admin/store') ||
                  pathname.startsWith('/admin/banking') ||
                  pathname.startsWith('/admin/users')
                }
                hasSubmenu={true}
                isSubmenuOpen={adminSubmenuOpen}
                toggleSubmenu={() => setAdminSubmenuOpen(!adminSubmenuOpen)}
              />

              {adminSubmenuOpen && (
                <div className="ml-6 space-y-1 pt-1">
                  <SidebarItem
                    icon={<ShoppingBag className="h-4 w-4" />}
                    title="Store Management"
                    href="/admin/store"
                    isActive={pathname.startsWith('/admin/store')}
                  />
                  <SidebarItem
                    icon={<CreditCard className="h-4 w-4" />}
                    title="Banking & Payments"
                    href="/admin/banking"
                    isActive={pathname.startsWith('/admin/banking')}
                  />
                  <SidebarItem
                    icon={<Users className="h-4 w-4" />}
                    title="User Management"
                    href="/admin/users"
                    isActive={pathname.startsWith('/admin/users')}
                  />
                </div>
              )}
            </>
          )}

          <SidebarItem
            icon={<Settings className="h-5 w-5" />}
            title="Settings"
            href="/admin/settings"
            isActive={pathname.startsWith('/admin/settings')}
          />
        </nav>
      </div>

      <div className="border-t p-4">
        <div className="rounded-md bg-gray-50 p-3 text-center text-xs text-gray-500">
          <p>Kingdom of Adukrom</p>
          <p className="mt-1 text-xs">Admin Portal v1.0</p>
        </div>
      </div>
    </div>
  );
}
