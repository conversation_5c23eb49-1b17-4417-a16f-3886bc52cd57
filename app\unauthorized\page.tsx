'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ShieldAlert, Home, ArrowLeft } from 'lucide-react';

export default function UnauthorizedPage() {
  const router = useRouter();
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-royalBlue to-royalBlue/80 p-4 text-white">
      <div className="text-center max-w-md mx-auto">
        <div className="mb-6 flex justify-center">
          <div className="relative w-24 h-24">
            <Image
              src="/Website Images/Logo.png"
              alt="Kingdom of Adukrom"
              fill
              className="object-contain"
            />
          </div>
        </div>
        
        <div className="bg-white/10 backdrop-blur-sm p-8 rounded-lg shadow-lg border border-white/20">
          <div className="flex justify-center mb-4">
            <ShieldAlert className="h-16 w-16 text-red-400" />
          </div>
          
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          
          <p className="mb-6 text-white/80">
            You don't have permission to access this area. If you believe this is an error, please contact the administrator.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              variant="outline" 
              className="border-white/20 text-white hover:bg-white/10"
              onClick={() => router.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            
            <Button asChild>
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Return to Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
