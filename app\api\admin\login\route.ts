import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

// In a real application, you would use a database or environment variables for this
// This is a simple implementation for multiple admins
interface Admin {
  username: string;
  password: string;
  role: string;
  name: string;
}

// List of admin users
const ADMINS: Admin[] = [
  {
    username: 'admin',
    password: 'kingdom2024',
    role: 'super_admin',
    name: 'Admin'
  },
  {
    username: 'king',
    password: 'kingdom2024',
    role: 'admin',
    name: 'King <PERSON>'
  },
  {
    username: 'editor',
    password: 'kingdom2024',
    role: 'editor',
    name: 'Content Editor'
  }
];

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    // Find the admin with matching username and password
    const admin = ADMINS.find(
      (a) => a.username.toLowerCase() === username.toLowerCase() && a.password === password
    );

    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Invalid username or password' },
        { status: 401 }
      );
    }

    // Create a token with admin info (in a real app, you would use JWT)
    const token = JSON.stringify({
      username: admin.username,
      role: admin.role,
      name: admin.name
    });

    // Set a secure HTTP-only cookie with the auth token
    const cookieStore = cookies();
    cookieStore.set('admin_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: '/',
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Login successful',
        user: {
          username: admin.username,
          role: admin.role,
          name: admin.name
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred' },
      { status: 500 }
    );
  }
}
