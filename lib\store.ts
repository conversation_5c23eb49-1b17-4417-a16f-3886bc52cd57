import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getGalleryImages, getNews, getCategories } from './sanity';
import { GalleryImage, NewsListItem, Category } from './types';

interface AppState {
  // Navigation state
  activeSection: string;
  isScrolled: boolean;
  isMobileMenuOpen: boolean;
  setActiveSection: (section: string) => void;
  setIsScrolled: (isScrolled: boolean) => void;
  setIsMobileMenuOpen: (isOpen: boolean) => void;
  toggleMobileMenu: () => void;
  
  // Gallery state
  galleryImages: GalleryImage[];
  filteredGalleryImages: GalleryImage[];
  selectedGalleryImage: string | null;
  galleryFilter: string;
  isGalleryLoading: boolean;
  fetchGalleryImages: () => Promise<void>;
  setGalleryFilter: (filter: string) => void;
  setSelectedGalleryImage: (image: string | null) => void;
  
  // News state
  newsArticles: NewsListItem[];
  filteredNewsArticles: NewsListItem[];
  categories: Category[];
  selectedCategory: string;
  isNewsLoading: boolean;
  searchQuery: string;
  fetchNewsData: () => Promise<void>;
  setSelectedCategory: (category: string) => void;
  setSearchQuery: (query: string) => void;
  
  // Theme state
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Navigation state
      activeSection: 'home',
      isScrolled: false,
      isMobileMenuOpen: false,
      setActiveSection: (section) => set({ activeSection: section }),
      setIsScrolled: (isScrolled) => set({ isScrolled }),
      setIsMobileMenuOpen: (isOpen) => set({ isMobileMenuOpen: isOpen }),
      toggleMobileMenu: () => set((state) => ({ isMobileMenuOpen: !state.isMobileMenuOpen })),
      
      // Gallery state
      galleryImages: [],
      filteredGalleryImages: [],
      selectedGalleryImage: null,
      galleryFilter: 'all',
      isGalleryLoading: false,
      fetchGalleryImages: async () => {
        set({ isGalleryLoading: true });
        try {
          const images = await getGalleryImages();
          set({ 
            galleryImages: images,
            filteredGalleryImages: images,
            isGalleryLoading: false 
          });
        } catch (error) {
          console.error('Error fetching gallery images:', error);
          set({ isGalleryLoading: false });
        }
      },
      setGalleryFilter: (filter) => {
        set({ galleryFilter: filter });
        const { galleryImages } = get();
        const filtered = filter === 'all' 
          ? galleryImages 
          : galleryImages.filter(item => item.category === filter);
        set({ filteredGalleryImages: filtered });
      },
      setSelectedGalleryImage: (image) => set({ selectedGalleryImage: image }),
      
      // News state
      newsArticles: [],
      filteredNewsArticles: [],
      categories: [],
      selectedCategory: '',
      isNewsLoading: false,
      searchQuery: '',
      fetchNewsData: async () => {
        set({ isNewsLoading: true });
        try {
          const [newsData, categoriesData] = await Promise.all([
            getNews(),
            getCategories(),
          ]);
          set({ 
            newsArticles: newsData,
            filteredNewsArticles: newsData,
            categories: categoriesData,
            isNewsLoading: false 
          });
        } catch (error) {
          console.error('Error fetching news data:', error);
          set({ isNewsLoading: false });
        }
      },
      setSelectedCategory: (category) => {
        set({ selectedCategory: category });
        const { newsArticles, searchQuery } = get();
        
        // Filter by category and search query
        const filtered = newsArticles.filter(article => {
          const matchesCategory = !category || 
            (article.categories && article.categories.includes(category));
          
          const matchesSearch = !searchQuery || 
            article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (article.excerpt && article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()));
          
          return matchesCategory && matchesSearch;
        });
        
        set({ filteredNewsArticles: filtered });
      },
      setSearchQuery: (query) => {
        set({ searchQuery: query });
        const { newsArticles, selectedCategory } = get();
        
        // Filter by category and search query
        const filtered = newsArticles.filter(article => {
          const matchesCategory = !selectedCategory || 
            (article.categories && article.categories.includes(selectedCategory));
          
          const matchesSearch = !query || 
            article.title.toLowerCase().includes(query.toLowerCase()) ||
            (article.excerpt && article.excerpt.toLowerCase().includes(query.toLowerCase()));
          
          return matchesCategory && matchesSearch;
        });
        
        set({ filteredNewsArticles: filtered });
      },
      
      // Theme state
      theme: 'light',
      toggleTheme: () => set((state) => ({ 
        theme: state.theme === 'light' ? 'dark' : 'light' 
      })),
    }),
    {
      name: 'kingdom-storage',
      partialize: (state) => ({ 
        theme: state.theme,
        // Only persist theme and other non-fetch-dependent state
      }),
    }
  )
);

// Helper hook for navigation
export function useNavigation() {
  return useAppStore(state => ({
    activeSection: state.activeSection,
    isScrolled: state.isScrolled,
    isMobileMenuOpen: state.isMobileMenuOpen,
    setActiveSection: state.setActiveSection,
    setIsScrolled: state.setIsScrolled,
    setIsMobileMenuOpen: state.setIsMobileMenuOpen,
    toggleMobileMenu: state.toggleMobileMenu,
  }));
}

// Helper hook for gallery
export function useGallery() {
  return useAppStore(state => ({
    galleryImages: state.galleryImages,
    filteredGalleryImages: state.filteredGalleryImages,
    selectedGalleryImage: state.selectedGalleryImage,
    galleryFilter: state.galleryFilter,
    isGalleryLoading: state.isGalleryLoading,
    fetchGalleryImages: state.fetchGalleryImages,
    setGalleryFilter: state.setGalleryFilter,
    setSelectedGalleryImage: state.setSelectedGalleryImage,
  }));
}

// Helper hook for news
export function useNews() {
  return useAppStore(state => ({
    newsArticles: state.newsArticles,
    filteredNewsArticles: state.filteredNewsArticles,
    categories: state.categories,
    selectedCategory: state.selectedCategory,
    isNewsLoading: state.isNewsLoading,
    searchQuery: state.searchQuery,
    fetchNewsData: state.fetchNewsData,
    setSelectedCategory: state.setSelectedCategory,
    setSearchQuery: state.setSearchQuery,
  }));
}

// Helper hook for theme
export function useTheme() {
  return useAppStore(state => ({
    theme: state.theme,
    toggleTheme: state.toggleTheme,
  }));
}
