'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { LogIn, LogOut, User } from 'lucide-react';

export default function SessionDebug() {
  const { data: session, status } = useSession();
  const [isVisible, setIsVisible] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Log session info for debugging
    console.log('SessionDebug Component:', {
      session,
      status,
      isAdmin: session?.user?.role === 'admin' || session?.user?.role === 'super_admin',
      role: session?.user?.role
    });
  }, [session, status]);

  // Don't render anything during SSR
  if (!mounted) return null;

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="fixed bottom-4 left-4 z-50 bg-black text-white"
        onClick={() => setIsVisible(true)}
      >
        <User className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/90 text-white p-4 rounded-lg shadow-lg max-w-md text-sm">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-lg">Session Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-white hover:text-red-500"
        >
          ✕
        </button>
      </div>
      <div className="space-y-2">
        <p><strong>Status:</strong> <span className={status === 'authenticated' ? 'text-green-400' : 'text-red-400'}>{status}</span></p>
        <p><strong>User:</strong> {session?.user?.name || 'Not logged in'}</p>
        <p><strong>Email:</strong> {session?.user?.email || 'N/A'}</p>
        <p><strong>Role:</strong> <span className={session?.user?.role ? 'text-green-400' : 'text-red-400'}>{session?.user?.role || 'No role'}</span></p>
        <p><strong>Is Admin:</strong> {(session?.user?.role === 'admin' || session?.user?.role === 'superadmin') ? '✅ Yes' : '❌ No'}</p>
      </div>

      <div className="mt-4 flex space-x-2">
        {status === 'authenticated' ? (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => signOut()}
            className="w-full"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        ) : (
          <Button
            variant="default"
            size="sm"
            onClick={() => signIn()}
            className="w-full"
          >
            <LogIn className="h-4 w-4 mr-2" />
            Sign In
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => window.location.href = '/admin'}
          className="w-full"
        >
          Go to Admin
        </Button>
      </div>

      <div className="mt-2 text-xs text-gray-400">
        <p>This debug panel is only visible during development.</p>
      </div>
    </div>
  );
}
