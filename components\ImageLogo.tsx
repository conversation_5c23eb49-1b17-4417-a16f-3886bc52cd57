'use client';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

interface ImageLogoProps {
  href?: string;
  width?: number;
  height?: number;
  className?: string;
}

export default function ImageLogo({
  href = '/',
  width = 100,
  height = 100,
  className = ''
}: ImageLogoProps) {
  return (
    <Link href={href} className={`flex items-center ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
        animate={{
          opacity: 1,
          scale: 1,
          rotate: 0,
          y: [0, -5, 0]
        }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          duration: 1.2,
          y: {
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }
        }}
        whileHover={{
          scale: 1.05,
          rotate: [0, -5, 5, -5, 0],
          transition: { duration: 0.5 }
        }}
        className="relative"
      >
        {/* No glow effect */}
        <Image
          src="/images/ai-logo1a.png"
          alt="Adukrom Kingdom Logo"
          width={width}
          height={height}
          className="object-contain relative z-10"
        />
      </motion.div>
      <motion.div
        className="ml-3"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <motion.h1
          className="text-white text-lg md:text-xl font-bold"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.0 }}
          whileHover={{
            color: "#D4AF37",
            transition: { duration: 0.3 }
          }}
        >
          Adukrom Kingdom
        </motion.h1>
        <motion.p
          className="text-royalGold text-xs md:text-sm"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.2 }}
          whileHover={{
            scale: 1.05,
            transition: { duration: 0.3 }
          }}
        >
          The Crown of Africa
        </motion.p>
      </motion.div>
    </Link>
  );
}
