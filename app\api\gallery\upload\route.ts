import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin, isAdmin } from '@/lib/auth-utils';
import { createClient } from '@sanity/client';
import crypto from 'crypto';

// POST /api/gallery/upload - Upload images to Sanity
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to upload images' },
        { status: 401 }
      );
    }

    // Get the Sanity write client (with server-side token)
    let client;
    try {
      client = getWriteClient();
      console.log('Successfully created Sanity write client');
    } catch (error) {
      console.error('Error creating write client:', error);

      // Fallback to direct client creation
      const token = process.env.SANITY_API_TOKEN;
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Server configuration error: Missing Sanity API token' },
          { status: 500 }
        );
      }

      console.log('Using fallback Sanity client');
      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }

    // Parse the request body
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const titles = JSON.parse(formData.get('titles') as string || '[]');
    const descriptions = JSON.parse(formData.get('descriptions') as string || '[]');
    const categories = JSON.parse(formData.get('categories') as string || '[]');
    const mediaTypes = JSON.parse(formData.get('mediaTypes') as string || '[]');

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No media files provided' },
        { status: 400 }
      );
    }

    // Check for duplicate files by calculating hashes
    const fileHashes = new Map<string, number>();
    const duplicates = new Map<number, number>();

    // Calculate hash for each file to detect duplicates
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const hash = crypto.createHash('md5').update(buffer).digest('hex');

      if (fileHashes.has(hash)) {
        // This is a duplicate, mark it
        duplicates.set(i, fileHashes.get(hash)!);
        console.log(`Duplicate detected: File ${i} is a duplicate of file ${fileHashes.get(hash)}`);
      } else {
        fileHashes.set(hash, i);
      }
    }

    const results = [];

    // Process each media file
    for (let i = 0; i < files.length; i++) {
      try {
        // Skip if this is a duplicate file
        if (duplicates.has(i)) {
          const originalIndex = duplicates.get(i)!;
          console.log(`Skipping duplicate file ${i}, using reference from file ${originalIndex} instead`);

          // Add a result for the duplicate, but mark it as a duplicate
          results.push({
            duplicate: true,
            originalIndex: originalIndex,
            index: i,
            fileName: files[i].name,
            message: `Duplicate file detected. Using existing reference instead of uploading again.`
          });

          continue;
        }

        const file = files[i];
        const mediaType = mediaTypes[i] || 'image'; // Default to image if not specified
        const title = titles[i] || `Untitled ${mediaType === 'video' ? 'Video' : 'Image'} ${new Date().toISOString()}`;
        const description = descriptions[i] || '';
        const category = categories[i] || undefined;

        // Log the category information
        console.log(`Category for ${mediaType} ${i+1}: ${category || 'none'}`);
        if (category) {
          try {
            // Verify the category exists
            const categoryDoc = await client.fetch(`*[_type == "category" && _id == $id][0]`, { id: category });
            if (!categoryDoc) {
              console.warn(`Category with ID ${category} not found, will create document without category reference`);
            } else {
              console.log(`Found category: ${categoryDoc.title}`);
            }
          } catch (categoryError) {
            console.warn(`Error verifying category: ${categoryError}`);
          }
        }

        console.log(`Processing ${mediaType} ${i+1}/${files.length}: ${file.name}, category: ${category || 'none'}`);

        // Convert the file to an array buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Upload the file to Sanity (as image or file based on mediaType)
        console.log(`Uploading ${mediaType} ${i+1} to Sanity...`);
        const assetType = mediaType === 'video' ? 'file' : 'image';
        const asset = await client.assets.upload(assetType, buffer, {
          filename: file.name,
          contentType: file.type,
        });

        console.log(`${mediaType === 'video' ? 'Video' : 'Image'} ${i+1} uploaded successfully, creating document...`);

        // Generate a slug from the title
        const slug = title
          ? title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
          : `${mediaType}-${Date.now()}`;

        // Create the gallery document
        const galleryDoc = {
          _type: 'gallery',
          title: title,
          slug: {
            _type: 'slug',
            current: slug
          },
          description: description,
          // Create an images array with a single media item (matching the schema)
          images: [
            {
              _type: 'object',
              mediaType: mediaType,
              ...(mediaType === 'image' ? {
                image: {
                  _type: 'image',
                  asset: {
                    _type: 'reference',
                    _ref: asset._id
                  },
                  hotspot: asset.hotspot,
                  crop: asset.crop
                }
              } : {
                video: {
                  _type: 'file',
                  asset: {
                    _type: 'reference',
                    _ref: asset._id
                  }
                }
              }),
              alt: title,
              caption: description
            }
          ],
          // Add the category reference if provided and valid
          category: category ? {
            _type: 'reference',
            _ref: category
          } : undefined,
          publishedAt: new Date().toISOString(),
          // Set default values for other fields
          featured: false,
          displayStyle: 'grid',
          backgroundStyle: 'none',
          order: 0
        };

        console.log(`Creating gallery document with title: "${title}"`);

        // Create the document in Sanity
        const document = await client.create(galleryDoc);

        console.log(`Document created for ${mediaType} ${i+1}: ${document._id}`);

        results.push({
          id: document._id,
          title: title,
          mediaType: mediaType,
          url: asset.url,
          index: i
        });
      } catch (mediaError) {
        console.error(`Error processing ${mediaTypes[i] || 'media'} ${i+1}:`, mediaError);

        // Determine the specific error type for better error messages
        let errorMessage = 'Unknown error';
        if (mediaError instanceof Error) {
          errorMessage = mediaError.message;

          // Check for specific error types
          if (errorMessage.includes('asset')) {
            errorMessage = `Failed to upload ${mediaTypes[i] || 'media'} to Sanity. The file may be too large or in an unsupported format.`;
          } else if (errorMessage.includes('slug')) {
            errorMessage = 'Failed to create a valid slug for the title.';
          } else if (errorMessage.includes('reference') || errorMessage.includes('category')) {
            errorMessage = 'Invalid category reference. Please select a valid category or create a new one.';
          } else if (errorMessage.includes('token') || errorMessage.includes('permission')) {
            errorMessage = 'Authentication error. You may not have permission to upload files.';
          }
        }

        // Continue with the next file instead of failing the entire batch
        results.push({
          error: true,
          message: errorMessage,
          index: i,
          fileName: files[i].name,
          fileSize: files[i].size,
          fileType: files[i].type,
          mediaType: mediaTypes[i] || 'unknown'
        });
      }
    }

    // Count successful, failed, and duplicate uploads
    const successCount = results.filter(r => !r.error && !r.duplicate).length;
    const failedCount = results.filter(r => r.error).length;
    const duplicateCount = results.filter(r => r.duplicate).length;

    // Determine overall success status
    const isCompleteSuccess = failedCount === 0;
    const isPartialSuccess = successCount > 0 && failedCount > 0;

    return NextResponse.json({
      success: isCompleteSuccess || isPartialSuccess,
      status: isCompleteSuccess ? 'complete' : (isPartialSuccess ? 'partial' : 'failed'),
      message: isCompleteSuccess
        ? `${successCount} files uploaded successfully${duplicateCount > 0 ? `, ${duplicateCount} duplicates detected` : ''}`
        : (isPartialSuccess
            ? `${successCount} files uploaded successfully, ${failedCount} failed${duplicateCount > 0 ? `, ${duplicateCount} duplicates detected` : ''}`
            : 'Failed to upload any files'),
      results,
      stats: {
        total: results.length,
        success: successCount,
        failed: failedCount,
        duplicates: duplicateCount
      }
    });
  } catch (error) {
    console.error('Error uploading media files:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to upload media files',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
