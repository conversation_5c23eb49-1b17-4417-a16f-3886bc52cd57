'use client';
import Link from 'next/link';
import { motion } from 'framer-motion';
import ScrollReveal from './ScrollReveal';
import { useEffect, useState } from 'react';
import { getFeaturedGallery } from '@/lib/sanity';


// Define GalleryItem type to match Sanity schema
type GalleryItem = {
  _id: string;
  title: string;
  description: string;
  image: any; // Sanity image type
  category?: {
    title: string;
    slug: { current: string };
  };
};

export default function Gallery() {
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        const featuredGallery = await getFeaturedGallery();
        setGalleryItems(featuredGallery);
      } catch (error) {
        console.error('Error fetching gallery:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGallery();
  }, []);

  // Helper function to safely get image URL from Sanity
  const getImageUrl = (image: any): string => {
    if (!image) return '/placeholder.jpg';

    try {
      // Use a simple approach that works with the fallback mechanism in urlFor
      const imageUrl = require('@/lib/sanity.client').urlFor(image).url();
      return imageUrl || '/placeholder.jpg';
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '/placeholder.jpg';
    }
  };

  return (
    <section id="gallery" className="py-20 bg-ivory relative">
      <div className="container mx-auto px-4">
        <ScrollReveal animation="fadeInDown">
          <h2 className="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-6 section-title">
            Royal Gallery
          </h2>
          <p className="text-center text-royalBlue/80 max-w-3xl mx-auto mb-16">
            Explore the rich cultural heritage and royal traditions of the Kingdom of Adukrom through our curated gallery of images.
          </p>
        </ScrollReveal>

        {isLoading ? (
          <div className="col-span-full flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {/* Use gallery items from Sanity if available, otherwise use hardcoded items */}
            {(galleryItems.length > 0 ? galleryItems : [
              {
                _id: 'gallery-1',
                title: 'Royal Coronation Ceremony',
                description: 'The official coronation ceremony of the King',
                image: '/Website Images/gallery/coronation1.jpg',
                category: { title: 'Ceremonies' }
              },
              {
                _id: 'gallery-2',
                title: 'Traditional Dance Performance',
                description: 'Cultural dance performances during royal events',
                image: '/Website Images/gallery/traditional-dance.jpg',
                category: { title: 'Culture' }
              },
              {
                _id: 'gallery-3',
                title: 'Royal Palace',
                description: 'The majestic royal palace of Adukrom',
                image: '/Website Images/gallery/palace.jpg',
                category: { title: 'Architecture' }
              },
              {
                _id: 'gallery-4',
                title: 'Community Gathering',
                description: 'Community members gathering for a royal event',
                image: '/Website Images/gallery/community.jpg',
                category: { title: 'Events' }
              },
              {
                _id: 'gallery-5',
                title: 'Royal Artifacts',
                description: 'Historical artifacts from the kingdom',
                image: '/Website Images/gallery/artifacts.jpg',
                category: { title: 'Heritage' }
              },
              {
                _id: 'gallery-6',
                title: 'Traditional Ceremony',
                description: 'Traditional ceremonies of the kingdom',
                image: '/Website Images/gallery/ceremony.jpg',
                category: { title: 'Ceremonies' }
              }
            ]).map((item, index) => (
              <ScrollReveal key={item._id} animation="fadeInUp" delay={0.1 * index}>
                <motion.div
                  className="rounded-lg overflow-hidden shadow-lg h-full"
                  whileHover={{
                    y: -5,
                    boxShadow: "0 20px 25px rgba(0, 0, 0, 0.2)",
                    transition: { duration: 0.3 }
                  }}
                >
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <motion.img
                      src={typeof item.image === 'string' ? item.image : getImageUrl(item.image)}
                      alt={item.title}
                      className="w-full h-full object-contain bg-gray-100"
                      initial={{ scale: 1 }}
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.5 }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                      <div className="p-4 text-white">
                        <h3 className="text-lg font-bold">{item.title}</h3>
                        {item.category && (
                          <span className="text-sm text-gray-200">{item.category.title}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              </ScrollReveal>
            ))}
          </div>
        )}

        <div className="text-center mt-12">
          <Link
            href="/gallery"
            className="inline-flex items-center px-8 py-3 bg-royalBlue text-white rounded-full hover:bg-royalBlue/90 transition-colors shadow-lg"
          >
            View Full Gallery
          </Link>
        </div>
      </div>
    </section>
  );
}