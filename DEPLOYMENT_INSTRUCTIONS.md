# Deployment Instructions for Kingdom of Adukrom Website (Updated)

## Summary of Changes Made

1. **Fixed Node.js Compatibility Issues**:
   - Updated Node.js version to 20.x in package.json
   - Modified Sanity client usage in RSVP.tsx to be compatible with both older and newer versions
   - Added fallback installation of Sanity client in heroku-build.js

2. **Resolved Merge Conflicts**:
   - Fixed conflicts in RSVP.tsx component
   - Removed duplicate gallery page (app/gallery/page.tsx)
   - Ensured Footer.tsx and Header.tsx are working correctly

3. **Added Deployment Scripts**:
   - Created deploy-heroku.js for Heroku deployment
   - Created push-to-github.js for GitHub deployment
   - Added npm scripts for easy deployment

## Manual Deployment to Heroku

Since you're experiencing authentication issues with Heroku Git, here's how to deploy manually:

### Option 1: Deploy from GitHub

1. Log in to your Heroku account: https://dashboard.heroku.com/
2. Create a new app (if you haven't already): `kingdomadukrom`
3. Go to the "Deploy" tab
4. Under "Deployment method", select "GitHub"
5. Connect to your GitHub repository: `joelgriiyo/kingdomadukrom`
6. Choose the branch to deploy: `main`
7. Click "Deploy Branch"

### Option 2: Deploy using Heroku CLI

```bash
# Login to Heroku
heroku login

# Create a new app (if you haven't already)
heroku create kingdomadukrom

# Set the buildpack
heroku buildpacks:set heroku/nodejs -a kingdomadukrom

# Set environment variables
heroku config:set NODE_ENV=production -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_DATASET=production -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_API_VERSION=2025-05-09 -a kingdomadukrom

# Deploy your code
git push heroku main
```

### Option 3: Deploy using Heroku Dashboard

1. Create a ZIP file of your project (excluding node_modules and .git folders)
2. Log in to your Heroku account: https://dashboard.heroku.com/
3. Create a new app (if you haven't already): `kingdomadukrom`
4. Go to the "Deploy" tab
5. Under "Deployment method", select "Container Registry"
6. Follow the instructions to deploy your container

## Local Development

You can use any of these commands to run the application locally:

```bash
# Run the Next.js development server
npm run dev

# Run the Express server for static files
npm run dev:express

# Run the local preview server
npm run dev:local
```

## GitHub Deployment

To push your changes to GitHub:

```bash
# Push to GitHub
npm run push
```

This script will:
1. Check if you have any uncommitted changes and offer to commit them
2. Ask which branch you want to push from and to
3. Ask if you want to force push

## Troubleshooting

If you encounter any issues during deployment, check the following:

1. **Node.js Version**: Make sure Heroku is using Node.js 20.x (check the package.json)
2. **Sanity Client**: If you get errors related to Sanity, try downgrading to version 6.x
3. **Build Errors**: Check the Heroku build logs for specific errors
4. **Environment Variables**: Ensure all required environment variables are set

## Next Steps

After successful deployment:

1. Test the website thoroughly
2. Set up a custom domain (if needed)
3. Configure SSL
4. Set up monitoring and analytics

## Contact

If you need further assistance, please contact the development team.
