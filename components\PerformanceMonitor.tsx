/**
 * PerformanceMonitor Component
 * 
 * A component that monitors and reports web vitals metrics.
 * Add this component to your layout to track performance.
 */

'use client';

import { useEffect, useState } from 'react';
import { useReportWebVitals } from 'next/web-vitals';

interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  label: string;
  startTime: number;
}

export default function PerformanceMonitor({ 
  debug = false 
}: { 
  debug?: boolean 
}) {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  
  useReportWebVitals((metric) => {
    // Store the metric
    setMetrics((prev) => [...prev, {
      id: metric.id,
      name: metric.name,
      value: metric.value,
      label: metric.label,
      startTime: metric.startTime,
    }]);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development' || debug) {
      console.log('Web Vital:', metric);
    }
    
    // You can send the metric to your analytics service here
    // Example: sendToAnalytics(metric);
  });
  
  // Display metrics in debug mode
  if (!debug) return null;
  
  return (
    <div className="fixed bottom-0 right-0 z-50 p-4 bg-black/80 text-white text-xs rounded-tl-lg max-w-xs max-h-64 overflow-auto">
      <h3 className="font-bold mb-2">Performance Metrics</h3>
      <ul>
        {metrics.map((metric, i) => (
          <li key={i} className="mb-1">
            <span className="font-semibold">{metric.name}:</span>{' '}
            {metric.value.toFixed(2)} ({metric.label})
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * Helper function to send metrics to an analytics service
 */
function sendToAnalytics(metric) {
  // Replace with your analytics code
  const body = JSON.stringify({
    name: metric.name,
    value: metric.value,
    id: metric.id,
    label: metric.label,
    startTime: metric.startTime,
    url: window.location.href,
  });
  
  // Example: Send to an endpoint
  // if (navigator.sendBeacon) {
  //   navigator.sendBeacon('/api/analytics', body);
  // } else {
  //   fetch('/api/analytics', {
  //     body,
  //     method: 'POST',
  //     keepalive: true,
  //   });
  // }
}
