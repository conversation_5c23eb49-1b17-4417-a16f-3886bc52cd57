'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Eye,
  Trash,
  Edit,
  Calendar,
  MapPin,
  Plus,
  Clock,
  Star,
  StarOff,
  Timer,
  Loader2,
  Upload,
} from 'lucide-react';
import { getSiteSettings } from '@/lib/sanity';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { useEventStore } from '@/lib/stores/eventStore';
import { useEventsQuery, useDeleteEventMutation, useSetCountdownTargetMutation } from '@/lib/hooks/useEvents';
import { formatErrorMessage } from '@/lib/errorHandling';

// Define the Event type
interface Event {
  _id: string;
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
}

export default function EventsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [coronationDate, setCoronationDate] = useState<string | null>(null);

  // State for confirmation dialogs
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [countdownDialogOpen, setCountdownDialogOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  // Use the events store and query hooks
  const { events: storeEvents, isLoading: storeLoading, error: storeError } = useEventStore();
  const { data: queryEvents, isLoading: queryLoading, error: queryError } = useEventsQuery();

  // Type assertion to fix TypeScript errors
  const typedQueryEvents = queryEvents as Event[] | undefined;

  useEffect(() => {
    // If we have events from the query, use those
    if (typedQueryEvents && typedQueryEvents.length > 0) {
      setEvents(typedQueryEvents);
      toast.success(`Loaded ${typedQueryEvents.length} events`);
    }
    // Otherwise, if we have events from the store, use those
    else if (storeEvents && storeEvents.length > 0) {
      setEvents(storeEvents);
    }

    // Fetch site settings to get coronation date
    const fetchSettings = async () => {
      try {
        const settings = await getSiteSettings();
        if (settings && settings.events && settings.events.coronationDate) {
          console.log('Coronation date from settings:', settings.events.coronationDate);
          setCoronationDate(settings.events.coronationDate);
        }
      } catch (settingsError) {
        console.error('Failed to fetch site settings:', settingsError);
        // Use a hardcoded coronation date if settings fetch fails
        setCoronationDate('2024-12-15T10:00:00.000Z');
      }
    };

    fetchSettings();
  }, [typedQueryEvents, storeEvents]);

  // Set loading state based on query and store loading states
  useEffect(() => {
    setIsLoading(queryLoading || storeLoading);
  }, [queryLoading, storeLoading]);

  // Show error toast if there's an error
  useEffect(() => {
    if (queryError) {
      toast.error('Failed to load events: ' + formatErrorMessage(queryError));
    } else if (storeError) {
      toast.error(storeError);
    }
  }, [queryError, storeError]);

  // Filter events based on search term
  const filteredEvents = events.filter(
    (event) =>
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase())
  );



  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Parse the date string - JavaScript automatically converts UTC to local time
      const date = new Date(dateString);

      // Format the date in the user's local time zone
      return format(date, 'MMM d, yyyy h:mm a');
    } catch (error) {
      console.error('Error formatting date:', error, dateString);
      return 'Invalid date';
    }
  };



  // Use the delete event mutation
  const deleteEventMutation = useDeleteEventMutation();

  // Delete event
  const deleteEvent = async () => {
    if (!selectedEventId) return;

    try {
      await deleteEventMutation.mutateAsync(selectedEventId);

      // Update the local state by removing the deleted event
      setEvents(prev => prev.filter(event => event._id !== selectedEventId));

      // Close the dialog
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Failed to delete event:', error);
      // Error is handled by the mutation
    }
  };

  // Use the set countdown target mutation
  const setCountdownTargetMutation = useSetCountdownTargetMutation();

  // Set an event as the countdown target
  const setAsCountdownTarget = async () => {
    if (!selectedEventId) return;

    try {
      await setCountdownTargetMutation.mutateAsync(selectedEventId);

      // Update the local state
      setEvents(prev => prev.map(event => ({
        ...event,
        isCountdownTarget: event._id === selectedEventId
      })));

      // Close the dialog
      setCountdownDialogOpen(false);
    } catch (error) {
      console.error('Failed to update countdown target:', error);
      // Error is handled by the mutation
    }
  };

  return (
    <div className="space-y-6">
      {/* Confirmation Dialogs */}
      <ConfirmationDialog
        title="Delete Event"
        description="Are you sure you want to delete this event? This action cannot be undone."
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={deleteEvent}
        confirmText="Delete"
        variant="destructive"
      />

      <ConfirmationDialog
        title="Set as Countdown Target"
        description="This will make this event the main countdown target on the website. Any previously set countdown target will be unset."
        open={countdownDialogOpen}
        onOpenChange={setCountdownDialogOpen}
        onConfirm={setAsCountdownTarget}
        confirmText="Set as Target"
        variant="default"
      />

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Events Management</h1>
          <p className="text-muted-foreground">
            Create and manage events for the Kingdom website.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => {
            toast.info('To upload events from the user-facing page to Sanity, run this command in your terminal:');
            toast.info('npm install @sanity/client dotenv');
            toast.info('node scripts/upload-events-to-sanity.js');
          }}>
            <Upload className="mr-2 h-4 w-4" />
            Upload to Sanity
          </Button>
          <Button asChild>
            <Link href="/admin/events/new">
              <Plus className="mr-2 h-4 w-4" />
              Add New Event
            </Link>
          </Button>
        </div>
      </div>

      <Separator />

      <div className="flex items-center gap-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search events..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Coronation Date from Settings */}
      {coronationDate && (
        <Card className="border-blue-200 bg-blue-50 mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-blue-800">Coronation Date from Settings</CardTitle>
            <CardDescription className="text-blue-700">
              This date is used for the countdown timer if no event is marked as the countdown target.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center text-blue-800">
              <Calendar className="h-5 w-5 mr-2" />
              <span className="font-medium">{formatDate(coronationDate)}</span>
            </div>
            <p className="mt-2 text-sm text-blue-700">
              You can update this date in the <Link href="/admin/settings" className="underline">Settings</Link> page.
            </p>
          </CardContent>
        </Card>
      )}



      <Card>
        <CardHeader>
          <CardTitle>Events</CardTitle>
          <CardDescription>
            Showing {filteredEvents.length} events
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <span className="ml-2">Loading events...</span>
            </div>
          ) : filteredEvents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Event</th>
                    <th className="text-left p-2">Date</th>
                    <th className="text-left p-2">Location</th>
                    <th className="text-right p-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEvents.map((event) => (
                    <tr key={event._id} className="border-b">
                      <td className="p-2 font-medium">
                        {event.title}
                        {event.isCountdownTarget && (
                          <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                            Countdown
                          </span>
                        )}
                      </td>
                      <td className="p-2">
                        {formatDate(event.date)}
                      </td>
                      <td className="p-2">
                        {event.location}
                      </td>
                      <td className="p-2 text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/admin/events/${event._id}`}>
                              View
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/admin/events/${event._id}/edit`}>
                              Edit
                            </Link>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedEventId(event._id);
                              setDeleteDialogOpen(true);
                            }}
                            className="text-red-500 border-red-200"
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No events found</h3>
              <p className="mt-2 text-muted-foreground">
                Try adjusting your search or create a new event.
              </p>
              <Button className="mt-4" asChild>
                <Link href="/admin/events/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Event
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
