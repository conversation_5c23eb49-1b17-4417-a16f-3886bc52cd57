'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { logError } from '@/lib/errorHandling';

interface SanityOperationParams {
  operation: 'create' | 'update' | 'patch' | 'delete';
  document?: any;
  params?: {
    id?: string;
    patch?: Record<string, any>;
  };
}

/**
 * Hook for interacting with <PERSON>ity through the secure API route
 */
export function useSanity() {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  /**
   * Perform a Sanity operation through the secure API route
   */
  const performOperation = async (params: SanityOperationParams) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/sanity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to perform Sanity operation');
      }
      
      const data = await response.json();
      return data.result;
    } catch (error) {
      logError(error, `useSanity.${params.operation}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Create a new document
   */
  const createDocument = useMutation({
    mutationFn: (document: any) => 
      performOperation({ operation: 'create', document }),
    onSuccess: () => {
      toast.success('Document created successfully');
      // Invalidate relevant queries
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`Failed to create document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  /**
   * Update an existing document
   */
  const updateDocument = useMutation({
    mutationFn: (document: any) => 
      performOperation({ operation: 'update', document }),
    onSuccess: () => {
      toast.success('Document updated successfully');
      // Invalidate relevant queries
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`Failed to update document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  /**
   * Patch an existing document
   */
  const patchDocument = useMutation({
    mutationFn: ({ id, patch }: { id: string; patch: Record<string, any> }) => 
      performOperation({ operation: 'patch', params: { id, patch } }),
    onSuccess: () => {
      toast.success('Document patched successfully');
      // Invalidate relevant queries
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`Failed to patch document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  /**
   * Delete a document
   */
  const deleteDocument = useMutation({
    mutationFn: (id: string) => 
      performOperation({ operation: 'delete', params: { id } }),
    onSuccess: () => {
      toast.success('Document deleted successfully');
      // Invalidate relevant queries
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast.error(`Failed to delete document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  });

  return {
    isLoading,
    createDocument,
    updateDocument,
    patchDocument,
    deleteDocument,
  };
}
