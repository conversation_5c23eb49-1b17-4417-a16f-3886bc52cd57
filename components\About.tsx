'use client';
import { motion } from 'framer-motion';
import <PERSON>rollReveal from './ScrollReveal';
import { MapPin, Truck, Zap, Leaf } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { getSiteSettings } from '@/lib/sanity';
import { AboutSection } from '@/lib/types/sanity';


export default function About() {
  const [isMounted, setIsMounted] = useState(false);
  // Using state for content even though we don't directly use the content variable in the component
  // This is because we need to update the content from the Sanity API
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setContent] = useState<AboutSection>({
    kingBio: '',
    kingVision: '',
    kingMission: '',
    kingPurpose: '',
    kingQuote: '',
    adukromDescription: [],
    adukromLocation: '',
    nifaheneDescription: []
  });

  useEffect(() => {
    setIsMounted(true);

    // Fetch content from Sanity
    const fetchContent = async () => {
      try {
        // Fetch settings from Sanity with the updated schema that includes about section
        const settings = await getSiteSettings();

        // Check if settings and about section exist
        if (settings && settings.about) {
          setContent({
            kingBio: settings.about.kingBio || '',
            kingVision: settings.about.kingVision || '',
            kingMission: settings.about.kingMission || '',
            kingPurpose: settings.about.kingPurpose || '',
            kingQuote: settings.about.kingQuote || '',
            adukromDescription: settings.about.adukromDescription || [],
            adukromLocation: settings.about.adukromLocation || '',
            nifaheneDescription: settings.about.nifaheneDescription || []
          });
        }
      } catch (error) {
        console.error('Error fetching about content:', error);
      }
    };

    fetchContent();
  }, [setContent]);

  if (!isMounted) {
    return null; // Return null on server-side and first render
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  // No state needed for editing anymore

  return (
    <section id="about" className="relative">
      {/* King Allen Ellison Section - Light Background */}
      <div className="py-16 bg-[#f9f7e8]">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-royalBlue mb-12 drop-shadow-lg tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            About King Allen Ellison
            <motion.div
              className="w-48 h-1 bg-royalGold mt-2 rounded-full shadow-lg"
              initial={{ width: 0 }}
              animate={{ width: 192 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            />
          </motion.h2>

          <div className="flex flex-col md:flex-row gap-12 items-start">
            <motion.div
              className="md:w-2/5"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="relative flex flex-col items-center">
                <motion.div
                  whileHover={{ scale: 1.04, boxShadow: '0 8px 32px 0 rgba(218,165,32,0.25)' }}
                  transition={{ duration: 0.3 }}
                  className="rounded-xl overflow-hidden shadow-2xl border-4 border-royalGold bg-gradient-to-br from-[#fffbe6] to-[#f9f7e8]"
                >
                  <Image
                    src="/Website Images/Allen-Ellison-Profile-scaled-1.png"
                    alt="King Allen Ellison"
                    width={500}
                    height={600}
                    className="rounded-xl object-cover shadow-xl border-4 border-royalGold"
                  />
                </motion.div>
                <motion.div
                  className="mt-8 p-6 bg-white/90 border-l-4 border-royalGold shadow-md rounded-lg backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <p className="italic text-royalBlue text-lg">
                    &ldquo;Our kingdom&apos;s strength lies in honoring our ancestors while building bridges to the future.
                    Together, we will create prosperity that respects our traditions and embraces innovation for The Rebirth
                    of Adukrom.&rdquo;
                  </p>
                  <p className="text-right mt-2 font-semibold text-royalBlue">— King Allen Ellison</p>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              className="md:w-3/5"
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
            >
              <motion.div className="mb-8" variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">Biography</h3>
                <p className="text-charcoal mb-4">
                  His Majesty King Allen Ellison, globally known for his visionary leadership, is being enstooled Mpuntuhene of Adukrom, Eastern Region of Ghana—an esteemed royal office dedicated to advancing trade, investment, innovation and economic development for the Kingdom and beyond. A statesman, entrepreneur, humanitarian, and cultural ambassador, King Allen stands at the intersection of African tradition and global transformation.
                </p>
                <p className="text-charcoal mb-4">
                  Born in Avon Park, Florida, USA, Allen Ellison has risen from humble beginnings to become one of the most dynamic royal figures of the 21st century. With a Bachelor of Arts degree in Political Science and Business Administration from Florida Southern College, he has spent over two decades driving economic empowerment, financial literacy, and sustainable development across communities in the United States, Asia the Caribbean, and Africa.
                </p>
                <p className="text-charcoal mb-4">
                  In 2019, His Majesty was honored with the royal title "Son of the Soil" by His Royal Majesty King Jonathan Danladi Gyet Maude of Nok Kingdom, Nigeria. On August 29, 2025, he will be formally enstooled as Mpuntuhene of Adukrom, a sacred office recognized by the Nifaman Council of the Akuapem State. His coronation will mark a new era of economic leadership, positioning Adukrom as a future-facing Kingdom ready to engage the world through commerce, diplomacy, and innovation.
                </p>
                <p className="text-charcoal mb-4">
                  As a Board member of The Bank of Humanity, custodian of the Ellison Royal Sovereign Wealth Fund and Chairman of The Ellison Family Office, His Majesty oversees global initiatives in renewable energy, fintech, education, agriculture, and infrastructure development. His leadership is rooted in the belief that traditional authority can be a powerful force for modern progress—unifying the African diaspora, attracting foreign direct investment, and elevating the continent's narrative on the global stage.
                </p>
                <p className="text-charcoal">
                  King Allen Ellison is also an actor of an award-winning film, published author, businessman, and former U.S. Senatorial candidate. His life and legacy are a living bridge between continents—proof that royalty can be as bold as it is benevolent, as visionary as it is rooted in ancestral truth.
                </p>
              </motion.div>

              <motion.div className="mb-8" variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">VISION</h3>
                <p className="text-charcoal">
                  Adukrom will shine as The Crown of Africa — a beacon of cultural pride, sovereign strength,
                  and innovative leadership, setting the standard for how kingdoms can drive 21st-century
                  progress across the continent and beyond.
                </p>
              </motion.div>

              <motion.div className="mb-8" variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">MISSION</h3>
                <p className="text-charcoal">
                  To lead the Rebirth of Adukrom by uniting royal heritage with transformative economic
                  development, fostering prosperity, sustainability, and global partnerships that empower
                  our people and inspire Africa&apos;s rise.
                </p>
              </motion.div>

              <motion.div variants={fadeInUp}>
                <h3 className="text-2xl font-bold text-royalBlue mb-4">PURPOSE STATEMENT</h3>
                <p className="text-charcoal">
                  Our purpose is to preserve tradition while creating opportunity — advancing trade,
                  investment, and diplomacy to uplift communities, bridge Africa with the world, and
                  ignite a lasting legacy of empowerment through The Rebirth of Adukrom.
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* About Adukrom Section - Dark Blue Background */}
      <div className="py-16 bg-gradient-to-b from-[#001a4d] via-[#002366] to-[#001a4d] text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10 pointer-events-none" style={{backgroundImage: 'url(/Website Images/kente-1.png)', backgroundSize: 'cover', backgroundRepeat: 'no-repeat', backgroundPosition: 'center'}} />
        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal animation="fadeInDown">
            <motion.h2
              className="text-3xl md:text-4xl font-bold text-white mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              ABOUT ADUKROM
              <motion.div
                className="w-48 h-1 bg-royalGold mt-2"
                initial={{ width: 0 }}
                animate={{ width: 192 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              />
            </motion.h2>
          </ScrollReveal>

          <motion.div
            className="space-y-8"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.p className="text-white/90" variants={fadeInUp}>
              Nestled atop the serene heights of the Togo Atakora Hills, Adukrom commands breathtaking views
              and an even more remarkable legacy. As the capital of the Okere District Assembly in Ghana&apos;s
              Eastern Region, Adukrom is a town where history breathes through every pathway, and where the
              future is being shaped by visionary leadership.
            </motion.p>

            <motion.p className="text-white/90" variants={fadeInUp}>
              Positioned along the crucial Ho-Koforidua main trunk road, Adukrom serves as a gateway between
              Ghana&apos;s vibrant eastern corridor and the broader West African trade routes. Its strategic location
              has long made it a center for commerce, cultural exchange, and governance within the Akuapem
              State.
            </motion.p>
          </motion.div>

          <motion.div
            className="mt-16"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.h3
              className="text-2xl font-bold text-royalGold mb-6"
              variants={fadeInUp}
            >
              CUSTODIANS OF TRADITION: THE NIFAHENE STOOL
            </motion.h3>
            <motion.p className="text-white/90 mb-4" variants={fadeInUp}>
              Adukrom is renowned as the seat of the revered Nifahene Stool of Akuapem — a symbol of
              authority, wisdom, and stewardship. The Nifahene plays a critical role in maintaining the balance of
              leadership within the Akuapem chieftaincy structure, overseeing matters of defense, diplomacy,
              and community cohesion.
            </motion.p>
            <motion.p className="text-white/90" variants={fadeInUp}>
              Today, under the reign of His Majesty Mpuntuhene Allen Ellison, this legacy continues — infused
              with a modern mandate to expand Adukrom&apos;s influence through trade, investment, and global
              partnerships.
            </motion.p>
          </motion.div>

          <motion.div
            className="mt-16"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.h3
              className="text-2xl font-bold text-royalGold mb-6"
              variants={fadeInUp}
            >
              A SACRED NEIGHBORHOOD: THE LEGACY OF OKOMFO ANOKYE
            </motion.h3>
            <motion.p className="text-white/90 mb-4" variants={fadeInUp}>
              Bordering the community of Awukugua Akuapem, Adukrom shares a deep spiritual and historical
              connection to Okomfo Anokye, one of the most legendary figures in Ghanaian history. As the co-
              founder of the Ashanti Empire and a mystic of unparalleled renown, Okomfo Anokye&apos;s birthplace
              adds a profound spiritual significance to the region.
            </motion.p>
            <motion.p className="text-white/90" variants={fadeInUp}>
              This proximity places Adukrom not only at the heart of political leadership but also within a sacred
              landscape of African heritage — making it a destination for scholars, historians, and cultural
              pilgrims alike.
            </motion.p>
          </motion.div>

          <motion.div
            className="mt-16"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            <motion.h3
              className="text-2xl font-bold text-royalGold mb-6"
              variants={fadeInUp}
            >
              GEOGRAPHY AS DESTINY: ADUKROM&apos;S STRATEGIC ADVANTAGE
            </motion.h3>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8"
              variants={staggerContainer}
            >
              {[
                {
                  emoji: "🌍",
                  icon: <MapPin className="w-6 h-6 text-white" />,
                  title: "Elevated Location",
                  description: "The Togo Atakora Hills offer natural fortification, cooler climates, and stunning vistas — ideal for eco-tourism, retreats, and royal ceremonies."
                },
                {
                  emoji: "🚚",
                  icon: <Truck className="w-6 h-6 text-white" />,
                  title: "Trade Connectivity",
                  description: "Sitting on a key transit route, Adukrom links major economic centers, enhancing its role as a burgeoning hub for commerce and logistics in Ghana's Eastern Region."
                },
                {
                  emoji: "🌿",
                  icon: <Leaf className="w-6 h-6 text-white" />,
                  title: "Agricultural Potential",
                  description: "Fertile lands and favorable weather conditions make Adukrom a center for sustainable agriculture and agri-business development."
                },
                {
                  emoji: "⚡",
                  icon: <Zap className="w-6 h-6 text-white" />,
                  title: "Renewable Energy Opportunities",
                  description: "The landscape is primed for green energy initiatives, including wind and solar projects aligned with the Kingdom's sustainability goals."
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-royalBlue/30 p-6 rounded-lg"
                  variants={fadeInUp}
                  whileHover={{ scale: 1.02, backgroundColor: "rgba(0, 26, 77, 0.4)" }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex items-center mb-3">
                    <motion.div
                      className="text-3xl mr-3"
                      whileHover={{ scale: 1.2, rotate: 5 }}
                    >
                      {item.emoji}
                    </motion.div>
                    <h4 className="text-xl font-semibold text-royalGold">{item.title}</h4>
                  </div>
                  <p className="text-white/90">{item.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* The Royal Family Section - Light Background */}
      <div className="py-16 bg-[#f9f7e8]">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-royalBlue mb-12 text-center drop-shadow-lg tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            THE ROYAL FAMILY
            <motion.div
              className="w-48 h-1 bg-royalGold mt-2 mx-auto rounded-full shadow-lg"
              initial={{ width: 0 }}
              animate={{ width: 192 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            />
          </motion.h2>

          <motion.div
            className="text-center mb-12 max-w-4xl mx-auto text-lg text-charcoal/90"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p>
              The Royal Family of Adukrom stands as a sacred pillar of tradition, dignity, and progressive
              leadership—honoring ancestral wisdom while guiding the Kingdom into a new era of prosperity
              and global significance.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            {[
              {
                image: "/Website Images/OsuodumgyaOtutuObabioVheadshot.png",
                name: "Osuodumgya Otutu Ababio V",
                title: "Akuapem Nifahene/Adukromhene"
              },
              {
                image: "/Website Images/KingEllisonHeadshot.png",
                name: "King Allen Ellison",
                title: "Mpuntuhene"
              },
              {
                image: "/Website Images/OwoabrenpongAwoAbenaKonamahheadshot.png",
                name: "Owoabrenpong Awo Abena Konamah",
                title: "Akuapem Nifahemaa/Adukromhemaa"
              }
            ].map((member, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-2xl shadow-xl overflow-hidden border-2 border-royalGold hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 group"
                variants={fadeInUp}
                whileHover={{ y: -10, scale: 1.03, boxShadow: '0 8px 32px 0 rgba(218,165,32,0.18)' }}
              >
                <motion.div
                  className="h-64 bg-gray-200 relative group-hover:scale-105 transition-transform duration-300"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    style={{ objectFit: 'cover' }}
                    className="transition-all duration-300 group-hover:brightness-105 group-hover:contrast-110"
                  />
                </motion.div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-royalBlue mb-1 tracking-tight">{member.name}</h3>
                  <p className="text-royalGold font-medium mb-2 text-base">{member.title}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
