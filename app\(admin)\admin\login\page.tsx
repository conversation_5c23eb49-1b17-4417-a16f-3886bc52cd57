'use client';

import { useState, useEffect, Suspense } from 'react';
import { signIn, useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock, Loader2 } from 'lucide-react';

// Login form component
function LoginForm() {
  const router = useRouter();
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/admin';
  const redirect = searchParams.get('redirect') || '/admin';
  const error = searchParams.get('error');

  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });

  // Redirect if already authenticated
  useEffect(() => {
    // Check for refresh parameter
    const refresh = searchParams.get('refresh');

    if (session?.user) {
      console.log('User is authenticated, redirecting to admin dashboard');

      if (refresh === 'true') {
        // If refresh parameter is present, show a message and redirect
        toast.success('Profile updated successfully! Logging back in...');
        setTimeout(() => {
          window.location.href = '/admin';
        }, 1000);
      } else {
        // Use location.href for a clean redirect that doesn't add to history
        window.location.href = '/admin';
      }
    }
  }, [session, searchParams]);

  // Show error toast if there's an error in the URL
  useEffect(() => {
    if (error) {
      toast.error(
        error === 'CredentialsSignin'
          ? 'Invalid email/username or password'
          : 'An error occurred during sign in'
      );
    }
  }, [error]);



  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.username || !formData.password) {
      toast.error('Please enter both email/username and password');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Attempting login with:', {
        username: formData.username,
        passwordProvided: !!formData.password
      });

      // First try with redirect: false to handle errors better
      const result = await signIn('credentials', {
        username: formData.username,
        password: formData.password,
        redirect: false,
      });

      console.log('Login result:', result);

      if (result?.error) {
        console.error('Login error:', result.error);
        toast.error('Invalid email/username or password');
        setIsLoading(false);
        return;
      }

      // If successful, show success message
      toast.success('Login successful');

      // Then redirect manually
      setTimeout(() => {
        let redirectUrl = redirect || callbackUrl;
        console.log('Login successful, redirecting to:', redirectUrl);

        // Use location.href for a clean redirect
        window.location.href = redirectUrl;
      }, 1000);

    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Login failed. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-royalBlue to-royalBlue/70 p-4">
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10"></div>
      <Card className="w-full max-w-md shadow-xl border-royalGold/30 backdrop-blur-sm bg-white/95">
        <CardHeader className="space-y-2 text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="absolute -inset-0.5 rounded-full bg-gradient-to-r from-royalBlue to-royalBlue/70 blur-sm"></div>
              <Image
                src="/images/ai-logo1a.png"
                alt="Kingdom Logo"
                width={100}
                height={100}
                className="relative rounded-full border-4 border-royalGold/50 shadow-lg"
              />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-royalBlue">Admin Login</CardTitle>
          <CardDescription className="text-royalBlue/70">
            Enter your credentials to access the admin dashboard
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-royalBlue">Email or Username</Label>
              <Input
                id="username"
                name="username"
                placeholder="<EMAIL>"
                value={formData.username}
                onChange={handleChange}
                className="border-royalBlue/30 focus:border-royalGold focus:ring-royalGold/30"
                required
              />
              <p className="text-xs text-gray-500">
                Use your email address if you were added as a new user
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-royalBlue">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  value={formData.password}
                  onChange={handleChange}
                  className="border-royalBlue/30 focus:border-royalGold focus:ring-royalGold/30"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-royalBlue/70 hover:text-royalBlue hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? 'Hide password' : 'Show password'}
                  </span>
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button
              type="submit"
              className="w-full bg-royalBlue hover:bg-royalBlue/90 text-white border-royalGold/20"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Logging in...
                </div>
              ) : (
                <div className="flex items-center">
                  <Lock className="mr-2 h-4 w-4" />
                  Login
                </div>
              )}
            </Button>

            <div className="text-xs text-gray-500 text-center mt-4">
              <a
                href="/forgot-password"
                className="text-royalBlue hover:text-royalBlue/80 hover:underline text-xs"
              >
                Forgot your password?
              </a>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

// Export the page component with Suspense
export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-royalBlue to-royalBlue/70 p-4">
        <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10"></div>
        <Card className="w-full max-w-md shadow-xl border-royalGold/30 backdrop-blur-sm bg-white/95">
          <CardHeader className="space-y-2 text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="absolute -inset-0.5 rounded-full bg-gradient-to-r from-royalBlue to-royalBlue/70 blur-sm"></div>
                <div className="relative rounded-full border-4 border-royalGold/50 shadow-lg h-[100px] w-[100px]"></div>
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-royalBlue">Admin Login</CardTitle>
            <CardDescription className="text-royalBlue/70">
              Loading...
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 flex justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-royalBlue" />
          </CardContent>
        </Card>
      </div>
    }>
      <LoginForm />
    </Suspense>
  );
}