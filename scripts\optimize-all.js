/**
 * Master Optimization Script
 * 
 * This script runs all optimization scripts in sequence:
 * 1. Codebase optimization
 * 2. Build optimization
 * 3. Asset optimization
 * 4. Database/API optimization
 * 5. Final cleanup and rebuild
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Run a script and measure execution time
 */
async function runScript(scriptName, scriptPath) {
  console.log(`\n${COLORS.cyan}=== Running ${scriptName} ====${COLORS.reset}`);
  
  const startTime = Date.now();
  
  try {
    runCommand(`node "${scriptPath}"`, { ignoreError: true });
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n${COLORS.green}✓ ${scriptName} completed in ${duration}s${COLORS.reset}`);
    return true;
  } catch (error) {
    console.error(`${COLORS.red}Error running ${scriptName}: ${error.message}${COLORS.reset}`);
    return false;
  }
}

/**
 * Update package.json to add optimization scripts
 */
function updatePackageJson() {
  console.log(`\n${COLORS.yellow}Updating package.json with optimization scripts...${COLORS.reset}`);
  
  const packageJsonPath = path.join(ROOT_DIR, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log(`${COLORS.red}package.json not found, skipping...${COLORS.reset}`);
    return;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add optimization scripts
  const scripts = {
    'optimize': 'node scripts/optimize-all.js',
    'optimize:codebase': 'node scripts/optimize-codebase.js',
    'optimize:build': 'node scripts/optimize-build.js',
    'optimize:assets': 'node scripts/optimize-assets.js',
  };
  
  let scriptsAdded = 0;
  
  for (const [name, command] of Object.entries(scripts)) {
    if (!packageJson.scripts[name]) {
      packageJson.scripts[name] = command;
      scriptsAdded++;
    }
  }
  
  if (scriptsAdded > 0) {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log(`${COLORS.green}✓ Added ${scriptsAdded} optimization scripts to package.json${COLORS.reset}`);
  } else {
    console.log(`${COLORS.green}✓ All optimization scripts already exist in package.json${COLORS.reset}`);
  }
}

/**
 * Create a performance monitoring component
 */
function createPerformanceMonitor() {
  console.log(`\n${COLORS.yellow}Creating performance monitoring component...${COLORS.reset}`);
  
  const componentDir = path.join(ROOT_DIR, 'components');
  const componentPath = path.join(componentDir, 'PerformanceMonitor.tsx');
  
  if (!fs.existsSync(componentDir)) {
    fs.mkdirSync(componentDir, { recursive: true });
  }
  
  const componentContent = `/**
 * PerformanceMonitor Component
 * 
 * A component that monitors and reports web vitals metrics.
 * Add this component to your layout to track performance.
 */

'use client';

import { useEffect, useState } from 'react';
import { useReportWebVitals } from 'next/web-vitals';

interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  label: string;
  startTime: number;
}

export default function PerformanceMonitor({ 
  debug = false 
}: { 
  debug?: boolean 
}) {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  
  useReportWebVitals((metric) => {
    // Store the metric
    setMetrics((prev) => [...prev, {
      id: metric.id,
      name: metric.name,
      value: metric.value,
      label: metric.label,
      startTime: metric.startTime,
    }]);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development' || debug) {
      console.log('Web Vital:', metric);
    }
    
    // You can send the metric to your analytics service here
    // Example: sendToAnalytics(metric);
  });
  
  // Display metrics in debug mode
  if (!debug) return null;
  
  return (
    <div className="fixed bottom-0 right-0 z-50 p-4 bg-black/80 text-white text-xs rounded-tl-lg max-w-xs max-h-64 overflow-auto">
      <h3 className="font-bold mb-2">Performance Metrics</h3>
      <ul>
        {metrics.map((metric, i) => (
          <li key={i} className="mb-1">
            <span className="font-semibold">{metric.name}:</span>{' '}
            {metric.value.toFixed(2)} ({metric.label})
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * Helper function to send metrics to an analytics service
 */
function sendToAnalytics(metric) {
  // Replace with your analytics code
  const body = JSON.stringify({
    name: metric.name,
    value: metric.value,
    id: metric.id,
    label: metric.label,
    startTime: metric.startTime,
    url: window.location.href,
  });
  
  // Example: Send to an endpoint
  // if (navigator.sendBeacon) {
  //   navigator.sendBeacon('/api/analytics', body);
  // } else {
  //   fetch('/api/analytics', {
  //     body,
  //     method: 'POST',
  //     keepalive: true,
  //   });
  // }
}
`;
  
  fs.writeFileSync(componentPath, componentContent);
  console.log(`${COLORS.green}✓ Created PerformanceMonitor component at components/PerformanceMonitor.tsx${COLORS.reset}`);
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Master Optimization Script ====${COLORS.reset}`);
  
  // Update package.json
  updatePackageJson();
  
  // Create performance monitoring component
  createPerformanceMonitor();
  
  // Run codebase optimization
  await runScript('Codebase Optimization', path.join(__dirname, 'optimize-codebase.js'));
  
  // Run build optimization
  await runScript('Build Optimization', path.join(__dirname, 'optimize-build.js'));
  
  // Run asset optimization
  await runScript('Asset Optimization', path.join(__dirname, 'optimize-assets.js'));
  
  // Final cleanup and rebuild
  console.log(`\n${COLORS.cyan}=== Final Cleanup and Rebuild ====${COLORS.reset}`);
  runCommand('npm run clean', { ignoreError: true });
  
  console.log(`\n${COLORS.green}✓ All optimizations complete!${COLORS.reset}`);
  console.log(`\n${COLORS.cyan}Next steps:${COLORS.reset}`);
  console.log(`1. Run ${COLORS.yellow}npm run rebuild${COLORS.reset} to rebuild the application with all optimizations`);
  console.log(`2. Run ${COLORS.yellow}npm run dev${COLORS.reset} to start the development server`);
  console.log(`3. Add the PerformanceMonitor component to your layout for performance tracking`);
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
