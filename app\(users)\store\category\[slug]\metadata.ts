import { Metada<PERSON> } from 'next';
import { generateDynamicStoreCategoryMetadata } from '@/lib/metadata-generator';
import { getStoreCategoryBySlug } from '@/lib/sanity';

// Generate dynamic metadata for store category pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Fetch the category data
    const category = await getStoreCategoryBySlug(params.slug);

    if (!category) {
      return {
        title: 'Category Not Found',
        description: 'The requested product category could not be found.',
      };
    }

    // Generate metadata for the category
    return generateDynamicStoreCategoryMetadata(category);
  } catch (error) {
    console.error('Error generating store category metadata:', error);

    // Fallback metadata
    return {
      title: 'Product Category',
      description: 'Browse products in this category from the Kingdom of Adukrom Royal Store.',
    };
  }
}
