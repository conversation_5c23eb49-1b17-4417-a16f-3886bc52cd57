'use client';
import { useEffect, useState } from 'react';

// Define a type for our particles
interface Particle {
  id: number;
  width: string;
  height: string;
  left: string;
  top: string;
  animationDelay: string;
  animationDuration: string;
}

export default function ParallaxBackground() {
  const [offset, setOffset] = useState(0);
  const [particles, setParticles] = useState<Particle[]>([]);

  useEffect(() => {
    const handleScroll = () => {
      setOffset(window.pageYOffset);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Generate particles only on the client side
  useEffect(() => {
    const newParticles = Array.from({ length: 20 }).map((_, i) => ({
      id: i,
      width: `${Math.random() * 10 + 5}px`,
      height: `${Math.random() * 10 + 5}px`,
      left: `${Math.random() * 100}%`,
      top: `${Math.random() * 100}%`,
      animationDelay: `${Math.random() * 5}s`,
      animationDuration: `${Math.random() * 5 + 3}s`,
    }));

    setParticles(newParticles);
  }, []);

  // Only render the full component on the client side
  if (typeof window === 'undefined') {
    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Static background for server-side rendering */}
        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: `url("/Website Images/ghana-scenic-landscape.png")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />

        {/* Static gradient overlay for server-side rendering */}
        <div className="absolute inset-0 z-20 bg-gradient-to-b from-royalBlue/50 to-royalBlue/90" />
      </div>
    );
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none" suppressHydrationWarning>
      {/* Background image */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `url("/Website Images/ghana-scenic-landscape.png")`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          transform: `translateY(${offset * 0.3}px)`,
          transition: 'transform 0.1s ease-out',
        }}
      />

      {/* Pattern overlay */}
      <div
        className="absolute inset-0 z-5"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 1200 800'%3E%3Cdefs%3E%3Cpattern id='pattern' width='100' height='100' patternUnits='userSpaceOnUse'%3E%3Cpath d='M50 0 L100 50 L50 100 L0 50 Z' fill='none' stroke='%23D4AF37' stroke-width='1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='none'/%3E%3Crect width='100%25' height='100%25' fill='url(%23pattern)' opacity='0.3'/%3E%3C/svg%3E")`,
          transform: `translateY(${offset * 0.2}px)`,
          transition: 'transform 0.1s ease-out',
          opacity: 0.7,
        }}
      />

      {/* Floating particles - only rendered after client-side generation */}
      <div className="absolute inset-0 z-10">
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute rounded-full bg-royalGold opacity-20 animate-float"
            style={{
              width: particle.width,
              height: particle.height,
              left: particle.left,
              top: particle.top,
              animationDelay: particle.animationDelay,
              animationDuration: particle.animationDuration,
            }}
          />
        ))}
      </div>

      {/* Gradient overlay */}
      <div
        className="absolute inset-0 z-20 bg-gradient-to-b from-royalBlue/50 to-royalBlue/90"
        style={{
          transform: `translateY(${offset * 0.1}px)`,
          transition: 'transform 0.1s ease-out',
        }}
      />
    </div>
  );
}
