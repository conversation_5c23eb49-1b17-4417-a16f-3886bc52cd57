// This script updates existing news articles in the Sanity dataset with complete content
require('dotenv').config();
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Array of news articles to update with complete content
const newsArticles = [
  {
    title: "<PERSON><PERSON><PERSON> Celebrates Coronation of King <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>",
    slug: "adukrom-celebrates-coronation-of-king-allen-el<PERSON>on-as-mpunt<PERSON><PERSON>",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Adukrom, Eastern Region, Ghana — August 29, 2025 — The historic town of Adukrom in Ghana's Eastern Region was alive with celebration today as <PERSON> was officially crowned as <PERSON><PERSON><PERSON><PERSON><PERSON> (Development Chief) in a grand coronation ceremony that blended centuries-old traditions with modern significance."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The ceremony, presided over by <PERSON> <PERSON>tutu Ababio V, the <PERSON>fahene of A<PERSON>apem and Adukromhene, marked a significant moment in the town's history as <PERSON>, an accomplished entrepreneur and philanthropist, was formally installed with the stool name <PERSON> <PERSON>puntuhene <PERSON>akyie I."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"Today we witness the beginning of a new chapter for Adukrom,\" declared <PERSON> Otutu Ababio V during the ceremony. \"In bestowing this title upon Allen Ellison, we recognize his commitment to our community's development and his vision for a prosperous future that honors our heritage while embracing progress.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The coronation featured traditional rites performed by local priests and elders, including the symbolic presentation of kente cloth, royal sandals, and other regalia that signify Ellison's new role. The ceremony was attended by dignitaries from across Ghana and international representatives, reflecting the global significance of this cultural milestone."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "In his acceptance speech, the newly crowned Mpuntuhene outlined his vision for Adukrom's development: \"I am deeply honored to accept this sacred responsibility. My commitment is to work alongside the people of Adukrom to create sustainable economic opportunities, preserve our rich cultural heritage, and build infrastructure that will serve generations to come. Together, we will transform Adukrom into a model of African prosperity while maintaining the values and traditions that make our community unique.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The coronation marks the culmination of years of engagement between Ellison and the Adukrom community, during which he has supported various development initiatives through the Ellison Outreach Foundation. His installation as Mpuntuhene formalizes his role in leading development efforts in the region."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Following the ceremony, a grand durbar was held featuring cultural performances, traditional music, and a feast for the entire community. The three-day celebration will continue through August 31st with various activities including a development forum, youth engagement programs, and community service initiatives."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The coronation of King Allen Ellison as Mpuntuhene represents a significant milestone in Adukrom's journey toward sustainable development and cultural preservation, blending traditional leadership with modern vision for the benefit of the community."
          }
        ]
      }
    ]
  },
  {
    title: "Adukrom Hosts Inaugural Global Economic Forum: Bridging Tradition and Innovation",
    slug: "adukrom-hosts-inaugural-global-economic-forum",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Adukrom, Eastern Region, Ghana — August 30, 2025 — Following the historic coronation of King Allen Ellison as Mpuntuhene, the town of Adukrom today hosted its first-ever Global Economic Forum, bringing together international investors, business leaders, and development experts to explore opportunities for sustainable economic growth in the region."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The forum, held at the newly established Adukrom Conference Center, featured panel discussions, investment pitches, and strategic planning sessions focused on key sectors including agriculture, renewable energy, technology, and cultural tourism. The event marks a significant step in implementing the development vision outlined during yesterday's coronation ceremony."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"This forum represents the practical application of our development philosophy,\" stated King Allen Ellison, addressing the gathering. \"We are creating a model that demonstrates how traditional governance structures can effectively partner with modern economic principles to create prosperity that respects our cultural values while embracing innovation.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The forum highlighted several flagship initiatives, including:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- The Adukrom Agricultural Innovation Hub: A center for developing and implementing sustainable farming practices that blend traditional knowledge with modern technology."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Renewable Energy Microgrid Project: A community-owned solar and hydroelectric power initiative designed to provide reliable, clean energy to the region."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Digital Skills Academy: A training center focused on equipping local youth with technology skills relevant to the global digital economy."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Cultural Heritage Tourism Circuit: A sustainable tourism initiative showcasing the rich traditions and natural beauty of the Eastern Region."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Nana Otutu Ababio V, the Nifahene of Akuapem and Adukromhene, emphasized the importance of community involvement in these initiatives: \"What makes our approach unique is that these projects are not being imposed from outside. They are being developed with the active participation of our people, ensuring that the benefits flow directly to the community while respecting our cultural values and environmental priorities.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The forum attracted significant interest from international investors, with several memoranda of understanding signed for initial investments totaling over $50 million across the various initiatives. Representatives from development agencies, including the African Development Bank and USAID, also expressed support for the integrated approach being taken."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Dr. Kwame Asante, an economist and development expert who attended the forum, noted: \"What we're seeing in Adukrom is a potentially transformative model that could be replicated across Africa. By explicitly connecting traditional governance structures with modern economic development strategies, they're creating a framework that is both culturally authentic and economically viable.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The Global Economic Forum will conclude tomorrow with community engagement sessions where local residents will have the opportunity to provide input on the proposed initiatives and participate in skills development workshops related to the various projects."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "This inaugural forum is planned to become an annual event, serving as a platform for monitoring progress, attracting new partnerships, and continuously refining the development strategy for Adukrom and the surrounding region."
          }
        ]
      }
    ]
  },
  {
    title: "Adukrom Launches Community Development Fund with $10 Million Initial Investment",
    slug: "adukrom-launches-community-development-fund",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Adukrom, Eastern Region, Ghana — August 31, 2025 — Concluding three days of historic events, the town of Adukrom today announced the establishment of the Adukrom Community Development Fund with an initial investment of $10 million, marking a significant step toward sustainable economic growth and community empowerment."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The announcement came during the final day of celebrations following the coronation of King Allen Ellison as Mpuntuhene and the inaugural Global Economic Forum. The fund will serve as a financial backbone for the various development initiatives outlined during these events, with a focus on projects that deliver direct benefits to local residents."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"This fund represents our commitment to turning vision into reality,\" stated King Allen Ellison during the announcement ceremony. \"Unlike traditional development approaches where communities often wait for external funding with numerous conditions attached, we are creating a self-sustaining financial mechanism that puts resources directly in the hands of the people who understand local needs best.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The $10 million initial investment comes from a combination of sources, including a personal contribution from King Ellison through the Ellison Outreach Foundation, commitments from diaspora groups, and matching funds from several international development partners who participated in the Global Economic Forum."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The fund will be structured with several key components:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Infrastructure Development Pool: Dedicated to improving roads, water systems, renewable energy infrastructure, and public facilities."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Education and Skills Development Fund: Supporting scholarships, vocational training, and educational infrastructure improvements."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Entrepreneurship and Small Business Financing: Providing low-interest loans and grants to local entrepreneurs, with a focus on women and youth."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Healthcare Improvement Initiative: Funding for clinic upgrades, medical equipment, and preventive health programs."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "- Cultural Preservation Projects: Supporting efforts to document, preserve, and promote local cultural heritage."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "A distinctive feature of the fund is its governance structure, which combines traditional authority with modern financial oversight. The fund will be managed by a board that includes representatives from the traditional council, elected community members, financial experts, and development professionals."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"This hybrid governance model ensures that the fund operates with both cultural authenticity and financial integrity,\" explained Nana Otutu Ababio V. \"Decisions about fund allocations will be made through a process that respects our traditional consensus-building approaches while incorporating modern standards of transparency and accountability.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The fund is designed to be self-sustaining, with a portion of returns from successful investments being reinvested to grow the principal amount. Additionally, a percentage of revenues from community-owned enterprises will be directed back into the fund."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Community members expressed enthusiasm about the initiative. \"This is different from other development projects we've seen,\" said Akosua Mensah, a local business owner. \"For the first time, we feel like we have a real stake in the development process and access to resources that can help us build our own future.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The first projects to be funded have already been identified through community consultations held during today's events, with implementation set to begin within 30 days. These include upgrades to the local health clinic, expansion of the community water system, and the launch of a youth entrepreneurship training program."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The establishment of the Adukrom Community Development Fund represents a significant innovation in local development financing in Ghana, potentially offering a model that could be adapted by other communities seeking to take greater control of their economic destiny."
          }
        ]
      }
    ]
  }
];

// Function to update a news article
async function updateNewsArticle(article) {
  try {
    console.log(`Updating article: ${article.title}`);

    // Find the article by slug
    const query = `*[_type == "news" && slug.current == $slug][0]._id`;
    const articleId = await client.fetch(query, { slug: article.slug });

    if (!articleId) {
      console.error(`Article with slug "${article.slug}" not found.`);
      return null;
    }

    // Update the article
    const updatedArticle = await client
      .patch(articleId)
      .set({ body: article.body })
      .commit();

    console.log(`Updated article with ID: ${updatedArticle._id}`);
    return updatedArticle;
  } catch (error) {
    console.error(`Error updating article "${article.title}":`, error);
    return null;
  }
}

// Main function to update all articles
async function updateAllArticles() {
  console.log('Starting to update news articles...');

  for (const article of newsArticles) {
    await updateNewsArticle(article);
  }

  console.log('Finished updating news articles.');
}

// Run the main function
updateAllArticles().catch(console.error);
