'use client';

import { VisualEditing } from 'next-sanity/visual-editing';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';

export default function SanityVisualEditing() {
  const { data: session } = useSession();
  const [mounted, setMounted] = useState(false);

  // Check if user is authenticated and has admin role
  const isAdmin = session?.user?.role === 'admin' || session?.user?.role === 'super_admin';

  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render for admin users and only on client-side
  if (!mounted || !isAdmin) return null;

  return <VisualEditing />;
}
