'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from 'sonner';
import {
  Shield,
  Users,
  UserPlus,
  Search,
  Edit,
  Trash2,
  AlertTriangle,
  UserCog,
  Loader2,
  RefreshCw,
  Mail,
  MoreHorizontal,
  Key,
  UserCheck,
  Clock
} from 'lucide-react';

export default function UsersClient() {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<any[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const router = useRouter();

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);

  // Form states
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'admin'
  });

  // Form states for editing
  const [editUser, setEditUser] = useState({
    id: '',
    name: '',
    email: '',
    password: '',
    role: ''
  });

  // Form states for inviting
  const [inviteUser, setInviteUser] = useState({
    email: '',
    role: 'user',
    expiresIn: '7d'
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inviteLink, setInviteLink] = useState('');

  // Get session directly from next-auth
  const { data: session, status } = useSession();

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setIsRefreshing(true);
      const response = await fetch('/api/admin/users');
      const data = await response.json();

      if (data.success) {
        setUsers(data.users);
      } else {
        toast.error(data.message || 'Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (status === 'loading') {
      return; // Still loading, don't do anything yet
    }

    // Check if the user is a super admin
    if (session?.user?.role === 'super_admin') {
      setIsAuthorized(true);
      // Fetch users when authorized
      fetchUsers();
    }

    console.log('Session in users client:', session);

    // Set loading to false
    setIsLoading(false);
  }, [session, status]);

  // Filter users based on search query
  const filteredUsers = users.filter(user =>
    user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Format date for display
  const formatDate = (date: string) => {
    if (!date) return 'Never';

    const d = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - d.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      if (diffHours === 0) {
        const diffMinutes = Math.floor(diffTime / (1000 * 60));
        if (diffMinutes === 0) {
          return 'Just now';
        }
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
      }
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 30) {
      const diffWeeks = Math.floor(diffDays / 7);
      return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
    } else {
      return d.toLocaleDateString();
    }
  };

  // Handle adding a new user
  const handleAddUser = async () => {
    try {
      setIsSubmitting(true);

      // Validate form
      if (!newUser.name || !newUser.email || !newUser.password || !newUser.role) {
        toast.error('Please fill in all required fields');
        return;
      }

      // Create username from email if not provided
      const username = newUser.email.split('@')[0];

      // Submit to API
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newUser,
          username: username
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('User added successfully');
        setIsAddDialogOpen(false);
        // Reset form
        setNewUser({
          name: '',
          email: '',
          password: '',
          role: 'admin'
        });
        // Refresh users list
        fetchUsers();
      } else {
        toast.error(data.message || 'Failed to add user');
      }
    } catch (error) {
      console.error('Error adding user:', error);
      toast.error('Failed to add user');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle inviting a new user (temporary account)
  const handleInviteUser = async () => {
    try {
      setIsSubmitting(true);
      setInviteLink('');

      // Validate form
      if (!inviteUser.email) {
        toast.error('Please enter an email address');
        return;
      }

      // Submit to API
      const response = await fetch('/api/users/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(inviteUser),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Invitation sent successfully');
        // Store the registration link
        setInviteLink(data.registrationLink);

        // Don't close dialog yet so they can copy the link

        // Refresh users list
        fetchUsers();
      } else {
        toast.error(data.message || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Error inviting user:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Copy invite link to clipboard
  const copyInviteLink = () => {
    if (inviteLink) {
      navigator.clipboard.writeText(inviteLink)
        .then(() => {
          toast.success('Invitation link copied to clipboard');
        })
        .catch((err) => {
          console.error('Failed to copy link:', err);
          toast.error('Failed to copy link');
        });
    }
  };

  // Close invite dialog and reset form
  const closeInviteDialog = () => {
    setIsInviteDialogOpen(false);
    setInviteLink('');
    setInviteUser({
      email: '',
      role: 'user',
      expiresIn: '7d'
    });
  };

  // Handle editing a user
  const handleEditUser = async () => {
    try {
      setIsSubmitting(true);

      // Validate form
      if (!editUser.name || !editUser.email || !editUser.role) {
        toast.error('Please fill in all required fields');
        return;
      }

      // Prepare data (only include password if it was changed)
      const updateData = {
        name: editUser.name,
        email: editUser.email,
        role: editUser.role,
      };

      if (editUser.password) {
        Object.assign(updateData, { password: editUser.password });
      }

      // Submit to API
      const response = await fetch(`/api/admin/users/${editUser.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('User updated successfully');
        setIsEditDialogOpen(false);
        // Refresh users list
        fetchUsers();
      } else {
        toast.error(data.message || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a user
  const handleDeleteUser = async () => {
    try {
      setIsSubmitting(true);

      if (!selectedUser?._id) {
        toast.error('No user selected');
        return;
      }

      // Submit to API
      const response = await fetch(`/api/admin/users/${selectedUser._id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('User deleted successfully');
        setIsDeleteDialogOpen(false);
        // Refresh users list
        fetchUsers();
      } else {
        toast.error(data.message || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open edit dialog with user data
  const openEditDialog = (user: any) => {
    setEditUser({
      id: user._id,
      name: user.name,
      email: user.email,
      password: '', // Don't populate password
      role: user.role
    });
    setIsEditDialogOpen(true);
  };

  // Open delete dialog with user data
  const openDeleteDialog = (user: any) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Users className="mr-2 h-6 w-6 text-royalBlue" />
          User Management
        </h1>
        <p className="text-muted-foreground">
          Manage admin users, roles, and permissions.
        </p>
      </div>

      <Separator />

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={fetchUsers}
            disabled={isRefreshing}
            title="Refresh users"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <div className="flex gap-2">
          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center">
                <Mail className="mr-2 h-4 w-4" />
                Invite User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite New User</DialogTitle>
                <DialogDescription>
                  Send an invitation to a new user to join the platform. They will receive an email with a link to set up their account.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="invite-email">Email</Label>
                  <Input
                    id="invite-email"
                    type="email"
                    value={inviteUser.email}
                    onChange={(e) => setInviteUser({...inviteUser, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invite-role">Role</Label>
                  <select
                    id="invite-role"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={inviteUser.role}
                    onChange={(e) => setInviteUser({...inviteUser, role: e.target.value})}
                  >
                    <option value="user">User</option>
                    <option value="editor">Editor</option>
                    <option value="admin">Admin</option>
                    {session?.user?.role === 'super_admin' && (
                      <option value="super_admin">Super Admin</option>
                    )}
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invite-expires">Link Expires In</Label>
                  <select
                    id="invite-expires"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={inviteUser.expiresIn}
                    onChange={(e) => setInviteUser({...inviteUser, expiresIn: e.target.value})}
                  >
                    <option value="1d">1 Day</option>
                    <option value="3d">3 Days</option>
                    <option value="7d">7 Days</option>
                    <option value="14d">14 Days</option>
                    <option value="30d">30 Days</option>
                  </select>
                </div>

                {inviteLink && (
                  <div className="mt-4 p-3 bg-muted rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <Label>Invitation Link</Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={copyInviteLink}
                        className="h-8 px-2"
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="text-xs break-all bg-background p-2 rounded border">
                      {inviteLink}
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Share this link with the user. They can use it to set up their account.
                    </p>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={closeInviteDialog}>
                  {inviteLink ? 'Close' : 'Cancel'}
                </Button>
                {!inviteLink && (
                  <Button onClick={handleInviteUser} disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Invitation'
                    )}
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center">
                <UserPlus className="mr-2 h-4 w-4" />
                Add New User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New User</DialogTitle>
                <DialogDescription>
                  Create a new user with admin access to the dashboard.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    placeholder="Full Name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    placeholder="••••••••"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <select
                    id="role"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newUser.role}
                    onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  >
                    <option value="admin">Admin</option>
                    <option value="super_admin">Super Admin</option>
                    <option value="editor">Editor</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancel</Button>
                <Button onClick={handleAddUser} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    'Add User'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Admin Users</CardTitle>
          <CardDescription>
            Manage users who have access to the admin dashboard.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isRefreshing && (
            <div className="flex justify-center my-4">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          )}

          {!isRefreshing && filteredUsers.length === 0 && (
            <div className="text-center py-6 text-muted-foreground">
              No users found. {searchQuery ? 'Try a different search term.' : ''}
            </div>
          )}

          {!isRefreshing && filteredUsers.length > 0 && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Activity</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user._id || user.id} className={user.isTemporary ? 'bg-muted/50' : ''}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {user.isTemporary ? (
                            <span className="text-muted-foreground italic">Pending Setup</span>
                          ) : (
                            user.name
                          )}
                        </span>
                        <span className="text-sm text-muted-foreground">{user.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          user.role === 'super_admin'
                            ? 'bg-yellow-100 text-yellow-800'
                            : user.role === 'admin'
                            ? 'bg-blue-100 text-blue-800'
                            : user.role === 'user'
                            ? 'bg-slate-100 text-slate-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {user.role === 'super_admin' && <Shield className="mr-1 h-3 w-3" />}
                          {user.role === 'admin' && <UserCog className="mr-1 h-3 w-3" />}
                          {user.role === 'super_admin' ? 'Super Admin' :
                           user.role === 'admin' ? 'Admin' :
                           user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </span>

                        {user.isTemporary && (
                          <span className="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800">
                            Temporary
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {user.isTemporary ? (
                            <span className="text-muted-foreground">Invited {formatDate(user.createdAt)}</span>
                          ) : (
                            formatDate(user.lastLogin || user.updatedAt)
                          )}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {user.isTemporary ? (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Pending User</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                // Re-invite user
                                setInviteUser({
                                  email: user.email,
                                  role: user.role,
                                  expiresIn: '7d'
                                });
                                setIsInviteDialogOpen(true);
                              }}
                            >
                              <Mail className="mr-2 h-4 w-4" />
                              Resend Invitation
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => openDeleteDialog(user)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Invitation
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      ) : (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openEditDialog(user)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              // Open edit dialog with focus on password field
                              openEditDialog(user);
                              // Focus on password field after dialog opens
                              setTimeout(() => {
                                const passwordField = document.getElementById('edit-password');
                                if (passwordField) passwordField.focus();
                              }, 100);
                            }}>
                              <Key className="mr-2 h-4 w-4" />
                              Reset Password
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => openDeleteDialog(user)}
                              className="text-red-600"
                              disabled={(user._id || user.id) === session?.user?.id} // Prevent deleting own account
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and permissions.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={editUser.name}
                onChange={(e) => setEditUser({...editUser, name: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editUser.email}
                onChange={(e) => setEditUser({...editUser, email: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-password">Password (leave blank to keep current)</Label>
              <Input
                id="edit-password"
                type="password"
                value={editUser.password}
                onChange={(e) => setEditUser({...editUser, password: e.target.value})}
                placeholder="••••••••"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-role">Role</Label>
              <select
                id="edit-role"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={editUser.role}
                onChange={(e) => setEditUser({...editUser, role: e.target.value})}
                disabled={editUser.id === session?.user?.id} // Prevent changing own role
              >
                <option value="admin">Admin</option>
                <option value="super_admin">Super Admin</option>
                <option value="editor">Editor</option>
              </select>
              {editUser.id === session?.user?.id && (
                <p className="text-xs text-muted-foreground mt-1">
                  You cannot change your own role.
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleEditUser} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user
              account for <strong>{selectedUser?.name || selectedUser?.email}</strong> and remove their access to the dashboard.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteUser}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete User'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Super Admin Access</h3>
            <p className="text-sm mt-1 text-yellow-700">
              Changes made here affect user access to the admin dashboard. Be careful when assigning
              Super Admin privileges as they grant full access to all system features.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}