import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getUserById } from '@/lib/users';
import { getWriteClient } from '@/lib/sanity.client';
import bcrypt from 'bcryptjs';

// POST /api/users/sync-password - Sync password from local to Sanity
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Get user ID from session
    const userId = session.user.id;

    // Get user from local database
    const localUser = getUserById(userId);
    if (!localUser) {
      return NextResponse.json(
        { success: false, message: 'User not found in local database' },
        { status: 404 }
      );
    }

    // Get Sanity client
    const client = getWriteClient();

    // Find user in Sanity by email
    const sanityUser = await client.fetch(
      `*[_type == "adminUser" && email == $email][0]`,
      { email: localUser.email }
    );

    if (!sanityUser) {
      return NextResponse.json(
        { success: false, message: 'User not found in Sanity' },
        { status: 404 }
      );
    }

    console.log(`Syncing password for user: ${localUser.email} (${sanityUser._id})`);

    // Verify that we have a valid hashed password
    if (!localUser.password || localUser.password.length < 20) {
      console.error('Invalid or missing password hash for user:', localUser.email);
      return NextResponse.json(
        { success: false, message: 'Invalid password hash' },
        { status: 400 }
      );
    }

    console.log(`Syncing password hash to Sanity for user: ${localUser.email}`);

    // Update password in Sanity
    const result = await client
      .patch(sanityUser._id)
      .set({
        hashedPassword: localUser.password,
        updatedAt: new Date().toISOString()
      })
      .commit();

    console.log('Sanity update result:', result._id);

    return NextResponse.json({
      success: true,
      message: 'Password synced successfully',
    });
  } catch (error) {
    console.error('Error syncing password:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to sync password' },
      { status: 500 }
    );
  }
}
