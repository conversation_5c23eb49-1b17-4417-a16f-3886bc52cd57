<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multiple Images Feature</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .gallery-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
        }
        .image-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #0070f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .remove-btn {
            background: #ff4444;
            padding: 5px 10px;
            font-size: 14px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Test Multiple Images Feature for News Articles</h1>
    
    <form id="newsForm">
        <div class="form-group">
            <label for="title">Article Title:</label>
            <input type="text" id="title" value="Test Article with Multiple Images" required>
        </div>
        
        <div class="form-group">
            <label for="excerpt">Excerpt:</label>
            <textarea id="excerpt" rows="3" required>This is a test article to verify that multiple images can be uploaded and displayed in the gallery.</textarea>
        </div>
        
        <div class="form-group">
            <label for="content">Content:</label>
            <textarea id="content" rows="5" required>This article demonstrates the new multiple images feature. Users can now upload several images that will be displayed in a gallery at the bottom of the news article page.</textarea>
        </div>
        
        <div class="form-group">
            <label for="category">Category:</label>
            <input type="text" id="category" value="Test" required>
        </div>
        
        <div class="form-group">
            <label for="galleryImages">Gallery Images (select multiple):</label>
            <input type="file" id="galleryImages" accept="image/*" multiple>
        </div>
        
        <div id="galleryPreview" class="gallery-container"></div>
        
        <button type="submit">Create News Article with Gallery</button>
    </form>
    
    <div id="result"></div>

    <script>
        let galleryImages = [];
        
        document.getElementById('galleryImages').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            
            files.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const imageData = {
                        file: file,
                        preview: event.target.result,
                        alt: '',
                        caption: '',
                        id: Date.now() + index
                    };
                    
                    galleryImages.push(imageData);
                    renderGalleryPreview();
                };
                reader.readAsDataURL(file);
            });
        });
        
        function renderGalleryPreview() {
            const container = document.getElementById('galleryPreview');
            container.innerHTML = '';
            
            if (galleryImages.length === 0) return;
            
            const title = document.createElement('h3');
            title.textContent = `Gallery Images (${galleryImages.length})`;
            container.appendChild(title);
            
            galleryImages.forEach((imageData, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'image-item';
                
                itemDiv.innerHTML = `
                    <img src="${imageData.preview}" alt="Preview" class="image-preview">
                    <div class="form-group">
                        <label>Alt Text (required):</label>
                        <input type="text" placeholder="Describe the image" value="${imageData.alt}" 
                               onchange="updateImageData(${index}, 'alt', this.value)" required>
                    </div>
                    <div class="form-group">
                        <label>Caption (optional):</label>
                        <input type="text" placeholder="Image caption" value="${imageData.caption}" 
                               onchange="updateImageData(${index}, 'caption', this.value)">
                    </div>
                    <button type="button" class="remove-btn" onclick="removeImage(${index})">Remove Image</button>
                `;
                
                container.appendChild(itemDiv);
            });
        }
        
        function updateImageData(index, field, value) {
            if (galleryImages[index]) {
                galleryImages[index][field] = value;
            }
        }
        
        function removeImage(index) {
            galleryImages.splice(index, 1);
            renderGalleryPreview();
        }
        
        document.getElementById('newsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Creating news article...</p>';
            
            const formData = new FormData();
            formData.append('title', document.getElementById('title').value);
            formData.append('excerpt', document.getElementById('excerpt').value);
            formData.append('content', document.getElementById('content').value);
            formData.append('category', document.getElementById('category').value);
            formData.append('date', new Date().toISOString().split('T')[0]);
            formData.append('status', 'draft');
            
            // Add gallery images
            galleryImages.forEach((imageData, index) => {
                formData.append(`galleryImage_${index}`, imageData.file);
                formData.append(`galleryAlt_${index}`, imageData.alt);
                formData.append(`galleryCaption_${index}`, imageData.caption);
            });
            formData.append('galleryCount', galleryImages.length.toString());
            
            try {
                const response = await fetch('/api/news', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>Success!</h3>
                            <p>News article created successfully with ${galleryImages.length} gallery images!</p>
                            <p>Article ID: ${result.article._id}</p>
                            <p>You can view it at: <a href="/news/${result.article.slug?.current || result.article._id}" target="_blank">/news/${result.article.slug?.current || result.article._id}</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>Error!</h3>
                            <p>${result.error || 'Failed to create news article'}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>Network Error!</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
