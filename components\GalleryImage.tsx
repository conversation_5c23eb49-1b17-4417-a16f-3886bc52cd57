'use client';

import { useState } from 'react';
import Image from 'next/image';
import { urlFor } from '@/lib/sanity';

interface GalleryImageProps {
  image: any;
  alt: string;
  fallbackSrc?: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
}

export default function GalleryImage({
  image,
  alt,
  fallbackSrc = '/Website Images/ghanaian_presidential_palace.png',
  width = 800,
  height = 600,
  className = '',
  priority = false,
  fill = false,
}: GalleryImageProps) {
  const [error, setError] = useState(false);
  
  // If no image is provided or there was an error loading the Sanity image, use the fallback
  if (!image || error) {
    return (
      <Image
        src={fallbackSrc}
        alt={alt}
        width={width}
        height={height}
        className={className}
        priority={priority}
        fill={fill}
      />
    );
  }
  
  // If it's a Sanity image, use the Sanity URL builder
  if (typeof image === 'object' && image.asset) {
    try {
      const imageUrl = urlFor(image).url();
      
      return (
        <Image
          src={imageUrl}
          alt={alt}
          width={width}
          height={height}
          className={className}
          priority={priority}
          fill={fill}
          onError={() => setError(true)}
        />
      );
    } catch (err) {
      console.error('Error generating Sanity image URL:', err);
      setError(true);
      return (
        <Image
          src={fallbackSrc}
          alt={alt}
          width={width}
          height={height}
          className={className}
          priority={priority}
          fill={fill}
        />
      );
    }
  }
  
  // If it's a string URL (e.g., from public folder)
  if (typeof image === 'string') {
    return (
      <Image
        src={image}
        alt={alt}
        width={width}
        height={height}
        className={className}
        priority={priority}
        fill={fill}
        onError={() => setError(true)}
      />
    );
  }
  
  // Fallback for any other case
  return (
    <Image
      src={fallbackSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      fill={fill}
    />
  );
}
