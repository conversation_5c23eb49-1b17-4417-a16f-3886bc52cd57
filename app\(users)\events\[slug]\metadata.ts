import { Metadata } from 'next';
import { generateDynamicEventMetadata } from '@/lib/metadata-generator';
import { getEventBySlug } from '@/lib/sanity';

// Generate dynamic metadata for event pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Fetch the event data
    const event = await getEventBySlug(params.slug);

    if (!event) {
      return {
        title: 'Event Not Found',
        description: 'The requested event could not be found.',
      };
    }

    // Generate metadata for the event
    return generateDynamicEventMetadata(event);
  } catch (error) {
    console.error('Error generating event metadata:', error);

    // Fallback metadata
    return {
      title: 'Event Details',
      description: 'View details about this royal event.',
    };
  }
}
