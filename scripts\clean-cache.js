/**
 * Clean Next.js Cache Script
 * 
 * This script cleans up the Next.js cache and temporary files
 * to help resolve performance issues during development.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const NEXT_CACHE_DIR = path.join(ROOT_DIR, '.next');
const NODE_MODULES_DIR = path.join(ROOT_DIR, 'node_modules');
const NEXT_CACHE_FOLDERS = [
  '.next/cache',
  '.next/server',
  '.next/static',
  '.next/trace',
];

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Delete a directory recursively
 */
function deleteDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }
  
  try {
    // On Windows, we need to use rimraf or a similar tool
    // because fs.rmSync might fail with EPERM errors
    if (process.platform === 'win32') {
      runCommand(`rmdir /s /q "${dirPath}"`, { ignoreError: true, silent: true });
    } else {
      fs.rmSync(dirPath, { recursive: true, force: true });
    }
    console.log(`${COLORS.green}✓ Deleted ${dirPath}${COLORS.reset}`);
  } catch (error) {
    console.error(`${COLORS.red}Error deleting ${dirPath}: ${error.message}${COLORS.reset}`);
  }
}

/**
 * Clean Next.js cache
 */
function cleanNextCache() {
  console.log(`${COLORS.yellow}Cleaning Next.js cache...${COLORS.reset}`);
  
  if (fs.existsSync(NEXT_CACHE_DIR)) {
    for (const folder of NEXT_CACHE_FOLDERS) {
      const folderPath = path.join(ROOT_DIR, folder);
      if (fs.existsSync(folderPath)) {
        deleteDirectory(folderPath);
      }
    }
  } else {
    console.log(`${COLORS.yellow}No .next directory found${COLORS.reset}`);
  }
}

/**
 * Clean node_modules/.cache
 */
function cleanNodeModulesCache() {
  console.log(`${COLORS.yellow}Cleaning node_modules/.cache...${COLORS.reset}`);
  
  const cacheDir = path.join(NODE_MODULES_DIR, '.cache');
  if (fs.existsSync(cacheDir)) {
    deleteDirectory(cacheDir);
  } else {
    console.log(`${COLORS.yellow}No node_modules/.cache directory found${COLORS.reset}`);
  }
}

/**
 * Run Next.js cache clean command
 */
function runNextClean() {
  console.log(`${COLORS.yellow}Running next clean...${COLORS.reset}`);
  
  try {
    runCommand('npx next clean', { ignoreError: true });
  } catch (error) {
    console.error(`${COLORS.red}Error running next clean: ${error.message}${COLORS.reset}`);
  }
}

/**
 * Clear temporary files
 */
function clearTempFiles() {
  console.log(`${COLORS.yellow}Clearing temporary files...${COLORS.reset}`);
  
  const tempFiles = [
    '.next-dev-config.js',
    '.env.local.backup',
  ];
  
  for (const file of tempFiles) {
    const filePath = path.join(ROOT_DIR, file);
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`${COLORS.green}✓ Deleted ${file}${COLORS.reset}`);
      } catch (error) {
        console.error(`${COLORS.red}Error deleting ${file}: ${error.message}${COLORS.reset}`);
      }
    }
  }
}

/**
 * Main function
 */
function main() {
  console.log(`${COLORS.cyan}=== Clean Next.js Cache ====${COLORS.reset}`);
  
  // Clean Next.js cache
  cleanNextCache();
  
  // Clean node_modules/.cache
  cleanNodeModulesCache();
  
  // Run Next.js cache clean command
  runNextClean();
  
  // Clear temporary files
  clearTempFiles();
  
  console.log(`${COLORS.green}✓ Cache cleaning complete!${COLORS.reset}`);
  console.log(`\n${COLORS.cyan}Next steps:${COLORS.reset}`);
  console.log(`1. Run ${COLORS.yellow}npm run dev${COLORS.reset} to start the development server`);
  console.log(`2. Or run ${COLORS.yellow}npm run dev:fast${COLORS.reset} for a faster development experience`);
}

// Run the main function
main();
