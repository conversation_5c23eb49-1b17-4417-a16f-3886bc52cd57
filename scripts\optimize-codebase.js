/**
 * Codebase Optimization Script
 * 
 * This script:
 * 1. Analyzes the codebase for performance issues
 * 2. Optimizes imports and dependencies
 * 3. Generates module/component index files
 * 4. Updates Next.js configuration for better performance
 * 5. Cleans up unused files and dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const COMPONENTS_DIR = path.join(ROOT_DIR, 'components');
const APP_DIR = path.join(ROOT_DIR, 'app');
const LIB_DIR = path.join(ROOT_DIR, 'lib');
const HOOKS_DIR = path.join(ROOT_DIR, 'hooks');

// Directories to create index files for
const DIRS_TO_INDEX = [
  COMPONENTS_DIR,
  path.join(COMPONENTS_DIR, 'ui'),
  path.join(COMPONENTS_DIR, 'PageBuilder'),
  path.join(COMPONENTS_DIR, 'PageBuilder', 'sections'),
  LIB_DIR,
  HOOKS_DIR,
];

// Files to ignore
const IGNORE_FILES = [
  'node_modules',
  '.next',
  'public',
  '.git',
  'test',
  '__tests__',
  'jest',
  'cypress',
];

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if a directory exists
 */
function directoryExists(dirPath) {
  try {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Create directory if it doesn't exist
 */
function ensureDirectoryExists(dirPath) {
  if (!directoryExists(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

/**
 * Generate index files for directories
 */
function generateIndexFiles() {
  console.log(`\n${COLORS.yellow}Generating index files for better imports...${COLORS.reset}`);
  
  let totalIndexed = 0;
  
  for (const dir of DIRS_TO_INDEX) {
    if (!directoryExists(dir)) {
      console.log(`${COLORS.yellow}Directory ${dir} does not exist, skipping...${COLORS.reset}`);
      continue;
    }
    
    console.log(`Processing ${path.relative(ROOT_DIR, dir)}...`);
    
    // Get all .ts, .tsx, .js, .jsx files in the directory (not in subdirectories)
    const files = fs.readdirSync(dir)
      .filter(file => {
        const filePath = path.join(dir, file);
        if (!fs.existsSync(filePath)) return false;
        
        try {
          const isDirectory = fs.statSync(filePath).isDirectory();
          const isIndexFile = file === 'index.ts' || file === 'index.js' || file === 'index.tsx' || file === 'index.jsx';
          const isValidFile = !isDirectory && 
            (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) && 
            !isIndexFile;
          
          return isValidFile;
        } catch (error) {
          return false;
        }
      });
    
    if (files.length === 0) {
      console.log(`  No files to index in ${path.relative(ROOT_DIR, dir)}`);
      continue;
    }
    
    // Generate index file content
    let indexContent = '/**\n * Generated index file\n * This file was automatically generated by the optimize-codebase script\n */\n\n';
    
    // Add exports for each file
    for (const file of files) {
      const baseName = path.basename(file, path.extname(file));
      indexContent += `export * from './${baseName}';\n`;
      
      // For React components, also add default export
      if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
        indexContent += `export { default as ${baseName} } from './${baseName}';\n`;
      }
    }
    
    // Write index file
    const indexPath = path.join(dir, 'index.ts');
    fs.writeFileSync(indexPath, indexContent);
    console.log(`  ${COLORS.green}✓ Created index file with ${files.length} exports${COLORS.reset}`);
    totalIndexed += files.length;
  }
  
  console.log(`${COLORS.green}✓ Generated index files for ${totalIndexed} components/modules${COLORS.reset}`);
}

/**
 * Update Next.js configuration for better performance
 */
function updateNextConfig() {
  console.log(`\n${COLORS.yellow}Updating Next.js configuration for better performance...${COLORS.reset}`);
  
  const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    console.log(`${COLORS.red}next.config.js not found, skipping...${COLORS.reset}`);
    return;
  }
  
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check if we need to update the config
  if (nextConfig.includes('compiler: {') && 
      nextConfig.includes('removeConsole:') && 
      nextConfig.includes('swcMinify: true')) {
    console.log(`${COLORS.green}✓ Next.js configuration already optimized${COLORS.reset}`);
    return;
  }
  
  // Add performance optimizations
  nextConfig = nextConfig.replace(
    'const nextConfig = {',
    `const nextConfig = {
  // Enable SWC minification for faster builds
  swcMinify: true,
  
  // Compiler options for better performance
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },`
  );
  
  // Write updated config
  fs.writeFileSync(nextConfigPath, nextConfig);
  console.log(`${COLORS.green}✓ Updated Next.js configuration for better performance${COLORS.reset}`);
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Codebase Optimization ====${COLORS.reset}`);
  
  // Generate index files
  generateIndexFiles();
  
  // Update Next.js configuration
  updateNextConfig();
  
  // Clean up
  console.log(`\n${COLORS.yellow}Cleaning up...${COLORS.reset}`);
  runCommand('npm run clean', { ignoreError: true });
  
  console.log(`\n${COLORS.green}✓ Codebase optimization complete!${COLORS.reset}`);
  console.log(`\n${COLORS.cyan}Next steps:${COLORS.reset}`);
  console.log(`1. Run ${COLORS.yellow}npm run rebuild${COLORS.reset} to rebuild the application`);
  console.log(`2. Run ${COLORS.yellow}npm run dev${COLORS.reset} to start the development server`);
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
