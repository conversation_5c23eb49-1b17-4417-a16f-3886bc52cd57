// This script forcefully deletes users from both Sanity and local storage
// Run with: node scripts/force-delete-users.js

const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Get all local users
const getLocalUsers = () => {
  try {
    if (fs.existsSync(usersFilePath)) {
      const usersData = fs.readFileSync(usersFilePath, 'utf8');
      return JSON.parse(usersData);
    }
    return [];
  } catch (error) {
    console.error('Error reading local users file:', error);
    return [];
  }
};

// Save local users
const saveLocalUsers = (users) => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
};

// Fetch all Sanity users
const fetchSanityUsers = async () => {
  try {
    console.log('Fetching users from Sanity...');
    
    const users = await client.fetch(
      `*[_type == "adminUser"] {
        _id,
        username,
        name,
        email,
        role,
        isActive,
        isProtected
      }`
    );
    
    console.log(`Found ${users.length} users in Sanity`);
    return users;
  } catch (error) {
    console.error('Error fetching Sanity users:', error);
    return [];
  }
};

// Force delete a user by email
const forceDeleteUserByEmail = async (email) => {
  try {
    console.log(`Attempting to delete user with email: ${email}`);
    
    // Find the user in Sanity
    const sanityUser = await client.fetch(
      `*[_type == "adminUser" && email == $email][0]`,
      { email }
    );
    
    if (sanityUser) {
      console.log(`Found user in Sanity: ${sanityUser.name} (${sanityUser._id})`);
      
      // Delete from Sanity
      try {
        await client.delete(sanityUser._id);
        console.log(`Deleted user from Sanity: ${sanityUser._id}`);
      } catch (error) {
        console.error(`Error deleting user from Sanity: ${error.message}`);
      }
    } else {
      console.log(`User with email ${email} not found in Sanity`);
    }
    
    // Delete from local storage
    const localUsers = getLocalUsers();
    const filteredUsers = localUsers.filter(user => user.email !== email);
    
    if (filteredUsers.length < localUsers.length) {
      console.log(`Removing user with email ${email} from local storage`);
      saveLocalUsers(filteredUsers);
    } else {
      console.log(`User with email ${email} not found in local storage`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting user with email ${email}:`, error);
    return false;
  }
};

// Main function
async function main() {
  console.log('Starting forced user deletion...');
  
  // List all users before deletion
  const beforeUsers = await fetchSanityUsers();
  console.log('\nUsers before deletion:');
  beforeUsers.forEach(user => {
    console.log(`- ${user.name || 'Unknown'} (${user.email || 'No email'}) [${user._id}] Protected: ${user.isProtected ? 'Yes' : 'No'}`);
  });
  
  // List of emails to delete
  const emailsToDelete = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  // Delete each user
  console.log('\nDeleting users...');
  for (const email of emailsToDelete) {
    await forceDeleteUserByEmail(email);
  }
  
  // List all users after deletion
  const afterUsers = await fetchSanityUsers();
  console.log('\nUsers after deletion:');
  afterUsers.forEach(user => {
    console.log(`- ${user.name || 'Unknown'} (${user.email || 'No email'}) [${user._id}] Protected: ${user.isProtected ? 'Yes' : 'No'}`);
  });
  
  console.log('\nForced user deletion complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
