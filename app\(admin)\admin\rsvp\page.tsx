'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { toast } from 'sonner';
import { format, parse } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  MoreHorizontal,
  Eye,
  Trash,
  Mail,
  Phone,
  Calendar,
  User,
  MapPin,
  Tag,
  Download,
  FileText,
  Upload,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { getRsvpSubmissions } from '@/lib/sanity';

// Define the RSVP submission type
interface RsvpSubmission {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  events: string[];
  attendanceType: string;
  reminderPreference?: string[];
  notes?: string;
  submittedAt: string;
}

export default function RsvpPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [rsvpSubmissions, setRsvpSubmissions] = useState<RsvpSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStats, setUploadStats] = useState<{
    total: number;
    success: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const fetchRsvpData = async () => {
    try {
      setIsLoading(true);
      const data = await getRsvpSubmissions();
      setRsvpSubmissions(data);
      toast.success('RSVP data loaded successfully');
    } catch (error) {
      console.error('Failed to fetch RSVP data:', error);
      toast.error('Failed to load RSVP data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRsvpData();
  }, []);

  // Create Sanity client with write permissions
  const createSanityClient = async () => {
    try {
      const { createClient } = await import('@sanity/client');

      // Get token from environment variable
      const token = process.env.NEXT_PUBLIC_SANITY_API_TOKEN;

      if (!token) {
        console.error('Sanity API token is missing');
        toast.error('API token is missing. Please check your environment variables.');
        throw new Error('Sanity API token is missing');
      }

      return createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2025-05-08',
        useCdn: false,
      });
    } catch (error) {
      console.error('Failed to create Sanity client:', error);
      toast.error('Failed to initialize content management system');
      throw error;
    }
  };

  // Delete RSVP submission
  const deleteRsvpSubmission = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this RSVP submission? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the API endpoint to delete the RSVP submission
      const response = await fetch(`/api/rsvp/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to delete RSVP submission');
      }

      // Update the local state by removing the deleted submission
      setRsvpSubmissions(prev => prev.filter(submission => submission._id !== id));

      toast.success('RSVP submission deleted successfully');

      // Refresh the data to ensure we have the latest
      setTimeout(() => {
        fetchRsvpData();
      }, 1000);
    } catch (error) {
      console.error('Failed to delete RSVP submission:', error);
      toast.error('Failed to delete RSVP submission: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // Handle file selection for CSV upload
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if it's a CSV file
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      toast.error('Please upload a CSV file');
      return;
    }

    setIsUploading(true);
    setUploadStats(null);

    try {
      const text = await file.text();
      const rows = text.split('\n');

      // Parse header row
      const headers = rows[0].split(',').map(header =>
        header.trim().replace(/^"(.*)"$/, '$1') // Remove quotes if present
      );

      // Expected headers
      const expectedHeaders = [
        'First Name', 'Last Name', 'Email', 'Phone', 'Country',
        'Events', 'Attendance Type', 'Reminder Preferences', 'Notes', 'Submitted At'
      ];

      // Validate headers
      const missingHeaders = expectedHeaders.filter(header => !headers.includes(header));
      if (missingHeaders.length > 0) {
        toast.error(`CSV is missing required headers: ${missingHeaders.join(', ')}`);
        setIsUploading(false);
        return;
      }

      // Create Sanity client
      const client = await createSanityClient();

      // Process data rows
      const dataRows = rows.slice(1).filter(row => row.trim() !== '');
      const stats = {
        total: dataRows.length,
        success: 0,
        failed: 0,
        errors: [] as string[],
      };

      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = dataRows[i];
          const values: string[] = [];
          let inQuotes = false;
          let currentValue = '';

          // Parse CSV row handling quoted values with commas
          for (let j = 0; j < row.length; j++) {
            const char = row[j];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              values.push(currentValue.trim());
              currentValue = '';
            } else {
              currentValue += char;
            }
          }
          values.push(currentValue.trim()); // Add the last value

          // Map values to object
          const rowData: Record<string, any> = {};
          headers.forEach((header, index) => {
            if (index < values.length) {
              let value = values[index].replace(/^"(.*)"$/, '$1'); // Remove quotes if present
              rowData[header] = value;
            }
          });

          // Parse events and reminder preferences
          const events = rowData['Events'] ? rowData['Events'].split(',').map((e: string) => e.trim()) : [];
          const reminderPrefs = rowData['Reminder Preferences'] ? rowData['Reminder Preferences'].split(',').map((r: string) => r.trim()) : [];

          // Parse date
          let submittedAt = new Date().toISOString();
          if (rowData['Submitted At']) {
            try {
              // Try to parse the date in various formats
              const dateStr = rowData['Submitted At'];
              if (dateStr.match(/^\d{4}-\d{2}-\d{2}/)) {
                // ISO format
                submittedAt = new Date(dateStr).toISOString();
              } else {
                // MMM d, yyyy h:mm a format
                const parsedDate = parse(dateStr, 'MMM d, yyyy h:mm a', new Date());
                submittedAt = parsedDate.toISOString();
              }
            } catch (error) {
              console.warn('Could not parse date:', rowData['Submitted At']);
            }
          }

          // Create document in Sanity
          await client.create({
            _type: 'rsvp',
            firstName: rowData['First Name'],
            lastName: rowData['Last Name'],
            email: rowData['Email'],
            phone: rowData['Phone'],
            country: rowData['Country'],
            events,
            attendanceType: rowData['Attendance Type'],
            reminderPreference: reminderPrefs,
            notes: rowData['Notes'],
            submittedAt,
          });

          stats.success++;
        } catch (error) {
          console.error('Error processing row:', error);
          stats.failed++;
          if (error instanceof Error) {
            stats.errors.push(`Row ${i + 2}: ${error.message}`);
          } else {
            stats.errors.push(`Row ${i + 2}: Unknown error`);
          }
        }
      }

      setUploadStats(stats);

      // Refresh the data
      if (stats.success > 0) {
        await fetchRsvpData();
        toast.success(`Successfully imported ${stats.success} RSVP submissions`);
      }

      if (stats.failed > 0) {
        toast.error(`Failed to import ${stats.failed} RSVP submissions`);
      }
    } catch (error) {
      console.error('Failed to process CSV:', error);
      toast.error('Failed to process CSV file');
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Filter submissions based on search term
  const filteredSubmissions = rsvpSubmissions.filter(
    (submission) =>
      submission.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.country.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Convert RSVP submissions to CSV format
  const exportToCSV = () => {
    // Define CSV headers
    const headers = [
      'First Name',
      'Last Name',
      'Email',
      'Phone',
      'Country',
      'Events',
      'Attendance Type',
      'Reminder Preferences',
      'Notes',
      'Submitted At'
    ];

    // Convert submissions to CSV rows
    const csvRows = filteredSubmissions.map(submission => {
      const events = submission.events ? submission.events.join(', ') : '';
      const reminderPrefs = submission.reminderPreference ? submission.reminderPreference.join(', ') : '';
      const submittedDate = submission.submittedAt ? formatDate(submission.submittedAt) : '';

      return [
        submission.firstName,
        submission.lastName,
        submission.email,
        submission.phone,
        submission.country,
        events,
        submission.attendanceType,
        reminderPrefs,
        submission.notes || '',
        submittedDate
      ];
    });

    // Combine headers and rows
    const csvContent = [
      headers.join(','),
      ...csvRows.map(row =>
        row.map(cell =>
          // Escape quotes and wrap in quotes if the cell contains commas, quotes, or newlines
          typeof cell === 'string' && (cell.includes(',') || cell.includes('"') || cell.includes('\n'))
            ? `"${cell.replace(/"/g, '""')}"`
            : cell
        ).join(',')
      )
    ].join('\n');

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Create a download link and trigger the download
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    const date = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD

    link.setAttribute('href', url);
    link.setAttribute('download', `rsvp-submissions-${date}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success notification
    toast.success(`RSVP data exported successfully (${filteredSubmissions.length} records)`);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">RSVP Management</h1>
          <p className="text-muted-foreground">
            View and manage RSVP submissions for Kingdom events.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".csv"
            className="hidden"
            id="csv-upload"
          />
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="flex items-center gap-2"
            variant="outline"
          >
            <Upload className="h-4 w-4" />
            {isUploading ? 'Uploading...' : 'Import CSV'}
          </Button>
          <Button
            onClick={exportToCSV}
            disabled={isLoading || filteredSubmissions.length === 0}
            className="flex items-center gap-2"
            title={`Download all ${filteredSubmissions.length} RSVP submissions as a CSV file`}
          >
            <Download className="h-4 w-4" />
            Export CSV ({filteredSubmissions.length})
          </Button>
        </div>
      </div>

      <Separator />

      {uploadStats && (
        <div className={`p-4 rounded-md ${uploadStats.failed > 0 ? 'bg-amber-50 border border-amber-200' : 'bg-green-50 border border-green-200'}`}>
          <div className="flex items-start gap-3">
            {uploadStats.failed > 0 ? (
              <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            )}
            <div>
              <h3 className="font-medium">CSV Import Results</h3>
              <p className="text-sm mt-1">
                Total: {uploadStats.total} | Successful: {uploadStats.success} | Failed: {uploadStats.failed}
              </p>
              {uploadStats.errors.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium text-amber-800">Errors:</p>
                  <ul className="text-xs mt-1 space-y-1 text-amber-800">
                    {uploadStats.errors.slice(0, 5).map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                    {uploadStats.errors.length > 5 && (
                      <li>...and {uploadStats.errors.length - 5} more errors</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center gap-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search submissions..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>RSVP Submissions</CardTitle>
          <CardDescription>
            Showing {filteredSubmissions.length} submissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <p>Loading RSVP submissions...</p>
            </div>
          ) : filteredSubmissions.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Country</TableHead>
                  <TableHead>Events</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubmissions.map((submission) => (
                  <TableRow key={submission._id}>
                    <TableCell className="font-medium">
                      {submission.firstName} {submission.lastName}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col space-y-1">
                        <span className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1" /> {submission.email}
                        </span>
                        <span className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1" /> {submission.phone}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{submission.country}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {submission.events.map((event) => (
                          <Badge key={event} variant="outline" className="text-xs">
                            {event}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(submission.submittedAt)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/rsvp/${submission._id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              window.location.href = `mailto:${submission.email}`;
                            }}
                          >
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => deleteRsvpSubmission(submission._id)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No submissions found</h3>
              <p className="mt-2 text-muted-foreground">
                Try adjusting your search to find what you&apos;re looking for.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
