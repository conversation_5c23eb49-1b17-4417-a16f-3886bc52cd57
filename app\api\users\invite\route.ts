import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createTemporaryUser } from '@/lib/users';
import { generateToken } from '@/lib/tokens';
import { sendEmail } from '@/lib/email';

// POST /api/users/invite - Invite a new user (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }
    
    // Check if user is an admin or super admin
    const userRole = session.user.role;
    if (userRole !== 'admin' && userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { email, role = 'user', expiresIn = '7d' } = body;
    
    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }
    
    try {
      // Create a temporary user
      const tempUser = await createTemporaryUser({
        email,
        role,
        invitedBy: session.user.id,
      });
      
      // Generate a registration token
      const token = generateToken({
        type: 'register',
        email,
        role,
        userId: tempUser.id,
      }, expiresIn);
      
      // Create registration link
      const registrationLink = `${process.env.NEXT_PUBLIC_APP_URL}/register?token=${token}`;
      
      // Send invitation email
      try {
        await sendEmail({
          to: email,
          subject: 'Invitation to join Kingdom Adukrom',
          text: `You have been invited to join Kingdom Adukrom. Please click the link below to complete your registration:\n\n${registrationLink}\n\nThis link will expire in ${expiresIn === '7d' ? '7 days' : expiresIn}.`,
          html: `
            <h1>Welcome to Kingdom Adukrom</h1>
            <p>You have been invited to join Kingdom Adukrom.</p>
            <p>Please click the button below to complete your registration:</p>
            <p>
              <a href="${registrationLink}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                Complete Registration
              </a>
            </p>
            <p>Or copy and paste this link into your browser:</p>
            <p>${registrationLink}</p>
            <p>This link will expire in ${expiresIn === '7d' ? '7 days' : expiresIn}.</p>
          `,
        });
      } catch (emailError) {
        console.error('Failed to send invitation email:', emailError);
        // Continue even if email fails, just return the token
      }
      
      return NextResponse.json({
        success: true,
        message: 'Invitation sent successfully',
        user: {
          id: tempUser.id,
          email: tempUser.email,
          role: tempUser.role,
          isTemporary: true,
        },
        registrationLink,
      });
    } catch (error: any) {
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to invite user' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error inviting user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to invite user' },
      { status: 500 }
    );
  }
}
