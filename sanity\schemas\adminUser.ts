import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'adminUser',
  title: 'Admin User',
  type: 'document',
  fields: [
    defineField({
      name: 'username',
      title: 'Username',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'email',
      title: 'Email',
      type: 'string',
      validation: (Rule) => Rule.required().email(),
    }),
    defineField({
      name: 'name',
      title: 'Full Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'role',
      title: 'Role',
      type: 'string',
      options: {
        list: [
          { title: 'Admin', value: 'admin' },
          { title: 'Super Admin', value: 'super_admin' },
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'hashedPassword',
      title: 'Hashed Password',
      type: 'string',
      hidden: true, // Hide this field in the Sanity Studio UI
    }),
    defineField({
      name: 'image',
      title: 'Profile Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alternative Text',
          type: 'string',
          description: 'Important for accessibility',
        },
      ],
    }),
    defineField({
      name: 'createdAt',
      title: 'Created At',
      type: 'datetime',
      readOnly: true,
    }),
    defineField({
      name: 'updatedAt',
      title: 'Updated At',
      type: 'datetime',
      readOnly: true,
    }),
    defineField({
      name: 'lastLogin',
      title: 'Last Login',
      type: 'datetime',
      readOnly: true,
    }),
    defineField({
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      description: 'Whether this user can log in',
      initialValue: true,
    }),
    defineField({
      name: 'isProtected',
      title: 'Is Protected',
      type: 'boolean',
      description: 'Protected users cannot be deleted',
      initialValue: false,
    }),
    defineField({
      name: 'permissions',
      title: 'Custom Permissions',
      type: 'object',
      description: 'Override default role permissions',
      fields: [
        { name: 'canManageContent', title: 'Can Manage Content', type: 'boolean' },
        { name: 'canManageEvents', title: 'Can Manage Events', type: 'boolean' },
        { name: 'canManageGallery', title: 'Can Manage Gallery', type: 'boolean' },
        { name: 'canManageNews', title: 'Can Manage News', type: 'boolean' },
        { name: 'canManageRsvp', title: 'Can Manage RSVP', type: 'boolean' },
        { name: 'canManageSettings', title: 'Can Manage Settings', type: 'boolean' },
        { name: 'canManageUsers', title: 'Can Manage Users', type: 'boolean' },
        { name: 'canManageStore', title: 'Can Manage Store', type: 'boolean' },
        { name: 'canManageBankingInfo', title: 'Can Manage Banking Info', type: 'boolean' },
      ],
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'role',
      media: 'image',
    },
    prepare(selection) {
      const { title, subtitle, media } = selection;
      return {
        title,
        subtitle: subtitle === 'super_admin' ? 'Super Admin' : 'Admin',
        media,
      };
    },
  },
});
