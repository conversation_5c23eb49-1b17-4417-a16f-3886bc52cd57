'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle } from 'lucide-react';
import { logError } from '@/lib/errorHandling';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * Error boundary component specifically for Sanity components
 * Catches errors in Sanity components and displays a fallback UI
 */
class SanityErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our error tracking system
    logError(error, 'SanityErrorBoundary');
    console.error('Sanity component error:', error, errorInfo);
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  public render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Otherwise, use the default fallback UI
      return (
        <div className="p-4 border border-amber-200 bg-amber-50 rounded-md my-4">
          <div className="flex items-center text-amber-800 mb-2">
            <AlertCircle className="h-5 w-5 mr-2" />
            <h3 className="font-medium">Content temporarily unavailable</h3>
          </div>
          <p className="text-sm text-amber-700">
            We're having trouble loading this content. Please try again later.
          </p>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2 text-sm text-amber-800 underline"
          >
            Try again
          </button>
        </div>
      );
    }

    // If there's no error, render the children
    return this.props.children;
  }
}

export default SanityErrorBoundary;
