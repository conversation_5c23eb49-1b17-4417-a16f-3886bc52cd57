'use client';

import { useState, useEffect, useRef } from 'react';
import { fileUrlFor, isVideoFile } from '@/lib/sanity.client';

interface SanityVideoProps {
  value: any;
  className?: string;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  poster?: string;
  width?: number | string;
  height?: number | string;
  style?: React.CSSProperties;
}

export default function SanityVideo({
  value,
  className = '',
  autoPlay = false,
  loop = false,
  muted = true,
  controls = true,
  poster,
  width = '100%',
  height = 'auto',
  style = {},
}: SanityVideoProps) {
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!value) {
      setError('No video source provided');
      setIsLoading(false);
      return;
    }

    try {
      // Check if it's a video file
      if (!isVideoFile(value)) {
        setError('Not a valid video file');
        setIsLoading(false);
        return;
      }

      // Get the URL for the video
      const url = fileUrlFor(value);
      if (!url) {
        setError('Could not generate video URL');
        setIsLoading(false);
        return;
      }

      setVideoUrl(url);
      setIsLoading(false);
      setError(null);
    } catch (err) {
      console.error('Error loading video:', err);
      setError('Error loading video');
      setIsLoading(false);
    }
  }, [value]);

  // Handle video load event
  const handleVideoLoad = () => {
    setIsLoading(false);
  };

  // Handle video error event
  const handleVideoError = () => {
    setError('Error loading video');
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={{ width, height, ...style }}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={{ width, height, ...style }}
      >
        <div className="text-red-500 text-center p-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10 mx-auto mb-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          {error}
        </div>
      </div>
    );
  }

  return (
    <video
      ref={videoRef}
      className={`object-contain ${className}`}
      width={width}
      height={height}
      style={style}
      controls={controls}
      autoPlay={autoPlay}
      loop={loop}
      muted={muted}
      playsInline
      poster={poster}
      onLoadedData={handleVideoLoad}
      onError={handleVideoError}
    >
      <source src={videoUrl} type="video/mp4" />
      Your browser does not support the video tag.
    </video>
  );
}
