import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';
import { getProductBySlug } from '@/lib/sanity';

// Generate dynamic metadata for product pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Fetch the product data
    const product = await getProductBySlug(params.slug);

    if (!product) {
      return generateDynamicMetadata({
        title: 'Product Not Found',
        description: 'The requested product could not be found.',
        url: `/store/${params.slug}`,
      });
    }

    // Get the product image URL
    let imageUrl = '/images/og-image.jpg';
    if (product.images && product.images.length > 0) {
      if (product.images[0].asset) {
        // This is a Sanity image
        imageUrl = product.images[0].url || imageUrl;
      } else if (product.images[0].url) {
        // This is a direct URL
        imageUrl = product.images[0].url;
      }
    }

    // Generate metadata for the product
    return generateDynamicMetadata({
      title: product.seo?.metaTitle || product.name,
      description: product.seo?.metaDescription || product.description,
      image: product.seo?.openGraph?.image?.asset?.url || imageUrl,
      url: `/store/${params.slug}`,
      type: 'website',
      keywords: product.seo?.seoKeywords || [
        'Royal Merchandise',
        product.name,
        ...(product.category ? [product.category.title] : []),
        ...(product.tags || [])
      ],
      noIndex: product.seo?.nofollowAttributes || false,
    });
  } catch (error) {
    console.error('Error generating product metadata:', error);

    // Fallback metadata
    return generateDynamicMetadata({
      title: 'Product Details',
      description: 'View details about this royal merchandise product.',
      url: `/store/${params.slug}`,
    });
  }
}
