import { Metadata } from 'next';
import { getSiteSettings } from './sanity';
import { env } from './env';
import { urlFor } from './sanity.client';

/**
 * Generate dynamic metadata from Sanity site settings
 *
 * This function fetches the site settings from Sanity and generates
 * metadata based on those settings. If the settings are not available,
 * it falls back to default values.
 */
export async function generateDynamicMetadata(
  options: {
    title?: string;
    description?: string;
    image?: string;
    url?: string;
    type?: 'website' | 'article' | 'profile' | 'book';
    keywords?: string[];
    noIndex?: boolean;
  } = {}
): Promise<Metadata> {
  // Fetch site settings from Sanity
  const settings = await getSiteSettings();

  // Get base values from settings or use defaults, prioritizing SEO fields over metadata fields
  const baseTitle = settings?.seo?.metaTitle || settings?.metadata?.metaTitle || settings?.title || 'The Royal Family of Africa';
  const baseDescription = settings?.seo?.metaDescription || settings?.metadata?.metaDescription || settings?.description || 'The Crown of Africa. The Rise of a New Era.';
  const baseKeywords = settings?.seo?.seoKeywords || settings?.metadata?.metaKeywords || settings?.keywords || ['royalty', 'crown', 'africa', 'rise', 'new era'];
  const titleTemplate = settings?.metadata?.titleTemplate || '%s | The Crown of Africa';

  // Determine the final values, prioritizing passed options over settings
  const title = options.title || baseTitle;
  const description = options.description || baseDescription;
  const keywords = options.keywords || baseKeywords;
  const type = options.type || 'website';
  const noIndex = options.noIndex || settings?.seo?.nofollowAttributes || false;

  // Handle URLs and images
  const baseUrl = settings?.metadata?.siteUrl || env.NEXT_PUBLIC_APP_URL || 'https://adukingdom.herokuapp.com';
  const url = options.url ? `${baseUrl}${options.url}` : baseUrl;

  // Handle image URL
  let imageUrl: string;
  if (options.image) {
    // If an image is provided in options, use it
    imageUrl = options.image.startsWith('http') ? options.image : `${baseUrl}${options.image}`;
  } else if (settings?.seo?.openGraph?.image) {
    // If there's an OG image in SEO settings, use it
    imageUrl = urlFor(settings.seo.openGraph.image).url();
  } else if (settings?.metadata?.ogImage) {
    // If there's an OG image in metadata settings, use it
    imageUrl = urlFor(settings.metadata.ogImage).url();
  } else {
    // Fall back to default image
    imageUrl = `${baseUrl}/images/og-image.jpg`;
  }

  // Build the metadata object
  return {
    title: {
      default: title,
      template: titleTemplate,
    },
    description,
    keywords,
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      siteName: baseTitle,
      images: [{ url: imageUrl, alt: title }],
      type,
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: settings?.metadata?.twitterHandle ? `@${settings.metadata.twitterHandle}` : '@KingdomAdukrom',
    },
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    icons: {
      icon: '/favicon.ico',
      apple: '/apple-touch-icon.png',
    },
  };
}

/**
 * Generate news article metadata
 */
export async function generateDynamicNewsMetadata(news: any): Promise<Metadata> {
  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: news.seo?.metaTitle || news.title,
    description: news.seo?.metaDescription || news.excerpt,
    image: news.seo?.openGraph?.image?.asset?.url || news.mainImage?.asset?.url,
    url: `/news/${news.slug?.current}`,
    type: 'article',
    keywords: news.seo?.seoKeywords || ['Adukrom News', news.title, news.category?.title, 'Royal News'],
    noIndex: news.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate event metadata
 */
export async function generateDynamicEventMetadata(event: any): Promise<Metadata> {
  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: event.seo?.metaTitle || event.title,
    description: event.seo?.metaDescription || event.description,
    image: event.seo?.openGraph?.image?.asset?.url || event.imageUrl,
    url: `/events/${event.slug?.current}`,
    type: 'website',
    keywords: event.seo?.seoKeywords || ['Adukrom Event', event.title, event.eventType, 'Royal Event'],
    noIndex: event.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate gallery metadata
 */
export async function generateDynamicGalleryMetadata(gallery?: any): Promise<Metadata> {
  if (!gallery) {
    return generateDynamicMetadata({
      title: 'Royal Gallery',
      description: 'Explore the royal gallery of the Kingdom of Adukrom',
      url: '/gallery',
      type: 'website',
      keywords: ['Adukrom Gallery', 'Royal Photos', 'Kingdom Gallery'],
    });
  }

  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: gallery.seo?.metaTitle || gallery.title,
    description: gallery.seo?.metaDescription || gallery.description,
    image: gallery.seo?.openGraph?.image?.asset?.url || gallery.images?.[0]?.asset?.url,
    url: `/gallery/${gallery.slug?.current}`,
    type: 'website',
    keywords: gallery.seo?.seoKeywords || ['Adukrom Gallery', 'Royal Photos', 'Kingdom Gallery'],
    noIndex: gallery.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate page metadata
 */
export async function generateDynamicPageMetadata(page: any): Promise<Metadata> {
  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: page.seo?.metaTitle || page.title,
    description: page.seo?.metaDescription || page.description || page.excerpt,
    image: page.seo?.openGraph?.image?.asset?.url || page.mainImage?.asset?.url,
    url: `/${page.slug?.current}`,
    type: 'website',
    keywords: page.seo?.seoKeywords || ['Kingdom of Adukrom', page.title],
    noIndex: page.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate category metadata
 */
export async function generateDynamicCategoryMetadata(category: any): Promise<Metadata> {
  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: category.seo?.metaTitle || `${category.title} - Category`,
    description: category.seo?.metaDescription || category.description || `Browse all ${category.title} content`,
    image: category.seo?.openGraph?.image?.asset?.url || category.image?.asset?.url,
    url: `/category/${category.slug?.current}`,
    type: 'website',
    keywords: category.seo?.seoKeywords || ['Adukrom Category', category.title],
    noIndex: category.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate store category metadata
 */
export async function generateDynamicStoreCategoryMetadata(category: any): Promise<Metadata> {
  // Use SEO fields if available, otherwise fall back to default fields
  return generateDynamicMetadata({
    title: category.seo?.metaTitle || `${category.title} - Store Category`,
    description: category.seo?.metaDescription || category.description || `Browse all ${category.title} products`,
    image: category.seo?.openGraph?.image?.asset?.url || category.image?.asset?.url,
    url: `/store/category/${category.slug?.current}`,
    type: 'website',
    keywords: category.seo?.seoKeywords || ['Adukrom Store', 'Royal Products', category.title],
    noIndex: category.seo?.nofollowAttributes || false,
  });
}

/**
 * Generate order confirmation metadata
 */
export async function generateOrderConfirmationMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Order Confirmation',
    description: 'Thank you for your order from the Kingdom of Adukrom Royal Store.',
    url: '/store/confirmation',
    type: 'website',
    keywords: ['Order Confirmation', 'Thank You', 'Purchase Complete'],
    noIndex: true, // We don't want search engines to index order confirmation pages
  });
}
