/**
 * Authentication utility functions
 * 
 * This module provides helper functions for authentication and authorization.
 */

import { AdminRole } from './types/admin';

/**
 * Helper function to check if a user is authenticated
 */
export function isAuthenticated(session: any): boolean {
  return !!session?.user;
}

/**
 * Helper function to check if a user has admin role
 */
export function isAdmin(session: any): boolean {
  return isAuthenticated(session) && (
    session.user.role === AdminRole.ADMIN ||
    session.user.role === AdminRole.SUPER_ADMIN
  );
}

/**
 * Helper function to check if a user has super admin role
 */
export function isSuperAdmin(session: any): boolean {
  return isAuthenticated(session) && session.user.role === AdminRole.SUPER_ADMIN;
}

/**
 * Helper function to check if a user has permission to access a feature
 */
export function hasPermission(
  session: any,
  permission: string
): boolean {
  if (!isAuthenticated(session)) return false;

  const role = session.user.role as AdminRole;

  // Define role permissions
  const rolePermissions = {
    [AdminRole.ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: false,
      canManageUsers: false,
      canManageStore: false,
      canManageBankingInfo: false,
    },
    [AdminRole.SUPER_ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: true,
      canManageUsers: true,
      canManageStore: true,
      canManageBankingInfo: true,
    }
  };

  return rolePermissions[role]?.[permission as keyof typeof rolePermissions[typeof role]] === true;
}
