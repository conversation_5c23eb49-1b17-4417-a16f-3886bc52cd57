/**
 * Type definitions for Sanity data
 * These types match the schema structure in the Sanity Studio
 */

export type MetadataSettings = {
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  ogImage?: any;
  twitterHandle?: string;
  siteUrl?: string;
  titleTemplate?: string;
};

export type SEOSettings = {
  _type?: string;
  metaTitle?: string;
  metaDescription?: string;
  seoKeywords?: string[];
  nofollowAttributes?: boolean;
  openGraph?: {
    _type?: string;
    title?: string;
    description?: string;
    url?: string;
    siteName?: string;
    image?: any;
  };
  twitter?: {
    _type?: string;
    site?: string;
    creator?: string;
    cardType?: string;
    handle?: string;
  };
  instagram?: {
    _type?: string;
    caption?: string;
    hashtags?: string[];
  };
  additionalMetaTags?: Array<{
    _type?: string;
    metaAttributes?: Array<{
      _type?: string;
      attributeKey?: string;
      attributeType?: string;
      attributeValueString?: string;
      attributeValueImage?: any;
    }>;
  }>;
};

export type SiteSettings = {
  title?: string;
  description?: string;
  keywords?: string[];
  logo?: any;
  favicon?: any;
  mainNavigation?: NavigationItem[];
  contact?: ContactInfo;
  social?: SocialMedia;
  footer?: FooterSettings;
  about?: AboutSection;
  events?: EventSettings;
  banking?: BankingSettings;
  store?: StoreSettings;
  maintenanceMode?: boolean;
  metadata?: MetadataSettings;
  seo?: SEOSettings;
};

export type NavigationItem = {
  name: string;
  link: string;
  isExternal?: boolean;
  isNew?: boolean;
  icon?: string;
  children?: NavigationItem[];
};

export type ContactInfo = {
  email?: string;
  phone?: string;
  address?: string;
  mapCoordinates?: string;
};

export type SocialMedia = {
  instagram?: string;
  facebook?: string;
  twitter?: string;
  linkedin?: string;
};

export type FooterSettings = {
  copyright?: string;
  links?: { text: string; url: string }[];
};

export type AboutSection = {
  kingBio?: string;
  kingVision?: string;
  kingMission?: string;
  kingPurpose?: string;
  kingQuote?: string;
  adukromDescription?: string[];
  adukromLocation?: string;
  nifaheneDescription?: string[];
};

export type EventSettings = {
  coronationDate?: string;
  coronationLocation?: string;
  showCountdown?: boolean;
};

export type BankingSettings = {
  accountName?: string;
  accountNumber?: string;
  routingNumber?: string;
  bankName?: string;
  swiftCode?: string;
  paymentProcessor?: string;
  enablePayments?: boolean;
};

export type NFTSettings = {
  contractAddress?: string;
  blockchainNetwork?: string;
  enableNFTs?: boolean;
};

export type StoreSettings = {
  enableStore?: boolean;
  shippingFee?: string;
  taxRate?: string;
  currencySymbol?: string;
  allowInternationalShipping?: boolean;
  minimumOrderAmount?: string;
  nftSettings?: NFTSettings;
};

export type Event = {
  _id: string;
  title: string;
  slug: { current: string };
  date: any;
  location: any;
  description: string;
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
};

export type GalleryItem = {
  _id: string;
  title: string;
  description: string;
  image: any;
  category?: {
    title: string;
    slug: { current: string };
  };
  tags?: string[];
  publishedAt?: string;
  featured?: boolean;
  order?: number;
};

export type NewsArticle = {
  _id: string;
  title: string;
  slug: { current: string };
  excerpt?: string;
  mainImage?: any;
  body?: any;
  publishedAt?: string;
  category?: {
    title: string;
    slug: { current: string };
  };
  author?: {
    name: string;
    image?: any;
    bio?: string;
  };
  featured?: boolean;
  status?: string;
};

export type Category = {
  _id: string;
  title: string;
  slug: { current: string };
  description?: string;
  color?: string;
  icon?: string;
  order?: number;
};

export type Page = {
  _id: string;
  title: string;
  slug: { current: string };
  description?: string;
  navMenu?: boolean;
  navOrder?: number;
  navCategory?: string;
  accessLevel?: string;
  pageBuilder?: any[];
  openGraphImage?: any;
};
