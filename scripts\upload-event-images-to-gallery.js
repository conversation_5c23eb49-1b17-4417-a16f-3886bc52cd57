const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2025-05-09',
  useCdn: false,
});

// Event images to upload
const EVENT_IMAGES = [
  {
    title: 'Royal Coronation Ceremony',
    description: 'Images from the Royal Coronation Ceremony of King <PERSON>',
    slug: 'royal-coronation-ceremony',
    images: [
      {
        path: 'public/Website Images/Royal Coronation Ceremony.png',
        alt: 'Royal Coronation Ceremony',
        caption: 'The official coronation ceremony of King <PERSON>'
      }
    ],
    category: 'events',
    tags: ['ceremony', 'coronation', 'royal'],
    featured: true
  },
  {
    title: 'Royal Gala Dinner',
    description: 'Images from the Royal Gala Dinner',
    slug: 'royal-gala-dinner',
    images: [
      {
        path: 'public/Website Images/ghana-osuodumgya-event-2023.png',
        alt: 'Royal Gala Dinner',
        caption: 'An elegant evening of celebration featuring traditional performances'
      }
    ],
    category: 'events',
    tags: ['dinner', 'gala', 'royal'],
    featured: false
  },
  {
    title: 'Global Economic Forum',
    description: 'Images from the Global Economic Forum',
    slug: 'global-economic-forum',
    images: [
      {
        path: 'public/Website Images/Global Economic Forum.webp',
        alt: 'Global Economic Forum',
        caption: 'A forward-looking conference bringing together investors and entrepreneurs'
      }
    ],
    category: 'events',
    tags: ['conference', 'economic', 'forum'],
    featured: false
  }
];

// Function to check if a gallery already exists in Sanity
async function galleryExists(slug) {
  const result = await client.fetch(
    `*[_type == "gallery" && slug.current == $slug][0]._id`,
    { slug }
  );
  return !!result;
}

// Function to get or create a category
async function getOrCreateCategory(categoryName) {
  try {
    // Check if category exists
    const existingCategory = await client.fetch(
      `*[_type == "category" && title == $title][0]`,
      { title: categoryName }
    );

    if (existingCategory) {
      console.log(`Category "${categoryName}" already exists with ID: ${existingCategory._id}`);
      return existingCategory._id;
    }

    // Create category if it doesn't exist
    const slug = categoryName
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');

    const newCategory = await client.create({
      _type: 'category',
      title: categoryName,
      slug: { current: slug },
      description: `${categoryName} category`,
      order: 0
    });

    console.log(`Created new category "${categoryName}" with ID: ${newCategory._id}`);
    return newCategory._id;
  } catch (error) {
    console.error(`Error getting/creating category "${categoryName}":`, error);
    return null;
  }
}

// Function to upload an image to Sanity
async function uploadImageToSanity(imagePath) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.error(`Image file not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const imageAsset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath)
    });

    console.log(`Uploaded image: ${imagePath} with ID: ${imageAsset._id}`);
    return imageAsset;
  } catch (error) {
    console.error(`Error uploading image ${imagePath}:`, error);
    return null;
  }
}

// Function to create a gallery in Sanity
async function createGallery(galleryData) {
  try {
    // Check if the gallery already exists
    const exists = await galleryExists(galleryData.slug);
    if (exists) {
      console.log(`Gallery "${galleryData.title}" already exists, skipping...`);
      return null;
    }

    // Get or create category
    const categoryId = await getOrCreateCategory(galleryData.category);
    if (!categoryId) {
      console.error(`Failed to get/create category for gallery "${galleryData.title}"`);
      return null;
    }

    // Upload images
    const galleryImages = [];
    for (const image of galleryData.images) {
      const imageAsset = await uploadImageToSanity(image.path);
      if (imageAsset) {
        galleryImages.push({
          mediaType: 'image',
          image: {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: imageAsset._id
            }
          },
          alt: image.alt,
          caption: image.caption
        });
      }
    }

    if (galleryImages.length === 0) {
      console.error(`No images were successfully uploaded for gallery "${galleryData.title}"`);
      return null;
    }

    // Create the gallery
    console.log(`Creating gallery: ${galleryData.title}`);
    const result = await client.create({
      _type: 'gallery',
      title: galleryData.title,
      slug: { current: galleryData.slug },
      description: galleryData.description,
      images: galleryImages,
      category: {
        _type: 'reference',
        _ref: categoryId
      },
      tags: galleryData.tags,
      publishedAt: new Date().toISOString(),
      featured: galleryData.featured,
      displayStyle: 'grid',
      backgroundStyle: 'none',
      order: 0
    });
    
    console.log(`Created gallery: ${galleryData.title} with ID: ${result._id}`);
    return result;
  } catch (error) {
    console.error(`Error creating gallery "${galleryData.title}":`, error);
    return null;
  }
}

// Main function to upload event images to gallery
async function uploadEventImagesToGallery() {
  console.log('Starting event image upload to gallery...');
  
  // Validate token
  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN is missing. Please add it to your .env.local file.');
    process.exit(1);
  }

  try {
    // Create each gallery
    const results = [];
    for (const galleryData of EVENT_IMAGES) {
      const result = await createGallery(galleryData);
      if (result) {
        results.push(result);
      }
    }

    console.log(`Upload complete. Created ${results.length} galleries.`);
    
    // Now update the events to reference these gallery images
    for (const gallery of results) {
      // Find the corresponding event
      const event = await client.fetch(
        `*[_type == "event" && slug.current == $slug][0]`,
        { slug: gallery.slug.current }
      );

      if (event) {
        console.log(`Updating event "${event.title}" with gallery image...`);
        
        // Get the first image from the gallery
        const galleryImage = gallery.images && gallery.images.length > 0 ? gallery.images[0].image : null;
        
        if (galleryImage) {
          // Update the event with the image
          await client
            .patch(event._id)
            .set({ image: galleryImage })
            .commit();
            
          console.log(`Updated event "${event.title}" with gallery image.`);
        }
      }
    }
    
    console.log('Event image update complete.');
  } catch (error) {
    console.error('Error during upload:', error);
    process.exit(1);
  }
}

// Run the upload
uploadEventImagesToGallery();
