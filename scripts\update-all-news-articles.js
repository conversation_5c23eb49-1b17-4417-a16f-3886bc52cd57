// This script runs all the news article update scripts in sequence
require('dotenv').config();
const { exec } = require('child_process');
const path = require('path');

console.log('Starting to update all news articles...');

// Function to run a script and wait for it to complete
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(`Running script: ${scriptPath}`);
    
    const process = exec(`node ${scriptPath}`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing script ${scriptPath}:`, error);
        return reject(error);
      }
      
      console.log(stdout);
      if (stderr) console.error(stderr);
      
      resolve();
    });
  });
}

// Run all scripts in sequence
async function updateAllArticles() {
  try {
    // Run the first script
    await runScript(path.join(__dirname, 'update-news-articles.js'));
    console.log('Completed first batch of articles');
    
    // Run the second script
    await runScript(path.join(__dirname, 'update-news-articles-part2.js'));
    console.log('Completed second batch of articles');
    
    // Run the third script
    await runScript(path.join(__dirname, 'update-news-articles-part3.js'));
    console.log('Completed third batch of articles');
    
    console.log('All news articles have been updated successfully!');
  } catch (error) {
    console.error('Error updating news articles:', error);
  }
}

// Run the main function
updateAllArticles();
