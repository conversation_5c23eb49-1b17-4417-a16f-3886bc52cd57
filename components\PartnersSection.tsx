'use client';

import React from 'react';

export default function PartnersSection() {
  return (
    <section id="partners" className="py-16 bg-[#f9f7e8]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#001a4d] mb-4">Strategic Partners of the Crown</h2>
          <div className="w-20 h-1 bg-royalGold mx-auto mb-6"></div>
          <p className="text-lg max-w-3xl mx-auto text-[#001a4d]">
            At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
          </p>
        </div>
        
        <div className="flex flex-wrap justify-center gap-12 md:gap-16">
          {/* Remit Global */}
          <div className="flex flex-col items-center max-w-[180px]">
            <div className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-white shadow-md">
              <img 
                src="/Website Images/remit-global-logo.png" 
                alt="Remit Global" 
                className="w-24 h-24 object-contain"
              />
            </div>
            <h3 className="text-lg font-bold text-[#001a4d] mb-1 text-center">Remit Global</h3>
            <p className="text-sm text-[#001a4d] text-center">Strategic Financial Partner</p>
          </div>
          
          {/* Royal Lion */}
          <div className="flex flex-col items-center max-w-[180px]">
            <div className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-white shadow-md">
              <img 
                src="/Website Images/royal_lion_logo.png" 
                alt="Royal Lion" 
                className="w-24 h-24 object-contain"
              />
            </div>
            <h3 className="text-lg font-bold text-[#001a4d] mb-1 text-center">Royal Lion</h3>
            <p className="text-sm text-[#001a4d] text-center">Heritage & Cultural Partner</p>
          </div>
          
          {/* TEF */}
          <div className="flex flex-col items-center max-w-[180px]">
            <div className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-white shadow-md">
              <img 
                src="/Website Images/tef-logo2-transparent.png" 
                alt="TEF" 
                className="w-24 h-24 object-contain"
              />
            </div>
            <h3 className="text-lg font-bold text-[#001a4d] mb-1 text-center">TEF</h3>
            <p className="text-sm text-[#001a4d] text-center">Educational Development Partner</p>
          </div>
          
          {/* Lightace Global */}
          <div className="flex flex-col items-center max-w-[180px]">
            <div className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-white shadow-md">
              <img 
                src="/Website Images/lightace-global-logo.png" 
                alt="Lightace Global" 
                className="w-24 h-24 object-contain"
              />
            </div>
            <h3 className="text-lg font-bold text-[#001a4d] mb-1 text-center">Lightace Global</h3>
            <p className="text-sm text-[#001a4d] text-center">Innovation & Technology Partner</p>
          </div>
          
          {/* Akuapem Nifaman Council */}
          <div className="flex flex-col items-center max-w-[180px]">
            <div className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-white shadow-md">
              <img 
                src="/Website Images/akuaapem_nifaman_council_logo.png" 
                alt="Akuapem Nifaman Council" 
                className="w-24 h-24 object-contain"
              />
            </div>
            <h3 className="text-lg font-bold text-[#001a4d] mb-1 text-center">Akuapem Nifaman Council</h3>
            <p className="text-sm text-[#001a4d] text-center">Traditional Governance Partner</p>
          </div>
        </div>
      </div>
    </section>
  );
}
