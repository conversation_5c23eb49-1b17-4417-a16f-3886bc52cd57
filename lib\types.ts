import { PortableTextBlock } from '@portabletext/types';

export interface Author {
  _id: string;
  name: string;
  slug?: {
    current: string;
  };
  image?: any;
  bio?: PortableTextBlock[];
  title?: string;
}

export interface Category {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  description?: string;
  color?: string;
}

export interface NewsArticle {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  publishedAt: string;
  excerpt?: string;
  mainImage?: any;
  body?: PortableTextBlock[];
  categories?: Category[] | string[];
  author?: Author | string;
  featured?: boolean;
}

export interface NewsListItem {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  publishedAt: string;
  excerpt?: string;
  mainImage?: any;
  categories?: string[];
  author?: string;
}

export interface GalleryImage {
  _id: string;
  title: string;
  description?: string;
  image: any;
  category: string;
  featured?: boolean;
  publishedAt: string;
  source?: string;
}
