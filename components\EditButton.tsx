'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useEffect, useState } from 'react';
import SimpleOverlayEditor from './SimpleOverlayEditor';

interface EditButtonProps {
  sectionName: string;
  adminPath?: string;
  sectionId?: string;
}

export default function EditButton({
  sectionName,
  adminPath,
  sectionId
}: EditButtonProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [showEditor, setShowEditor] = useState(false);

  // Convert sectionName to a type that can be used by the editor
  const sectionType = sectionName.toLowerCase().replace(/\s+/g, '');

  // Check if user is authenticated and has admin role
  const isAdmin = session?.user?.role === 'admin' || session?.user?.role === 'super_admin';

  useEffect(() => {
    setMounted(true);
    console.log(`EditButton for ${sectionName}:`, {
      session,
      status,
      isAdmin,
      role: session?.user?.role
    });
  }, [session, status, isAdmin, sectionName]);

  // Don't render anything during SSR or if user is not admin
  if (!mounted || !isAdmin) return null;

  const handleClick = () => {
    // Show the overlay editor
    setShowEditor(true);
    toast.info(`Editing ${sectionName} section`);
  };

  return (
    <>
      <Button
        variant="default"
        size="sm"
        onClick={handleClick}
        className="absolute top-4 right-4 z-50 bg-royalGold hover:bg-yellow-500 text-royalBlue font-bold shadow-lg"
      >
        <Edit className="h-4 w-4 mr-2" />
        Edit {sectionName}
      </Button>

      {showEditor && (
        <SimpleOverlayEditor
          sectionType={sectionType}
          sectionId={sectionId}
          onClose={() => setShowEditor(false)}
        />
      )}
    </>
  );
}
