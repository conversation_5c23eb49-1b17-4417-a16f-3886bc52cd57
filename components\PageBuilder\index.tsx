'use client';

import dynamic from 'next/dynamic';
import { SectionProps } from './types';
import EditableSectionWrapper from './EditableSectionWrapper';

// Dynamically import section components
const Hero = dynamic(() => import('./sections/Hero'));
const TextSection = dynamic(() => import('./sections/TextSection'));
const ImageGallery = dynamic(() => import('./sections/ImageGallery'));
const FeaturedContent = dynamic(() => import('./sections/FeaturedContent'));
const ContactForm = dynamic(() => import('./sections/ContactForm'));

// Map section types to components
const sectionComponents: Record<string, React.ComponentType<any>> = {
  hero: Hero,
  textSection: TextSection,
  imageGallery: ImageGallery,
  featuredContent: FeaturedContent,
  contactForm: ContactForm,
};

interface PageBuilderProps {
  sections: any[];
  pageId?: string;
}

export default function PageBuilder({ sections, pageId }: PageBuilderProps) {
  if (!sections || sections.length === 0) {
    return (
      <div className="py-20 text-center">
        <h2 className="text-2xl text-gray-500">No content sections found</h2>
      </div>
    );
  }

  return (
    <div className="page-builder">
      {sections.map((section, index) => {
        const SectionComponent = sectionComponents[section._type];

        if (!SectionComponent) {
          console.warn(`Section component not found for type: ${section._type}`);
          return null;
        }

        return (
          <section key={`${section._type}-${index}`} className="section-wrapper">
            <EditableSectionWrapper section={section} index={index}>
              <SectionComponent {...section} />
            </EditableSectionWrapper>
          </section>
        );
      })}
    </div>
  );
}
