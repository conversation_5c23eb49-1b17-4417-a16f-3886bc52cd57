'use client';

import { useState } from 'react';

export default function TestNewsPage() {
  const [galleryImages, setGalleryImages] = useState<Array<{
    file: File;
    preview: string;
    alt: string;
    caption: string;
  }>>([]);

  const handleGalleryImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      
      files.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target) {
            const newGalleryImage = {
              file,
              preview: event.target.result as string,
              alt: '',
              caption: ''
            };
            setGalleryImages(prev => [...prev, newGalleryImage]);
          }
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const updateGalleryImage = (index: number, field: 'alt' | 'caption', value: string) => {
    setGalleryImages(prev => prev.map((img, i) => 
      i === index ? { ...img, [field]: value } : img
    ));
  };

  const removeGalleryImage = (index: number) => {
    setGalleryImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('title', 'Test News Article');
    formData.append('excerpt', 'This is a test article with multiple images');
    formData.append('content', 'Test content for the news article');
    formData.append('category', 'Test Category');
    formData.append('date', new Date().toISOString().split('T')[0]);
    formData.append('status', 'draft');

    // Add gallery images
    galleryImages.forEach((galleryImage, index) => {
      formData.append(`galleryImage_${index}`, galleryImage.file);
      formData.append(`galleryAlt_${index}`, galleryImage.alt);
      formData.append(`galleryCaption_${index}`, galleryImage.caption);
    });
    formData.append('galleryCount', galleryImages.length.toString());

    try {
      const response = await fetch('/api/news', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      
      if (response.ok) {
        alert('News article created successfully with ' + galleryImages.length + ' gallery images!');
        console.log('Success:', result);
      } else {
        alert('Error: ' + result.error);
        console.error('Error:', result);
      }
    } catch (error) {
      alert('Network error: ' + error);
      console.error('Network error:', error);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Test News Creation with Multiple Images</h1>
      
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '20px' }}>
          <h3>Gallery Images</h3>
          <p>Add multiple images to display at the bottom of the article</p>
          
          <input
            type="file"
            accept="image/*"
            multiple
            onChange={handleGalleryImageChange}
            style={{ marginBottom: '10px' }}
          />
        </div>

        {galleryImages.length > 0 && (
          <div style={{ marginBottom: '20px' }}>
            <h4>Gallery Images ({galleryImages.length})</h4>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
              {galleryImages.map((galleryImage, index) => (
                <div key={index} style={{ border: '1px solid #ccc', borderRadius: '8px', padding: '15px' }}>
                  <div style={{ position: 'relative', marginBottom: '10px' }}>
                    <img
                      src={galleryImage.preview}
                      alt={galleryImage.alt || `Gallery image ${index + 1}`}
                      style={{ width: '100%', height: '150px', objectFit: 'cover', borderRadius: '4px' }}
                    />
                    <button
                      type="button"
                      onClick={() => removeGalleryImage(index)}
                      style={{
                        position: 'absolute',
                        top: '5px',
                        right: '5px',
                        background: 'red',
                        color: 'white',
                        border: 'none',
                        borderRadius: '50%',
                        width: '25px',
                        height: '25px',
                        cursor: 'pointer'
                      }}
                    >
                      ×
                    </button>
                  </div>
                  <div style={{ marginBottom: '10px' }}>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                      Alt Text *
                    </label>
                    <input
                      type="text"
                      placeholder="Describe the image for accessibility"
                      value={galleryImage.alt}
                      onChange={(e) => updateGalleryImage(index, 'alt', e.target.value)}
                      required
                      style={{ width: '100%', padding: '5px', border: '1px solid #ccc', borderRadius: '4px' }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                      Caption (Optional)
                    </label>
                    <input
                      type="text"
                      placeholder="Image caption"
                      value={galleryImage.caption}
                      onChange={(e) => updateGalleryImage(index, 'caption', e.target.value)}
                      style={{ width: '100%', padding: '5px', border: '1px solid #ccc', borderRadius: '4px' }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <button
          type="submit"
          style={{
            background: '#0070f3',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Create Test News Article
        </button>
      </form>
    </div>
  );
}
