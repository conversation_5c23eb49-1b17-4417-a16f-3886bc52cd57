'use client';

import { createClient } from '@sanity/client';
import { apiVersion, dataset, projectId, token } from '@/sanity/env';
import { nanoid } from 'nanoid';

// Create a Sanity client
const client = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false,
});

/**
 * Recursively adds _key properties to arrays in a Sanity document
 * @param obj The object to process
 * @returns The processed object with _key properties added to arrays
 */
function addKeysToArrays(obj: any): any {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  // If it's an array, process each item and ensure they have _key properties
  if (Array.isArray(obj)) {
    return obj.map((item) => {
      // Skip primitive values
      if (!item || typeof item !== 'object') {
        return item;
      }
      
      // Add a _key if it doesn't exist
      const processedItem = addKeysToArrays(item);
      if (!processedItem._key) {
        processedItem._key = nanoid(12);
      }
      
      return processedItem;
    });
  }

  // If it's an object, process each property
  const result = { ...obj };
  for (const key in result) {
    if (Object.prototype.hasOwnProperty.call(result, key)) {
      result[key] = addKeysToArrays(result[key]);
    }
  }

  return result;
}

/**
 * Fixes missing _key properties in a Sanity document
 * @param documentId The ID of the document to fix
 * @returns A promise that resolves when the document has been fixed
 */
export async function fixMissingKeys(documentId: string): Promise<void> {
  try {
    // Fetch the document
    const document = await client.getDocument(documentId);
    if (!document) {
      throw new Error(`Document with ID ${documentId} not found`);
    }

    // Process the document to add missing _key properties
    const processedDocument = addKeysToArrays(document);

    // Update the document
    await client.createOrReplace(processedDocument);

    console.log(`Fixed missing keys in document ${documentId}`);
  } catch (error) {
    console.error(`Error fixing missing keys in document ${documentId}:`, error);
    throw error;
  }
}

/**
 * Fixes missing _key properties in all documents of a specific type
 * @param documentType The type of documents to fix
 * @returns A promise that resolves when all documents have been fixed
 */
export async function fixMissingKeysForType(documentType: string): Promise<void> {
  try {
    // Fetch all documents of the specified type
    const documents = await client.fetch(`*[_type == $type]._id`, { type: documentType });

    // Process each document
    for (const documentId of documents) {
      await fixMissingKeys(documentId);
    }

    console.log(`Fixed missing keys in ${documents.length} documents of type ${documentType}`);
  } catch (error) {
    console.error(`Error fixing missing keys for type ${documentType}:`, error);
    throw error;
  }
}
