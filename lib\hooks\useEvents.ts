'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getEvents, getEventById, getCountdownTargetEvent } from '@/lib/sanity';
import { useEventStore } from '@/lib/stores/eventStore';
import { toast } from 'sonner';
import { logError } from '@/lib/errorHandling';

// Query keys
export const eventKeys = {
  all: ['events'] as const,
  lists: () => [...eventKeys.all, 'list'] as const,
  list: (filters: any) => [...eventKeys.lists(), { filters }] as const,
  details: () => [...eventKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventKeys.details(), id] as const,
  countdown: () => [...eventKeys.all, 'countdown'] as const,
};

// Hook for fetching all events
export function useEventsQuery() {
  const { fetchEvents } = useEventStore();

  return useQuery({
    queryKey: eventKeys.lists(),
    queryFn: fetchEvents,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching a single event
export function useEventQuery(id: string) {
  const { fetchEventById } = useEventStore();

  return useQuery({
    queryKey: eventKeys.detail(id),
    queryFn: () => fetchEventById(id),
    enabled: !!id,
  });
}

// Hook for fetching the countdown event
export function useCountdownEventQuery() {
  return useQuery({
    queryKey: eventKeys.countdown(),
    queryFn: getCountdownTargetEvent,
  });
}

// Hook for deleting an event
export function useDeleteEventMutation() {
  const queryClient = useQueryClient();
  const { deleteEvent } = useEventStore();

  return useMutation({
    mutationFn: deleteEvent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      toast.success('Event deleted successfully');
    },
    onError: (error) => {
      logError(error, 'deleteEventMutation');
      toast.error('Failed to delete event');
    },
  });
}

// Hook for setting an event as the countdown target
export function useSetCountdownTargetMutation() {
  const queryClient = useQueryClient();
  const { setCountdownTarget } = useEventStore();

  return useMutation({
    mutationFn: setCountdownTarget,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.countdown() });
      toast.success('Countdown target updated successfully');
    },
    onError: (error) => {
      logError(error, 'setCountdownTargetMutation');
      toast.error('Failed to update countdown target');
    },
  });
}

// Hook for creating a new event
export function useCreateEventMutation() {
  const queryClient = useQueryClient();
  const { createEvent } = useEventStore();

  return useMutation({
    mutationFn: createEvent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      toast.success('Event created successfully');
    },
    onError: (error) => {
      logError(error, 'createEventMutation');
      toast.error('Failed to create event');
    },
  });
}

// Hook for updating an event
export function useUpdateEventMutation() {
  const queryClient = useQueryClient();
  const { updateEvent } = useEventStore();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateEvent(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: eventKeys.countdown() });
      toast.success('Event updated successfully');
    },
    onError: (error) => {
      logError(error, 'updateEventMutation');
      toast.error('Failed to update event: ' + (error instanceof Error ? error.message : 'Unknown error'));
    },
  });
}
