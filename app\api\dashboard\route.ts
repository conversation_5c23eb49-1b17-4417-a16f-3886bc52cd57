import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';
import { readClient } from '@/lib/sanity.client';

// GET /api/dashboard - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to access dashboard data' },
        { status: 401 }
      );
    }

    console.log('Fetching dashboard statistics...');

    // Fetch counts from Sanity
    const client = readClient;

    // Fetch total news count
    const newsCount = await client.fetch(
      `count(*[_type == "news"])`,
      {},
      { cache: 'no-store' }
    );

    // Fetch total gallery items count
    const galleryCount = await client.fetch(
      `count(*[_type == "gallery"])`,
      {},
      { cache: 'no-store' }
    );

    // Fetch total RSVP submissions count
    const rsvpCount = await client.fetch(
      `count(*[_type == "rsvp"])`,
      {},
      { cache: 'no-store' }
    );

    // Fetch upcoming events count (events with date in the future)
    const upcomingEventsCount = await client.fetch(
      `count(*[_type == "event" && dateTime(date) > dateTime(now())])`,
      {},
      { cache: 'no-store' }
    );

    // Fetch total users count
    const usersCount = await client.fetch(
      `count(*[_type == "user"])`,
      {},
      { cache: 'no-store' }
    );

    // Fetch recent news articles
    const recentNews = await client.fetch(
      `*[_type == "news"] | order(publishedAt desc)[0...3] {
        _id,
        title,
        slug,
        publishedAt,
        status
      }`,
      {},
      { cache: 'no-store' }
    );

    // Fetch recent gallery items
    const recentGallery = await client.fetch(
      `*[_type == "gallery"] | order(_createdAt desc)[0...3] {
        _id,
        title,
        slug,
        _createdAt
      }`,
      {},
      { cache: 'no-store' }
    );

    // Fetch recent RSVP submissions
    const recentRsvp = await client.fetch(
      `*[_type == "rsvp"] | order(submittedAt desc)[0...3] {
        _id,
        firstName,
        lastName,
        email,
        events,
        submittedAt
      }`,
      {},
      { cache: 'no-store' }
    );

    // Fetch recent activity (a combination of recent content updates)
    const recentActivity = await client.fetch(
      `*[_type in ["news", "gallery", "rsvp", "event", "user"]] | order(_updatedAt desc)[0...5] {
        _id,
        _type,
        _updatedAt,
        title,
        firstName,
        lastName,
        name
      }`,
      {},
      { cache: 'no-store' }
    );

    // Format the recent activity data
    const formattedActivity = recentActivity.map((item: any) => {
      let title = '';
      let type = '';

      switch (item._type) {
        case 'news':
          title = item.title || 'Untitled article';
          type = 'News article';
          break;
        case 'gallery':
          title = item.title || 'Untitled gallery';
          type = 'Gallery item';
          break;
        case 'rsvp':
          title = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Anonymous';
          type = 'RSVP submission';
          break;
        case 'event':
          title = item.title || 'Untitled event';
          type = 'Event';
          break;
        case 'user':
          title = item.name || 'Unknown user';
          type = 'User';
          break;
        default:
          title = 'Unknown item';
          type = 'Content';
      }

      return {
        id: item._id,
        type: item._type,
        title,
        description: `${type} was updated`,
        updatedAt: item._updatedAt
      };
    });

    // Return the dashboard statistics
    return NextResponse.json({
      success: true,
      stats: {
        totalNews: newsCount,
        totalGalleryItems: galleryCount,
        totalRsvps: rsvpCount,
        upcomingEvents: upcomingEventsCount,
        totalUsers: usersCount,
        // Placeholder for visitor count
        totalVisitors: 0
      },
      recentNews,
      recentGallery,
      recentRsvp,
      recentActivity: formattedActivity
    });
  } catch (error) {
    console.error('Error fetching dashboard statistics:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch dashboard statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
