import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate email
    if (!email || typeof email !== 'string' || !email.includes('@')) {
      return NextResponse.json(
        { success: false, message: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Get Sanity client
    const client = getWriteClient();

    // Check if the email already exists
    const existingSubscriber = await client.fetch(
      `*[_type == "newsletterSubscriber" && email == $email][0]`,
      { email }
    );

    if (existingSubscriber) {
      return NextResponse.json(
        { success: true, message: 'You are already subscribed' },
        { status: 200 }
      );
    }

    // Create a new subscriber
    const subscriber = await client.create({
      _type: 'newsletterSubscriber',
      email,
      subscriptionDate: new Date().toISOString(),
      status: 'active',
      source: 'website'
    });

    return NextResponse.json(
      { 
        success: true, 
        message: 'Successfully subscribed to the newsletter',
        subscriber
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to subscribe to the newsletter',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
