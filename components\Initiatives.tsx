'use client';
import Link from 'next/link';
import { motion } from 'framer-motion';
import ScrollReveal from '@/components/ScrollReveal';
import FloatingParticles from '@/components/FloatingParticles';


export default function Initiatives() {
  const initiatives = [
    {
      title: 'Uplift Ghana',
      description:
        'A Royal Initiative for National Prosperity, Inclusion, and Innovation, uniting public and private sectors to build a thriving, future-ready Ghana.',
      icon: (
        <svg className="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"
          />
        </svg>
      ),
      image: '/Website Images/Flags Ghana.png'
    },
    {
      title: 'Education Fund',
      description:
        'Supporting educational opportunities for youth across the Eastern Region through scholarships and school infrastructure development.',
      icon: (
        <svg className="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
      ),
      image: '/Website Images/Education.jpg'
    },
    {
      title: 'Healthcare Access',
      description:
        'Improving healthcare facilities and services in rural communities through mobile clinics and medical training programs.',
      icon: (
        <svg className="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
      ),
      image: '/Website Images/Medical.jpg'
    },
    {
      title: 'Cultural Preservation',
      description:
        'Documenting and preserving traditional knowledge, arts, and practices through digital archives and community festivals.',
      icon: (
        <svg className="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      image: '/Website Images/kente-1.png'
    },
    {
      title: 'Sustainable Development',
      description:
        'Promoting eco-friendly practices and renewable energy solutions to protect our natural resources for future generations.',
      icon: (
        <svg className="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
          />
        </svg>
      ),
      image: '/Website Images/shu-Ghana-Umbrella-Rock_243946015.png'
    },
  ];

  return (
    <section id="initiatives" className="py-20 royal-gradient relative overflow-hidden">
      <FloatingParticles
        count={25}
        colors={['#D4AF37', '#FFFFFF', '#AD8A56']}
        minSize={3}
        maxSize={8}
      />

      <div className="container mx-auto px-4 relative z-10">
        <ScrollReveal animation="fadeInDown">
          <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-16 section-title">
            Royal Initiatives
          </h2>
        </ScrollReveal>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
          {initiatives.map((initiative, index) => (
            <ScrollReveal key={index} animation="fadeInUp" delay={0.2 * index}>
              <motion.div
                className="bg-ivory rounded-lg overflow-hidden shadow-lg"
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px rgba(0, 0, 0, 0.2)",
                  transition: { duration: 0.3 }
                }}
              >
                <div className="h-48 relative overflow-hidden">
                  <motion.img
                    src={initiative.image}
                    alt={initiative.title}
                    className="w-full h-full object-cover"
                    initial={{ scale: 1 }}
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.5 }}
                  />
                  <div className="absolute inset-0 bg-royalBlue/30"></div>
                </div>
                <div className="p-6 relative">
                  <motion.div
                    className="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto -mt-14 mb-4 border-4 border-ivory relative z-10 shadow-lg"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 1 }}
                  >
                    {initiative.icon}
                  </motion.div>
                  <motion.h3
                    className="text-xl font-bold text-royalBlue text-center mb-4"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 + (0.1 * index) }}
                  >
                    {initiative.title}
                  </motion.h3>
                  <motion.p
                    className="text-charcoal text-center mb-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 + (0.1 * index) }}
                  >
                    {initiative.description}
                  </motion.p>
                  <motion.div
                    className="text-center"
                    whileHover={{ scale: 1.05 }}
                  >
                    {initiative.title === 'Uplift Ghana' ? (
                      <Link
                        href="/uplift-ghana"
                        className="text-royalBlue font-medium hover:text-royalGold transition-colors"
                      >
                        Learn More →
                      </Link>
                    ) : (
                      <span className="text-royalBlue font-medium">
                        More Info Coming Soon
                      </span>
                    )}
                  </motion.div>
                </div>
              </motion.div>
            </ScrollReveal>
          ))}
        </div>

        <ScrollReveal animation="fadeInUp" delay={0.8}>
          <div className="mt-16 text-center">
            <motion.p
              className="text-white text-lg mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              Interested in partnering with us on these initiatives?
            </motion.p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="#contact"
                className="royal-button inline-block bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
              >
                Partnership Opportunities
              </Link>
            </motion.div>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
}