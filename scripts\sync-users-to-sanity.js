// This script syncs all users from the local JSON file to Sanity CMS
const fs = require('fs');
const path = require('path');
const { createClient } = require('@sanity/client');
const bcrypt = require('bcryptjs');

// Load environment variables
require('dotenv').config();

// Create Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
  apiVersion: '2023-05-03'
});

// Path to users.json file
const usersFilePath = path.join(__dirname, '../data/users.json');

async function syncUsersToSanity() {
  try {
    // Read users from JSON file
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    const users = JSON.parse(usersData);

    console.log(`Found ${users.length} users in local JSON file`);

    // Get existing users from Sanity
    const existingUsers = await client.fetch(`
      *[_type == "adminUser"] {
        _id,
        email
      }
    `);

    console.log(`Found ${existingUsers.length} users in Sanity`);

    // Create a map of existing users by email for quick lookup
    const existingUsersByEmail = {};
    existingUsers.forEach(user => {
      existingUsersByEmail[user.email] = user._id;
    });

    // Process each user
    for (const user of users) {
      const existingUserId = existingUsersByEmail[user.email];

      if (existingUserId) {
        console.log(`Updating existing user: ${user.name} (${user.email})`);
        
        // Update existing user
        await client
          .patch(existingUserId)
          .set({
            name: user.name,
            role: user.role,
            hashedPassword: user.password, // Already hashed in the JSON file
            updatedAt: new Date().toISOString(),
            lastLogin: user.lastLogin || null,
            isActive: true
          })
          .commit();
          
        console.log(`Updated user: ${user.name}`);
      } else {
        console.log(`Creating new user: ${user.name} (${user.email})`);
        
        // Generate a username from email if not present
        const username = user.username || user.email.split('@')[0];
        
        // Create new user in Sanity
        const newUser = {
          _type: 'adminUser',
          _id: user.id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          username,
          email: user.email,
          name: user.name,
          role: user.role,
          hashedPassword: user.password, // Already hashed in the JSON file
          createdAt: user.createdAt || new Date().toISOString(),
          updatedAt: user.updatedAt || new Date().toISOString(),
          lastLogin: user.lastLogin || null,
          isActive: true
        };
        
        const result = await client.createOrReplace(newUser);
        console.log(`Created user: ${user.name} with ID: ${result._id}`);
      }
    }

    console.log('User synchronization completed successfully!');
  } catch (error) {
    console.error('Error syncing users to Sanity:', error);
  }
}

// Run the sync function
syncUsersToSanity();
