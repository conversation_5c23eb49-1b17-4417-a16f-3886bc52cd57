import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// POST /api/gallery/from-article - Add a news article image to the gallery
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to add images to the gallery' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Parse the request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, message: 'Title is required' },
        { status: 400 }
      );
    }

    if (!body.mainImage || !body.mainImage.asset) {
      return NextResponse.json(
        { success: false, message: 'Image is required' },
        { status: 400 }
      );
    }

    // Check if a gallery item already exists for this article
    if (body.fromArticle && body.fromArticle._ref) {
      const existingItem = await client.fetch(
        `*[_type == "gallery" && fromArticle._ref == $articleId][0]._id`,
        { articleId: body.fromArticle._ref }
      );

      if (existingItem) {
        console.log(`Gallery item already exists for article ${body.fromArticle._ref}, updating it`);
        
        // Update the existing gallery item
        const updatedItem = await client
          .patch(existingItem)
          .set({
            title: body.title,
            description: body.description || '',
            category: body.category,
            images: [
              {
                _type: 'object',
                image: body.mainImage,
                alt: body.title,
                caption: body.description || ''
              }
            ],
            updatedAt: new Date().toISOString()
          })
          .commit();

        return NextResponse.json({
          success: true,
          message: 'Gallery item updated successfully',
          item: updatedItem
        });
      }
    }

    // Generate a slug from the title
    const slug = body.title
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    // Create the gallery document
    const galleryDoc = {
      _type: 'gallery',
      title: body.title,
      slug: {
        _type: 'slug',
        current: `${slug}-${Date.now()}`
      },
      description: body.description || '',
      // Create an images array with a single image (matching the schema)
      images: [
        {
          _type: 'object',
          image: body.mainImage,
          alt: body.title,
          caption: body.description || ''
        }
      ],
      // Add the category reference if provided
      category: body.category,
      // Add reference to the source article
      fromArticle: body.fromArticle,
      publishedAt: new Date().toISOString(),
      // Set default values for other fields
      featured: false,
      displayStyle: 'grid',
      backgroundStyle: 'none',
      order: 0
    };

    console.log('Creating gallery item from article image');

    // Create the document in Sanity
    const document = await client.create(galleryDoc);

    return NextResponse.json({
      success: true,
      message: 'Image added to gallery successfully',
      item: document
    });
  } catch (error) {
    console.error('Error adding article image to gallery:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to add image to gallery',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
