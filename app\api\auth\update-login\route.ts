import { NextRequest, NextResponse } from 'next/server';
import { updateLastLogin } from '@/lib/users';

// POST /api/auth/update-login - Update user's last login time
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { userId } = body;

    // Validate required fields
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // For built-in admin/super_admin users, just return success
    // This avoids the need to update the JSON file
    if (userId === '1' || userId === '2') {
      return NextResponse.json({
        success: true,
        message: 'Last login time updated successfully'
      });
    }

    // For other users, update the last login time
    try {
      updateLastLogin(userId);
    } catch (err) {
      console.warn('Could not update last login time, but continuing:', err);
      // Continue even if this fails
    }

    return NextResponse.json({
      success: true,
      message: 'Last login time updated successfully'
    });
  } catch (error) {
    console.error('Error updating last login time:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred' },
      { status: 500 }
    );
  }
}
