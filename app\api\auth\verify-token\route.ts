import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/tokens';

// POST /api/auth/verify-token - Verify a token
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { token, type } = body;
    
    // Validate required fields
    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Token is required' },
        { status: 400 }
      );
    }
    
    try {
      // Verify the token
      const tokenData = verifyToken(token);
      
      // Check if token type matches (if type is specified)
      if (type && tokenData.type !== type) {
        return NextResponse.json(
          { success: false, message: 'Invalid token type' },
          { status: 400 }
        );
      }
      
      // Return token data (without sensitive information)
      return NextResponse.json({
        success: true,
        type: tokenData.type,
        email: tokenData.email,
        userId: tokenData.userId,
        // Don't include role or other sensitive data
      });
    } catch (tokenError) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error verifying token:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to verify token' },
      { status: 500 }
    );
  }
}
