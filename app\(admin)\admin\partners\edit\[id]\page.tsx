'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import axios from 'axios';
import { toast } from 'sonner';
import { ArrowLeft, Save, Trash2, Upload } from 'lucide-react';
// Using the existing admin layout, no need for separate header and container
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { urlFor } from '@/lib/sanity.client';

// Define the partner type
interface Partner {
  _id: string;
  name: string;
  slug: { current: string };
  description: string;
  website?: string;
  logo?: any;
  partnershipType: string;
  featured: boolean;
  order: number;
  startDate?: string;
  active: boolean;
}

export default function EditPartnerPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  // Use React.use to unwrap params (fixing the warning)
  const unwrappedParams = React.use(params as any) as { id: string };
  const id = unwrappedParams.id;

  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Fetch partner data on component mount
  useEffect(() => {
    const fetchPartner = async () => {
      try {
        console.log(`Fetching partner with ID: ${id}`);
        const response = await axios.get(`/api/partners/${id}`);
        console.log('API Response:', response.data);

        if (response.data.success && response.data.partner) {
          console.log('Partner found:', response.data.partner);
          setPartner(response.data.partner);
        } else {
          console.error('Failed to fetch partner details:', response.data.message || 'Unknown error');
          toast.error(response.data.message || 'Failed to fetch partner details');
        }
      } catch (error: any) {
        console.error('Error fetching partner:', error);

        // Log more detailed error information
        if (error.response) {
          console.error('Error response:', error.response.data);
          console.error('Error status:', error.response.status);

          // Handle 404 specifically
          if (error.response.status === 404) {
            toast.error('Partner not found. It may have been deleted.');
          } else {
            toast.error(`Error: ${error.response.data.message || 'An error occurred'}`);
          }
        } else if (error.request) {
          console.error('Error request:', error.request);
          toast.error('Network error. Please check your connection.');
        } else {
          toast.error(`Error: ${error.message || 'An unknown error occurred'}`);
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchPartner();
    } else {
      console.error('No ID provided');
      toast.error('No partner ID provided');
      setLoading(false);
    }
  }, [id]);

  // Function to handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!partner) return;

    setSubmitting(true);

    try {
      // First, upload the logo if provided
      let logoRef = partner.logo;
      if (logoFile) {
        const formData = new FormData();
        formData.append('file', logoFile);
        formData.append('type', 'image');

        const uploadResponse = await axios.post('/api/upload', formData);
        if (uploadResponse.data.success) {
          // Create a proper Sanity image reference with required alt text
          logoRef = {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: uploadResponse.data.asset._id
            },
            alt: partner.name + ' logo'
          };
          console.log('Logo reference created:', logoRef);
        } else {
          toast.error('Failed to upload logo');
          setSubmitting(false);
          return;
        }
      }

      // Update the partner
      const response = await axios.patch(`/api/partners/${id}`, {
        ...partner,
        logo: logoRef
      });

      if (response.data.success) {
        toast.success('Partner updated successfully');
        // Update the partner state with the new data
        setPartner(response.data.partner);
        setLogoFile(null);
      } else {
        toast.error(response.data.message || 'Failed to update partner');
      }
    } catch (error) {
      console.error('Error updating partner:', error);
      toast.error('An error occurred while updating the partner');
    } finally {
      setSubmitting(false);
    }
  };

  // Function to handle partner deletion
  const handleDeletePartner = async () => {
    try {
      const response = await axios.delete(`/api/partners/${id}`);
      if (response.data.success) {
        toast.success('Partner deleted successfully');
        router.push('/admin/partners');
      } else {
        toast.error(response.data.message || 'Failed to delete partner');
      }
    } catch (error) {
      console.error('Error deleting partner:', error);
      toast.error('An error occurred while deleting the partner');
    }
  };

  // Function to handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!partner) return;

    const { name, value } = e.target;
    setPartner({
      ...partner,
      [name]: value
    });
  };

  // Function to handle select changes
  const handleSelectChange = (name: string, value: string) => {
    if (!partner) return;

    setPartner({
      ...partner,
      [name]: value
    });
  };

  // Function to handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    if (!partner) return;

    setPartner({
      ...partner,
      [name]: checked
    });
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow p-6 relative">
        <h1 className="text-2xl font-bold text-royalBlue mb-6">{`Edit Partner: ${partner?.name || 'Loading...'}`}</h1>
        <div className="flex justify-between items-center mb-6">
          <Button variant="outline" onClick={() => router.push('/admin/partners')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Partners
          </Button>
          <div className="flex space-x-2">
            <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)} disabled={!partner || loading}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
            <Button onClick={handleSubmit} disabled={submitting || loading || !partner}>
              <Save className="mr-2 h-4 w-4" />
              {submitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue mb-4"></div>
              <p className="text-gray-500">Loading partner data...</p>
            </div>
          </div>
        ) : partner ? (
          <form onSubmit={handleSubmit}>
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-6">
                  <div className="grid gap-2">
                    <Label htmlFor="name">Partner Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      value={partner.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={partner.description}
                      onChange={handleInputChange}
                      rows={4}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="website">Website URL</Label>
                    <Input
                      id="website"
                      name="website"
                      type="url"
                      value={partner.website || ''}
                      onChange={handleInputChange}
                      placeholder="https://example.com"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="logo">Logo</Label>
                    <div className="relative h-40 w-full mb-4 bg-gray-100 rounded-md overflow-hidden">
                      {logoPreview ? (
                        // Show the new logo preview if a file is selected
                        <Image
                          src={logoPreview}
                          alt={partner.name + " (new logo)"}
                          fill
                          className="object-contain"
                        />
                      ) : partner.logo && urlFor(partner.logo).url() ? (
                        // Show the existing logo if available
                        <Image
                          src={urlFor(partner.logo).url() || ''}
                          alt={partner.name}
                          fill
                          className="object-contain"
                          unoptimized={true}
                        />
                      ) : (
                        // Show "No logo" message if no logo is available
                        <div className="flex items-center justify-center h-full">
                          <p className="text-gray-400">No logo</p>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-4">
                      <Input
                        id="logo"
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0] || null;
                          setLogoFile(file);

                          // Create preview URL for the selected image
                          if (file) {
                            const reader = new FileReader();
                            reader.onloadend = () => {
                              setLogoPreview(reader.result as string);
                            };
                            reader.readAsDataURL(file);
                          } else {
                            setLogoPreview(null);
                          }
                        }}
                      />
                      {logoFile && (
                        <Button type="button" onClick={() => {
                          setLogoFile(null);
                          setLogoPreview(null);
                        }} variant="outline">
                          Cancel
                        </Button>
                      )}

                    </div>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="partnershipType">Partnership Type *</Label>
                    <Select
                      value={partner.partnershipType}
                      onValueChange={(value) => handleSelectChange('partnershipType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select partnership type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="corporate">Corporate</SelectItem>
                        <SelectItem value="ngo">NGO</SelectItem>
                        <SelectItem value="government">Government</SelectItem>
                        <SelectItem value="educational">Educational</SelectItem>
                        <SelectItem value="media">Media</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="order">Display Order</Label>
                    <Input
                      id="order"
                      name="order"
                      type="number"
                      value={partner.order.toString()}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 0;
                        setPartner({
                          ...partner,
                          order: value
                        });
                      }}
                    />
                    <p className="text-sm text-gray-500">Partners with lower numbers will be displayed first</p>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="startDate">Partnership Start Date</Label>
                    <Input
                      id="startDate"
                      name="startDate"
                      type="date"
                      value={partner.startDate || ''}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="featured"
                      checked={partner.featured}
                      onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                    />
                    <Label htmlFor="featured">Featured Partner (Display prominently on the homepage)</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="active"
                      checked={partner.active}
                      onCheckedChange={(checked) => handleSwitchChange('active', checked)}
                    />
                    <Label htmlFor="active">Active Partnership (Show on the website)</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </form>
        ) : (
          <div className="text-center py-12 bg-white rounded-lg shadow-md">
            <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">Partner Not Found</h3>
            <p className="text-gray-600 mb-6">The partner you're looking for could not be found or may have been deleted.</p>
            <Button onClick={() => router.push('/admin/partners')} className="bg-royalBlue hover:bg-royalBlue/90">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Partners
            </Button>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Partner</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete {partner?.name}? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeletePartner}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
