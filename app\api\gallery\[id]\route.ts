import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/gallery/[id] - Get a specific gallery item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // Get the Sanity client
    const client = getWriteClient();

    // Get the gallery item
    const galleryItem = await client.fetch(
      `*[_type == "gallery" && _id == $id][0]`,
      { id }
    );

    if (!galleryItem) {
      return NextResponse.json(
        { success: false, message: 'Gallery item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      item: galleryItem
    });
  } catch (error) {
    console.error('Error fetching gallery item:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch gallery item',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/gallery/[id] - Update a gallery item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update gallery items' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Parse the request body
    const body = await request.json();

    // Get the Sanity client
    const client = getWriteClient();

    // Check if the gallery item exists
    const existingItem = await client.fetch(
      `*[_type == "gallery" && _id == $id][0]`,
      { id }
    );

    if (!existingItem) {
      return NextResponse.json(
        { success: false, message: 'Gallery item not found' },
        { status: 404 }
      );
    }

    // Update the gallery item
    const updatedItem = await client.patch(id)
      .set({
        title: body.title,
        description: body.description,
        category: body.category ? {
          _type: 'reference',
          _ref: body.category
        } : undefined,
        updatedAt: new Date().toISOString(),
      })
      .commit();

    // Log the updated item for debugging
    console.log('Updated gallery item:', {
      id: updatedItem._id,
      title: updatedItem.title,
      category: updatedItem.category ? updatedItem.category._ref : 'none'
    });

    return NextResponse.json({
      success: true,
      message: 'Gallery item updated successfully',
      item: updatedItem
    });
  } catch (error) {
    console.error('Error updating gallery item:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update gallery item',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/gallery/[id] - Delete a gallery item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete gallery items' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Get the Sanity client
    const client = getWriteClient();

    // Check if the gallery item exists
    const existingItem = await client.fetch(
      `*[_type == "gallery" && _id == $id][0]`,
      { id }
    );

    if (!existingItem) {
      return NextResponse.json(
        { success: false, message: 'Gallery item not found' },
        { status: 404 }
      );
    }

    // Delete the gallery item
    await client.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Gallery item deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting gallery item:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete gallery item',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
