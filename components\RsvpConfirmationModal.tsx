'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X, Mail, Phone } from 'lucide-react';

interface RsvpConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  firstName: string;
  reminderPreference: string[];
}

export default function RsvpConfirmationModal({
  isOpen,
  onClose,
  firstName,
  reminderPreference
}: RsvpConfirmationModalProps) {
  const [isClosing, setIsClosing] = useState(false);

  // Close the modal after 8 seconds automatically
  useEffect(() => {
    if (isOpen) {
      console.log('Modal opened with firstName:', firstName, 'and reminderPreference:', reminderPreference);
      const timer = setTimeout(() => {
        handleClose();
      }, 8000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, firstName, reminderPreference]);

  // Handle the close animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };

  // Determine the contact method message based on reminder preferences
  const getContactMethodMessage = () => {
    if (reminderPreference.includes('email') && reminderPreference.includes('sms')) {
      return 'via email and SMS';
    } else if (reminderPreference.includes('email')) {
      return 'via email';
    } else if (reminderPreference.includes('sms')) {
      return 'via SMS';
    } else {
      return 'soon';
    }
  };

  // Get the appropriate icon based on reminder preferences
  const getContactIcon = () => {
    if (reminderPreference.includes('email') && reminderPreference.includes('sms')) {
      return (
        <div className="flex space-x-2">
          <Mail className="h-5 w-5 text-royalGold" />
          <Phone className="h-5 w-5 text-royalGold" />
        </div>
      );
    } else if (reminderPreference.includes('email')) {
      return <Mail className="h-5 w-5 text-royalGold" />;
    } else if (reminderPreference.includes('sms')) {
      return <Phone className="h-5 w-5 text-royalGold" />;
    } else {
      return null;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6 border border-royalGold/30"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: isClosing ? 0.9 : 1, opacity: isClosing ? 0 : 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            {/* Close button */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Content */}
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>

              <h3 className="text-2xl font-bold text-royalBlue mb-2">
                Thank You, {firstName}!
              </h3>

              <p className="text-lg text-gray-700 mb-4">
                Your RSVP has been successfully submitted.
              </p>

              <div className="bg-ivory p-4 rounded-lg mb-4">
                <p className="text-charcoal flex items-center justify-center">
                  <span className="mr-2">We will contact you {getContactMethodMessage()}</span>
                  {getContactIcon()}
                </p>
              </div>

              <p className="text-sm text-gray-500">
                If you have any questions, please feel free to contact us.
              </p>

              <button
                onClick={handleClose}
                className="mt-6 royal-button bg-royalGold text-royalBlue font-bold py-2 px-6 rounded-full hover:bg-yellow-500 transition-colors shadow-md"
              >
                Close
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
