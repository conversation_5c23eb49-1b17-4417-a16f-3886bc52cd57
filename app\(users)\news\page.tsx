'use client';

import Link from 'next/link';
import Image from 'next/image';
import { getNews, getCategories } from '@/lib/sanity';
import { urlFor } from '@/lib/sanity';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { formatDate } from '@/lib/utils';
import { useState, useEffect } from 'react';

// Define types for news articles
interface NewsArticle {
  _id: string;
  title: string;
  slug: { current: string };
  excerpt?: string;
  publishedAt?: string;
  mainImage?: any;
  category?: {
    _id: string;
    title: string;
    slug: { current: string };
  };
  featured?: boolean;
  status?: string;
}

interface Category {
  _id: string;
  title: string;
  slug: { current: string };
}

export default function NewsPage() {
  const [newsArticles, setNewsArticles] = useState<NewsArticle[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<NewsArticle[] | null>(null);

  // Fetch news articles and categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching news articles from client side...');

        // Add a cache-busting parameter to force a fresh fetch
        const timestamp = new Date().getTime();
        const articles = await getNews();
        const cats = await getCategories();

        console.log(`Fetched ${articles.length} articles from Sanity`);
        if (articles.length > 0) {
          console.log('First article:', articles[0].title);
          console.log('First article image:', articles[0].mainImage);
        }

        setNewsArticles(articles);
        setCategories(cats);
      } catch (error) {
        console.error('Error fetching news data:', error);
      }
    };

    fetchData();

    // Set up an interval to refresh the data every 30 seconds
    const refreshInterval = setInterval(() => {
      console.log('Refreshing news data...');
      fetchData();
    }, 30000);

    // Clean up the interval when the component unmounts
    return () => clearInterval(refreshInterval);
  }, []);

  // Handle search form submission
  const handleSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!searchQuery.trim()) return;

    setIsSearching(true);

    try {
      const response = await fetch(`/api/news/search?q=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();

      if (data.articles) {
        setSearchResults(data.articles);
      }
    } catch (error) {
      console.error('Error searching news:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Reset search results
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
  };

  // Find featured article or use first article
  const findFeaturedArticle = (articles: NewsArticle[]) => {
    // First try to find an article marked as featured
    const featured = articles.find(article => article.featured === true);
    // If no featured article found, use the first article
    return featured || (articles.length > 0 ? articles[0] : null);
  };

  // Featured news (featured article or first article or fallback)
  const featuredNews = searchResults
    ? findFeaturedArticle(searchResults)
    : findFeaturedArticle(newsArticles);

  console.log('Featured news:', featuredNews?.title);

  // Secondary news (next 4 articles, excluding the featured one)
  const secondaryNews = searchResults
    ? searchResults
        .filter(article => article._id !== (featuredNews?._id || ''))
        .slice(0, 4)
    : newsArticles
        .filter(article => article._id !== (featuredNews?._id || ''))
        .slice(0, 4);

  console.log('Secondary news count:', secondaryNews.length);

  // Latest news (remaining articles, excluding featured and secondary)
  const latestNews = searchResults
    ? searchResults
        .filter(article =>
          article._id !== (featuredNews?._id || '') &&
          !secondaryNews.some(a => a._id === article._id)
        )
        .slice(0, 6)
    : newsArticles
        .filter(article =>
          article._id !== (featuredNews?._id || '') &&
          !secondaryNews.some(a => a._id === article._id)
        )
        .slice(0, 6);

  console.log('Latest news count:', latestNews.length);

  return (
    <>
      <Header />
      <main className="overflow-x-hidden">
        {/* Hero Section */}
        <section className="royal-gradient py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">
                Royal News & Updates
              </h1>
              <p className="text-xl text-royalGold mb-8">
                Stay informed about the latest developments in the Kingdom of Adukrom
              </p>

              <div className="flex justify-center mt-8">
                <form className="flex w-full max-w-lg" onSubmit={handleSearch}>
                  <input
                    type="text"
                    placeholder="Search news articles..."
                    className="w-full px-4 py-3 rounded-l-lg border-0 focus:outline-none"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <button
                    type="submit"
                    className="bg-royalGold text-royalBlue px-6 py-3 rounded-r-lg hover:bg-yellow-500 transition-colors"
                    disabled={isSearching}
                  >
                    {isSearching ? (
                      <div className="w-5 h-5 border-2 border-royalBlue border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"></path>
                      </svg>
                    )}
                  </button>
                </form>
              </div>

              {searchResults && (
                <div className="mt-4 text-center">
                  <p className="text-white">
                    {searchResults.length === 0
                      ? 'No results found for your search.'
                      : `Found ${searchResults.length} result${searchResults.length === 1 ? '' : 's'} for "${searchQuery}"`}
                  </p>
                  <button
                    onClick={clearSearch}
                    className="mt-2 text-royalGold underline hover:text-yellow-400"
                  >
                    Clear search
                  </button>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Featured News Section */}
        <section className="py-16 bg-ivory">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-royalBlue text-center mb-16 section-title">Featured News</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Main Featured Article */}
              <div className="featured-news h-96 relative rounded-lg overflow-hidden shadow-xl w-full">
                <div className="absolute inset-0 bg-royalBlue">
                  {featuredNews && featuredNews.mainImage && urlFor(featuredNews.mainImage).url() ? (
                    <Image
                      src={urlFor(featuredNews.mainImage).url()}
                      alt={featuredNews.title || "Featured news"}
                      fill
                      className="object-cover opacity-40"
                      unoptimized={true}
                    />
                  ) : (
                    <div className="w-full h-full bg-royalBlue opacity-40"></div>
                  )}
                </div>
                <div className="absolute inset-0 flex flex-col justify-end p-4 md:p-8 z-10">
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="bg-royalGold text-royalBlue text-xs font-bold px-3 py-1 rounded-full">FEATURED</span>
                    {featuredNews && featuredNews.category && (
                      <span className="bg-white text-royalBlue text-xs font-bold px-3 py-1 rounded-full">
                        {featuredNews.category.title}
                      </span>
                    )}
                  </div>
                  <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-2">
                    {featuredNews ? featuredNews.title : "International Dignitaries Confirm Attendance at Royal Events"}
                  </h3>
                  <p className="text-white/80 mb-4 line-clamp-3">
                    {featuredNews ? featuredNews.excerpt : "Representatives from over 20 countries will attend the historic ceremonies of King Allen Ellison, marking a new era for Adukrom Kingdom."}
                  </p>
                  <div className="flex flex-wrap justify-between items-center gap-2">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-royalGold flex items-center justify-center text-royalBlue font-bold">R</div>
                      <span className="ml-2 text-white">Royal Press Office</span>
                    </div>
                    <span className="text-white/70 text-sm">
                      {featuredNews && featuredNews.publishedAt ? formatDate(featuredNews.publishedAt) : "August 15, 2023"}
                    </span>
                  </div>
                  <Link
                    href={featuredNews ? `/news/${featuredNews.slug?.current || featuredNews._id}` : "#"}
                    className="absolute inset-0"
                    aria-label="Read full article"
                    prefetch={true}
                    onClick={(e) => {
                      if (featuredNews) {
                        console.log(`Navigating to featured article: /news/${featuredNews.slug?.current || featuredNews._id}`);
                      }
                    }}
                  ></Link>
                </div>
              </div>

              {/* Secondary Featured Articles */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
                {secondaryNews.length > 0 ? (
                  secondaryNews.map((article: any) => (
                    <div key={article._id} className="news-card rounded-lg overflow-hidden shadow-lg h-64 relative w-full">
                      <div className="absolute inset-0 bg-royalBlue">
                        {article.mainImage && urlFor(article.mainImage).url() ? (
                          <Image
                            src={urlFor(article.mainImage).url()}
                            alt={article.title || "News image"}
                            fill
                            className="object-cover opacity-40"
                            unoptimized={true}
                          />
                        ) : (
                          <div className="w-full h-full bg-royalBlue opacity-40"></div>
                        )}
                      </div>
                      <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                        <div className="flex flex-wrap gap-2 mb-3">
                          {article.category && (
                            <span className="bg-white text-royalBlue text-xs font-bold px-3 py-1 rounded-full">
                              {article.category.title}
                            </span>
                          )}
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">{article.title}</h3>
                        <p className="text-white/80 mb-3 line-clamp-2">{article.excerpt}</p>
                        <div className="flex flex-wrap justify-between items-center gap-2">
                          <span className="text-white/70 text-sm">
                            {article.publishedAt ? formatDate(article.publishedAt) : ""}
                          </span>
                          <Link
                            href={`/news/${article.slug?.current || article._id}`}
                            className="text-white hover:text-royalGold transition-colors font-medium"
                            prefetch={true}
                            onClick={(e) => {
                              console.log(`Navigating to: /news/${article.slug?.current || article._id}`);
                            }}
                          >
                            Read More →
                          </Link>
                        </div>
                        <Link
                          href={`/news/${article.slug?.current || article._id}`}
                          className="absolute inset-0"
                          aria-label="Read full article"
                          prefetch={true}
                        ></Link>
                      </div>
                    </div>
                  ))
                ) : (
                  // Fallback content if no secondary news
                  <>
                    <div className="news-card rounded-lg overflow-hidden shadow-lg h-64 relative">
                      <div className="absolute inset-0 bg-royalBlue">
                        <div className="w-full h-full bg-royalBlue opacity-40"></div>
                      </div>
                      <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                        <div className="flex space-x-2 mb-3">
                          <span className="bg-white text-royalBlue text-xs font-bold px-3 py-1 rounded-full">CEREMONY</span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Sacred Crown Jewels Unveiled for Upcoming Ceremony</h3>
                        <p className="text-white/80 mb-3 line-clamp-2">The historic crown jewels, kept in sacred storage for decades, have been revealed in preparation for the ceremony.</p>
                        <div className="flex justify-between items-center">
                          <span className="text-white/70 text-sm">August 12, 2023</span>
                          <Link href="#" className="text-white hover:text-royalGold transition-colors font-medium">Read More →</Link>
                        </div>
                        <Link href="#" className="absolute inset-0" aria-label="Read full article"></Link>
                      </div>
                    </div>

                    <div className="news-card rounded-lg overflow-hidden shadow-lg h-64 relative">
                      <div className="absolute inset-0 bg-royalBlue">
                        <div className="w-full h-full bg-royalBlue opacity-40"></div>
                      </div>
                      <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                        <div className="flex space-x-2 mb-3">
                          <span className="bg-white text-forestGreen text-xs font-bold px-3 py-1 rounded-full">CULTURE</span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Traditional Artisans Create Ceremonial Garments</h3>
                        <p className="text-white/80 mb-3 line-clamp-2">Master weavers and craftspeople have been working for months to create the elaborate ceremonial attire.</p>
                        <div className="flex justify-between items-center">
                          <span className="text-white/70 text-sm">August 10, 2023</span>
                          <Link href="#" className="text-white hover:text-royalGold transition-colors font-medium">Read More →</Link>
                        </div>
                        <Link href="#" className="absolute inset-0" aria-label="Read full article"></Link>
                      </div>
                    </div>

                    <div className="news-card rounded-lg overflow-hidden shadow-lg h-64 relative">
                      <div className="absolute inset-0 bg-royalBlue">
                        <div className="w-full h-full bg-royalBlue opacity-40"></div>
                      </div>
                      <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                        <div className="flex space-x-2 mb-3">
                          <span className="bg-white text-royalBlue text-xs font-bold px-3 py-1 rounded-full">INTERVIEW</span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Exclusive: King Allen Ellison Shares Vision for Adukrom Kingdom</h3>
                        <p className="text-white/80 mb-3 line-clamp-2">In a rare interview, King Allen Ellison discusses his plans for cultural preservation and economic growth.</p>
                        <div className="flex justify-between items-center">
                          <span className="text-white/70 text-sm">August 8, 2023</span>
                          <Link href="#" className="text-white hover:text-royalGold transition-colors font-medium">Read More →</Link>
                        </div>
                        <Link href="#" className="absolute inset-0" aria-label="Read full article"></Link>
                      </div>
                    </div>

                    <div className="news-card rounded-lg overflow-hidden shadow-lg h-64 relative">
                      <div className="absolute inset-0 bg-royalBlue">
                        <Image
                          src="/Website Images/1H2A8839.png"
                          alt="Adukrom Kingdom History"
                          fill
                          className="object-cover opacity-40"
                        />
                      </div>
                      <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                        <div className="flex space-x-2 mb-3">
                          <span className="bg-white text-red-700 text-xs font-bold px-3 py-1 rounded-full">HISTORY</span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">The Legacy of Adukrom Kingdom: A Historical Timeline</h3>
                        <p className="text-white/80 mb-3 line-clamp-2">Exploring the rich history and traditions of Adukrom Kingdom as it prepares for its first major event in 25 years.</p>
                        <div className="flex justify-between items-center">
                          <span className="text-white/70 text-sm">August 5, 2023</span>
                          <Link href="#" className="text-white hover:text-royalGold transition-colors font-medium">Read More →</Link>
                        </div>
                        <Link href="#" className="absolute inset-0" aria-label="Read full article"></Link>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-16 royal-gradient">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-white text-center mb-16 section-title">Timeline of Adukrom Kingdom: A Legacy of Leadership and Resilience</h2>

            <div className="max-w-4xl mx-auto timeline">
              <div className="timeline-item py-8">
                <div className="timeline-content timeline-left bg-white p-6 rounded-lg shadow-lg animate-fadeInLeft">
                  <span className="text-royalGold font-bold">16TH CENTURY</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Settlement Begins</h3>
                  <p className="text-charcoal">The Aklemede Clan, led by Opanyin Kokora Boame, is the first to settle the area now known as Adukrom. Boame names the new settlement Asienso between Apirede and Awukugua. Later, Opanyin Adu (Adumanuru), a hunter and herbalist from Akyem Kotoku joins, bringing the black stool of the Aduana clan. His influence leads to the town eventually being called Adukrom — Adu's town.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-right bg-white p-6 rounded-lg shadow-lg animate-fadeInRight">
                  <span className="text-royalGold font-bold">1730-1733</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Formation of Akuapem State & Chieftaincy System</h3>
                  <p className="text-charcoal">Adukrom is named the capital of the Nifa Division, one of the key divisions of the Akuapem State. Traditional priesthood governance transitions into a formal chieftaincy with the reign of Nana Otutu Kono I (1733–1764), the first enstooled chief of Adukrom.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-left bg-white p-6 rounded-lg shadow-lg animate-fadeInLeft delay-200">
                  <span className="text-royalGold font-bold">1772-1843</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Era of Consolidation & Military Victory</h3>
                  <p className="text-charcoal">Reign of Nana Otutu Ababio I (Ohene Kwame Donkor) from 1772-1813 marks a defining era of consolidation for Adukrom's authority. Nana Otutu Ababio II (1813-1843) participates in the Akatamanso War (1826)—a historic victory over the Ashanti Empire at Dodowa.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-right bg-white p-6 rounded-lg shadow-lg animate-fadeInRight delay-200">
                  <span className="text-royalGold font-bold">1843-1900</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Heroism & Royal Recognition</h3>
                  <p className="text-charcoal">Nana Otutu Opare Ababio I (Aketewa Mpenyinsem) shows great bravery during the Awuna War (1866), rescuing the Okuapehene and killing the Anlo leader Djo Kpantanpklan. As a reward, Adukrom receives sacred items in 1869, including Adongua, a feathered cap, drums, and a palanquin. The Otutu Stool Symbol is adopted: a man standing on thorns to lift a leopard cub—symbolizing courage and royal sacrifice.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-left bg-white p-6 rounded-lg shadow-lg animate-fadeInLeft delay-400">
                  <span className="text-royalGold font-bold">1900-2016</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Modern Leadership</h3>
                  <p className="text-charcoal">A succession of Otutu Chiefs preserves Adukrom's spiritual and political legacy: Nana Otutu Ababio III (1900–1933), Nana Otutu Kono II (1933–1943), Nana Opare Ababio II (1943–1945), Osuodumgya Otutu Ababio IV (1945–2000) – one of the longest reigning chiefs. From 2002-2016, Nana Otutu Kono leads the town through modern administrative transitions.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-right bg-white p-6 rounded-lg shadow-lg animate-fadeInRight delay-400">
                  <span className="text-royalGold font-bold">2016-PRESENT</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Current Leadership</h3>
                  <p className="text-charcoal">Reign of Osuodumgya Otutu Ababio V (Paul Yeboah Konoboa): Current traditional ruler of Adukrom and the spiritual anchor of the Otutu stool. Under his leadership, Adukrom has experienced a renaissance in cultural preservation while embracing economic development initiatives.</p>
                </div>
              </div>

              <div className="timeline-item py-8">
                <div className="timeline-content timeline-left bg-white p-6 rounded-lg shadow-lg animate-fadeInLeft delay-600">
                  <span className="text-royalGold font-bold">2025</span>
                  <h3 className="text-xl font-bold text-royalBlue mt-2 mb-3">Grand Coronation of His Majesty Mpuntuhene Allen Ellison</h3>
                  <p className="text-charcoal">Enstooled as Mpuntuhene – King of Trade, Investment, and Economic Development, His Majesty ushers Adukrom into a global renaissance, bridging tradition with innovation and establishing the Ellison Royal Sovereign Wealth Fund to empower the Kingdom and beyond.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Latest News Section */}
        <section className="py-16 bg-ivory">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center mb-12 gap-4">
              <h2 className="text-3xl font-bold text-royalBlue section-title">Latest News</h2>

              <div className="flex flex-wrap gap-2 justify-center">
                <Link href="/news" className="px-4 py-2 rounded-full bg-royalBlue text-white">All</Link>
                {categories.map((category: any) => (
                  <Link
                    key={category._id}
                    href={`/news/category/${category.slug.current}`}
                    className="px-4 py-2 rounded-full bg-white text-royalBlue border border-royalBlue hover:bg-royalBlue hover:text-white transition-all"
                  >
                    {category.title}
                  </Link>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full">
              {latestNews.length > 0 ? (
                latestNews.map((article: any) => (
                  <div key={article._id} className="news-card rounded-lg overflow-hidden shadow-lg h-80 relative w-full">
                    <div className="absolute inset-0 bg-royalBlue">
                      {article.mainImage && urlFor(article.mainImage).url() ? (
                        <Image
                          src={urlFor(article.mainImage).url()}
                          alt={article.title || "Kingdom News"}
                          fill
                          className="object-cover opacity-40"
                          unoptimized={true}
                        />
                      ) : (
                        <div className="w-full h-full bg-royalBlue opacity-40"></div>
                      )}
                    </div>
                    <div className="absolute inset-0 flex flex-col justify-end p-6 z-10">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {article.category && (
                          <span className="bg-white text-royalBlue text-xs font-bold px-3 py-1 rounded-full">
                            {article.category.title}
                          </span>
                        )}
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">{article.title}</h3>
                      <p className="text-white/80 mb-3 line-clamp-3">{article.excerpt}</p>
                      <div className="flex flex-wrap justify-between items-center gap-2">
                        <span className="text-white/70 text-sm">
                          {article.publishedAt ? formatDate(article.publishedAt) : ""}
                        </span>
                        <Link
                          href={`/news/${article.slug?.current || article._id}`}
                          className="text-white hover:text-royalGold transition-colors font-medium"
                          prefetch={true}
                          onClick={(e) => {
                            console.log(`Navigating to: /news/${article.slug?.current || article._id}`);
                          }}
                        >
                          Read More →
                        </Link>
                      </div>
                      <Link
                        href={`/news/${article.slug?.current || article._id}`}
                        className="absolute inset-0"
                        aria-label="Read full article"
                        prefetch={true}
                      ></Link>
                    </div>
                  </div>
                ))
              ) : (
                // If no latest news, show a message
                <div className="col-span-3 text-center py-12">
                  <p className="text-xl text-gray-500">More news articles coming soon!</p>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-16 royal-gradient">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-white mb-6">Stay Updated</h2>
              <p className="text-royalGold text-lg mb-8">Subscribe to receive the latest news and updates about the kingdom directly to your inbox</p>

              <form className="flex flex-col md:flex-row gap-4">
                <input type="email" placeholder="Your email address" className="newsletter-input flex-grow px-6 py-4 rounded-lg border-0 focus:outline-none" />
                <button type="submit" className="royal-button bg-royalGold text-royalBlue font-bold py-4 px-8 rounded-lg hover:bg-yellow-500 transition-colors">
                  Subscribe
                </button>
              </form>

              <p className="text-white/70 text-sm mt-4">We respect your privacy. Unsubscribe at any time.</p>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
