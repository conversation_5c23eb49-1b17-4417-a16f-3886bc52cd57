/**
 * Next.js Build Optimization Script
 * 
 * This script:
 * 1. Optimizes the Next.js build process
 * 2. Implements code splitting and tree shaking
 * 3. Reduces bundle size
 * 4. Improves build performance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Update Next.js configuration for better build performance
 */
function updateNextConfig() {
  console.log(`\n${COLORS.yellow}Updating Next.js configuration for better build performance...${COLORS.reset}`);
  
  const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    console.log(`${COLORS.red}next.config.js not found, skipping...${COLORS.reset}`);
    return;
  }
  
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check if we need to update the config
  if (nextConfig.includes('poweredByHeader: false') && 
      nextConfig.includes('productionBrowserSourceMaps: false')) {
    console.log(`${COLORS.green}✓ Next.js build configuration already optimized${COLORS.reset}`);
    return;
  }
  
  // Add build performance optimizations
  if (!nextConfig.includes('poweredByHeader: false')) {
    nextConfig = nextConfig.replace(
      'const nextConfig = {',
      `const nextConfig = {
  // Disable the powered by header
  poweredByHeader: false,
  
  // Disable source maps in production for smaller bundles
  productionBrowserSourceMaps: false,`
    );
  }
  
  // Add webpack optimizations if they don't exist
  if (!nextConfig.includes('optimization: {')) {
    const webpackConfigRegex = /webpack:\s*\(\s*config\s*(?:,\s*{\s*(?:isServer|dev|buildId|webpack|nextRuntime)\s*})?\s*\)\s*=>\s*{/;
    
    if (webpackConfigRegex.test(nextConfig)) {
      nextConfig = nextConfig.replace(
        webpackConfigRegex,
        match => `${match}
    // Add optimization for production builds
    if (process.env.NODE_ENV === 'production') {
      config.optimization = {
        ...config.optimization,
        runtimeChunk: 'single',
        splitChunks: {
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 20000,
          cacheGroups: {
            vendor: {
              test: /[\\\\/]node_modules[\\\\/]/,
              name(module) {
                // Get the name of the npm package
                const packageName = module.context.match(/[\\\\/]node_modules[\\\\/](.*?)([\\\\/]|$)/)[1];
                // Return a readable name for the package
                return \`npm.\${packageName.replace('@', '')}\`;
              },
            },
          },
        },
      };
    }`
      );
    }
  }
  
  // Write updated config
  fs.writeFileSync(nextConfigPath, nextConfig);
  console.log(`${COLORS.green}✓ Updated Next.js configuration for better build performance${COLORS.reset}`);
}

/**
 * Create a module/component analyzer script
 */
function createAnalyzerScript() {
  console.log(`\n${COLORS.yellow}Creating bundle analyzer script...${COLORS.reset}`);
  
  const analyzerScriptPath = path.join(ROOT_DIR, 'scripts', 'analyze-bundle.js');
  
  const analyzerScript = `/**
 * Bundle Analyzer Script
 * 
 * This script analyzes the Next.js bundle size and composition.
 * Run with: node scripts/analyze-bundle.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('Installing required packages...');
execSync('npm install --no-save cross-env @next/bundle-analyzer', { stdio: 'inherit' });

console.log('\\nAnalyzing bundle...');
execSync('cross-env ANALYZE=true next build', { 
  stdio: 'inherit',
  env: {
    ...process.env,
    ANALYZE: 'true',
  }
});

console.log('\\nBundle analysis complete!');
console.log('Check the report in your browser.');
`;
  
  fs.writeFileSync(analyzerScriptPath, analyzerScript);
  console.log(`${COLORS.green}✓ Created bundle analyzer script at scripts/analyze-bundle.js${COLORS.reset}`);
  
  // Update package.json to add analyze script
  const packageJsonPath = path.join(ROOT_DIR, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (!packageJson.scripts.analyze) {
      packageJson.scripts.analyze = 'node scripts/analyze-bundle.js';
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log(`${COLORS.green}✓ Added 'analyze' script to package.json${COLORS.reset}`);
    }
  }
}

/**
 * Update Next.js configuration to enable bundle analyzer
 */
function enableBundleAnalyzer() {
  console.log(`\n${COLORS.yellow}Enabling bundle analyzer in Next.js config...${COLORS.reset}`);
  
  const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    console.log(`${COLORS.red}next.config.js not found, skipping...${COLORS.reset}`);
    return;
  }
  
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check if bundle analyzer is already configured
  if (nextConfig.includes('@next/bundle-analyzer')) {
    console.log(`${COLORS.green}✓ Bundle analyzer already configured${COLORS.reset}`);
    return;
  }
  
  // Add bundle analyzer configuration
  nextConfig = `const withBundleAnalyzer = process.env.ANALYZE === 'true' 
  ? require('@next/bundle-analyzer')({ enabled: true })
  : (config) => config;

${nextConfig}`;

  // Update module.exports to wrap with withBundleAnalyzer
  nextConfig = nextConfig.replace(
    'module.exports = nextConfig;',
    'module.exports = withBundleAnalyzer(nextConfig);'
  );
  
  // Write updated config
  fs.writeFileSync(nextConfigPath, nextConfig);
  console.log(`${COLORS.green}✓ Enabled bundle analyzer in Next.js config${COLORS.reset}`);
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Next.js Build Optimization ====${COLORS.reset}`);
  
  // Update Next.js configuration for better build performance
  updateNextConfig();
  
  // Create bundle analyzer script
  createAnalyzerScript();
  
  // Enable bundle analyzer in Next.js config
  enableBundleAnalyzer();
  
  console.log(`\n${COLORS.green}✓ Build optimization complete!${COLORS.reset}`);
  console.log(`\n${COLORS.cyan}Next steps:${COLORS.reset}`);
  console.log(`1. Run ${COLORS.yellow}npm run analyze${COLORS.reset} to analyze your bundle size`);
  console.log(`2. Run ${COLORS.yellow}npm run build${COLORS.reset} to build with optimizations`);
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
