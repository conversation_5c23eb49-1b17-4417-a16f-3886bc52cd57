'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash,
  Eye,
  Image as ImageIcon,
  Upload,
  Filter,
  Loader2
} from 'lucide-react';
import { urlFor, fileUrlFor, isVideoFile } from '@/lib/sanity.client';
import DuplicateMedia<PERSON>inder from '@/components/DuplicateMediaFinder';

// Interface for gallery items
interface GalleryItem {
  _id: string;
  title: string;
  description?: string;
  image?: any; // For backward compatibility
  images?: Array<{
    mediaType?: 'image' | 'video';
    image?: any;
    video?: any;
    thumbnail?: any;
    alt?: string;
    caption?: string;
  }>;
  category?: {
    _id: string;
    title: string;
    slug: {
      current: string;
    };
  };
  publishedAt: string;
  featured?: boolean;
  order?: number;
}

// Interface for categories
interface Category {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
}

export default function GalleryPage() {
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isCleaningUp, setIsCleaningUp] = useState(false);

  // Function to fetch gallery items
  const fetchGalleryItems = async () => {
    setIsLoading(true);
    try {
      // Fetch gallery items
      const galleryResponse = await fetch('/api/gallery');
      const galleryData = await galleryResponse.json();

      if (galleryData.success) {
        setGalleryItems(galleryData.items || []);
      } else {
        console.error('Failed to fetch gallery items:', galleryData.message);
        toast.error('Failed to load gallery items');
      }
    } catch (error) {
      console.error('Error fetching gallery items:', error);
      toast.error('Failed to load gallery items');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch categories
  const fetchCategories = async () => {
    try {
      const categoriesResponse = await fetch('/api/categories');
      const categoriesData = await categoriesResponse.json();

      if (categoriesData.success) {
        setCategories(categoriesData.categories || []);
      } else {
        console.error('Failed to fetch categories:', categoriesData.message);
        toast.error('Failed to load categories');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load categories');
    }
  };

  // Fetch gallery items and categories on component mount
  useEffect(() => {
    const fetchGalleryData = async () => {
      setIsLoading(true);
      await Promise.all([fetchGalleryItems(), fetchCategories()]);
      setIsLoading(false);
    };

    fetchGalleryData();
  }, []);

  // Handle image deletion
  const handleDeleteImage = async (id: string) => {
    if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
      setIsDeleting(id);
      try {
        const response = await fetch(`/api/gallery/${id}`, {
          method: 'DELETE',
        });

        const data = await response.json();

        if (data.success) {
          toast.success('Image deleted successfully');
          // Remove the deleted item from the state
          setGalleryItems(galleryItems.filter(item => item._id !== id));
        } else {
          toast.error(data.message || 'Failed to delete image');
        }
      } catch (error) {
        console.error('Error deleting image:', error);
        toast.error('Failed to delete image');
      } finally {
        setIsDeleting(null);
      }
    }
  };

  // Handle gallery cleanup (remove duplicates)
  const handleCleanupGallery = async () => {
    if (confirm('This will remove duplicate gallery items. Are you sure you want to continue?')) {
      setIsCleaningUp(true);
      try {
        const response = await fetch('/api/gallery/cleanup', {
          method: 'POST',
        });

        const data = await response.json();

        if (data.success) {
          toast.success(data.message);
          // Refresh the gallery items
          await fetchGalleryItems();
        } else {
          toast.error(data.message || 'Failed to clean up gallery');
        }
      } catch (error) {
        console.error('Error cleaning up gallery:', error);
        toast.error('Failed to clean up gallery');
      } finally {
        setIsCleaningUp(false);
      }
    }
  };

  // Filter gallery items based on search term and category
  const filteredGalleryItems = galleryItems.filter(
    (item) => {
      const matchesSearch =
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description?.toLowerCase().includes(searchTerm.toLowerCase()) || false);

      const matchesCategory =
        selectedCategory === 'All' ||
        item.category?.slug.current === selectedCategory;

      return matchesSearch && matchesCategory;
    }
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gallery Management</h1>
          <p className="text-muted-foreground">
            Upload, organize, and manage images and videos for the Kingdom gallery.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCleanupGallery}
            disabled={isCleaningUp || isLoading}
          >
            {isCleaningUp ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cleaning...
              </>
            ) : (
              <>
                <Filter className="mr-2 h-4 w-4" />
                Remove Duplicates
              </>
            )}
          </Button>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/admin/gallery/upload">
                <Upload className="mr-2 h-4 w-4" />
                Upload Media
              </Link>
            </Button>
            <DuplicateMediaFinder onDuplicatesRemoved={fetchGalleryItems} />
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex items-center gap-2 flex-1">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search gallery..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <select
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            disabled={isLoading}
          >
            <option value="All">All Categories</option>
            {categories.map((category) => (
              <option key={category._id} value={category.slug.current}>
                {category.title}
              </option>
            ))}
          </select>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-12">
          <Loader2 className="mx-auto h-12 w-12 text-primary animate-spin" />
          <h3 className="mt-4 text-lg font-semibold">Loading gallery...</h3>
          <p className="mt-2 text-muted-foreground">
            Please wait while we fetch the gallery media.
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGalleryItems.map((item) => {
              // Get media URL from Sanity
              let mediaUrl = '';
              let isVideo = false;
              let thumbnailUrl = '';

              try {
                // Try to get media from the images array first
                if (item.images && item.images.length > 0) {
                  const firstMedia = item.images[0];

                  // Check if it's a video
                  if (firstMedia.mediaType === 'video' && firstMedia.video) {
                    isVideo = true;
                    // Check if video has a direct URL from the expanded reference
                    if (firstMedia.video.asset && firstMedia.video.asset.url) {
                      mediaUrl = firstMedia.video.asset.url;
                    } else {
                      mediaUrl = fileUrlFor(firstMedia.video);
                    }

                    // Use thumbnail if available
                    if (firstMedia.thumbnail) {
                      try {
                        const thumbnailBuilder = urlFor(firstMedia.thumbnail);
                        if (thumbnailBuilder && typeof thumbnailBuilder.url === 'function') {
                          thumbnailUrl = thumbnailBuilder.url();
                        }
                      } catch (thumbnailError) {
                        console.error('Error generating thumbnail URL:', thumbnailError);
                      }
                    }
                  }
                  // Otherwise treat as image
                  else if (firstMedia.image) {
                    try {
                      // Handle case where image is already a string URL
                      if (typeof firstMedia.image === 'string') {
                        mediaUrl = firstMedia.image;
                      }
                      // Check if image has a direct URL from the expanded reference
                      else if (firstMedia.image.asset && firstMedia.image.asset.url) {
                        mediaUrl = firstMedia.image.asset.url;
                      }
                      // Handle case where image is a Sanity reference
                      else {
                        const imageBuilder = urlFor(firstMedia.image);
                        if (imageBuilder && typeof imageBuilder.url === 'function') {
                          mediaUrl = imageBuilder.url();
                        }
                      }
                    } catch (imageError) {
                      console.error('Error generating image URL:', imageError);
                    }
                  }
                }
                // Fall back to the legacy image field
                else if (item.image) {
                  try {
                    // Handle case where image is already a string URL
                    if (typeof item.image === 'string') {
                      mediaUrl = item.image;
                    }
                    // Check if image has a direct URL from the expanded reference
                    else if (item.image.asset && item.image.asset.url) {
                      mediaUrl = item.image.asset.url;
                    }
                    // Handle case where image is a Sanity reference
                    else {
                      const imageBuilder = urlFor(item.image);
                      if (imageBuilder && typeof imageBuilder.url === 'function') {
                        mediaUrl = imageBuilder.url();
                      }
                    }
                  } catch (imageError) {
                    console.error('Error generating legacy image URL:', imageError);
                  }
                }

                // Use placeholder if no media found
                if (!mediaUrl) {
                  mediaUrl = '/images/placeholder.jpg';
                  console.warn('No media URL found for gallery item:', item._id);
                }
              } catch (error) {
                console.error('Error generating media URL:', error);
                mediaUrl = '/images/placeholder.jpg';
              }

              // Format date
              const publishedDate = item.publishedAt ?
                new Date(item.publishedAt).toLocaleDateString() :
                'Unknown date';

              return (
                <Card key={item._id} className="overflow-hidden">
                  <div className="relative aspect-video">
                    {isVideo ? (
                      <div className="relative w-full h-full bg-black overflow-hidden">
                        {/* Direct video element with controls */}
                        <video
                          className="w-full h-full object-contain"
                          src={mediaUrl}
                          poster={thumbnailUrl || '/images/video-thumbnail.png'}
                          controls
                          preload="auto"
                          onError={(e) => {
                            console.error('Video error:', e);
                          }}
                        />

                        {/* Video badge */}
                        <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
                          Video
                        </div>
                      </div>
                    ) : (
                      <Image
                        src={mediaUrl}
                        alt={item.title}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          console.error('Image failed to load:', mediaUrl);
                          // Set fallback image
                          (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                        }}
                      />
                    )}
                  </div>
                  <CardHeader className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{item.title}</CardTitle>
                        <CardDescription className="line-clamp-2">
                          {item.description || 'No description'}
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/gallery/edit/${item._id}`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/gallery?image=${item._id}`} target="_blank">
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteImage(item._id)}
                            disabled={isDeleting === item._id}
                          >
                            {isDeleting === item._id ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <Trash className="mr-2 h-4 w-4" />
                            )}
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardFooter className="p-4 pt-0 flex justify-between">
                    {item.category ? (
                      <Badge>{item.category.title}</Badge>
                    ) : (
                      <Badge variant="outline">Uncategorized</Badge>
                    )}
                    <span className="text-sm text-muted-foreground">{publishedDate}</span>
                  </CardFooter>
                </Card>
              );
            })}
          </div>

          {filteredGalleryItems.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No images found</h3>
              <p className="mt-2 text-muted-foreground">
                Try adjusting your search or filter to find what you're looking for.
              </p>
              <Button className="mt-4" asChild>
                <Link href="/admin/gallery/upload">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload New Media
                </Link>
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
