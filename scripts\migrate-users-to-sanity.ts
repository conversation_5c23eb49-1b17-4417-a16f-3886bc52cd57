/**
 * User Migration Script
 * 
 * This script migrates all users from the local JSON file to Sanity CMS.
 * It ensures that all users are properly synchronized and marks <PERSON><PERSON> as the source of truth.
 * 
 * Usage:
 * 1. Run this script with: npx ts-node scripts/migrate-users-to-sanity.ts
 * 2. After migration, update your code to use <PERSON><PERSON> as the primary source for user data
 */

import fs from 'node:fs';
import path from 'node:path';
import { getWriteClient } from '../lib/sanity.client';
import bcrypt from 'bcryptjs';

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Function to read users from the local JSON file
function readLocalUsers() {
  if (!fs.existsSync(usersFilePath)) {
    console.log('No local users file found at:', usersFilePath);
    return [];
  }

  try {
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    return JSON.parse(usersData);
  } catch (error) {
    console.error('Error reading local users file:', error);
    return [];
  }
}

// Function to get all users from Sanity
async function getSanityUsers() {
  try {
    const client = getWriteClient();
    
    const users = await client.fetch(`
      *[_type == "adminUser"] {
        _id,
        username,
        email,
        name,
        role,
        hashedPassword,
        createdAt,
        lastLogin,
        isActive,
        permissions
      }
    `);
    
    return users;
  } catch (error) {
    console.error('Error fetching users from Sanity:', error);
    return [];
  }
}

// Function to create a user in Sanity
async function createSanityUser(user: any) {
  try {
    const client = getWriteClient();
    
    // Generate a unique ID for the user if not provided
    const userId = user.id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Generate username from email if not provided
    const username = user.username || user.email.split('@')[0];
    
    // Use existing hashed password or hash the password if provided
    let hashedPassword = user.hashedPassword;
    if (!hashedPassword && user.password) {
      hashedPassword = await bcrypt.hash(user.password, 10);
    }
    
    // Create the user document
    const newUser = {
      _type: 'adminUser',
      _id: userId,
      username,
      email: user.email,
      name: user.name,
      role: user.role || 'user',
      hashedPassword,
      createdAt: user.createdAt || new Date().toISOString(),
      updatedAt: user.updatedAt || new Date().toISOString(),
      lastLogin: user.lastLogin || null,
      isActive: user.isActive !== undefined ? user.isActive : true,
      permissions: user.permissions || {},
      isProtected: user.role === 'super_admin', // Protect super admin users
    };
    
    // Create the document in Sanity
    const createdUser = await client.createOrReplace(newUser);
    console.log(`Created user in Sanity: ${createdUser.name} (${createdUser._id})`);
    
    return createdUser;
  } catch (error) {
    console.error(`Error creating user in Sanity: ${user.name} (${user.email})`, error);
    throw error;
  }
}

// Main migration function
async function migrateUsers() {
  console.log('Starting user migration from local JSON to Sanity CMS...');
  
  // Read local users
  const localUsers = readLocalUsers();
  console.log(`Found ${localUsers.length} users in local storage`);
  
  // Get Sanity users
  const sanityUsers = await getSanityUsers();
  console.log(`Found ${sanityUsers.length} users in Sanity CMS`);
  
  // Create a map of Sanity users by email for quick lookup
  const sanityUsersByEmail = new Map();
  sanityUsers.forEach((user: any) => {
    if (user.email) {
      sanityUsersByEmail.set(user.email.toLowerCase(), user);
    }
  });
  
  // Track migration results
  const results = {
    migrated: 0,
    skipped: 0,
    failed: 0,
  };
  
  // Migrate each local user to Sanity if they don't already exist
  for (const localUser of localUsers) {
    try {
      if (!localUser.email) {
        console.warn(`Skipping user without email: ${localUser.name || 'Unknown'}`);
        results.skipped++;
        continue;
      }
      
      const existingSanityUser = sanityUsersByEmail.get(localUser.email.toLowerCase());
      
      if (existingSanityUser) {
        console.log(`User already exists in Sanity: ${localUser.name} (${localUser.email})`);
        results.skipped++;
        continue;
      }
      
      // Create the user in Sanity
      await createSanityUser(localUser);
      results.migrated++;
    } catch (error) {
      console.error(`Failed to migrate user: ${localUser.name} (${localUser.email})`, error);
      results.failed++;
    }
  }
  
  // Print migration results
  console.log('\nMigration completed:');
  console.log(`- Migrated: ${results.migrated} users`);
  console.log(`- Skipped: ${results.skipped} users (already exist in Sanity)`);
  console.log(`- Failed: ${results.failed} users`);
  
  // Create a backup of the local users file
  if (fs.existsSync(usersFilePath)) {
    const backupPath = `${usersFilePath}.backup-${Date.now()}`;
    fs.copyFileSync(usersFilePath, backupPath);
    console.log(`\nCreated backup of local users file: ${backupPath}`);
  }
  
  console.log('\nNext steps:');
  console.log('1. Verify that all users were migrated correctly');
  console.log('2. Update your code to use Sanity as the primary source for user data');
  console.log('3. Consider removing the local JSON file once you\'ve confirmed everything works');
}

// Run the migration
migrateUsers().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
