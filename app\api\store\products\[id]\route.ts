import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/store/products/[id] - Get a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Fetch the product
    const product = await client.fetch(`
      *[_type == "product" && (_id == $id || slug.current == $id)][0] {
        _id,
        name,
        slug,
        description,
        longDescription,
        price,
        compareAtPrice,
        currency,
        images,
        mainImage,
        sku,
        inventory,
        category->,
        tags,
        featured,
        isDigital,
        digitalFile,
        weight,
        dimensions,
        variants,
        seo,
        publishedAt
      }
    `, { id });
    
    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      product,
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch product',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/store/products/[id] - Update a specific product
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update products' },
        { status: 401 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Check if the product exists
    const existingProduct = await client.fetch(`
      *[_type == "product" && _id == $id][0]
    `, { id });
    
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }
    
    // Prepare the update object
    const updateObj: Record<string, any> = {
      _type: 'product',
    };
    
    // Add fields to update
    if (body.name !== undefined) updateObj.name = body.name;
    if (body.description !== undefined) updateObj.description = body.description;
    if (body.longDescription !== undefined) updateObj.longDescription = body.longDescription;
    if (body.price !== undefined) updateObj.price = parseFloat(body.price);
    if (body.compareAtPrice !== undefined) updateObj.compareAtPrice = parseFloat(body.compareAtPrice);
    if (body.currency !== undefined) updateObj.currency = body.currency;
    if (body.images !== undefined) updateObj.images = body.images;
    if (body.sku !== undefined) updateObj.sku = body.sku;
    if (body.inventory !== undefined) updateObj.inventory = body.inventory;
    if (body.category !== undefined) {
      updateObj.category = body.category ? { _type: 'reference', _ref: body.category } : undefined;
    }
    if (body.tags !== undefined) updateObj.tags = body.tags;
    if (body.featured !== undefined) updateObj.featured = body.featured;
    if (body.isDigital !== undefined) updateObj.isDigital = body.isDigital;
    if (body.variants !== undefined) updateObj.variants = body.variants;
    if (body.seo !== undefined) updateObj.seo = body.seo;
    
    // Update the slug if name is changed and slug is not provided
    if (body.name && !body.slug) {
      updateObj.slug = {
        _type: 'slug',
        current: body.name
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim(),
      };
    } else if (body.slug) {
      updateObj.slug = body.slug;
    }
    
    // Update the product in Sanity
    const updatedProduct = await client
      .patch(id)
      .set(updateObj)
      .commit();
    
    return NextResponse.json({
      success: true,
      message: 'Product updated successfully',
      product: updatedProduct,
    });
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update product',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/store/products/[id] - Delete a specific product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete products' },
        { status: 401 }
      );
    }
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Check if the product exists
    const existingProduct = await client.fetch(`
      *[_type == "product" && _id == $id][0]
    `, { id });
    
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }
    
    // Delete the product from Sanity
    await client.delete(id);
    
    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete product',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
