/**
 * Environment variables management
 *
 * This module provides a structured way to access environment variables
 * with validation and type safety.
 */

import { logError } from './errorHandling';

interface EnvVariables {
  // Sanity configuration
  NEXT_PUBLIC_SANITY_PROJECT_ID: string;
  NEXT_PUBLIC_SANITY_DATASET: string;
  NEXT_PUBLIC_SANITY_API_TOKEN: string; // Public token for client-side queries (read-only)
  SANITY_API_TOKEN: string; // Private token for server-side operations (full access)
  NEXT_PUBLIC_SANITY_API_VERSION: string;

  // Application configuration
  NODE_ENV: 'development' | 'production' | 'test';
  NEXT_PUBLIC_APP_URL: string;

  // Validate environment variables
  validate(): boolean;

  // Get all variables as an object
  getAll(): Record<string, string>;
}

class Environment implements EnvVariables {
  private validateToken(token: string | undefined, name: string): string {
    if (!token) {
      if (this.NODE_ENV === 'production') {
        throw new Error(`Missing required token: ${name}`);
      }
      console.warn(`Warning: ${name} is not set. Some features may not work correctly.`);
      return '';
    }
    return token;
  }

  // Sanity configuration
  get NEXT_PUBLIC_SANITY_PROJECT_ID(): string {
    const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID;
    if (!projectId) {
      throw new Error('NEXT_PUBLIC_SANITY_PROJECT_ID is required');
    }
    return projectId;
  }

  get NEXT_PUBLIC_SANITY_DATASET(): string {
    const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET;
    if (!dataset) {
      throw new Error('NEXT_PUBLIC_SANITY_DATASET is required');
    }
    return dataset;
  }

  get NEXT_PUBLIC_SANITY_API_TOKEN(): string {
    return this.validateToken(
      process.env.NEXT_PUBLIC_SANITY_API_TOKEN,
      'NEXT_PUBLIC_SANITY_API_TOKEN'
    );
  }

  get SANITY_API_TOKEN(): string {
    return this.validateToken(
      process.env.SANITY_API_TOKEN,
      'SANITY_API_TOKEN'
    );
  }

  get NEXT_PUBLIC_SANITY_API_VERSION(): string {
    return process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2025-05-09';
  }

  // Application configuration
  get NODE_ENV(): 'development' | 'production' | 'test' {
    const env = process.env.NODE_ENV as 'development' | 'production' | 'test';
    if (!env || !['development', 'production', 'test'].includes(env)) {
      return 'development';
    }
    return env;
  }

  get NEXT_PUBLIC_APP_URL(): string {
    const url = process.env.NEXT_PUBLIC_APP_URL;
    if (!url) {
      return this.NODE_ENV === 'development'
        ? 'http://localhost:3000'
        : 'https://kingdomadukrom.com';
    }
    return url;
  }

  /**
   * Validate required environment variables
   * Returns true if all required variables are present, false otherwise
   */
  validate(): boolean {
    try {
      // Always required
      const requiredVariables = [
        'NEXT_PUBLIC_SANITY_PROJECT_ID',
        'NEXT_PUBLIC_SANITY_DATASET',
      ];

      // In production, we need additional variables
      if (this.NODE_ENV === 'production') {
        requiredVariables.push('NEXT_PUBLIC_SANITY_API_TOKEN');
        // Server-side token is only required in production for admin operations
        if (typeof window === 'undefined') {
          requiredVariables.push('SANITY_API_TOKEN');
        }
      }

      const missing = requiredVariables.filter(key => !this[key as keyof this]);

      if (missing.length > 0) {
        const errorMessage = `Missing required environment variables: ${missing.join(', ')}`;

        if (this.NODE_ENV === 'development') {
          console.error('⚠️ ' + errorMessage);
          console.error('Please check your .env.local file');
        } else {
          logError(new Error(errorMessage), 'env.validate');
        }

        return false;
      }

      return true;
    } catch (error) {
      logError(error, 'env.validate');
      return false;
    }
  }

  /**
   * Get all environment variables as an object
   * Useful for debugging
   */
  getAll(): Record<string, string> {
    return {
      NEXT_PUBLIC_SANITY_PROJECT_ID: this.NEXT_PUBLIC_SANITY_PROJECT_ID,
      NEXT_PUBLIC_SANITY_DATASET: this.NEXT_PUBLIC_SANITY_DATASET,
      NEXT_PUBLIC_SANITY_API_VERSION: this.NEXT_PUBLIC_SANITY_API_VERSION,
      NODE_ENV: this.NODE_ENV,
      NEXT_PUBLIC_APP_URL: this.NEXT_PUBLIC_APP_URL,
      // Don't include sensitive values like API tokens
    };
  }
}

export const env = new Environment();
