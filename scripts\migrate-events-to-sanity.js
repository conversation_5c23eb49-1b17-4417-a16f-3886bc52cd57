const { createClient } = require('@sanity/client');
require('dotenv').config({ path: '.env.local' });

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2025-05-09',
  useCdn: false,
});

// Hardcoded events from the events page
const DEFAULT_EVENTS = [
  {
    title: 'Royal Coronation Ceremony',
    slug: { current: 'royal-coronation-ceremony' },
    date: '2025-08-29T07:30:00.000Z',
    endDate: '2025-08-29T21:00:00.000Z',
    location: 'Royal Palace Grounds',
    description: 'The official coronation ceremony of King <PERSON>, marking a new era for the Adukrom Kingdom.',
    isCountdownTarget: true,
    isHighlighted: true,
    showRsvp: true,
    eventType: 'ceremony',
    order: 1
  },
  {
    title: 'Royal Gala Dinner',
    slug: { current: 'royal-gala-dinner' },
    date: '2025-08-30T19:00:00.000Z',
    location: 'Grand Ballroom, Accra',
    description: 'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests.',
    isCountdownTarget: false,
    isHighlighted: false,
    showRsvp: true,
    eventType: 'dinner',
    order: 2
  },
  {
    title: 'Global Economic Forum',
    slug: { current: 'global-economic-forum' },
    date: '2025-08-31T10:00:00.000Z',
    location: 'Adukrom Convention Center',
    description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Adukrom Kingdom.',
    isCountdownTarget: false,
    isHighlighted: false,
    showRsvp: true,
    eventType: 'conference',
    order: 3
  }
];

// Function to check if an event already exists in Sanity
async function eventExists(slug) {
  const result = await client.fetch(
    `*[_type == "event" && slug.current == $slug][0]._id`,
    { slug }
  );
  return !!result;
}

// Function to create an event in Sanity
async function createEvent(event) {
  try {
    // Check if the event already exists
    const exists = await eventExists(event.slug.current);
    if (exists) {
      console.log(`Event "${event.title}" already exists, skipping...`);
      return null;
    }

    // Create the event
    console.log(`Creating event: ${event.title}`);
    const result = await client.create({
      _type: 'event',
      ...event
    });

    console.log(`Created event: ${event.title} with ID: ${result._id}`);
    return result;
  } catch (error) {
    console.error(`Error creating event "${event.title}":`, error);
    return null;
  }
}

// Main function to migrate events
async function migrateEvents() {
  console.log('Starting event migration to Sanity...');

  // Validate token
  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN is missing. Please add it to your .env.local file.');
    process.exit(1);
  }

  try {
    // Create each event
    const results = [];
    for (const event of DEFAULT_EVENTS) {
      const result = await createEvent(event);
      if (result) {
        results.push(result);
      }
    }

    console.log(`Migration complete. Created ${results.length} events.`);

    // If we created the coronation event, make sure it's set as the countdown target
    const coronationEvent = results.find(event => event.slug?.current === 'royal-coronation-ceremony');
    if (coronationEvent) {
      console.log('Setting coronation event as countdown target...');
      await client
        .patch(coronationEvent._id)
        .set({ isCountdownTarget: true })
        .commit();
      console.log('Coronation event set as countdown target.');
    }
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
migrateEvents();
