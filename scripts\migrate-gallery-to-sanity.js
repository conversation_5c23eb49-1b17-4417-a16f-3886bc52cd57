/**
 * <PERSON><PERSON><PERSON> to migrate gallery images and create initial content in Sanity
 * 
 * This script:
 * 1. Uploads all images from public/Website Images to Sanity
 * 2. Creates a gallery document with references to the uploaded images
 * 3. Sets up proper metadata for each image
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@sanity/client');
const { createReadStream } = require('fs');

// Configure Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2025-05-09',
  useCdn: false,
});

// Source directory for images
const sourceDir = path.join(__dirname, '../public/Website Images');

// Function to generate a clean title from filename
function generateTitleFromFilename(filename) {
  return filename
    .replace(/\.\w+$/, '') // Remove file extension
    .replace(/[-_]/g, ' ') // Replace hyphens and underscores with spaces
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
    .trim(); // Trim leading and trailing spaces
}

// Function to upload a single image to Sanity
async function uploadImageToSanity(filePath, fileName) {
  try {
    console.log(`Uploading ${fileName}...`);
    
    // Create a read stream for the file
    const stream = createReadStream(filePath);
    
    // Upload the file to Sanity
    const asset = await client.assets.upload('image', stream, {
      filename: fileName,
    });
    
    console.log(`Uploaded ${fileName} successfully!`);
    return asset;
  } catch (error) {
    console.error(`Error uploading ${fileName}:`, error);
    return null;
  }
}

// Function to create a gallery document with the uploaded images
async function createGalleryDocument(images) {
  try {
    console.log('Creating gallery document...');
    
    // Create the gallery document
    const gallery = {
      _type: 'gallery',
      title: 'Kingdom of Adukrom Gallery',
      slug: {
        _type: 'slug',
        current: 'gallery',
      },
      description: 'Official gallery of the Kingdom of Adukrom',
      images: images.map((image, index) => ({
        _key: `image${index}`,
        image: {
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: image._id,
          },
        },
        caption: generateTitleFromFilename(image.originalFilename),
        alt: generateTitleFromFilename(image.originalFilename),
      })),
      displayStyle: 'grid',
      backgroundStyle: 'royalBlue',
      animation: {
        duration: 0.6,
        delay: 0,
        stagger: 0.2,
        type: 'spring',
      },
      publishedAt: new Date().toISOString(),
    };
    
    // Check if gallery document already exists
    const existingGallery = await client.fetch('*[_type == "gallery" && slug.current == "gallery"][0]');
    
    if (existingGallery) {
      console.log('Gallery document already exists, updating...');
      await client
        .patch(existingGallery._id)
        .set({
          images: gallery.images,
          animation: gallery.animation,
          displayStyle: gallery.displayStyle,
          backgroundStyle: gallery.backgroundStyle,
          description: gallery.description,
        })
        .commit();
      console.log('Gallery document updated successfully!');
      return existingGallery._id;
    } else {
      console.log('Creating new gallery document...');
      const result = await client.create(gallery);
      console.log('Gallery document created successfully!');
      return result._id;
    }
  } catch (error) {
    console.error('Error creating gallery document:', error);
    return null;
  }
}

// Function to create a home page that references the gallery
async function createHomePage(galleryId) {
  try {
    console.log('Creating home page...');
    
    // Create hero section
    const heroSection = {
      _key: `hero_${Date.now()}`,
      _type: 'hero',
      heading: 'The Royal Family of Africa',
      tagline: 'The Crown of Africa. The Rise of a New Era.',
      backgroundImage: {
        _type: 'image',
        asset: {
          _type: 'reference',
          _ref: await getImageAssetId('ghana-scenic-landscape.png') || '',
        },
      },
      ctas: [
        {
          _key: 'cta1',
          title: 'Explore Our Heritage',
          link: '#gallery',
          isPrimary: true,
        },
        {
          _key: 'cta2',
          title: 'Contact Us',
          link: '#contact',
          isPrimary: false,
        },
      ],
      animation: {
        duration: 0.8,
        delay: 0,
        stagger: 0.3,
        type: 'spring',
      },
    };
    
    // Create gallery section reference
    const gallerySection = {
      _key: `gallery_${Date.now()}`,
      _type: 'reference',
      _ref: galleryId,
    };
    
    // Create text section
    const textSection = {
      _key: `text_${Date.now()}`,
      _type: 'textSection',
      heading: 'About Our Kingdom',
      text: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'The Kingdom of Adukrom has a rich cultural heritage and history dating back centuries. Our traditions and customs are preserved and celebrated by our people.',
            },
          ],
          style: 'normal',
        },
      ],
      backgroundStyle: 'ivory',
      textAlign: 'center',
      animation: {
        duration: 0.6,
        delay: 0.2,
        stagger: 0,
        type: 'spring',
      },
    };
    
    // Create the page document
    const homePage = {
      _type: 'page',
      title: 'Home',
      slug: {
        _type: 'slug',
        current: 'home',
      },
      navMenu: true,
      navOrder: 10,
      pageBuilder: [
        heroSection,
        textSection,
        // Include gallery reference
        {
          _key: `imageGallery_${Date.now()}`,
          _type: 'imageGallery',
          heading: 'Our Gallery',
          description: 'Explore the beauty and culture of the Kingdom of Adukrom',
          displayStyle: 'grid',
          backgroundStyle: 'royalBlue',
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring',
          },
        },
      ],
      description: 'The Crown of Africa. The Rise of a New Era.',
      seoTitle: 'The Royal Family of Africa',
      seoDescription: 'The Royal Family of Africa. The Crown of Africa. The Rise of a New Era.',
    };
    
    // Check if home page already exists
    const existingPage = await client.fetch('*[_type == "page" && slug.current == "home"][0]');
    
    if (existingPage) {
      console.log('Home page already exists, updating...');
      await client
        .patch(existingPage._id)
        .set({
          pageBuilder: homePage.pageBuilder,
        })
        .commit();
      console.log('Home page updated successfully!');
    } else {
      console.log('Creating new home page...');
      await client.create(homePage);
      console.log('Home page created successfully!');
    }
  } catch (error) {
    console.error('Error creating home page:', error);
  }
}

// Helper function to get an image asset ID by filename
async function getImageAssetId(filename) {
  try {
    const assets = await client.fetch('*[_type == "sanity.imageAsset" && originalFilename == $filename][0]', {
      filename,
    });
    return assets?._id;
  } catch (error) {
    console.error(`Error finding asset for ${filename}:`, error);
    return null;
  }
}

// Main function to upload all images and create content
async function migrateContentToSanity() {
  try {
    console.log('Starting content migration to Sanity...');
    
    // Read all files from the source directory
    const files = fs.readdirSync(sourceDir);
    
    // Filter image files (jpg, jpeg, png, webp, gif)
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext);
    });
    
    console.log(`Found ${imageFiles.length} image files to upload.`);
    
    // Upload each image to Sanity
    const uploadedImages = [];
    for (const file of imageFiles) {
      const filePath = path.join(sourceDir, file);
      const asset = await uploadImageToSanity(filePath, file);
      if (asset) {
        uploadedImages.push(asset);
      }
    }
    
    console.log(`Successfully uploaded ${uploadedImages.length} images.`);
    
    // Create the gallery document with the uploaded images
    const galleryId = await createGalleryDocument(uploadedImages);
    
    // Create the home page that references the gallery
    if (galleryId) {
      await createHomePage(galleryId);
    }
    
    console.log('Content migration completed successfully!');
  } catch (error) {
    console.error('Error migrating content to Sanity:', error);
  }
}

// Run the script
migrateContentToSanity();
