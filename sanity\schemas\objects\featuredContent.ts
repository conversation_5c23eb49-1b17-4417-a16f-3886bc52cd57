import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'featuredContent',
  title: 'Featured Content',
  type: 'object',
  fields: [
    defineField({
      name: 'heading',
      title: 'Heading',
      type: 'string',
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 2,
    }),
    defineField({
      name: 'items',
      title: 'Featured Items',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
              rows: 2,
            },
            {
              name: 'image',
              title: 'Image',
              type: 'image',
              options: {
                hotspot: true,
              },
            },
            {
              name: 'link',
              title: 'Link',
              type: 'string',
            },
            {
              name: 'linkText',
              title: 'Link Text',
              type: 'string',
              hidden: ({ parent }) => !parent?.link,
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              description: 'Icon name from Lucide icons library',
            },
          ],
          preview: {
            select: {
              title: 'title',
              media: 'image',
            },
          },
        },
      ],
    }),
    defineField({
      name: 'layout',
      title: 'Layout',
      type: 'string',
      options: {
        list: [
          { title: 'Cards', value: 'cards' },
          { title: 'List', value: 'list' },
          { title: 'Feature Grid', value: 'grid' },
        ],
      },
      initialValue: 'cards',
    }),
    defineField({
      name: 'animation',
      title: 'Animation Settings',
      type: 'object',
      fields: [
        { name: 'duration', type: 'number', title: 'Duration', initialValue: 0.6 },
        { name: 'delay', type: 'number', title: 'Delay', initialValue: 0 },
        { name: 'stagger', type: 'number', title: 'Stagger Children', initialValue: 0 },
        { name: 'type', type: 'string', title: 'Type', initialValue: 'spring' }
      ]
    }),
    defineField({
      name: 'backgroundStyle',
      title: 'Background Style',
      type: 'string',
      options: {
        list: [
          { title: 'None', value: 'none' },
          { title: 'Light', value: 'light' },
          { title: 'Dark', value: 'dark' },
          { title: 'Royal Blue', value: 'royalBlue' },
          { title: 'Royal Gold', value: 'royalGold' },
          { title: 'Ivory', value: 'ivory' },
        ],
      },
      initialValue: 'none',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      items: 'items',
    },
    prepare({ title, items }) {
      return {
        title: `Featured: ${title || 'Untitled'}`,
        subtitle: `${items?.length || 0} item${items?.length === 1 ? '' : 's'}`,
      };
    },
  },
});
