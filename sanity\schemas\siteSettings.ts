import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'siteSettings',
  title: 'Site Settings',
  type: 'document',
  groups: [
    { name: 'general', title: 'General' },
    { name: 'metadata', title: 'SEO & Metadata' },
    { name: 'contact', title: 'Contact' },
    { name: 'social', title: 'Social Media' },
    { name: 'about', title: 'About' },
    { name: 'events', title: 'Events' },
    { name: 'banking', title: 'Banking' },
    { name: 'store', title: 'Store' },
  ],
  fields: [
    // General Settings
    defineField({
      name: 'title',
      title: 'Site Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
      group: 'general',
    }),
    defineField({
      name: 'description',
      title: 'Site Description',
      type: 'text',
      rows: 3,
      validation: (Rule) => Rule.required(),
      group: 'general',
    }),
    defineField({
      name: 'keywords',
      title: 'Keywords',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags',
      },
      group: 'general',
    }),
    defineField({
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alternative Text',
          type: 'string',
          description: 'Important for SEO and accessibility',
        },
      ],
    }),
    defineField({
      name: 'favicon',
      title: 'Favicon',
      type: 'image',
      group: 'general',
    }),

    // SEO & Metadata Settings
    defineField({
      name: 'metadata',
      title: 'Legacy SEO & Metadata',
      type: 'object',
      group: 'metadata',
      description: 'Legacy metadata settings (use the SEO plugin settings below for new content)',
      fields: [
        {
          name: 'metaTitle',
          title: 'Default Meta Title',
          type: 'string',
          description: 'Default title used for search engines and social sharing (if left empty, Site Title will be used)',
        },
        {
          name: 'metaDescription',
          title: 'Default Meta Description',
          type: 'text',
          rows: 3,
          description: 'Default description for search engines and social sharing (if left empty, Site Description will be used)',
        },
        {
          name: 'metaKeywords',
          title: 'Default Meta Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            layout: 'tags',
          },
          description: 'Default keywords for search engines (if left empty, Keywords will be used)',
        },
        {
          name: 'ogImage',
          title: 'Default Social Sharing Image',
          type: 'image',
          options: {
            hotspot: true,
          },
          description: 'Default image used when sharing on social media (1200×630 recommended)',
          fields: [
            {
              name: 'alt',
              title: 'Alternative Text',
              type: 'string',
              description: 'Important for SEO and accessibility',
            },
          ],
        },
        {
          name: 'twitterHandle',
          title: 'X Handle (formerly Twitter)',
          type: 'string',
          description: 'Your X username without the @ symbol',
        },
        {
          name: 'siteUrl',
          title: 'Site URL',
          type: 'url',
          description: 'The full URL of your site (e.g., https://kingdomadukrom.com)',
        },
        {
          name: 'titleTemplate',
          title: 'Title Template',
          type: 'string',
          description: 'Template for page titles. Use %s to insert the page title (e.g., "%s | The Crown of Africa")',
          initialValue: '%s | The Crown of Africa',
        },
      ],
    }),
    // SEO Plugin Settings
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoMetaFields',
      group: 'metadata',
      description: 'Search engine optimization settings (recommended for new content)',
    }),
    defineField({
      name: 'mainNavigation',
      title: 'Main Navigation',
      type: 'array',
      group: 'general',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Name',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'link',
              title: 'Link',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'isExternal',
              title: 'Is External Link',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'isNew',
              title: 'Mark as New',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              description: 'SVG path data for the icon',
            },
            {
              name: 'children',
              title: 'Children',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'name',
                      title: 'Name',
                      type: 'string',
                      validation: (Rule) => Rule.required(),
                    },
                    {
                      name: 'link',
                      title: 'Link',
                      type: 'string',
                      validation: (Rule) => Rule.required(),
                    },
                    {
                      name: 'isExternal',
                      title: 'Is External Link',
                      type: 'boolean',
                      initialValue: false,
                    },
                    {
                      name: 'isNew',
                      title: 'Mark as New',
                      type: 'boolean',
                      initialValue: false,
                    },
                    {
                      name: 'description',
                      title: 'Description',
                      type: 'string',
                    },
                    {
                      name: 'icon',
                      title: 'Icon',
                      type: 'string',
                      description: 'SVG path data for the icon',
                    },
                  ],
                },
              ],
            },
          ],
          preview: {
            select: {
              title: 'name',
              subtitle: 'link',
            },
          },
        },
      ],
    }),
    defineField({
      name: 'contact',
      title: 'Contact Information',
      type: 'object',
      group: 'contact',
      fields: [
        {
          name: 'email',
          title: 'Email',
          type: 'string',
        },
        {
          name: 'phone',
          title: 'Phone',
          type: 'string',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'text',
          rows: 3,
        },
        {
          name: 'mapCoordinates',
          title: 'Map Coordinates',
          type: 'string',
          description: 'Latitude and longitude for the map (e.g., "40.7128° N, 74.0060° W")',
        },
      ],
    }),
    defineField({
      name: 'social',
      title: 'Social Media',
      type: 'object',
      group: 'social',
      fields: [
        {
          name: 'instagram',
          title: 'Instagram',
          type: 'url',
        },
        {
          name: 'facebook',
          title: 'Facebook',
          type: 'url',
        },
        {
          name: 'twitter',
          title: 'X (formerly Twitter)',
          type: 'url',
        },
        {
          name: 'linkedin',
          title: 'LinkedIn',
          type: 'url',
        },
      ],
    }),
    defineField({
      name: 'about',
      title: 'About Section',
      type: 'object',
      description: 'Content for the About section of the website',
      group: 'about',
      fields: [
        {
          name: 'kingBio',
          title: 'King Biography',
          type: 'text',
          rows: 4,
        },
        {
          name: 'kingVision',
          title: 'King Vision',
          type: 'text',
          rows: 3,
        },
        {
          name: 'kingMission',
          title: 'King Mission',
          type: 'text',
          rows: 3,
        },
        {
          name: 'kingPurpose',
          title: 'King Purpose Statement',
          type: 'text',
          rows: 3,
        },
        {
          name: 'kingQuote',
          title: 'King Quote',
          type: 'text',
          rows: 2,
        },
        {
          name: 'adukromDescription',
          title: 'Adukrom Description',
          type: 'array',
          of: [{ type: 'text', rows: 3 }],
        },
        {
          name: 'adukromLocation',
          title: 'Adukrom Location',
          type: 'text',
          rows: 2,
        },
        {
          name: 'nifaheneDescription',
          title: 'Nifahene Description',
          type: 'array',
          of: [{ type: 'text', rows: 3 }],
        },
      ],
    }),
    defineField({
      name: 'footer',
      title: 'Footer',
      type: 'object',
      group: 'general',
      fields: [
        {
          name: 'text',
          title: 'Footer Text',
          type: 'text',
          rows: 2,
        },
        {
          name: 'copyright',
          title: 'Copyright Text',
          type: 'string',
        },
      ],
    }),
    defineField({
      name: 'maintenanceMode',
      title: 'Maintenance Mode',
      type: 'boolean',
      description: 'Enable maintenance mode for the website',
      initialValue: false,
      group: 'general',
    }),
    defineField({
      name: 'events',
      title: 'Events',
      type: 'object',
      group: 'events',
      fields: [
        {
          name: 'coronationDate',
          title: 'Coronation Date',
          type: 'datetime',
          description: 'The date and time of the coronation event (used for countdown timer)',
          validation: (Rule) => Rule.required(),
        },
        {
          name: 'coronationLocation',
          title: 'Coronation Location',
          type: 'string',
        },
        {
          name: 'showCountdown',
          title: 'Show Countdown',
          type: 'boolean',
          description: 'Show or hide the countdown timer on the website',
          initialValue: true,
        },
      ],
    }),
    defineField({
      name: 'banking',
      title: 'Banking Information',
      type: 'object',
      description: 'Banking and payment processing settings (Super Admin only)',
      group: 'banking',
      fields: [
        {
          name: 'accountName',
          title: 'Account Name',
          type: 'string',
        },
        {
          name: 'accountNumber',
          title: 'Account Number',
          type: 'string',
        },
        {
          name: 'routingNumber',
          title: 'Routing Number',
          type: 'string',
        },
        {
          name: 'bankName',
          title: 'Bank Name',
          type: 'string',
        },
        {
          name: 'swiftCode',
          title: 'SWIFT/BIC Code',
          type: 'string',
        },
        {
          name: 'paymentProcessor',
          title: 'Payment Processor',
          type: 'string',
          options: {
            list: [
              { title: 'Stripe', value: 'stripe' },
              { title: 'PayPal', value: 'paypal' },
              { title: 'Square', value: 'square' },
              { title: 'Manual Processing', value: 'manual' },
            ],
          },
          initialValue: 'stripe',
        },
        {
          name: 'enablePayments',
          title: 'Enable Payment Processing',
          type: 'boolean',
          initialValue: false,
        },
      ],
    }),
    defineField({
      name: 'store',
      title: 'Store Settings',
      type: 'object',
      description: 'E-commerce store settings (Super Admin only)',
      group: 'store',
      fields: [
        {
          name: 'enableStore',
          title: 'Enable Online Store',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'shippingFee',
          title: 'Default Shipping Fee',
          type: 'string',
          initialValue: '10.00',
        },
        {
          name: 'taxRate',
          title: 'Tax Rate (%)',
          type: 'string',
          initialValue: '7.5',
        },
        {
          name: 'currencySymbol',
          title: 'Currency Symbol',
          type: 'string',
          initialValue: '$',
        },
        {
          name: 'allowInternationalShipping',
          title: 'Allow International Shipping',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'minimumOrderAmount',
          title: 'Minimum Order Amount',
          type: 'string',
          initialValue: '25.00',
        },
        {
          name: 'nftSettings',
          title: 'NFT Marketplace Settings',
          type: 'object',
          fields: [
            {
              name: 'contractAddress',
              title: 'Smart Contract Address',
              type: 'string',
            },
            {
              name: 'blockchainNetwork',
              title: 'Blockchain Network',
              type: 'string',
              options: {
                list: [
                  { title: 'Ethereum Mainnet', value: 'ethereum' },
                  { title: 'Polygon', value: 'polygon' },
                  { title: 'Optimism', value: 'optimism' },
                  { title: 'Arbitrum', value: 'arbitrum' },
                  { title: 'Base', value: 'base' },
                ],
              },
              initialValue: 'ethereum',
            },
            {
              name: 'enableNFTs',
              title: 'Enable NFT Marketplace',
              type: 'boolean',
              initialValue: false,
            },
          ],
        },
      ],
    }),
  ],
  preview: {
    select: {
      title: 'title',
    },
    prepare(selection) {
      return {
        ...selection,
        subtitle: 'Site Settings',
      };
    },
  },
});
