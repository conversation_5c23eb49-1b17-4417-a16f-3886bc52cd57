// This script assigns categories to gallery items in Sanity
// Run with: node scripts/assign-gallery-categories.js

const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Function to fetch all gallery items
async function fetchGalleryItems() {
  try {
    console.log('Fetching gallery items from Sanity...');

    const query = `
      *[_type == "gallery"] {
        _id,
        title,
        slug,
        description,
        images,
        category->{
          _id,
          title,
          slug
        },
        publishedAt,
        featured,
        order
      }
    `;

    const galleryItems = await client.fetch(query);

    console.log(`Found ${galleryItems.length} gallery items`);
    return galleryItems;
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return [];
  }
}

// Function to fetch all categories
async function fetchCategories() {
  try {
    console.log('Fetching categories from Sanity...');

    const query = `
      *[_type == "category"] {
        _id,
        title,
        slug,
        description,
        color,
        icon,
        order
      }
    `;

    const categories = await client.fetch(query);

    console.log(`Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Function to assign categories to gallery items
async function assignCategories(galleryItems, categories) {
  if (categories.length === 0) {
    console.log('No categories found. Please create categories first.');
    return;
  }

  if (galleryItems.length === 0) {
    console.log('No gallery items found.');
    return;
  }

  console.log(`\nAssigning categories to ${galleryItems.length} gallery items...`);

  // Get the General category if it exists, otherwise use the first category
  const generalCategory = categories.find(cat => cat.title === 'General') || categories[0];
  console.log(`Using category: ${generalCategory.title} (${generalCategory._id})`);

  // Get items without categories
  const itemsWithoutCategory = galleryItems.filter(item => !item.category);
  console.log(`Found ${itemsWithoutCategory.length} items without categories`);

  if (itemsWithoutCategory.length === 0) {
    console.log('All items already have categories assigned.');
    return;
  }

  // Create a transaction to update all items at once
  const transaction = client.transaction();

  for (const item of itemsWithoutCategory) {
    transaction.patch(item._id, {
      set: {
        category: {
          _type: 'reference',
          _ref: generalCategory._id
        }
      }
    });

    console.log(`Adding "${item.title}" to transaction`);
  }

  try {
    console.log(`Committing transaction for ${itemsWithoutCategory.length} items...`);
    const result = await transaction.commit();
    console.log(`Transaction committed successfully. Updated ${result.documentIds.length} items.`);
  } catch (error) {
    console.error('Error committing transaction:', error);
  }
}

// Main function
async function main() {
  console.log('Starting category assignment...');

  // Fetch all gallery items and categories
  const [galleryItems, categories] = await Promise.all([
    fetchGalleryItems(),
    fetchCategories()
  ]);

  // Assign categories to gallery items
  await assignCategories(galleryItems, categories);

  console.log('\nProcess complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
