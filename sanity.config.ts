'use client'

/**
 * This configuration is used to for the Sanity Studio that’s mounted on the `/app/studio/[[...tool]]/page.tsx` route
 */

import {visionTool} from '@sanity/vision'
import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {seoMetaFields} from 'sanity-plugin-seo'

// Go to https://www.sanity.io/docs/api-versioning to learn how API versioning works
import {apiVersion, dataset, projectId} from './sanity/env'
import {schema} from './sanity/schemas'
import {structure} from './sanity/structure'

export default defineConfig({
  basePath: '/studio',
  projectId,
  dataset,
  // Add and edit the content schema in the './sanity/schemas' folder
  schema,
  plugins: [
    structureTool({
      structure,
      // Force studio to use the structure even if schema types are not found initially
      defaultDocumentNode: (S) => S.document(),
    }),
    // Vision is for querying with GROQ from inside the Studio
    // https://www.sanity.io/docs/the-vision-plugin
    visionTool({defaultApiVersion: apiVersion}),
    // SEO plugin for better search engine optimization
    seoMetaFields(),
  ],
  // Enable Sanity Studio features
  studio: {
    components: {
      // Logo component can be customized here
    }
  },
  // Set structure as the default tool when opening Sanity Studio
  defaultToolName: 'structure',
})
