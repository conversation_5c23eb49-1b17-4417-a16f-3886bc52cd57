'use client';

import { motion } from 'framer-motion';
import { FeaturedContentProps } from '../types';
import Image from 'next/image';
import Link from 'next/link';
import { LucideIcon } from 'lucide-react';
import * as LucideIcons from 'lucide-react';
import { urlFor } from '@/lib/sanity.client';

export default function FeaturedContent({
  heading,
  description,
  items = [],
  layout = 'cards',
  backgroundStyle = 'none'
}: FeaturedContentProps) {
  // Helper function to safely get image URL
  const getImageUrl = (image: any): string => {
    if (!image) return '/placeholder.jpg';
    try {
      const imageUrl = urlFor(image).url();
      return imageUrl;
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '/placeholder.jpg';
    }
  };

  // Background style classes
  const bgClasses = {
    none: 'bg-white',
    light: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    royalBlue: 'bg-[#001a4d] text-white',
    royalGold: 'bg-amber-700 text-white',
    ivory: 'bg-[#f9f7e8]',
  };

  // Determine grid layout based on display style
  const gridLayout = {
    cards: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    list: 'grid-cols-1',
    grid: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
  };

  // Get Lucide icon by name
  const getIcon = (iconName: string): React.ReactNode => {
    // LucideIcons is a module, so icons are available as LucideIcons[iconName] but type is unknown.
    const Icon = (LucideIcons as Record<string, any>)[iconName] as LucideIcon | undefined;
    if (!Icon) {
      console.warn(`Icon not found: ${iconName}`);
      return null;
    }
    return <Icon className="h-6 w-6" />;
  };


  return (
    <div className={`py-16 ${bgClasses[backgroundStyle]}`}>
      <div className="container mx-auto px-4">
        {/* Heading */}
        {heading && (
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {heading}
          </motion.h2>
        )}
        
        {/* Description */}
        {description && (
          <motion.p
            className="text-lg text-center max-w-3xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {description}
          </motion.p>
        )}
        
        {/* Featured Items */}
        {items.length > 0 ? (
          <motion.div
            className={`grid ${gridLayout[layout]} gap-8`}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            {items.map((item, index) => (
              <motion.div
                key={item._key || index}
                className={`feature-item ${
                  layout === 'cards' 
                    ? 'bg-white rounded-lg shadow-lg overflow-hidden' 
                    : layout === 'list'
                    ? 'flex items-start gap-4 p-4 bg-white/50 rounded-lg'
                    : 'text-center p-6'
                }`}
                whileHover={{ y: -5, transition: { duration: 0.3 } }}
                transition={{ delay: index * 0.1 }}
              >
                {/* Card Layout */}
                {layout === 'cards' && (
                  <>
                    {item.image && (
                      <div className="relative h-48 overflow-hidden">
                        <Image
                          src={getImageUrl(item.image)}
                          alt={item.title}
                          className="w-full h-full object-cover"
                          width={400}
                          height={300}
                        />
                      </div>
                    )}
                    <div className="p-6">
                      {item.icon && (
                        <div className="mb-4 text-royalBlue">
                          {getIcon(item.icon)}
                        </div>
                      )}
                      <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                      {item.description && (
                        <p className="text-gray-600 mb-4">{item.description}</p>
                      )}
                      {item.link && (
                        <Link 
                          href={item.link} 
                          className="text-royalBlue font-medium hover:underline"
                        >
                          {item.linkText || 'Learn More'}
                        </Link>
                      )}
                    </div>
                  </>
                )}
                
                {/* List Layout */}
                {layout === 'list' && (
                  <>
                    {(item.icon || item.image) && (
                      <div className="flex-shrink-0">
                        {item.icon ? (
                          <div className="w-12 h-12 rounded-full bg-royalBlue/10 flex items-center justify-center text-royalBlue">
                            {getIcon(item.icon)}
                          </div>
                        ) : item.image ? (
                          <div className="w-16 h-16 rounded-full overflow-hidden">
                            <Image
                              src={getImageUrl(item.image)}
                              alt={item.title}
                              className="w-full h-full object-cover"
                              width={64}
                              height={64}
                            />
                          </div>
                        ) : null}
                      </div>
                    )}
                    <div>
                      <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                      {item.description && (
                        <p className="text-gray-600 mb-2">{item.description}</p>
                      )}
                      {item.link && (
                        <Link 
                          href={item.link} 
                          className="text-royalBlue font-medium hover:underline"
                        >
                          {item.linkText || 'Learn More'}
                        </Link>
                      )}
                    </div>
                  </>
                )}
                
                {/* Grid Layout */}
                {layout === 'grid' && (
                  <>
                    {item.icon && (
                      <div className="mb-4 mx-auto w-16 h-16 rounded-full bg-royalBlue/10 flex items-center justify-center text-royalBlue">
                        {getIcon(item.icon)}
                      </div>
                    )}
                    {item.image && !item.icon && (
                      <div className="mb-4 mx-auto w-20 h-20 rounded-full overflow-hidden">
                        <Image
                          src={getImageUrl(item.image)}
                          alt={item.title}
                          className="w-full h-full object-cover"
                          width={80}
                          height={80}
                        />
                      </div>
                    )}
                    <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                    {item.description && (
                      <p className="text-gray-600 mb-4">{item.description}</p>
                    )}
                    {item.link && (
                      <Link 
                        href={item.link} 
                        className="text-royalBlue font-medium hover:underline"
                      >
                        {item.linkText || 'Learn More'}
                      </Link>
                    )}
                  </>
                )}
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="text-center py-10">
            <p className="text-gray-500">No featured items found</p>
          </div>
        )}
      </div>
    </div>
  );
}
