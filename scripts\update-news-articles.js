// This script updates existing news articles in the Sanity dataset with complete content
require('dotenv').config();
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Array of news articles to update with complete content
const newsArticles = [
  {
    title: "The Power of a Resource-Based Economy: A Vision for Africa's Future",
    slug: "the-power-of-a-resource-based-economy-a-vision-for-africas-future",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Africa is a continent blessed with abundant natural resources — minerals, oil, arable land, and biodiversity. Yet, despite this wealth, many African nations continue to grapple with poverty, underdevelopment, and economic instability. The disconnect between the continent's natural wealth and the quality of life of its people underscores a crucial question: how can Africa's resources be leveraged to create a prosperous, self-sustaining economy that improves the quality of life for all its citizens?"
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "Harnessing Africa's Resources for Economic Empowerment"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "A resource-based economy, when strategically managed, can be the cornerstone of Africa's economic transformation. By shifting the focus from mere extraction to value-added processing and local development, African nations can unlock immense economic potential. This approach involves not just exporting raw materials, but also investing in the infrastructure, technology, and human capital needed to process and refine these resources within the continent."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "For instance, establishing mineral refineries and depository facilities in resource-rich nations like Uganda can keep more of the value chain within Africa. This not only creates jobs but also fosters the development of local industries, from manufacturing to technology. By monetizing and tokenizing these resources, Africa can attract global investment while ensuring that the wealth generated benefits the local economy."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "Building a Resilient Agricultural Sector"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Agriculture is another key pillar of a resource-based economy. With its vast tracts of fertile land, Africa has the potential to not only feed its population but also become a global breadbasket. To realize this potential, African nations must invest in sustainable farming practices that increase productivity while preserving the environment."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Developing farm-to-table initiatives can combat food insecurity by ensuring that communities have access to nutritious, locally grown food. Additionally, these initiatives can create jobs, promote agricultural innovation, and foster economic independence. By focusing on crops that are both nutritious and suitable for export, Africa can also boost its position in global markets."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "Integrating Technology and Innovation"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The integration of digital technology is critical to modernizing Africa's resource-based economy. From blockchain technology for secure and transparent transactions to satellite imaging for resource assessment, technology can optimize the management and utilization of resources."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "For example, using satellite x-rays to assess inground mineral reserves can provide accurate data that informs sustainable extraction practices. This technology not only enhances efficiency but also minimizes environmental impact, ensuring that resource extraction does not come at the cost of Africa's rich natural heritage."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "Promoting Ecological Sustainability"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Sustainable development is essential for the long-term success of a resource-based economy. Africa's natural resources, particularly its biodiversity, are not just economic assets but also integral to the continent's identity and future. Initiatives like establishing nature reserves and wildlife conservatories can protect these resources while promoting eco-tourism."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Eco-tourism, in turn, can generate revenue, create jobs, and raise awareness about the importance of conservation. By balancing economic development with environmental stewardship, Africa can preserve its natural beauty for future generations while also enhancing its global standing."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "The Role of Leadership and Governance"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Effective leadership and governance are crucial in transforming Africa's resource wealth into widespread prosperity. Governments must foster an environment of transparency, accountability, and inclusivity. This includes ensuring that the benefits of resource extraction are equitably distributed and that local communities are actively involved in decision-making processes."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Collaboration between governments, private enterprises, and international partners is also essential. By working together, these stakeholders can create a unified strategy that maximizes the benefits of a resource-based economy while addressing challenges such as corruption and mismanagement."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "A Royal Vision for Africa's Future"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "As someone who has been entrusted with both business leadership and royal responsibilities, I see a unique opportunity to usher in a new era of prosperity for Africa. My vision for Africa's future is one where our natural resources are the foundation of a thriving economy that uplifts all citizens. By leveraging our resources wisely, integrating technology, and prioritizing sustainable practices, we can create a resilient, prosperous Africa that stands tall on the global stage."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Africa's future is bright, but it requires bold, visionary leadership to turn potential into reality. By embracing a resource-based economy, we can not only improve the quality of life for millions of Africans but also create a legacy of sustainable development that future generations will be proud of."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The time to act is now. Together, we can harness Africa's natural wealth to build a brighter, more prosperous future for all."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "About Prince Allen Ellison"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Allen Ellison is a multifaceted individual whose dynamic career spans business, politics, international development, and entertainment. At 44 years old, he brings a unique blend of experiences and talents to his work, making him a visionary leader in various sectors."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "As the President & CEO of Remit Global Inc., Allen has revolutionized cross-border transactions and financial services on a global scale. Under his leadership, Remit Global has become a prominent player in the industry, known for its innovative solutions and commitment to financial inclusion. Allen's business acumen is further demonstrated by his role as the founder of Chez Remit, Inc. in Canada, where he continues to expand his global business footprint."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Allen's influence extends beyond the corporate world. He is the Chairman of the Board of Directors of the Ellison Outreach Foundation, Inc., a 501(c)3 not-for-profit organization dedicated to positively impacting the lives of families and communities worldwide. The foundation's initiatives, including programs focused on financial literacy, mentorship, and scholarships, reflect Allen's dedication to uplifting underserved populations and driving sustainable development."
          }
        ]
      }
    ]
  },
  {
    title: "Ellison Outreach Foundation, Inc. Sponsors Three Schools in Ghana's Eastern Region",
    slug: "ellison-outreach-foundation-sponsors-three-schools-in-ghana",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "August 31, 2024 — The Ellison Outreach Foundation, Inc. is pleased to announce the successful sponsorship of school supplies for three senior high schools in the Eastern Region of Ghana. This initiative was made possible by the generous donation from the John J. and Katherine Jenkins Estate which is under the John and Katherine Jenkins Humanitarian Fund and is part of the foundation's broader mission to support education and empower students in underserved communities."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h3',
        children: [
          {
            _type: 'span',
            text: "Sponsored Schools:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "NIFA Senior High School - P.O. Box 27, Adukrom-Akuapem, Eastern Region, Ghana"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Presbyterian Senior High School - P.O. Box 58, Adukrom-Akuapem, Eastern Region, Ghana"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "J.G. Knol Technical Institute - P.O. Box RD 39, Adukrom-Akuapem, Eastern Region, Ghana"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The donation of essential school supplies will ensure that students in these institutions have the necessary tools to succeed academically, thereby enhancing the overall quality of education in the region."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"Education is the cornerstone of a thriving community, and we are committed to supporting the academic journey of students in the Eastern Region of Ghana. By providing these supplies, we hope to alleviate some of the challenges faced by students and encourage them to pursue their studies with confidence,\" said Prince Allen Ellison, Chairman of the Ellison Outreach Foundation."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"I am deeply grateful to the Ellison Outreach Foundation for their generous support of our students. Education is the key to unlocking the potential of our young people, and these school supplies will play a crucial role in their academic success. This contribution reflects the foundation's commitment to empowering our future leaders and strengthening the fabric of our community.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'h3',
        children: [
          {
            _type: 'span',
            text: "About the Ellison Outreach Foundation:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The Ellison Outreach Foundation, Inc. is a 501(c)(3) not-for-profit organization dedicated to uplifting communities through education, economic empowerment, and sustainable development. With a focus on underserved populations, the foundation implements programs that foster resilience, prosperity, and positive change worldwide."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h3',
        children: [
          {
            _type: 'span',
            text: "Future Sponsorship Plans:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The foundation plans to extend its support to additional schools in Adukrom, including:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "1. Nifa Primary and KG, Box 27, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "2. Community JHS, Box 1, GES Office, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "3. Nifa Primary and JHS, Box 27, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "4. Adukrom Presby Primary, Box 2, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "5. Bethel Presby JHS, Box 2, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "6. Methodist Primary School, Box 1, GES Office, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "7. Methodist JHS, Box 1, GES Office, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "8. Resettlement Presby Primary, Box 2, Adukrom"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "9. Adukrom Presby JHS, Box 2, Adukrom"
          }
        ]
      }
    ]
  }
];

// Function to update a news article
async function updateNewsArticle(article) {
  try {
    console.log(`Updating article: ${article.title}`);

    // Find the article by slug
    const query = `*[_type == "news" && slug.current == $slug][0]._id`;
    const articleId = await client.fetch(query, { slug: article.slug });

    if (!articleId) {
      console.error(`Article with slug "${article.slug}" not found.`);
      return null;
    }

    // Update the article
    const updatedArticle = await client
      .patch(articleId)
      .set({ body: article.body })
      .commit();

    console.log(`Updated article with ID: ${updatedArticle._id}`);
    return updatedArticle;
  } catch (error) {
    console.error(`Error updating article "${article.title}":`, error);
    return null;
  }
}

// Main function to update all articles
async function updateAllArticles() {
  console.log('Starting to update news articles...');

  for (const article of newsArticles) {
    await updateNewsArticle(article);
  }

  console.log('Finished updating news articles.');
}

// Run the main function
updateAllArticles().catch(console.error);
