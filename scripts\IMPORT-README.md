# Blog Content Import Script

This script imports blog articles and images from an external website into your Sanity database.

## Overview

The script performs the following actions:

1. Fetches the blog index page from the specified URL
2. Extracts links to individual article pages
3. For each article:
   - Extracts the title, date, content, and images
   - Downloads all images
   - Uploads images to Sanity
   - Converts HTML content to Portable Text format
   - Creates or updates the article in Sanity

## Prerequisites

Before running the script, you need to install the required dependencies:

```bash
npm install axios cheerio form-data dotenv
```

You also need to set up the following environment variables in your `.env.local` file:

```
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_write_token
```

Make sure your Sanity API token has write permissions.

## Usage

To run the script:

```bash
node scripts/import-blog-content.js
```

## Configuration

You can modify the configuration in the script:

```javascript
const config = {
  sourceUrl: 'https://cil.xem.mybluehost.me/website_963da4e6/blog/',
  tempDir: path.join(__dirname, '../temp'),
  imageDir: path.join(__dirname, '../temp/images'),
};
```

- `sourceUrl`: The URL of the blog index page
- `tempDir`: Temporary directory for downloaded files
- `imageDir`: Directory for downloaded images

## Troubleshooting

### Access Issues

If you're having trouble accessing the source website, you might need to:

1. Check if the website is accessible from your current network
2. Try using a VPN or proxy
3. Check if the website requires authentication

### HTML Structure

The script uses CSS selectors to extract content from the HTML. If the extraction is not working correctly, you might need to adjust the selectors in the script:

```javascript
// In extractArticleLinks function
$('a.article-link, .post-title a, .entry-title a').each((i, el) => {
  // ...
});

// In extractArticleData function
const title = $('.post-title, .entry-title').first().text().trim();
const date = $('.post-date, .entry-date').first().text().trim();
const content = $('.post-content, .entry-content').html();
```

### Manual Import

If the automatic import doesn't work, you can:

1. Manually download the articles and images
2. Use the Sanity Studio interface to create the articles
3. Upload the images through the Sanity Studio interface

## Alternative Approach

If the script doesn't work due to website access issues, you can try:

1. Contact the website owner for a data export
2. Use a browser extension to save the pages locally
3. Use a different web scraping tool or service

## After Import

After importing the content:

1. Check the imported articles in Sanity Studio
2. Verify that images are displaying correctly
3. Make any necessary adjustments to the content
4. Publish the articles if they're not already published
