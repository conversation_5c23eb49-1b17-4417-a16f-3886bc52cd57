'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { TextSectionProps } from '../types';
import { urlFor } from '@/lib/sanity.client';
import { PortableText } from 'next-sanity';

export default function TextSection({ 
  heading, 
  text, 
  backgroundStyle = 'none',
  textAlign = 'left',
  image,
  imagePosition = 'none'
}: TextSectionProps) {
  // Helper function to safely get image URL
  const getImageUrl = (image: any): string => {
    if (!image) return '';
    try {
      const imageUrl = urlFor(image).url();
      return imageUrl;
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '';
    }
  };


  // Background style classes
  const bgClasses = {
    none: 'bg-white',
    light: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    royalBlue: 'bg-[#001a4d] text-white',
    royalGold: 'bg-amber-700 text-white',
    ivory: 'bg-[#f9f7e8]',
  };

  // Text alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  // Determine layout based on image position
  const hasImage = image && imagePosition !== 'none';
  const isBackgroundImage = imagePosition === 'background';
  
  return (
    <div 
      className={`py-16 ${bgClasses[backgroundStyle]} relative overflow-hidden`}
      style={isBackgroundImage ? {
        backgroundImage: `url(${getImageUrl(image)})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      } : {}}
    >
      {/* Overlay for background image */}
      {isBackgroundImage && (
        <div className={`absolute inset-0 ${
          backgroundStyle === 'dark' || backgroundStyle === 'royalBlue' || backgroundStyle === 'royalGold'
            ? 'bg-black/70'
            : 'bg-white/70'
        }`}></div>
      )}
      
      <div className="container mx-auto px-4 relative z-10">
        <div className={`${hasImage && !isBackgroundImage ? 'grid md:grid-cols-2 gap-12 items-center' : ''}`}>
          {/* Image (if not background and position is left) */}
          {hasImage && imagePosition === 'left' && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="rounded-lg overflow-hidden shadow-lg"
            >
              <Image 
                src={getImageUrl(image)} 
                alt={heading || 'Section image'} 
                width={600}
                height={400}
                className="w-full h-auto object-cover"
              />
            </motion.div>
          )}
          
          {/* Content */}
          <div className={alignClasses[textAlign]}>
            {heading && (
              <motion.h2
                className="text-3xl md:text-4xl font-bold mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                {heading}
              </motion.h2>
            )}
            
            {text && (
              <motion.div
                className="prose max-w-none"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <PortableText value={text} />
              </motion.div>
            )}
          </div>
          
          {/* Image (if not background and position is right) */}
          {hasImage && imagePosition === 'right' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="rounded-lg overflow-hidden shadow-lg"
            >
              <Image 
                src={getImageUrl(image)} 
                alt={heading || 'Section image'} 
                width={600}
                height={400}
                className="w-full h-auto object-cover"
              />
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
