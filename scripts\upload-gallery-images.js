/**
 * <PERSON><PERSON><PERSON> to upload gallery images from public/Website Images to Sanity
 * 
 * This script:
 * 1. Reads all images from public/Website Images
 * 2. Uploads them to Sanity as assets
 * 3. Creates a gallery document with references to the uploaded images
 * 
 * Run with: node scripts/upload-gallery-images.js
 */

// Import required modules
const fs = require('fs');
const path = require('path');
const sanityClient = require('@sanity/client');
const { createReadStream } = require('fs');

// Configure Sanity client
const client = sanityClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Source directory for images
const sourceDir = path.join(__dirname, '../public/Website Images');

// Function to upload a single image to Sanity
async function uploadImageToSanity(filePath, fileName) {
  try {
    console.log(`Uploading ${fileName}...`);
    
    // Create a read stream for the file
    const stream = createReadStream(filePath);
    
    // Upload the file to Sanity
    const asset = await client.assets.upload('image', stream, {
      filename: fileName,
    });
    
    console.log(`Uploaded ${fileName} successfully!`);
    return asset;
  } catch (error) {
    console.error(`Error uploading ${fileName}:`, error);
    return null;
  }
}

// Function to create a gallery document with the uploaded images
async function createGalleryDocument(images) {
  try {
    console.log('Creating gallery document...');
    
    // Create the gallery document
    const gallery = {
      _type: 'gallery',
      title: 'Kingdom of Adukrom Gallery',
      slug: {
        _type: 'slug',
        current: 'gallery',
      },
      description: 'Official gallery of the Kingdom of Adukrom',
      images: images.map((image, index) => ({
        _key: `image${index}`,
        image: {
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: image._id,
          },
        },
        caption: image.originalFilename.replace(/[-_]/g, ' ').replace(/\.\w+$/, ''),
        alt: image.originalFilename.replace(/[-_]/g, ' ').replace(/\.\w+$/, ''),
      })),
      displayStyle: 'grid',
      backgroundStyle: 'none',
      animation: {
        duration: 0.6,
        delay: 0,
        stagger: 0.2,
        type: 'spring',
      },
      publishedAt: new Date().toISOString(),
    };
    
    // Check if gallery document already exists
    const existingGallery = await client.fetch('*[_type == "gallery" && slug.current == "gallery"][0]');
    
    if (existingGallery) {
      console.log('Gallery document already exists, updating...');
      await client
        .patch(existingGallery._id)
        .set({
          images: gallery.images,
          animation: gallery.animation,
          displayStyle: gallery.displayStyle,
          backgroundStyle: gallery.backgroundStyle,
          description: gallery.description,
        })
        .commit();
      console.log('Gallery document updated successfully!');
    } else {
      console.log('Creating new gallery document...');
      await client.create(gallery);
      console.log('Gallery document created successfully!');
    }
  } catch (error) {
    console.error('Error creating gallery document:', error);
  }
}

// Main function to upload all images and create the gallery document
async function uploadGalleryImages() {
  try {
    console.log('Starting gallery image upload...');
    
    // Read all files from the source directory
    const files = fs.readdirSync(sourceDir);
    
    // Filter image files (jpg, jpeg, png, webp, gif)
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext);
    });
    
    console.log(`Found ${imageFiles.length} image files to upload.`);
    
    // Upload each image to Sanity
    const uploadedImages = [];
    for (const file of imageFiles) {
      const filePath = path.join(sourceDir, file);
      const asset = await uploadImageToSanity(filePath, file);
      if (asset) {
        uploadedImages.push(asset);
      }
    }
    
    console.log(`Successfully uploaded ${uploadedImages.length} images.`);
    
    // Create the gallery document with the uploaded images
    await createGalleryDocument(uploadedImages);
    
    console.log('Gallery migration completed successfully!');
  } catch (error) {
    console.error('Error uploading gallery images:', error);
  }
}

// Run the script
uploadGalleryImages();
