'use client';

import { PortableText as SanityPortableText } from '@portabletext/react';
import { urlFor } from '@/lib/sanity';
import Image from 'next/image';
import Link from 'next/link';

// Define custom components for the PortableText renderer
const components = {
  types: {
    image: ({ value }: any) => {
      if (!value?.asset?._ref) {
        return null;
      }

      return (
        <div className="relative w-full my-6 overflow-hidden rounded-lg">
          <Image
            src={urlFor(value).url()}
            alt={value.alt || 'Image'}
            width={800}
            height={500}
            className="object-cover w-full"
          />
          {value.caption && (
            <div className="p-2 text-sm text-center text-gray-600">
              {value.caption}
            </div>
          )}
        </div>
      );
    },
  },
  marks: {
    link: ({ children, value }: any) => {
      const rel = !value.href.startsWith('/') ? 'noreferrer noopener' : undefined;
      const target = !value.href.startsWith('/') ? '_blank' : undefined;

      return (
        <Link
          href={value.href}
          rel={rel}
          target={target}
          className="text-royalBlue hover:text-royalGold underline transition-colors"
        >
          {children}
        </Link>
      );
    },
  },
  block: {
    h1: ({ children }: any) => (
      <h1 className="text-3xl font-bold mt-8 mb-4">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-2xl font-bold mt-6 mb-3">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-xl font-bold mt-5 mb-2">{children}</h3>
    ),
    h4: ({ children }: any) => (
      <h4 className="text-lg font-bold mt-4 mb-2">{children}</h4>
    ),
    normal: ({ children }: any) => (
      <p className="mb-4 leading-relaxed">{children}</p>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="pl-4 border-l-4 border-royalGold italic my-6">
        {children}
      </blockquote>
    ),
  },
  list: {
    bullet: ({ children }: any) => (
      <ul className="list-disc pl-6 mb-4 space-y-1">{children}</ul>
    ),
    number: ({ children }: any) => (
      <ol className="list-decimal pl-6 mb-4 space-y-1">{children}</ol>
    ),
  },
  listItem: {
    bullet: ({ children }: any) => <li>{children}</li>,
    number: ({ children }: any) => <li>{children}</li>,
  },
};

interface PortableTextProps {
  value: any;
  className?: string;
}

export default function PortableText({ value, className = '' }: PortableTextProps) {
  if (!value) {
    console.warn('PortableText: No value provided');
    return <div className={className}>No content available.</div>;
  }

  // Add error handling for different types of content
  if (!Array.isArray(value)) {
    console.warn('PortableText: Expected an array but got:', typeof value);
    return (
      <div className={className}>
        {typeof value === 'string' ? value : 'Content is not in the expected format.'}
      </div>
    );
  }

  if (value.length === 0) {
    console.warn('PortableText: Empty array provided');
    return <div className={className}>No content available.</div>;
  }

  try {
    return (
      <div className={className}>
        <SanityPortableText value={value} components={components} />
      </div>
    );
  } catch (error) {
    console.error('Error rendering PortableText:', error);
    return (
      <div className={className}>
        <p>There was an error displaying this content.</p>
      </div>
    );
  }
}
