'use client';

import { useState, useEffect } from 'react';
import { urlFor } from '@/lib/sanity';
import Link from 'next/link';
import Image from 'next/image';

// Initial data to avoid loading state
interface LiveNewsProps {
  initialData: any[];
}

export default function LiveNews({ initialData }: LiveNewsProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [news, setNews] = useState(initialData);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null; // Return null on server-side and first render
  }

  return (
    <div className="bg-royalBlue py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-12">
          Latest News
          <div className="w-48 h-1 bg-royalGold mt-2"></div>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {news.map((item) => (
            <Link
              key={item._id}
              href={`/news/${item.slug.current}`}
              className="group"
            >
              <div className="bg-white rounded-lg overflow-hidden shadow-lg transition-transform duration-300 group-hover:scale-105">
                <div className="relative h-48">
                  <Image
                    src={urlFor(item.mainImage).url()}
                    alt={item.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-royalBlue mb-2 group-hover:text-royalGold transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 line-clamp-3">
                    {item.excerpt}
                  </p>
                  <div className="mt-4 flex items-center text-sm text-gray-500">
                    <span>{new Date(item.publishedAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
