'use client';
import { motion } from 'framer-motion';
import ScrollReveal from './ScrollReveal';
import FloatingParticles from './FloatingParticles';


export default function Contact() {
    const contactInfo = [
      {
        title: 'Address',
        value: 'Anunkode Royal Palace',
        description: 'P.O. Box 1 Adukron-Akuapem Ghana, West Africa',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        ),
      },
      {
        title: 'Phone',
        value: '+****************',
        description: '',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        ),
      },
      {
        title: 'Fax',
        value: '+****************',
        description: '',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        ),
      },
      {
        title: 'Email',
        value: '<EMAIL>',
        description: '',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        ),
      },
    ];

    const socialLinks = [
      {
        platform: 'Facebook',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
          </svg>
        ),
      },
      {
        platform: 'Instagram',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" />
          </svg>
        ),
      },
      {
        platform: 'Twitter',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
          </svg>
        ),
      },
      {
        platform: 'YouTube',
        icon: (
          <svg className="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
          </svg>
        ),
      },
    ];

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const form = e.currentTarget;
    const formData = new FormData(form);

    const name = formData.get('contactName') as string;
    const email = formData.get('contactEmail') as string;
    const subject = formData.get('contactSubject') as string;
    const message = formData.get('contactMessage') as string;

    try {
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'contact',
          name,
          email,
          subject,
          message,
        }),
      });

      const data = await response.json();

      if (data.success) {
        alert('Your message has been sent successfully!');
        form.reset();
      } else {
        alert('Failed to send message. Please try again later.');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('An error occurred. Please try again later.');
    }
  };

  return (
      <section id="contact" className="py-20 royal-gradient relative overflow-hidden">
        <FloatingParticles
          count={20}
          colors={['#D4AF37', '#FFFFFF', '#AD8A56']}
          minSize={3}
          maxSize={8}
        />

        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal animation="fadeInDown">
            <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-6 section-title">
              Contact The Royal Palace of Akuapem Nifahene
            </h2>
            <p className="text-center text-white/90 max-w-3xl mx-auto mb-4">
              Where Connections Forge Legacy and Opportunity
            </p>
            <p className="text-center text-royalGold max-w-3xl mx-auto mb-16">
              The gates of the Kingdom are open to meaningful dialogue, collaboration, and global impact.
            </p>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <ScrollReveal animation="fadeInLeft">
              <motion.div
                className="bg-ivory rounded-lg p-8 shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                whileHover={{ boxShadow: "0 20px 25px rgba(0, 0, 0, 0.2)" }}
              >
                <motion.h3
                  className="text-2xl font-bold text-royalBlue mb-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  Get in Touch
                </motion.h3>
                <motion.p
                  className="text-charcoal/80 mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.25 }}
                >
                  This is more than a point of contact. It is your invitation to engage with a Kingdom where tradition empowers transformation, and where every connection contributes to shaping Africa's prosperous future.
                </motion.p>

                <form id="contact-form" className="space-y-6" onSubmit={handleSubmit}>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <label htmlFor="contactName" className="block text-charcoal font-medium mb-2">
                      Full Name*
                    </label>
                    <motion.input
                      type="text"
                      id="contactName"
                      name="contactName"
                      required
                      className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                      whileFocus={{ scale: 1.01, borderColor: "#D4AF37" }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <label htmlFor="contactEmail" className="block text-charcoal font-medium mb-2">
                      Email Address*
                    </label>
                    <motion.input
                      type="email"
                      id="contactEmail"
                      name="contactEmail"
                      required
                      className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                      whileFocus={{ scale: 1.01, borderColor: "#D4AF37" }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <label htmlFor="contactSubject" className="block text-charcoal font-medium mb-2">
                      Subject*
                    </label>
                    <motion.select
                      id="contactSubject"
                      name="contactSubject"
                      required
                      className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                      whileFocus={{ scale: 1.01, borderColor: "#D4AF37" }}
                    >
                      <option value="">Select a subject</option>
                      {[
                        'General Inquiry',
                        'Event Information',
                        'Partnership Opportunity',
                        'Media Request',
                        'Support',
                      ].map((subject) => (
                        <option key={subject} value={subject}>
                          {subject}
                        </option>
                      ))}
                    </motion.select>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <label htmlFor="contactMessage" className="block text-charcoal font-medium mb-2">
                      Message*
                    </label>
                    <motion.textarea
                      id="contactMessage"
                      name="contactMessage"
                      rows={4}
                      required
                      className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                      whileFocus={{ scale: 1.01, borderColor: "#D4AF37" }}
                    />
                  </motion.div>

                  <motion.div
                    className="text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <motion.button
                      type="submit"
                      className="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Send Message
                    </motion.button>
                  </motion.div>
                </form>
              </motion.div>
            </ScrollReveal>

            <ScrollReveal animation="fadeInRight">
              <div className="space-y-8">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + (index * 0.1) }}
                    whileHover={{ x: 5 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center flex-shrink-0"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      {info.icon}
                    </motion.div>
                    <div className="ml-4">
                      <h3 className="text-xl font-bold text-white">{info.title}</h3>
                      <p className="text-royalGold mt-1">{info.value}</p>
                      {info.description && <p className="text-white mt-2">{info.description}</p>}
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                className="mt-12 flex justify-center space-x-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                {socialLinks.map((link, index) => (
                  <motion.a
                    key={index}
                    href="#"
                    className="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center hover:bg-yellow-500 transition-colors"
                    whileHover={{ scale: 1.2, rotate: 10 }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.9 + (index * 0.1) }}
                  >
                    {link.icon}
                  </motion.a>
                ))}
              </motion.div>
            </ScrollReveal>
          </div>
        </div>
      </section>
    );
  }
