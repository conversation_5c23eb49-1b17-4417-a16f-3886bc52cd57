import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';
import { getWriteClient } from '@/lib/sanity.client';
import { logError } from '@/lib/errorHandling';

// GET /api/partners - Get all strategic partners
export async function GET(request: NextRequest) {
  try {
    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (clientError) {
      console.error('Error getting Sanity client:', clientError);

      // Use a read-only client as fallback
      const { readClient } = await import('@/lib/sanity.client');
      client = readClient;
      console.log('Using read-only client as fallback');
    }

    // Query to fetch all strategic partners
    const query = `*[_type == "strategicPartner"] | order(order asc) {
      _id,
      name,
      slug,
      description,
      website,
      logo,
      partnershipType,
      featured,
      order,
      startDate,
      active
    }`;

    // Fetch strategic partners from Sanity
    const partners = await client.fetch(query, {}, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    // Log the first partner for debugging
    if (partners && partners.length > 0) {
      console.log('First partner:', JSON.stringify(partners[0], null, 2));
    }

    return NextResponse.json({
      success: true,
      partners,
    });
  } catch (error) {
    console.error('Error fetching strategic partners:', error);
    logError(error, 'GET /api/partners');

    return NextResponse.json(
      { success: false, message: 'Failed to fetch strategic partners', error: String(error) },
      { status: 500 }
    );
  }
}

// POST /api/partners - Create a new strategic partner
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to create strategic partners' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.description || !body.partnershipType) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields: name, description, and partnershipType are required' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Create a slug if not provided
    if (!body.slug) {
      const slug = body.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      body.slug = {
        _type: 'slug',
        current: slug
      };
    }

    // Log the data being sent to Sanity
    console.log('Creating strategic partner with data:', {
      name: body.name,
      slug: body.slug,
      description: body.description,
      website: body.website,
      logo: body.logo ? 'Logo provided' : 'No logo',
      partnershipType: body.partnershipType,
      featured: body.featured || false,
      order: body.order || 0,
    });

    // Create the strategic partner document
    const partner = await client.create({
      _type: 'strategicPartner',
      name: body.name,
      slug: body.slug,
      description: body.description,
      website: body.website,
      logo: body.logo,
      partnershipType: body.partnershipType,
      featured: body.featured || false,
      order: body.order || 0,
      startDate: body.startDate,
      active: body.active !== undefined ? body.active : true,
    });

    console.log('Strategic partner created successfully:', {
      _id: partner._id,
      name: partner.name,
    });

    return NextResponse.json({
      success: true,
      message: 'Strategic partner created successfully',
      partner,
    });
  } catch (error) {
    console.error('Error creating strategic partner:', error);
    logError(error, 'POST /api/partners');

    return NextResponse.json(
      { success: false, message: 'Failed to create strategic partner', error: String(error) },
      { status: 500 }
    );
  }
}
