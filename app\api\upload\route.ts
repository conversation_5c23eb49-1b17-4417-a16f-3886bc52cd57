import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// POST /api/upload - Upload a file to Sanity
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to upload files' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Parse the form data
    const formData = await request.formData();

    // Check for 'file' or 'image' in the form data (for backward compatibility)
    const file = formData.get('file') as File || formData.get('image') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4'];
    const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: 'Invalid file type. Only JPEG, PNG, GIF, WebP images and MP4 videos are allowed.' },
        { status: 400 }
      );
    }

    // Check file size (limit to 50MB for videos, 5MB for images)
    const isVideo = allowedVideoTypes.includes(file.type);
    const maxSize = isVideo ? 50 * 1024 * 1024 : 5 * 1024 * 1024; // 50MB for videos, 5MB for images
    if (file.size > maxSize) {
      return NextResponse.json(
        {
          success: false,
          message: isVideo
            ? 'File too large. Maximum size for videos is 50MB.'
            : 'File too large. Maximum size for images is 5MB.'
        },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to Sanity - use 'file' for videos, 'image' for images
    const assetType = isVideo ? 'file' : 'image';
    console.log(`Uploading ${isVideo ? 'video' : 'image'}: ${file.name} (${file.size} bytes)`);

    const asset = await client.assets.upload(assetType, buffer, {
      filename: file.name,
      contentType: file.type,
    });

    console.log('Asset uploaded successfully:', {
      _id: asset._id,
      url: asset.url,
      assetType: assetType
    });

    return NextResponse.json({
      success: true,
      message: `${isVideo ? 'Video' : 'Image'} uploaded successfully`,
      asset,
      imageAsset: asset, // For compatibility with the news editor
      fileType: isVideo ? 'video' : 'image',
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to upload file',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
