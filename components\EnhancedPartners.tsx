'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getStrategicPartners } from '@/lib/sanity';
import { urlFor } from '@/lib/sanity.client';

// Define partner types with specific labels
const partnerTypeLabels: Record<string, string> = {
  'corporate': 'Strategic Financial Partner',
  'heritage': 'Heritage & Cultural Partner',
  'educational': 'Educational Development Partner',
  'technology': 'Innovation & Technology Partner',
  'investment': 'Strategic Investment Partner',
  'governance': 'Traditional Governance Partner',
  'ngo': 'Development Partner',
  'government': 'Government Partner',
  'media': 'Media Partner',
  'other': 'Strategic Partner'
};

interface Partner {
  _id: string;
  name: string;
  slug: { current: string };
  description: string;
  website?: string;
  logo?: any;
  partnershipType: string;
  featured?: boolean;
  order?: number;
  active?: boolean;
}

export default function EnhancedPartners() {
  const [allPartners, setAllPartners] = useState<Partner[]>([]);
  const [featuredPartners, setFeaturedPartners] = useState<Partner[]>([]);
  const [regularPartners, setRegularPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(true);

  // Helper function to safely get image URL
  const getImageUrl = (image: any): string => {
    if (!image) return '/placeholder.jpg';

    // If image is already a string (direct path), return it
    if (typeof image === 'string') {
      return image.startsWith('/') ? image : `/${image}`;
    }

    try {
      const imageUrl = urlFor(image).url();
      return imageUrl || '/placeholder.jpg';
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '/placeholder.jpg';
    }
  };

  useEffect(() => {
    const fetchPartners = async () => {
      try {
        console.log('Loading strategic partners...');
        const data = await getStrategicPartners();
        console.log('Fetched partners:', data);
        
        // Filter active partners
        const activePartners = data.filter((partner: Partner) => partner.active !== false);
        
        // Sort by order
        const sortedPartners = activePartners.sort((a: Partner, b: Partner) => 
          (a.order || 999) - (b.order || 999)
        );
        
        setAllPartners(sortedPartners);
        
        // Separate featured and regular partners
        const featured = sortedPartners.filter((partner: Partner) => partner.featured === true);
        const regular = sortedPartners.filter((partner: Partner) => partner.featured !== true);
        
        setFeaturedPartners(featured);
        setRegularPartners(regular);
      } catch (error) {
        console.error('Error fetching partners:', error);
        // Fallback data
        const fallbackPartners = [
          {
            _id: 'remit-global',
            name: 'Remit Global',
            slug: { current: 'remit-global' },
            description: 'Strategic Financial Partner',
            partnershipType: 'corporate',
            logo: '/Website Images/remit-global-logo.png',
            featured: true,
            order: 1
          },
          {
            _id: 'royal-lion',
            name: 'Royal Lion',
            slug: { current: 'royal-lion' },
            description: 'Heritage & Cultural Partner',
            partnershipType: 'heritage',
            logo: '/Website Images/royal_lion_logo.png',
            featured: true,
            order: 2
          },
          {
            _id: 'tef',
            name: 'TEF',
            slug: { current: 'tef' },
            description: 'Educational Development Partner',
            partnershipType: 'educational',
            logo: '/Website Images/tef-logo2-transparent.png',
            featured: true,
            order: 3
          },
          {
            _id: 'lightace-global',
            name: 'Lightace Global',
            slug: { current: 'lightace-global' },
            description: 'Innovation & Technology Partner',
            partnershipType: 'technology',
            logo: '/Website Images/lightace-global-logo.png',
            featured: false,
            order: 4
          },
          {
            _id: 'akuapem-nifaman-council',
            name: 'Akuapem Nifaman Council',
            slug: { current: 'akuapem-nifaman-council' },
            description: 'Traditional Governance Partner',
            partnershipType: 'governance',
            logo: '/Website Images/akuaapem_nifaman_council_logo.png',
            featured: false,
            order: 5
          }
        ];
        
        setAllPartners(fallbackPartners);
        setFeaturedPartners(fallbackPartners.filter(p => p.featured));
        setRegularPartners(fallbackPartners.filter(p => !p.featured));
      } finally {
        setLoading(false);
      }
    };

    fetchPartners();
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  if (loading) {
    return (
      <section id="partners" className="pt-0 pb-20 royal-gradient text-white" style={{zIndex: 10, position: 'relative', marginTop: '-1px'}}>
        {/* Decorative elements - removed top divider to eliminate gap */}
        <div className="absolute bottom-0 left-0 w-full h-8 bg-ivory" style={{ clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0 100%)' }}></div>
        
        <div className="container mx-auto px-4 pt-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">Strategic Partners of the Crown</h2>
            <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
            <p className="text-xl max-w-3xl mx-auto text-gray-200">
              At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
            </p>
          </div>
          
          {/* Featured Partners Loading Skeleton */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-white mb-8 text-center">Featured Partners</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-xl p-6 animate-pulse">
                  <div className="w-32 h-32 bg-gray-200/30 rounded-full mb-6 border-4 border-royalGold/50 shadow-lg"></div>
                  <div className="h-6 bg-gray-200/30 rounded w-32 mb-2"></div>
                  <div className="h-4 bg-gray-200/30 rounded w-40 mb-1"></div>
                  <div className="h-4 bg-gray-200/30 rounded w-24 mt-4"></div>
                </div>
              ))}
            </div>
          </div>
          
          {/* All Partners Loading Skeleton */}
          <div>
            <h3 className="text-2xl font-bold text-white mb-8 text-center">All Partners</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 max-w-6xl mx-auto">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-xl p-6 animate-pulse">
                  <div className="w-32 h-32 bg-gray-200/30 rounded-full mb-6 border-4 border-royalGold/50 shadow-lg"></div>
                  <div className="h-6 bg-gray-200/30 rounded w-32 mb-2"></div>
                  <div className="h-4 bg-gray-200/30 rounded w-40 mb-1"></div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="text-center mt-16">
            <div className="inline-flex items-center px-8 py-3 bg-royalGold/50 text-transparent rounded-full w-40 h-12 animate-pulse"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="partners" className="pt-0 pb-20 royal-gradient text-white" style={{zIndex: 10, position: 'relative', marginTop: '-1px'}}>
      {/* Decorative elements - removed top divider to eliminate gap */}
      <div className="absolute bottom-0 left-0 w-full h-8 bg-ivory" style={{ clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0 100%)' }}></div>
      
      <div className="container mx-auto px-4 pt-20">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-5xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Strategic Partners of the Crown
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-royalGold mx-auto mb-8"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.3 }}
          ></motion.div>
          <motion.p
            className="text-xl max-w-3xl mx-auto text-gray-200"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
          </motion.p>
        </div>
        
        {/* Featured Partners Section */}
        {featuredPartners.length > 0 && (
          <div className="mb-16">
            <motion.h3 
              className="text-2xl font-bold text-white mb-8 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              Featured Partners
            </motion.h3>
            
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {featuredPartners.map((partner, index) => (
                <motion.div
                  key={partner._id}
                  className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all duration-300 hover:shadow-xl"
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, boxShadow: "0 20px 25px rgba(0, 0, 0, 0.3)" }}
                >
                  {partner.logo ? (
                    <motion.div
                      className="relative w-36 h-36 mb-6 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-royalGold shadow-lg"
                      whileHover={{ rotate: 5 }}
                    >
                      <img
                        src={getImageUrl(partner.logo)}
                        alt={partner.name}
                        className="w-28 h-28 object-contain"
                      />
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-royalGold/10"></div>
                    </motion.div>
                  ) : (
                    <div className="w-36 h-36 mb-6 rounded-full bg-white flex items-center justify-center border-4 border-royalGold shadow-lg">
                      <span className="text-gray-400 text-3xl font-bold">{partner.name.charAt(0)}</span>
                    </div>
                  )}
                  <h3 className="text-xl font-bold text-white mb-2 text-center">{partner.name}</h3>
                  <p className="text-sm text-gray-300 text-center mb-4">
                    {partner.description || partnerTypeLabels[partner.partnershipType] || 'Strategic Partner'}
                  </p>
                  
                  {partner.website && (
                    <motion.a
                      href={partner.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-2 text-royalGold hover:text-white text-sm font-medium transition-colors flex items-center"
                      whileHover={{ x: 3 }}
                    >
                      Visit Website
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </motion.a>
                  )}
                </motion.div>
              ))}
            </motion.div>
          </div>
        )}
        
        {/* All Partners Section */}
        <div>
          <motion.h3 
            className="text-2xl font-bold text-white mb-8 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            All Partners
          </motion.h3>
          
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 max-w-6xl mx-auto"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {allPartners.map((partner, index) => (
              <motion.div
                key={partner._id}
                className="flex flex-col items-center bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all duration-300 hover:shadow-xl"
                variants={itemVariants}
                whileHover={{ scale: 1.05, boxShadow: "0 20px 25px rgba(0, 0, 0, 0.3)" }}
                transition={{ delay: 0.1 * (index % 5) }}
              >
                {partner.logo ? (
                  <motion.div
                    className="relative w-32 h-32 mb-6 rounded-full overflow-hidden bg-white flex items-center justify-center border-4 border-royalGold shadow-lg"
                    whileHover={{ rotate: 5 }}
                  >
                    <img
                      src={getImageUrl(partner.logo)}
                      alt={partner.name}
                      className="w-24 h-24 object-contain"
                    />
                  </motion.div>
                ) : (
                  <div className="w-32 h-32 mb-6 rounded-full bg-white flex items-center justify-center border-4 border-royalGold shadow-lg">
                    <span className="text-gray-400 text-xl font-bold">{partner.name.charAt(0)}</span>
                  </div>
                )}
                <h3 className="text-lg font-bold text-white mb-2 text-center">{partner.name}</h3>
                <p className="text-sm text-gray-300 text-center">
                  {partner.description || partnerTypeLabels[partner.partnershipType] || 'Strategic Partner'}
                </p>
                
                {partner.website && (
                  <motion.a
                    href={partner.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-4 text-royalGold hover:text-white text-sm font-medium transition-colors flex items-center"
                    whileHover={{ x: 3 }}
                  >
                    Visit Website
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </motion.a>
                )}
              </motion.div>
            ))}
          </motion.div>
        </div>
        
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <motion.a
            href="/partners"
            className="inline-flex items-center px-8 py-3 bg-royalGold text-royalBlue font-bold rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Partners
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
}
