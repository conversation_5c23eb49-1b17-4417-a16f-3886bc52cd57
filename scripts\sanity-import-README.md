# Sanity Data Import Scripts

This directory contains scripts to import data into your Sanity CMS.

## Prerequisites

Before running these scripts, make sure you have:

1. Node.js installed
2. A Sanity API token with write permissions
3. The correct environment variables set up

## Setting Up Environment Variables

Create a `.env.local` file in the root of your project with the following variables:

```
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_api_token
```

## Available Scripts

### 1. Add Strategic Partners

This script adds the strategic partners shown in the "Strategic Partners of the Crown" section to Sanity.

```bash
# Run the script
node scripts/add-strategic-partners.js
```

The script will:
- Check if partners already exist in Sanity
- Create new partners or update existing ones
- Set all partners as featured and active so they appear on the homepage

### 2. Add Partner Logos

This script adds logos to the strategic partners in Sanity.

```bash
# Run the script
node scripts/add-partner-logos.js
```

The script will:
- Find existing partners by slug
- Upload logo images to Sanity
- Update partner documents with logo references

### 3. Add Gallery Items

This script adds gallery items to Sanity for the Gallery section.

```bash
# Run the script
node scripts/add-gallery-items.js
```

The script will:
- Create categories if they don't exist
- Upload images to Sanity
- Create gallery items with the uploaded images
- Set all items as featured so they appear on the homepage

## Troubleshooting

If you encounter any issues:

1. Make sure your Sanity API token has write permissions
2. Check that the image paths in the scripts are correct
3. Verify that your Sanity schema has the correct document types (strategicPartner, gallery, category)

## After Running the Scripts

After running the scripts, you should see:

1. Strategic partners in the "Strategic Partners of the Crown" section on the homepage
2. Gallery items in the Gallery section on the homepage

You can then edit these items in the Sanity Studio as needed.

## Getting a Sanity API Token

1. Go to [manage.sanity.io](https://manage.sanity.io/)
2. Select your project
3. Go to API > Tokens
4. Click "Add API token"
5. Give it a name (e.g., "Import Scripts")
6. Select "Editor" or "Write" permissions
7. Copy the token and add it to your .env.local file
