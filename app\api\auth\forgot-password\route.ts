import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail } from '@/lib/users';
import { generateToken } from '@/lib/tokens';
import { sendEmail } from '@/lib/email';

// POST /api/auth/forgot-password - Request a password reset
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { email } = body;
    
    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Check if user exists
    const user = getUserByEmail(email);
    if (!user) {
      // For security reasons, don't reveal that the email doesn't exist
      // Just return success even though no email will be sent
      return NextResponse.json({
        success: true,
        message: 'If your email is registered, you will receive a password reset link',
      });
    }
    
    // Generate a password reset token
    const token = generateToken({
      type: 'reset-password',
      email,
      userId: user.id,
    }, '1h'); // Token expires in 1 hour
    
    // Create password reset link
    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${token}`;
    
    // Send password reset email
    try {
      await sendEmail({
        to: email,
        subject: 'Reset Your Password - Kingdom Adukrom',
        text: `You requested to reset your password. Please click the link below to reset your password:\n\n${resetLink}\n\nThis link will expire in 1 hour. If you didn't request this, please ignore this email.`,
        html: `
          <h1>Reset Your Password</h1>
          <p>You requested to reset your password for your Kingdom Adukrom account.</p>
          <p>Please click the button below to reset your password:</p>
          <p>
            <a href="${resetLink}" style="display: inline-block; background-color: #4F46E5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Reset Password
            </a>
          </p>
          <p>Or copy and paste this link into your browser:</p>
          <p>${resetLink}</p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this, please ignore this email.</p>
        `,
      });
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);
      // Continue even if email fails, just return success
    }
    
    return NextResponse.json({
      success: true,
      message: 'If your email is registered, you will receive a password reset link',
    });
  } catch (error) {
    console.error('Error during password reset request:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process password reset request' },
      { status: 500 }
    );
  }
}
