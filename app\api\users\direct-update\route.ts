import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs';
import path from 'node:path';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { getToken } from 'next-auth/jwt';

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// PUT /api/users/direct-update - Directly update user data in the JSON file
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Get user email from session
    const userEmail = session.user.email;
    console.log('User email from session:', userEmail);

    // Parse request body
    const body = await request.json();
    const { name } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { success: false, message: 'Name is required' },
        { status: 400 }
      );
    }

    try {
      // Read the users file directly
      const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

      // Find the user by email
      const userIndex = usersData.findIndex((user: any) =>
        user.email && user.email.toLowerCase() === userEmail.toLowerCase()
      );

      if (userIndex === -1) {
        console.error('User not found with email:', userEmail);
        return NextResponse.json(
          { success: false, message: 'User not found' },
          { status: 404 }
        );
      }

      console.log('Found user in JSON file:', {
        id: usersData[userIndex].id,
        email: usersData[userIndex].email,
        name: usersData[userIndex].name,
        sanityId: usersData[userIndex].sanityId
      });

      // Store the Sanity ID if it exists
      let sanityId = usersData[userIndex].sanityId;

      // Update the user's name
      usersData[userIndex].name = name;
      usersData[userIndex].updatedAt = new Date().toISOString();

      // Write the updated data back to the file
      fs.writeFileSync(usersFilePath, JSON.stringify(usersData, null, 2));

      // Also update user in Sanity
      try {
        // Get Sanity client
        const client = getWriteClient();

        console.log('Searching for user in Sanity with email:', usersData[userIndex].email);

        // Try to find the user by email
        let sanityUser = null;

        try {
          // Use a direct query to find the user by email
          sanityUser = await client.fetch(
            `*[_type == "adminUser" && email == $email][0]{_id, name, email, username, role}`,
            { email: usersData[userIndex].email }
          );

          console.log('Sanity user search result:', sanityUser);
        } catch (fetchError) {
          console.error('Error fetching user from Sanity:', fetchError);
        }

        if (sanityUser) {
          console.log(`Updating user in Sanity: ${sanityUser._id}`);
          console.log('New name to set in Sanity:', name);

          try {
            // Use a simple mutation
            const mutation = {
              patch: {
                id: sanityUser._id,
                set: {
                  name: name,
                  updatedAt: new Date().toISOString()
                }
              }
            };

            const result = await client.mutate([mutation]);
            console.log('Sanity update successful:', result);
          } catch (updateError) {
            console.error('Error updating user in Sanity:', updateError);
          }
        } else {
          console.warn(`User not found in Sanity: ${usersData[userIndex].email}`);

          // Try to create the user in Sanity if not found
          try {
            console.log('Attempting to create user in Sanity');

            const username = usersData[userIndex].email.split('@')[0];

            const doc = {
              _type: 'adminUser',
              name: name,
              email: usersData[userIndex].email,
              username: username,
              role: usersData[userIndex].role || 'super_admin',
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              hashedPassword: 'placeholder' // This is required by the schema but will be updated later
            };

            const createResult = await client.create(doc);
            console.log('User created in Sanity successfully:', createResult);
          } catch (createError) {
            console.error('Error creating user in Sanity:', createError);
          }
        }
      } catch (sanityError) {
        console.error('Error updating user in Sanity:', sanityError);
        // Don't fail the request if Sanity update fails
      }

      // Return success response
      return NextResponse.json({
        success: true,
        message: 'User name updated successfully',
        user: {
          id: usersData[userIndex].id,
          name: usersData[userIndex].name,
          email: usersData[userIndex].email,
          role: usersData[userIndex].role,
          updatedAt: usersData[userIndex].updatedAt
        }
      });
    } catch (error: any) {
      console.error('Error updating user name:', error);
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to update user name' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in direct-update API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user name' },
      { status: 500 }
    );
  }
}
