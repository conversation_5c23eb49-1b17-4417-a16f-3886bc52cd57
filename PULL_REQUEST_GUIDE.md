# Pull Request Guide for Kingdom Adukrom Website

This document provides instructions for creating a pull request to publish the changes we've made to the Kingdom Adukrom website.

## Changes Summary

The following changes have been made to the website:

1. **Strategic Partners Section**
   - Changed "Royal Gallery" to "Strategic Partners of the Crown"
   - Added description about the Kingdom's vision for prosperity, unity and global impact
   - Added 6 circular frames with strategic partner logos:
     - Remit Global (Strategic Financial Partner)
     - Royal Lion (Heritage & Cultural Partner)
     - TEF (Educational Development Partner)
     - Lightace Global (Innovation & Technology Partner)
     - <PERSON><PERSON>ur <PERSON> (Strategic Investment Partner)
     - Akuapem Nifaman Council (Traditional Governance Partner)

2. **Contact Page Updates**
   - Updated contact information for The Royal Palace of Akuapem Nifahene
   - Added text about connections forging legacy and opportunity
   - Updated address, phone numbers, and email

3. **Next.js Configuration Fixes**
   - Removed deprecated options from next.config.js
   - Updated images configuration to use the latest format
   - Created documentation about Next.js configuration updates

4. **Performance Optimizations**
   - Created scripts for optimizing the build process
   - Added tools for optimizing images and assets
   - Created a cache cleaning script to improve development performance
   - Added a fast development server option

5. **Bug Fixes**
   - Fixed apostrophe syntax errors causing build failures
   - Created a custom icons component to fix the "Module not found: Can't resolve 'lucide-react'" error
   - Updated all components to use the custom icons

## Creating a Pull Request

### Option 1: Using GitHub Web Interface

1. **Go to the Repository**
   - Visit https://github.com/joelgriiyo/kingdomadukrom

2. **Switch to the Branch**
   - Click on the branch dropdown (usually shows "main")
   - Select the "joel-updates" branch

3. **Create Pull Request**
   - Click on "Contribute" and then "Open pull request"
   - Review the changes
   - Click "Create pull request"

4. **Fill in Pull Request Details**
   - Title: "Strategic Partners and Website Improvements"
   - Description: Copy the changes summary from above
   - Click "Create pull request"

5. **Merge the Pull Request**
   - Once the pull request is created, click "Merge pull request"
   - Confirm the merge
   - Delete the branch (optional)

### Option 2: Using GitHub CLI

If you have GitHub CLI installed, you can create a pull request with the following command:

```bash
gh pr create --title "Strategic Partners and Website Improvements" --body "
## Strategic Partners and Website Improvements

This PR includes several important updates to the Kingdom Adukrom website:

### Strategic Partners Section
- Changed \"Royal Gallery\" to \"Strategic Partners of the Crown\"
- Added the description about the Kingdom's vision for prosperity, unity and global impact
- Added 6 circular frames with strategic partner logos

### Contact Page Updates
- Updated the contact information with the new details for The Royal Palace of Akuapem Nifahene
- Added the text about connections forging legacy and opportunity
- Updated the address, phone numbers, and email

### Next.js Configuration Fixes
- Removed deprecated options from next.config.js
- Updated the images configuration to use the latest format
- Created documentation about the Next.js configuration updates

### Performance Optimizations
- Created scripts for optimizing the build process
- Added tools for optimizing images and assets
- Created a cache cleaning script to improve development performance
- Added a fast development server option

### Bug Fixes
- Fixed apostrophe syntax errors that were causing build failures
- Created a custom icons component to fix the \"Module not found: Can't resolve 'lucide-react'\" error
- Updated all components to use the custom icons
" --base main --head joel-updates
```

### Option 3: Using Git Commands

If you prefer using Git commands, follow these steps:

1. **Push the Branch to GitHub** (if not already done)
   ```bash
   git push origin joel-updates
   ```

2. **Create Pull Request**
   - Go to https://github.com/joelgriiyo/kingdomadukrom/pull/new/joel-updates
   - Fill in the details as described in Option 1

## After Creating the Pull Request

1. **Review the Changes**
   - Go through the files changed to ensure everything is correct
   - Check for any conflicts that need to be resolved

2. **Merge the Pull Request**
   - Click "Merge pull request"
   - Confirm the merge
   - Delete the branch (optional)

3. **Deploy the Changes**
   - After merging, deploy the changes to your production environment
   - You can use the deployment scripts we created:
     ```bash
     npm run deploy
     ```

## Troubleshooting

If you encounter any issues during the pull request process:

1. **Conflicts**
   - If there are conflicts, you'll need to resolve them before merging
   - You can resolve conflicts on GitHub or locally using Git

2. **Build Failures**
   - If the CI/CD checks fail, review the error messages
   - Fix any issues and push the changes to the branch

3. **Permission Issues**
   - Ensure you have the necessary permissions to create pull requests and merge them
   - Contact the repository administrator if needed

## Additional Resources

- [GitHub Documentation on Pull Requests](https://docs.github.com/en/pull-requests)
- [Git Documentation](https://git-scm.com/doc)
- [Next.js Documentation](https://nextjs.org/docs)

---

This guide was created to help you publish the changes we've made to the Kingdom Adukrom website. If you have any questions or need further assistance, please don't hesitate to ask.
