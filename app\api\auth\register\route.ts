import { NextRequest, NextResponse } from 'next/server';
import { createUser, getUserByEmail } from '@/lib/users';
import { generateToken, verifyToken } from '@/lib/tokens';
import { sendEmail } from '@/lib/email';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isSuperAdmin } from '@/lib/auth-utils';
import { getWriteClient } from '@/lib/sanity.client';
import bcrypt from 'bcryptjs';

// POST /api/auth/register - Register a new user
export async function POST(request: NextRequest) {
  try {
    // Check if this is an admin-initiated registration
    const session = await getServerSession(authOptions);
    const isAdminRegistration = session?.user && isSuperAdmin(session);

    // Parse request body
    const body = await request.json();

    // Handle different registration flows
    if (isAdminRegistration) {
      // Admin is creating a user directly
      const { id, name, email, password, role } = body;

      if (!name || !email || !password) {
        return NextResponse.json(
          { success: false, message: 'Name, email, and password are required' },
          { status: 400 }
        );
      }

      try {
        // Create user in the local system
        const user = await createUser({
          name,
          email,
          password,
          role: role || 'user',
        });

        // Also create or update the user in Sanity
        try {
          const client = getWriteClient();

          // Hash the password for Sanity
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          // Generate a unique ID if not provided
          const userId = id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

          // Create the user in Sanity
          const username = email.split('@')[0];

          const userData = {
            _type: 'adminUser',
            _id: userId,
            username,
            email,
            name,
            role: role || 'user',
            hashedPassword,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isActive: true,
          };

          await client.createOrReplace(userData);

          console.log('User created in both systems:', {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role
          });
        } catch (sanityError) {
          console.error('Error creating user in Sanity:', sanityError);
          // Continue anyway since the user is created in the local system
        }

        return NextResponse.json({
          success: true,
          message: 'User registered successfully',
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
          },
        });
      } catch (error: any) {
        return NextResponse.json(
          { success: false, message: error.message || 'Failed to register user' },
          { status: 400 }
        );
      }
    } else {
      // Regular user registration with token
      const { token, name, email, password } = body;

      // Validate required fields
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Registration token is required' },
          { status: 400 }
        );
      }

      if (!name || !email || !password) {
        return NextResponse.json(
          { success: false, message: 'Name, email, and password are required' },
          { status: 400 }
        );
      }

      try {
        // Verify the registration token
        const tokenData = verifyToken(token);

        // Check if token is for registration and matches the email
        if (tokenData.type !== 'register' || tokenData.email !== email) {
          return NextResponse.json(
            { success: false, message: 'Invalid or expired registration token' },
            { status: 400 }
          );
        }

        // Check if user already exists
        const existingUser = getUserByEmail(email);
        if (existingUser) {
          // If user exists but hasn't completed registration (has temporary flag)
          if (existingUser.isTemporary) {
            // Update the user with the provided information
            try {
              const user = await createUser({
                name,
                email,
                password,
                role: tokenData.role || 'user', // Use role from token or default to 'user'
              });

              // Also create or update the user in Sanity
              try {
                const client = getWriteClient();

                // Hash the password for Sanity
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(password, salt);

                // Create the user in Sanity
                const username = email.split('@')[0];

                const userData = {
                  _type: 'adminUser',
                  _id: user.id,
                  username,
                  email,
                  name,
                  role: tokenData.role || 'user',
                  hashedPassword,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  isActive: true,
                };

                await client.createOrReplace(userData);
              } catch (sanityError) {
                console.error('Error creating user in Sanity:', sanityError);
                // Continue anyway since the user is created in the local system
              }

              return NextResponse.json({
                success: true,
                message: 'Registration successful',
                user: {
                  id: user.id,
                  name: user.name,
                  email: user.email,
                  role: user.role,
                },
              });
            } catch (error: any) {
              return NextResponse.json(
                { success: false, message: error.message || 'Failed to register user' },
                { status: 400 }
              );
            }
          } else {
            // User already exists and has completed registration
            return NextResponse.json(
              { success: false, message: 'User with this email already exists' },
              { status: 400 }
            );
          }
        } else {
          // Create new user
          try {
            const user = await createUser({
              name,
              email,
              password,
              role: tokenData.role || 'user', // Use role from token or default to 'user'
            });

            // Also create the user in Sanity
            try {
              const client = getWriteClient();

              // Hash the password for Sanity
              const salt = await bcrypt.genSalt(10);
              const hashedPassword = await bcrypt.hash(password, salt);

              // Create the user in Sanity
              const username = email.split('@')[0];

              const userData = {
                _type: 'adminUser',
                _id: user.id,
                username,
                email,
                name,
                role: tokenData.role || 'user',
                hashedPassword,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                isActive: true,
              };

              await client.createOrReplace(userData);
            } catch (sanityError) {
              console.error('Error creating user in Sanity:', sanityError);
              // Continue anyway since the user is created in the local system
            }

            return NextResponse.json({
              success: true,
              message: 'Registration successful',
              user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
              },
            });
          } catch (error: any) {
            return NextResponse.json(
              { success: false, message: error.message || 'Failed to register user' },
              { status: 400 }
            );
          }
        }
      } catch (tokenError) {
        return NextResponse.json(
          { success: false, message: 'Invalid or expired registration token' },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error('Error during registration:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to register user' },
      { status: 500 }
    );
  }
}
