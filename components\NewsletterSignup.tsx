'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

export default function NewsletterSignup() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Call the API to subscribe to the newsletter
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setIsSuccess(true);
        setEmail('');
        toast.success('Thank you for subscribing to our newsletter!');
      } else {
        throw new Error(data.message || 'Failed to subscribe');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Failed to subscribe. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="newsletter-signup">
      <h3 className="text-lg font-semibold mb-2 text-white">Subscribe to Our Newsletter</h3>
      <p className="text-sm text-gray-300 mb-4">
        Stay updated with the latest news and events from the Kingdom.
      </p>
      
      {isSuccess ? (
        <div className="bg-green-900/30 border border-green-700 rounded-md p-3 text-green-100">
          <p className="text-sm">
            Thank you for subscribing! You will receive updates from us soon.
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-2">
          <Input
            type="email"
            placeholder="Your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            required
          />
          <Button 
            type="submit" 
            disabled={isLoading}
            className="bg-royalGold hover:bg-yellow-600 text-royalBlue"
          >
            {isLoading ? 'Subscribing...' : 'Subscribe'}
          </Button>
        </form>
      )}
    </div>
  );
}
