import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the main store page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Royal Store',
    description: 'Shop exclusive royal merchandise and products from the Kingdom of Adukrom.',
    url: '/store',
    keywords: ['Adukrom Store', 'Royal Merchandise', 'Kingdom Products', 'African Royal Gifts'],
  });
}
