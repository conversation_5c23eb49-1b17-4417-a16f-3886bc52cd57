import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/store/categories - Get all store categories
export async function GET(request: NextRequest) {
  try {
    // Get the Sanity client
    const client = getWriteClient();

    // Fetch categories from Sanity
    const categories = await client.fetch(`
      *[_type == "storeCategory"] | order(order asc) {
        _id,
        title,
        slug,
        description,
        color,
        "icon": icon.asset->url,
        order
      }
    `);

    return NextResponse.json({
      success: true,
      categories,
    });
  } catch (error) {
    console.error('Error fetching store categories:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch store categories',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/store/categories - Create a new store category
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to create categories' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, message: 'Missing required field: title is required' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Generate a slug from the title if not provided
    if (!body.slug) {
      body.slug = {
        _type: 'slug',
        current: body.title
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim(),
      };
    }

    // Create the category document
    const categoryDoc = {
      _type: 'storeCategory',
      title: body.title,
      slug: body.slug,
      description: body.description || '',
      color: body.color || '#002366',
      order: body.order || 0,
      // Add icon if provided
      ...(body.icon && { icon: body.icon }),
    };

    // Create the document in Sanity
    const createdCategory = await client.create(categoryDoc);

    return NextResponse.json({
      success: true,
      message: 'Store category created successfully',
      category: createdCategory,
    });
  } catch (error) {
    console.error('Error creating store category:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create store category',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
