import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs';
import path from 'node:path';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// GET /api/users/current - Get current user data directly from the JSON file
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Get user ID from session
    const userId = session.user.id;

    try {
      // Read the users file directly
      const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

      // Find the user by ID
      let user = usersData.find((user: any) => user.id === userId);

      // If user not found by ID, try to find by email
      if (!user && session.user.email) {
        console.log('User not found by ID, trying email:', session.user.email);
        user = usersData.find((user: any) => user.email === session.user.email);
      }

      // If still not found, return the session user data as fallback
      if (!user) {
        console.log('User not found in JSON file, using session data as fallback');

        // Create a fallback user object from session data
        return NextResponse.json({
          success: true,
          user: {
            id: session.user.id || 'unknown',
            name: session.user.name || 'Unknown User',
            email: session.user.email || '<EMAIL>',
            role: (session.user as any).role || 'admin',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastLogin: new Date().toISOString()
          }
        });
      }

      // Return user data without password
      const { password, ...userWithoutPassword } = user;

      return NextResponse.json({
        success: true,
        user: userWithoutPassword
      });
    } catch (error: any) {
      console.error('Error getting current user data:', error);
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to get user data' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in current user API:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get user data' },
      { status: 500 }
    );
  }
}
