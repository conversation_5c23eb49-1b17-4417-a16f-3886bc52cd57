import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate dynamic metadata for this page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Strategic Partners of the Crown',
    description: 'At the heart of the Kingdom\'s vision for prosperity, unity and global impact are our Strategic Partners of the Crown.',
    url: '/gallery',
    keywords: ['Strategic Partners', 'Adukrom Kingdom', 'Crown Partners', 'Global Impact'],
  });
}

export default function GalleryLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <div>{children}</div>;
}
