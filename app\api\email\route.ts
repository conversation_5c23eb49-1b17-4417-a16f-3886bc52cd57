import { NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { type, email, name, subject, message, formData, formType } = body;

    // Get the form type - support both old and new formats
    const formTypeToUse = formType || type;

    // Validate required fields for old format
    if (formTypeToUse === 'contact' && !formData && (!email || !name || !subject || !message)) {
      return NextResponse.json(
        { error: 'Missing required fields for contact form' },
        { status: 400 }
      );
    }

    if (formTypeToUse === 'newsletter' && !formData && !email) {
      return NextResponse.json(
        { error: 'Email is required for newsletter subscription' },
        { status: 400 }
      );
    }

    console.log('Form submission received:', formTypeToUse);

    // Get the Sanity write client
    const client = getWriteClient();

    // Create a document in Sanity based on the form type
    let document;
    let documentType;

    // Handle both old and new formats
    if (formData) {
      // New format with formData object
      switch (formTypeToUse) {
        case 'contact':
          documentType = 'contact';
          document = {
            _type: 'contact',
            name: formData.name || '',
            email: formData.email || '',
            phone: formData.phone || '',
            message: formData.message || '',
            subject: formData.subject || 'Website Contact Form',
            submittedAt: new Date().toISOString(),
          };
          break;

        case 'newsletter':
          documentType = 'newsletter';
          document = {
            _type: 'newsletter',
            email: formData.email || '',
            name: formData.name || '',
            submittedAt: new Date().toISOString(),
          };
          break;

        default:
          documentType = 'formSubmission';
          document = {
            _type: 'formSubmission',
            formType: formTypeToUse || 'unknown',
            data: formData,
            submittedAt: new Date().toISOString(),
          };
      }
    } else {
      // Old format with individual fields
      if (formTypeToUse === 'contact') {
        documentType = 'contact';
        document = {
          _type: 'contact',
          name: name || '',
          email: email || '',
          subject: subject || '',
          message: message || '',
          submittedAt: new Date().toISOString(),
        };
      } else if (formTypeToUse === 'newsletter') {
        documentType = 'newsletter';
        document = {
          _type: 'newsletter',
          email: email || '',
          name: name || '',
          submittedAt: new Date().toISOString(),
        };
      }
    }

    if (document) {
      console.log(`Creating ${documentType} document in Sanity:`, document);

      // Create the document in Sanity
      // Use type assertion to handle different document types
      const result = await client.create(document as any);

      console.log(`${documentType} document created successfully with ID:`, result._id);
    }

    // In a real implementation, you would also use a service like SendGrid, Mailchimp, etc.
    // to send notification emails

    return NextResponse.json({
      success: true,
      message: 'Form submitted successfully'
    });
  } catch (error) {
    console.error('Error processing form submission:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to process form submission',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
