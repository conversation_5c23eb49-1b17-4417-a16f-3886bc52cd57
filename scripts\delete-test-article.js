// This script deletes the "This is Just a test" article from the Sanity dataset
require('dotenv').config();
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Function to delete the test article
async function deleteTestArticle() {
  try {
    console.log('Searching for test article...');
    
    // Query to find the test article
    const query = `*[_type == "news" && title match "This is Just a test" || title match "test" || title match "Test Article"]{
      _id,
      title
    }`;
    
    const testArticles = await client.fetch(query);
    
    if (testArticles.length === 0) {
      console.log('No test articles found.');
      return;
    }
    
    console.log(`Found ${testArticles.length} test article(s):`);
    testArticles.forEach(article => {
      console.log(`- ${article.title} (ID: ${article._id})`);
    });
    
    // Delete each test article
    for (const article of testArticles) {
      console.log(`Deleting article: ${article.title}`);
      await client.delete(article._id);
      console.log(`Deleted article with ID: ${article._id}`);
    }
    
    console.log('Finished deleting test articles.');
  } catch (error) {
    console.error('Error deleting test article:', error);
  }
}

// Function to find and delete duplicate articles
async function deleteDuplicateArticles() {
  try {
    console.log('Searching for duplicate articles...');
    
    // Get all articles
    const allArticles = await client.fetch(`*[_type == "news"] | order(title asc) {
      _id,
      title,
      slug,
      publishedAt
    }`);
    
    // Find duplicates by title
    const titleMap = new Map();
    const duplicates = [];
    
    allArticles.forEach(article => {
      if (titleMap.has(article.title)) {
        // This is a duplicate
        const existingArticle = titleMap.get(article.title);
        const existingDate = existingArticle.publishedAt ? new Date(existingArticle.publishedAt) : new Date(0);
        const currentDate = article.publishedAt ? new Date(article.publishedAt) : new Date(0);
        
        // Keep the newer article, mark the older one for deletion
        if (currentDate > existingDate) {
          duplicates.push(existingArticle);
          titleMap.set(article.title, article);
        } else {
          duplicates.push(article);
        }
      } else {
        titleMap.set(article.title, article);
      }
    });
    
    if (duplicates.length === 0) {
      console.log('No duplicate articles found.');
      return;
    }
    
    console.log(`Found ${duplicates.length} duplicate article(s):`);
    duplicates.forEach(article => {
      console.log(`- ${article.title} (ID: ${article._id})`);
    });
    
    // Delete each duplicate article
    for (const article of duplicates) {
      console.log(`Deleting duplicate article: ${article.title}`);
      await client.delete(article._id);
      console.log(`Deleted article with ID: ${article._id}`);
    }
    
    console.log('Finished deleting duplicate articles.');
  } catch (error) {
    console.error('Error deleting duplicate articles:', error);
  }
}

// Run both functions
async function cleanupArticles() {
  console.log('Starting article cleanup...');
  await deleteTestArticle();
  await deleteDuplicateArticles();
  console.log('Article cleanup complete.');
}

// Run the cleanup
cleanupArticles().catch(console.error);
