import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'page',
  title: 'Pages',
  type: 'document',
  groups: [
    { name: 'content', title: 'Content' },
    { name: 'seo', title: 'SEO' },
    { name: 'settings', title: 'Settings' },
  ],
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
      group: 'content',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      description: 'The URL path for this page (e.g., "about-us")',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
      group: 'settings',
    }),
    defineField({
      name: 'navMenu',
      title: 'Show in Navigation Menu',
      type: 'boolean',
      initialValue: false,
      group: 'settings',
    }),
    defineField({
      name: 'navOrder',
      title: 'Navigation Order',
      description: 'Order in the navigation menu (lower numbers appear first)',
      type: 'number',
      initialValue: 100,
      hidden: ({ document }) => !document?.navMenu,
      group: 'settings',
    }),
    defineField({
      name: 'navCategory',
      title: 'Navigation Category',
      type: 'string',
      options: {
        list: [
          { title: 'About', value: 'About' },
          { title: 'Media', value: 'Media' },
          { title: 'Events', value: 'Events' },
          { title: 'Other', value: 'Other' }
        ],
        layout: 'dropdown',
      },
      description: 'Group this page under a dropdown in the navigation menu',
      group: 'settings',
    }),
    defineField({
      name: 'pageBuilder',
      title: 'Page Content',
      description: 'Add, edit, and reorder sections of your page',
      type: 'array',
      of: [
        { type: 'hero' },
        { type: 'textSection' },
        { type: 'imageGallery' },
        { type: 'featuredContent' },
        { type: 'contactForm' },
      ],
      group: 'content',
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoMetaFields',
      group: 'seo',
    }),
    defineField({
      name: 'includeInSitemap',
      title: 'Include in Sitemap',
      type: 'boolean',
      initialValue: true,
      group: 'seo',
    }),
    defineField({
      name: 'accessLevel',
      title: 'Access Level',
      description: 'Who can create and edit this page',
      type: 'string',
      options: {
        list: [
          { title: 'Admin Only', value: 'admin' },
          { title: 'Super Admin Only', value: 'super_admin' },
          { title: 'Both Admin & Super Admin', value: 'both' },
        ],
      },
      initialValue: 'both',
      group: 'settings',
    }),
  ],
  preview: {
    select: {
      title: 'title',
      slug: 'slug.current',
      media: 'openGraphImage',
    },
    prepare({ title, slug, media }) {
      return {
        title,
        subtitle: `/${slug}`,
        media,
      };
    },
  },
});
