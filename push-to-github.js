// GitHub push script
const { execSync } = require('child_process');
const readline = require('readline');

// Function to execute shell commands and log output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Function to get user input
function getUserInput(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer);
    });
  });
}

// Main GitHub push process
async function pushToGitHub() {
  console.log('Starting GitHub push process...');
  
  // Check if GitHub remote exists
  console.log('Checking GitHub remote...');
  try {
    const remotes = execSync('git remote -v', { encoding: 'utf8' });
    if (!remotes.includes('github')) {
      console.log('GitHub remote not found. Adding it now...');
      runCommand('git remote add github https://github.com/joelgriiyo/kingdomadukrom.git');
    }
  } catch (error) {
    console.error('Error checking git remotes:', error);
    process.exit(1);
  }

  // Check for uncommitted changes
  console.log('Checking for uncommitted changes...');
  try {
    const status = execSync('git status --porcelain', { encoding: 'utf8' });
    if (status) {
      console.log('You have uncommitted changes:');
      console.log(status);
      const commitChanges = await getUserInput('Do you want to commit these changes? (y/n): ');
      if (commitChanges.toLowerCase() === 'y') {
        const commitMessage = await getUserInput('Enter commit message: ');
        runCommand('git add .');
        runCommand(`git commit -m "${commitMessage}"`);
      } else {
        const continuePush = await getUserInput('Continue pushing without committing changes? (y/n): ');
        if (continuePush.toLowerCase() !== 'y') {
          console.log('Push aborted.');
          process.exit(0);
        }
      }
    } else {
      console.log('No uncommitted changes found.');
    }
  } catch (error) {
    console.error('Error checking git status:', error);
    process.exit(1);
  }

  // Push to GitHub
  console.log('Pushing to GitHub repository...');
  try {
    // Ask which branch to push
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    const branch = await getUserInput(`Which branch do you want to push to GitHub? (default: ${currentBranch}): `) || currentBranch;
    
    // Ask which remote branch to push to
    const remoteBranch = await getUserInput('Which remote branch do you want to push to? (default: main): ') || 'main';
    
    // Ask if force push is needed
    const forcePush = await getUserInput('Do you want to force push? (y/n): ');
    const forceFlag = forcePush.toLowerCase() === 'y' ? '-f' : '';
    
    // Push to GitHub
    runCommand(`git push github ${branch}:${remoteBranch} ${forceFlag}`);
    console.log('Successfully pushed to GitHub!');
  } catch (error) {
    console.error('Error pushing to GitHub:', error);
    process.exit(1);
  }
}

// Run the GitHub push process
pushToGitHub();
