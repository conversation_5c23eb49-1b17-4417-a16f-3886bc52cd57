# News Import Script

This script imports news articles from the website https://cil.xem.mybluehost.me/website_963da4e6/blog/ into your Sanity CMS.

## Prerequisites

1. Make sure you have the required environment variables set:
   - `NEXT_PUBLIC_SANITY_PROJECT_ID`: Your Sanity project ID
   - `NEXT_PUBLIC_SANITY_DATASET`: Your Sanity dataset (usually "production")
   - `SANITY_API_TOKEN`: A write token for your Sanity project

## How to Get a Sanity API Token

1. Go to https://www.sanity.io/manage
2. Select your project
3. Go to API tab
4. Click "Add API token"
5. Give it a name like "News Import"
6. Set the permissions to "Editor" (or higher)
7. Copy the token

## Running the Script

You can run the script using npm:

```bash
# Set the environment variables (replace with your actual values)
export NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
export NEXT_PUBLIC_SANITY_DATASET=production
export SANITY_API_TOKEN=your-token

# Run the import script
npm run import-news
```

Or you can create a `.env.local` file in the root of your project with these variables:

```
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-token
```

And then run:

```bash
npm run import-news
```

## What the Script Does

1. Fetches articles from the source website
2. For each article:
   - Extracts the title, excerpt, date, and image
   - Fetches the full content
   - Creates a Sanity image asset from the image URL
   - Creates or updates a Sanity document for the article
   - Associates the article with a default category

## Troubleshooting

If you encounter any issues:

1. Make sure your environment variables are set correctly
2. Check that your Sanity API token has write permissions
3. Look at the error messages in the console for specific issues

## Customizing the Script

You can modify the script to:

- Import from a different source
- Change how articles are formatted
- Add more metadata to the articles
- Create different categories for articles

Just edit the `scripts/import-news-to-sanity.js` file.
