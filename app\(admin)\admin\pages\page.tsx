'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { getPages } from '@/lib/sanity';

export default function PagesAdmin() {
  const [pages, setPages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchPages = async () => {
      try {
        setIsLoading(true);
        const pagesData = await getPages();
        setPages(pagesData);
      } catch (error) {
        console.error('Error fetching pages:', error);
        toast.error('Failed to load pages');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPages();
  }, []);

  const handleDelete = async (pageId: string) => {
    // In a real implementation, you would call your API to delete the page
    if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      toast.success('Page deleted successfully');
      // Refresh the page list
      setPages(pages.filter(page => page._id !== pageId));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Page Management</h1>
          <p className="text-muted-foreground">
            Create, edit, and manage pages for your website.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/admin/pages/create">
              <Plus className="mr-2 h-4 w-4" /> Create Page
            </Link>
          </Button>
        </div>
      </div>

      <Separator />

      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Pages</TabsTrigger>
          <TabsTrigger value="navigation">Navigation Pages</TabsTrigger>
          <TabsTrigger value="hidden">Hidden Pages</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading pages...</p>
            </div>
          ) : pages.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {pages.map((page) => (
                <Card key={page._id}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium">{page.title}</CardTitle>
                    <CardDescription>/{page.slug.current}</CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <FileText className="h-4 w-4" />
                      <span>{page.pageBuilder?.length || 0} sections</span>
                    </div>
                    <div className="mt-2 flex items-center gap-2">
                      {page.navMenu && (
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                          Navigation
                        </span>
                      )}
                      {page.navCategory && (
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                          {page.navCategory}
                        </span>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/${page.slug.current}`} target="_blank">
                        <Eye className="mr-2 h-4 w-4" /> View
                      </Link>
                    </Button>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/pages/edit/${page._id}`}>
                          <Edit className="mr-2 h-4 w-4" /> Edit
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(page._id)}>
                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No pages found. Create your first page to get started.</p>
              <Button className="mt-4" asChild>
                <Link href="/admin/pages/create">
                  <Plus className="mr-2 h-4 w-4" /> Create Page
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="navigation" className="mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading pages...</p>
            </div>
          ) : pages.filter(page => page.navMenu).length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {pages
                .filter(page => page.navMenu)
                .map((page) => (
                  <Card key={page._id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium">{page.title}</CardTitle>
                      <CardDescription>/{page.slug.current}</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <FileText className="h-4 w-4" />
                        <span>{page.pageBuilder?.length || 0} sections</span>
                      </div>
                      <div className="mt-2 flex items-center gap-2">
                        {page.navCategory && (
                          <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                            {page.navCategory}
                          </span>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/${page.slug.current}`} target="_blank">
                          <Eye className="mr-2 h-4 w-4" /> View
                        </Link>
                      </Button>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/pages/edit/${page._id}`}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDelete(page._id)}>
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No navigation pages found.</p>
              <Button className="mt-4" asChild>
                <Link href="/admin/pages/create">
                  <Plus className="mr-2 h-4 w-4" /> Create Navigation Page
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="hidden" className="mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading pages...</p>
            </div>
          ) : pages.filter(page => !page.navMenu).length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {pages
                .filter(page => !page.navMenu)
                .map((page) => (
                  <Card key={page._id}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium">{page.title}</CardTitle>
                      <CardDescription>/{page.slug.current}</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <FileText className="h-4 w-4" />
                        <span>{page.pageBuilder?.length || 0} sections</span>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/${page.slug.current}`} target="_blank">
                          <Eye className="mr-2 h-4 w-4" /> View
                        </Link>
                      </Button>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/pages/edit/${page._id}`}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDelete(page._id)}>
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No hidden pages found.</p>
              <Button className="mt-4" asChild>
                <Link href="/admin/pages/create">
                  <Plus className="mr-2 h-4 w-4" /> Create Hidden Page
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
