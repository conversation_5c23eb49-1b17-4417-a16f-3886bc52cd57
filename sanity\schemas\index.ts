import { type SchemaTypeDefinition } from 'sanity'

// Import all schema types
import adminUser from './adminUser'
import author from './author'
import category from './category'
import contact from './contact'
import event from './event'
import formSubmission from './formSubmission'
import gallery from './gallery'
import news from './news'
import newsletter from './newsletter'
import newsletterSubscriber from './newsletterSubscriber'
import order from './order'
import page from './page'
import product from './product'
import rsvp from './rsvp'
import siteSettings from './siteSettings'
import storeCategory from './storeCategory'
import strategicPartner from './strategicPartner'

// Import object schemas
import contactForm from './objects/contactForm'
import featuredContent from './objects/featuredContent'
import hero from './objects/hero'
import imageGallery from './objects/imageGallery'
import seo from './objects/seo'
import textSection from './objects/textSection'

// Export the schema
export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    // Document types
    adminUser,
    author,
    category,
    contact,
    event,
    formSubmission,
    gallery,
    news,
    newsletter,
    newsletterSubscriber,
    order,
    page,
    product,
    rsvp,
    siteSettings,
    storeCategory,
    strategicPartner,

    // Object types
    contactForm,
    featuredContent,
    hero,
    imageGallery,
    seo,
    textSection,
  ],
}
export default schema;
