'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Globe,
  Facebook,
  Twitter,
  Info,
  Plus,
  X,
  Upload,
  Check,
  Image as ImageIcon
} from 'lucide-react';
import SeoPreview from './SeoPreview';
import { urlFor } from '@/lib/sanity';

interface SeoFormProps {
  title: string;
  description: string;
  slug: string;
  image?: any;
  contentType?: string;
  seoData: any;
  onSeoChange: (seoData: any) => void;
  onImageUpload?: (file: File) => Promise<any>;
}

export default function SeoForm({
  title,
  description,
  slug,
  image,
  contentType = 'article',
  seoData,
  onSeoChange,
  onImageUpload
}: SeoFormProps) {
  const [keywords, setKeywords] = useState<string[]>(seoData?.seoKeywords || []);
  const [keywordInput, setKeywordInput] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle adding a keyword
  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      const newKeywords = [...keywords, keywordInput.trim()];
      setKeywords(newKeywords);

      // Update the parent component
      onSeoChange({
        ...seoData,
        seoKeywords: newKeywords
      });

      // Clear the input
      setKeywordInput('');
    }
  };

  // Handle removing a keyword
  const removeKeyword = (keyword: string) => {
    const newKeywords = keywords.filter(k => k !== keyword);
    setKeywords(newKeywords);

    // Update the parent component
    onSeoChange({
      ...seoData,
      seoKeywords: newKeywords
    });
  };

  // Handle keyword input keydown (for Enter key)
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addKeyword();
    }
  };

  // Calculate character counts
  const metaTitleLength = (seoData?.metaTitle || '').length;
  const metaDescriptionLength = (seoData?.metaDescription || '').length;

  // Handle image upload
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file');
      return;
    }

    // Create a preview URL
    const reader = new FileReader();
    reader.onloadend = () => {
      const result = reader.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);

    setImageFile(file);

    // If onImageUpload is provided, upload the image to Sanity
    if (onImageUpload) {
      try {
        setIsUploading(true);
        const uploadedImage = await onImageUpload(file);

        // Update the SEO data with the uploaded image
        onSeoChange({
          ...seoData,
          openGraph: {
            ...seoData?.openGraph,
            image: uploadedImage
          },
          twitter: {
            ...seoData?.twitter,
            image: uploadedImage
          }
        });

        setIsUploading(false);
      } catch (error) {
        console.error('Error uploading image:', error);
        setIsUploading(false);
        alert('Failed to upload image. Please try again.');
      }
    }
  };

  // Handle image upload button click
  const handleImageUploadClick = () => {
    fileInputRef.current?.click();
  };

  // Determine if title and description are optimal length
  const isTitleOptimal = metaTitleLength >= 30 && metaTitleLength <= 60;
  const isDescriptionOptimal = metaDescriptionLength >= 70 && metaDescriptionLength <= 160;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left column: SEO Settings */}
        <div className="space-y-6">
          <div className="space-y-5">
            <div className="flex items-center border-b pb-2">
              <Search className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-medium">Search Engine Optimization</h3>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="metaTitle">Meta Title</Label>
                <span className={`text-xs ${isTitleOptimal ? 'text-green-600' : 'text-amber-600'} font-medium`}>
                  {metaTitleLength}/60
                </span>
              </div>
              <Input
                id="metaTitle"
                value={seoData?.metaTitle || ''}
                onChange={(e) => onSeoChange({ ...seoData, metaTitle: e.target.value })}
                placeholder={title}
                className="mt-1"
              />
              <p className={`text-xs mt-1 ${isTitleOptimal ? 'text-green-600' : 'text-amber-600'} flex items-center`}>
                {isTitleOptimal ? (
                  <>
                    <Check className="h-3 w-3 mr-1" />
                    Great! Your title is the optimal length.
                  </>
                ) : (
                  <>
                    <Info className="h-3 w-3 mr-1" />
                    Optimal length is between 30-60 characters
                  </>
                )}
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <Label htmlFor="metaDescription">Meta Description</Label>
                <span className={`text-xs ${isDescriptionOptimal ? 'text-green-600' : 'text-amber-600'} font-medium`}>
                  {metaDescriptionLength}/160
                </span>
              </div>
              <Textarea
                id="metaDescription"
                value={seoData?.metaDescription || ''}
                onChange={(e) => onSeoChange({ ...seoData, metaDescription: e.target.value })}
                placeholder={description}
                className="mt-1"
                rows={3}
              />
              <p className={`text-xs mt-1 ${isDescriptionOptimal ? 'text-green-600' : 'text-amber-600'} flex items-center`}>
                {isDescriptionOptimal ? (
                  <>
                    <Check className="h-3 w-3 mr-1" />
                    Great! Your description is the optimal length.
                  </>
                ) : (
                  <>
                    <Info className="h-3 w-3 mr-1" />
                    Optimal length is between 70-160 characters
                  </>
                )}
              </p>
            </div>

            <div>
              <Label htmlFor="seoKeywords">Keywords</Label>
              <div className="flex mt-1">
                <Input
                  id="seoKeywords"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Add keywords and press Enter"
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={addKeyword}
                  variant="outline"
                  className="ml-2"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2 mt-3 min-h-[40px] p-2 border rounded-md bg-muted/30">
                {keywords.map((keyword, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {keyword}
                    <button
                      type="button"
                      onClick={() => removeKeyword(keyword)}
                      className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
                {keywords.length === 0 && (
                  <p className="text-xs text-muted-foreground">
                    No keywords added yet. Keywords help search engines understand your content.
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Switch
                id="nofollowAttributes"
                checked={seoData?.nofollowAttributes || false}
                onCheckedChange={(checked) => onSeoChange({ ...seoData, nofollowAttributes: checked })}
              />
              <Label htmlFor="nofollowAttributes">No-index (hide from search engines)</Label>
            </div>
          </div>

          <div className="space-y-5 pt-4 border-t">
            <div className="flex items-center border-b pb-2">
              <Facebook className="h-5 w-5 mr-2 text-blue-600" />
              <h3 className="text-lg font-medium">Social Media Sharing</h3>
            </div>

            <div className="space-y-4 mb-4">
              <div>
                <Label htmlFor="socialImage">Social Media Image</Label>
                <div className="mt-2 border rounded-md p-4 bg-muted/30">
                  <div className="flex flex-col gap-4">
                    {/* Image preview */}
                    <div className="relative aspect-video w-full overflow-hidden rounded-md border border-border bg-muted">
                      {imagePreview ? (
                        // Show the new image preview if a file is selected
                        <Image
                          src={imagePreview}
                          alt="Social media preview"
                          fill
                          className="object-cover"
                        />
                      ) : seoData?.openGraph?.image ? (
                        // Show the existing image if available
                        <Image
                          src={
                            typeof seoData.openGraph.image === 'string'
                              ? seoData.openGraph.image
                              : (() => {
                                  try {
                                    const imageBuilder = urlFor(seoData.openGraph.image);
                                    return imageBuilder && typeof imageBuilder.width === 'function'
                                      ? imageBuilder.width(1200).height(630).url()
                                      : '/images/placeholder-og.jpg';
                                  } catch (err) {
                                    console.error('Error processing openGraph image:', err);
                                    return '/images/placeholder-og.jpg';
                                  }
                                })()
                          }
                          alt="Social media preview"
                          fill
                          className="object-cover"
                        />
                      ) : image ? (
                        // Show the content image if available
                        <Image
                          src={
                            typeof image === 'string'
                              ? image
                              : (() => {
                                  try {
                                    const imageBuilder = urlFor(image);
                                    return imageBuilder && typeof imageBuilder.width === 'function'
                                      ? imageBuilder.width(1200).height(630).url()
                                      : '/images/placeholder-og.jpg';
                                  } catch (err) {
                                    console.error('Error processing content image:', err);
                                    return '/images/placeholder-og.jpg';
                                  }
                                })()
                          }
                          alt="Content image"
                          fill
                          className="object-cover"
                        />
                      ) : (
                        // Show placeholder if no image is available
                        <div className="flex items-center justify-center h-full">
                          <ImageIcon className="h-12 w-12 text-muted-foreground" />
                          <span className="ml-2 text-muted-foreground">No image selected</span>
                        </div>
                      )}
                    </div>

                    {/* Upload button */}
                    <div className="flex items-center gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex gap-2"
                        onClick={handleImageUploadClick}
                        disabled={isUploading}
                      >
                        {isUploading ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4" />
                            {seoData?.openGraph?.image || imagePreview ? 'Change Image' : 'Upload Image'}
                          </>
                        )}
                      </Button>
                      <input
                        type="file"
                        id="socialImage"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                      <p className="text-xs text-muted-foreground">
                        Recommended size: 1200x630 pixels
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="ogTitle">Facebook Title</Label>
                <Input
                  id="ogTitle"
                  value={seoData?.openGraph?.title || ''}
                  onChange={(e) => onSeoChange({
                    ...seoData,
                    openGraph: {
                      ...seoData?.openGraph,
                      title: e.target.value
                    }
                  })}
                  placeholder={seoData?.metaTitle || title}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="twitterTitle">X Title (formerly Twitter)</Label>
                <Input
                  id="twitterTitle"
                  value={seoData?.twitter?.title || ''}
                  onChange={(e) => onSeoChange({
                    ...seoData,
                    twitter: {
                      ...seoData?.twitter,
                      title: e.target.value
                    }
                  })}
                  placeholder={seoData?.openGraph?.title || seoData?.metaTitle || title}
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="ogDescription">Facebook Description</Label>
                <Textarea
                  id="ogDescription"
                  value={seoData?.openGraph?.description || ''}
                  onChange={(e) => onSeoChange({
                    ...seoData,
                    openGraph: {
                      ...seoData?.openGraph,
                      description: e.target.value
                    }
                  })}
                  placeholder={seoData?.metaDescription || description}
                  className="mt-1"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="twitterDescription">X Description (formerly Twitter)</Label>
                <Textarea
                  id="twitterDescription"
                  value={seoData?.twitter?.description || ''}
                  onChange={(e) => onSeoChange({
                    ...seoData,
                    twitter: {
                      ...seoData?.twitter,
                      description: e.target.value
                    }
                  })}
                  placeholder={seoData?.openGraph?.description || seoData?.metaDescription || description}
                  className="mt-1"
                  rows={2}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="twitterCardType">X Card Type (formerly Twitter)</Label>
              <select
                id="twitterCardType"
                value={seoData?.twitter?.cardType || 'summary_large_image'}
                onChange={(e) => onSeoChange({
                  ...seoData,
                  twitter: {
                    ...seoData?.twitter,
                    cardType: e.target.value
                  }
                })}
                className="w-full mt-1 px-3 py-2 border rounded-md"
              >
                <option value="summary">Summary</option>
                <option value="summary_large_image">Summary with Large Image</option>
              </select>
            </div>

            <div className="mt-6">
              <h3 className="text-lg font-medium">Instagram</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Instagram doesn't use meta tags like Facebook and X, but you can prepare captions for sharing.
              </p>

              <div>
                <Label htmlFor="instagramCaption">Instagram Caption</Label>
                <Textarea
                  id="instagramCaption"
                  value={seoData?.instagram?.caption || ''}
                  onChange={(e) => onSeoChange({
                    ...seoData,
                    instagram: {
                      ...seoData?.instagram,
                      caption: e.target.value
                    }
                  })}
                  placeholder={`Check out ${seoData?.openGraph?.title || seoData?.metaTitle || title || 'our page'}!`}
                  className="mt-1"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Craft a compelling caption for when this content is shared on Instagram.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right column: Preview */}
        <div>
          <SeoPreview
            title={title}
            description={description}
            slug={slug}
            image={image}
            contentType={contentType}
            seoData={seoData || {}}
            onSeoChange={onSeoChange}
          />
        </div>
      </div>
    </div>
  );
}
