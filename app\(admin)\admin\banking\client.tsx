'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import {
  CreditCard,
  DollarSign,
  AlertTriangle,
  Wallet,
  BarChart4,
  Save,
  RefreshCw,
  Loader2,
  Shield
} from 'lucide-react';

export default function BankingClient() {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const router = useRouter();

  // Banking settings
  const [bankingSettings, setBankingSettings] = useState({
    accountName: 'Kingdom of Adukrom',
    accountNumber: '',
    routingNumber: '',
    bankName: '',
    swiftCode: '',
    paymentProcessor: 'stripe',
    enablePayments: false,
  });

  // Get session directly from next-auth
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'loading') {
      return; // Still loading, don't do anything yet
    }

    // Check if the user is a super admin
    if (session?.user?.role === 'super_admin') {
      setIsAuthorized(true);
    }

    console.log('Session in banking client:', session);

    // Set loading to false
    setIsLoading(false);
  }, [session, status]);

  // Handle form submissions
  const handleBankingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting banking settings:', bankingSettings);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'banking',
          data: bankingSettings
        }),
      });

      const result = await response.json();
      console.log('API response:', result);

      if (result.success) {
        toast.success('Banking settings saved successfully');
      } else {
        throw new Error(result.message || 'Failed to save banking settings');
      }
    } catch (error) {
      console.error('Failed to save banking settings:', error);
      toast.error('Failed to save banking settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <CreditCard className="mr-2 h-6 w-6 text-royalBlue" />
          Banking & Payments
        </h1>
        <p className="text-muted-foreground">
          Manage banking information and payment processing settings.
        </p>
      </div>

      <Separator />

      <Tabs defaultValue="banking" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="banking">Banking Information</TabsTrigger>
          <TabsTrigger value="payments">Payment Processing</TabsTrigger>
          <TabsTrigger value="transactions">Transaction History</TabsTrigger>
        </TabsList>

        {/* Banking Information */}
        <TabsContent value="banking" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5 text-royalBlue" />
                Banking Information
              </CardTitle>
              <CardDescription>
                Manage banking details for the organization.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleBankingSubmit}>
              <CardContent className="space-y-4">
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
                  <div className="flex items-start gap-2">
                    <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-800">Super Admin Only</h3>
                      <p className="text-sm text-yellow-700">
                        Banking information is restricted to super administrators only.
                        This information is used for payment processing and financial operations.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountName">Account Name</Label>
                  <Input
                    id="accountName"
                    value={bankingSettings.accountName}
                    onChange={(e) => setBankingSettings({ ...bankingSettings, accountName: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="accountNumber">Account Number</Label>
                    <Input
                      id="accountNumber"
                      type="password"
                      value={bankingSettings.accountNumber}
                      onChange={(e) => setBankingSettings({ ...bankingSettings, accountNumber: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="routingNumber">Routing Number</Label>
                    <Input
                      id="routingNumber"
                      type="password"
                      value={bankingSettings.routingNumber}
                      onChange={(e) => setBankingSettings({ ...bankingSettings, routingNumber: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bankName">Bank Name</Label>
                    <Input
                      id="bankName"
                      value={bankingSettings.bankName}
                      onChange={(e) => setBankingSettings({ ...bankingSettings, bankName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="swiftCode">SWIFT/BIC Code</Label>
                    <Input
                      id="swiftCode"
                      value={bankingSettings.swiftCode}
                      onChange={(e) => setBankingSettings({ ...bankingSettings, swiftCode: e.target.value })}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Banking Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Payment Processing */}
        <TabsContent value="payments" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="mr-2 h-5 w-5" />
                Payment Processing
              </CardTitle>
              <CardDescription>
                Configure payment processors and methods.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="paymentProcessor">Payment Processor</Label>
                <select
                  id="paymentProcessor"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={bankingSettings.paymentProcessor}
                  onChange={(e) => setBankingSettings({ ...bankingSettings, paymentProcessor: e.target.value })}
                >
                  <option value="stripe">Stripe</option>
                  <option value="paypal">PayPal</option>
                  <option value="square">Square</option>
                  <option value="manual">Manual Processing</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="enablePayments"
                  checked={bankingSettings.enablePayments}
                  onCheckedChange={(checked) => setBankingSettings({ ...bankingSettings, enablePayments: checked })}
                />
                <Label htmlFor="enablePayments">Enable Payment Processing</Label>
              </div>

              <Button className="mt-4">Configure Stripe Settings</Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Transaction History */}
        <TabsContent value="transactions" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart4 className="mr-2 h-5 w-5" />
                Transaction History
              </CardTitle>
              <CardDescription>
                View and manage financial transactions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8 text-muted-foreground">
                Transaction history will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Banking Security</h3>
            <p className="text-sm mt-1 text-yellow-700">
              Banking information is highly sensitive. Ensure you're using secure connections and
              following best practices for financial data security. All banking information is
              encrypted and stored securely.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
