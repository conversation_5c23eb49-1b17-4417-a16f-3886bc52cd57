// This script creates initial categories in Sanity
// Run with: node scripts/create-initial-categories.js

const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Initial categories to create
const initialCategories = [
  {
    title: 'General',
    description: 'General gallery images',
    color: '#002366',
    order: 0
  },
  {
    title: 'Events',
    description: 'Images from various events',
    color: '#8B0000',
    order: 1
  },
  {
    title: 'Royal Family',
    description: 'Images of the royal family',
    color: '#4B0082',
    order: 2
  },
  {
    title: 'Culture',
    description: 'Cultural images and artifacts',
    color: '#006400',
    order: 3
  },
  {
    title: 'Community',
    description: 'Community events and gatherings',
    color: '#FF8C00',
    order: 4
  }
];

// Function to create a category
async function createCategory(category) {
  try {
    // Generate a slug from the title
    const slug = category.title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Create the category document
    const categoryDoc = {
      _type: 'category',
      title: category.title,
      slug: { _type: 'slug', current: slug },
      description: category.description,
      color: category.color,
      order: category.order
    };

    // Create the document in Sanity
    const createdCategory = await client.create(categoryDoc);
    console.log(`Created category: ${category.title} (${createdCategory._id})`);
    return createdCategory;
  } catch (error) {
    console.error(`Error creating category ${category.title}:`, error);
    return null;
  }
}

// Main function to create all categories
async function createInitialCategories() {
  console.log('Creating initial categories...');
  
  try {
    // Check if we have any existing categories
    const existingCategories = await client.fetch('*[_type == "category"]');
    
    if (existingCategories.length > 0) {
      console.log(`Found ${existingCategories.length} existing categories. Skipping creation.`);
      console.log('Existing categories:');
      existingCategories.forEach(cat => {
        console.log(`- ${cat.title} (${cat._id})`);
      });
      return;
    }
    
    // Create all categories
    const results = await Promise.all(
      initialCategories.map(category => createCategory(category))
    );
    
    const successCount = results.filter(Boolean).length;
    console.log(`Successfully created ${successCount} out of ${initialCategories.length} categories`);
  } catch (error) {
    console.error('Error in createInitialCategories:', error);
  }
}

// Run the main function
createInitialCategories()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
