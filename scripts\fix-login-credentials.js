// This script fixes login credentials by ensuring both Sanity and local users are in sync
// Run with: node scripts/fix-login-credentials.js

const { createClient } = require('@sanity/client');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Ensure the data directory exists
const ensureDataDir = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Create the users file if it doesn't exist
const ensureUsersFile = () => {
  ensureDataDir();
  if (!fs.existsSync(usersFilePath)) {
    fs.writeFileSync(usersFilePath, JSON.stringify([], null, 2));
  }
};

// Get all local users
const getLocalUsers = () => {
  ensureUsersFile();
  try {
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    return JSON.parse(usersData);
  } catch (error) {
    console.error('Error reading local users file:', error);
    return [];
  }
};

// Save local users
const saveLocalUsers = (users) => {
  ensureUsersFile();
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
};

// Fetch all Sanity users
const fetchSanityUsers = async () => {
  try {
    console.log('Fetching users from Sanity...');

    const users = await client.fetch(
      `*[_type == "adminUser"] {
        _id,
        username,
        name,
        email,
        role,
        hashedPassword,
        isActive,
        isProtected,
        createdAt,
        updatedAt,
        lastLogin
      }`
    );

    console.log(`Found ${users.length} users in Sanity`);
    return users;
  } catch (error) {
    console.error('Error fetching Sanity users:', error);
    return [];
  }
};

// Sync users from Sanity to local
const syncUsersFromSanity = async () => {
  try {
    const sanityUsers = await fetchSanityUsers();
    const localUsers = getLocalUsers();

    console.log(`Found ${localUsers.length} local users`);

    // Track changes
    let added = 0;
    let updated = 0;
    let unchanged = 0;

    // Create a map of local users by email for quick lookup
    const localUsersByEmail = {};
    localUsers.forEach(user => {
      localUsersByEmail[user.email] = user;
    });

    // Create a new array for updated local users
    const updatedLocalUsers = [];

    // Process each Sanity user
    for (const sanityUser of sanityUsers) {
      if (!sanityUser.email) {
        console.log(`Skipping Sanity user ${sanityUser._id} with no email`);
        continue;
      }

      const localUser = localUsersByEmail[sanityUser.email];

      if (localUser) {
        // User exists locally, update it
        const updatedUser = {
          ...localUser,
          name: sanityUser.name || localUser.name,
          role: sanityUser.role || localUser.role,
          // Only update password if it exists in Sanity
          ...(sanityUser.hashedPassword && { password: sanityUser.hashedPassword }),
          updatedAt: new Date().toISOString(),
        };

        updatedLocalUsers.push(updatedUser);
        updated++;
      } else {
        // User doesn't exist locally, create it
        const newLocalUser = {
          id: sanityUser._id,
          name: sanityUser.name || '',
          email: sanityUser.email,
          password: sanityUser.hashedPassword || await bcrypt.hash('TemporaryPassword123!', 10),
          role: sanityUser.role || 'admin',
          isTemporary: false,
          createdAt: sanityUser.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastLogin: sanityUser.lastLogin || null,
        };

        updatedLocalUsers.push(newLocalUser);
        added++;
      }
    }

    // Add any local users that don't exist in Sanity
    const sanityEmails = sanityUsers.map(user => user.email);

    for (const localUser of localUsers) {
      if (!sanityEmails.includes(localUser.email)) {
        updatedLocalUsers.push(localUser);
        unchanged++;
      }
    }

    // Save the updated local users
    saveLocalUsers(updatedLocalUsers);

    console.log(`Sync complete: ${added} added, ${updated} updated, ${unchanged} unchanged`);
    return { added, updated, unchanged };
  } catch (error) {
    console.error('Error syncing users from Sanity:', error);
    throw error;
  }
};

// Reset a user's password in both Sanity and local
const resetUserPassword = async (email, newPassword) => {
  try {
    console.log(`Resetting password for user: ${email}`);

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update in Sanity
    const sanityUser = await client.fetch(
      `*[_type == "adminUser" && email == $email][0]`,
      { email }
    );

    if (sanityUser) {
      console.log(`Found Sanity user: ${sanityUser._id}`);

      await client
        .patch(sanityUser._id)
        .set({
          hashedPassword,
          updatedAt: new Date().toISOString()
        })
        .commit();

      console.log('Updated password in Sanity');
    } else {
      console.log('User not found in Sanity');
    }

    // Update locally
    const localUsers = getLocalUsers();
    const userIndex = localUsers.findIndex(user => user.email === email);

    if (userIndex !== -1) {
      console.log('Found local user');

      localUsers[userIndex].password = hashedPassword;
      localUsers[userIndex].updatedAt = new Date().toISOString();

      saveLocalUsers(localUsers);
      console.log('Updated password locally');
    } else {
      console.log('User not found locally');
    }

    return true;
  } catch (error) {
    console.error('Error resetting password:', error);
    return false;
  }
};

// Main function
async function main() {
  console.log('Starting login credentials fix...');

  // Sync users from Sanity to local
  await syncUsersFromSanity();

  // Reset password for specific users
  console.log('Resetting passwords for super admin users...');

  // First, let's list all users to see their emails
  const sanityUsers = await fetchSanityUsers();
  console.log('Sanity users:');
  sanityUsers.forEach(user => {
    console.log(`- ${user.name || 'Unknown'} (${user.email || 'No email'}) [${user._id}]`);
  });

  // Reset passwords for the super admin users
  for (const user of sanityUsers) {
    if (user.role === 'super_admin') {
      console.log(`Resetting password for super admin: ${user.name} (${user.email})`);
      await resetUserPassword(user.email, 'Admin123!');
    }
  }

  console.log('\nLogin credentials fix complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
