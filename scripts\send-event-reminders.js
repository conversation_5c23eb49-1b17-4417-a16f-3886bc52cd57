/**
 * <PERSON><PERSON><PERSON> to send event reminders to RSVP submissions
 * 
 * This script fetches all RSVP submissions from Sanity and sends reminders
 * to attendees based on their reminder preferences.
 * 
 * Usage:
 * node scripts/send-event-reminders.js [event-code] [days-before]
 * 
 * Example:
 * node scripts/send-event-reminders.js coronation 7
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@sanity/client');

// Import the notification functions
const { sendEventReminderNotifications } = require('../lib/notifications');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with read access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Event dates (hardcoded for simplicity)
const eventDates = {
  'coronation': new Date('2025-08-29'),
  'gala': new Date('2025-08-30'),
  'forum': new Date('2025-08-31'),
};

// Main function
async function sendReminders() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const eventCode = args[0] || 'coronation';
    const daysBefore = parseInt(args[1] || '7', 10);
    
    // Validate event code
    if (!eventDates[eventCode]) {
      console.error(`Error: Invalid event code "${eventCode}". Valid options are: coronation, gala, forum`);
      process.exit(1);
    }
    
    // Calculate the target date for the reminder
    const eventDate = eventDates[eventCode];
    const today = new Date();
    const daysUntilEvent = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));
    
    if (daysUntilEvent !== daysBefore) {
      console.log(`Not sending reminders today. Event is in ${daysUntilEvent} days, but reminders are set for ${daysBefore} days before.`);
      return;
    }
    
    console.log(`Sending ${daysBefore}-day reminders for ${eventCode} event...`);
    
    // Fetch all RSVP submissions for this event
    const query = `*[_type == "rsvp" && $eventCode in events] {
      _id,
      firstName,
      lastName,
      email,
      phone,
      events,
      reminderPreference
    }`;
    
    const rsvps = await client.fetch(query, { eventCode });
    
    console.log(`Found ${rsvps.length} RSVPs for ${eventCode} event`);
    
    // Send reminders to each RSVP
    let successCount = 0;
    let errorCount = 0;
    
    for (const rsvp of rsvps) {
      try {
        await sendEventReminderNotifications(rsvp, eventCode, daysUntilEvent);
        successCount++;
        console.log(`Sent reminder to ${rsvp.firstName} ${rsvp.lastName} (${rsvp.email})`);
      } catch (error) {
        errorCount++;
        console.error(`Error sending reminder to ${rsvp.firstName} ${rsvp.lastName}:`, error);
      }
    }
    
    console.log(`Reminder sending complete. Success: ${successCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error sending reminders:', error);
    process.exit(1);
  }
}

// Run the script
sendReminders();
