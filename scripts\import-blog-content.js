/**
 * <PERSON><PERSON><PERSON> to import blog content from an external website into Sanity
 * 
 * This script fetches articles and images from the specified website and imports them into Sanity.
 * 
 * Usage:
 * node scripts/import-blog-content.js
 * 
 * Requirements:
 * - axios: for making HTTP requests
 * - cheerio: for parsing HTML
 * - form-data: for handling file uploads
 * - dotenv: for loading environment variables
 * 
 * Install dependencies:
 * npm install axios cheerio form-data dotenv
 */

require('dotenv').config({ path: '.env.local' });
const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Configuration
const config = {
  sourceUrl: 'https://cil.xem.mybluehost.me/website_963da4e6/blog/',
  tempDir: path.join(__dirname, '../temp'),
  imageDir: path.join(__dirname, '../temp/images'),
};

// Create temporary directories if they don't exist
if (!fs.existsSync(config.tempDir)) {
  fs.mkdirSync(config.tempDir, { recursive: true });
}
if (!fs.existsSync(config.imageDir)) {
  fs.mkdirSync(config.imageDir, { recursive: true });
}

/**
 * Fetch HTML content from a URL
 * @param {string} url - URL to fetch
 * @returns {Promise<string>} - HTML content
 */
async function fetchHtml(url) {
  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${url}:`, error.message);
    return null;
  }
}

/**
 * Extract article links from the blog index page
 * @param {string} html - HTML content of the blog index page
 * @returns {Array<string>} - Array of article URLs
 */
function extractArticleLinks(html) {
  const $ = cheerio.load(html);
  const links = [];
  
  // This selector might need to be adjusted based on the actual HTML structure
  $('a.article-link, .post-title a, .entry-title a').each((i, el) => {
    const href = $(el).attr('href');
    if (href && !links.includes(href)) {
      links.push(href.startsWith('http') ? href : new URL(href, config.sourceUrl).href);
    }
  });
  
  return links;
}

/**
 * Extract article data from an article page
 * @param {string} html - HTML content of the article page
 * @param {string} url - URL of the article
 * @returns {Object} - Article data
 */
function extractArticleData(html, url) {
  const $ = cheerio.load(html);
  
  // These selectors might need to be adjusted based on the actual HTML structure
  const title = $('.post-title, .entry-title').first().text().trim();
  const date = $('.post-date, .entry-date').first().text().trim();
  const content = $('.post-content, .entry-content').html();
  
  // Extract images
  const images = [];
  $('.post-content img, .entry-content img').each((i, el) => {
    const src = $(el).attr('src');
    const alt = $(el).attr('alt') || title;
    
    if (src) {
      const imageUrl = src.startsWith('http') ? src : new URL(src, url).href;
      images.push({ url: imageUrl, alt });
    }
  });
  
  // Extract excerpt
  let excerpt = '';
  $('.post-content p, .entry-content p').each((i, el) => {
    if (i === 0) {
      excerpt = $(el).text().trim();
    }
  });
  
  // Generate slug from title
  const slug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
  
  return {
    title,
    slug,
    date: date ? new Date(date) : new Date(),
    content,
    excerpt: excerpt || title,
    images,
  };
}

/**
 * Download an image from a URL
 * @param {string} url - Image URL
 * @param {string} filename - Filename to save the image as
 * @returns {Promise<string>} - Path to the downloaded image
 */
async function downloadImage(url, filename) {
  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'stream',
    });
    
    const filePath = path.join(config.imageDir, filename);
    const writer = fs.createWriteStream(filePath);
    
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(filePath));
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading image ${url}:`, error.message);
    return null;
  }
}

/**
 * Upload an image to Sanity
 * @param {string} filePath - Path to the image file
 * @param {string} filename - Original filename
 * @returns {Promise<Object>} - Uploaded image asset
 */
async function uploadImageToSanity(filePath, filename) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const asset = await client.assets.upload('image', fileBuffer, {
      filename,
    });
    return asset;
  } catch (error) {
    console.error(`Error uploading image ${filename} to Sanity:`, error.message);
    return null;
  }
}

/**
 * Convert HTML content to Portable Text format
 * @param {string} html - HTML content
 * @param {Array<Object>} imageAssets - Array of image assets
 * @returns {Array<Object>} - Portable Text blocks
 */
function htmlToPortableText(html, imageAssets) {
  const $ = cheerio.load(html);
  const blocks = [];
  let blockKey = 1;
  
  // Process each element in the content
  $('body > *').each((i, el) => {
    const tagName = el.tagName.toLowerCase();
    
    // Handle paragraphs
    if (tagName === 'p') {
      blocks.push({
        _type: 'block',
        _key: `block-${blockKey++}`,
        style: 'normal',
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: `span-${blockKey}`,
            text: $(el).text().trim(),
            marks: [],
          },
        ],
      });
    }
    
    // Handle headings
    else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      blocks.push({
        _type: 'block',
        _key: `block-${blockKey++}`,
        style: tagName,
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: `span-${blockKey}`,
            text: $(el).text().trim(),
            marks: [],
          },
        ],
      });
    }
    
    // Handle images
    else if (tagName === 'img') {
      const src = $(el).attr('src');
      const alt = $(el).attr('alt') || '';
      
      // Find the matching image asset
      const imageAsset = imageAssets.find(asset => asset.originalFilename === path.basename(src));
      
      if (imageAsset) {
        blocks.push({
          _type: 'image',
          _key: `image-${blockKey++}`,
          asset: {
            _type: 'reference',
            _ref: imageAsset._id,
          },
          alt,
        });
      }
    }
  });
  
  return blocks;
}

/**
 * Import articles into Sanity
 */
async function importArticles() {
  try {
    console.log('Starting blog content import...');
    
    // Fetch the blog index page
    console.log(`Fetching blog index page: ${config.sourceUrl}`);
    const indexHtml = await fetchHtml(config.sourceUrl);
    
    if (!indexHtml) {
      console.error('Failed to fetch blog index page');
      return;
    }
    
    // Extract article links
    const articleLinks = extractArticleLinks(indexHtml);
    console.log(`Found ${articleLinks.length} article links`);
    
    // Process each article
    for (let i = 0; i < articleLinks.length; i++) {
      const articleUrl = articleLinks[i];
      console.log(`Processing article ${i + 1}/${articleLinks.length}: ${articleUrl}`);
      
      // Fetch article HTML
      const articleHtml = await fetchHtml(articleUrl);
      
      if (!articleHtml) {
        console.error(`Failed to fetch article: ${articleUrl}`);
        continue;
      }
      
      // Extract article data
      const articleData = extractArticleData(articleHtml, articleUrl);
      console.log(`Extracted article: ${articleData.title}`);
      
      // Download and upload images
      const imageAssets = [];
      for (let j = 0; j < articleData.images.length; j++) {
        const image = articleData.images[j];
        const filename = path.basename(image.url);
        
        console.log(`Downloading image ${j + 1}/${articleData.images.length}: ${image.url}`);
        const filePath = await downloadImage(image.url, filename);
        
        if (filePath) {
          console.log(`Uploading image to Sanity: ${filename}`);
          const asset = await uploadImageToSanity(filePath, filename);
          
          if (asset) {
            imageAssets.push(asset);
          }
        }
      }
      
      // Convert HTML content to Portable Text
      const portableTextContent = htmlToPortableText(articleData.content, imageAssets);
      
      // Check if article already exists in Sanity
      const existingArticle = await client.fetch(
        `*[_type == "news" && slug.current == $slug][0]`,
        { slug: articleData.slug }
      );
      
      if (existingArticle) {
        console.log(`Article already exists: ${articleData.title}. Updating...`);
        
        // Update existing article
        await client
          .patch(existingArticle._id)
          .set({
            title: articleData.title,
            excerpt: articleData.excerpt,
            body: portableTextContent,
            publishedAt: articleData.date.toISOString(),
          })
          .commit();
        
        console.log(`Article updated: ${articleData.title}`);
      } else {
        console.log(`Creating new article: ${articleData.title}`);
        
        // Create new article
        const newArticle = await client.create({
          _type: 'news',
          title: articleData.title,
          slug: { _type: 'slug', current: articleData.slug },
          excerpt: articleData.excerpt,
          body: portableTextContent,
          publishedAt: articleData.date.toISOString(),
          status: 'published',
        });
        
        console.log(`Article created: ${articleData.title} with ID: ${newArticle._id}`);
      }
    }
    
    console.log('Blog content import completed successfully!');
  } catch (error) {
    console.error('Error importing blog content:', error);
  }
}

// Run the import function
importArticles();
