import { Metadata } from 'next';
import { generateDynamicNewsMetadata } from '@/lib/metadata-generator';
import { getNewsBySlug } from '@/lib/sanity';

// Generate dynamic metadata for news article pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Fetch the news article data
    const news = await getNewsBySlug(params.slug);

    if (!news) {
      return {
        title: 'Article Not Found',
        description: 'The requested news article could not be found.',
      };
    }

    // Generate metadata for the news article
    return generateDynamicNewsMetadata(news);
  } catch (error) {
    console.error('Error generating news metadata:', error);

    // Fallback metadata
    return {
      title: 'News Article',
      description: 'Read the latest news from the Kingdom of Adukrom.',
    };
  }
}
