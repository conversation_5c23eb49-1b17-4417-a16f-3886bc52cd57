'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import Link from 'next/link';
import { Shield, Users, CreditCard, ShoppingBag, Settings, AlertTriangle } from 'lucide-react';

export default function SuperAdminClient() {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if the user is authorized
    const checkAuth = async () => {
      try {
        // Get the session
        const session = await fetch('/api/auth/session').then(res => res.json());
        
        // Check if the user is a super admin
        if (session && session.user && session.user.role === 'SUPER_ADMIN') {
          setIsAuthorized(true);
        } else {
          // Redirect to the admin dashboard if not authorized
          router.push('/admin');
        }
        
        // Set loading to false
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking auth:', error);
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, [router]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
        <Button 
          variant="outline" 
          className="mt-4" 
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  // Show super admin content
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Shield className="mr-2 h-6 w-6 text-royalGold" />
          Super Admin Dashboard
        </h1>
        <p className="text-muted-foreground">
          Access restricted super administrator features and settings.
        </p>
      </div>

      <Separator />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="border-royalGold/20">
          <CardHeader className="bg-royalGold/5 rounded-t-lg">
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-royalBlue" />
              User Management
            </CardTitle>
            <CardDescription>
              Manage admin users and permissions
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <p className="text-sm mb-4">
              Add, remove, or modify admin accounts and control access levels.
            </p>
            <Button asChild>
              <Link href="/admin/users">
                Manage Users
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border-royalGold/20">
          <CardHeader className="bg-royalGold/5 rounded-t-lg">
            <CardTitle className="flex items-center">
              <ShoppingBag className="mr-2 h-5 w-5 text-royalBlue" />
              Store Management
            </CardTitle>
            <CardDescription>
              Configure the online store
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <p className="text-sm mb-4">
              Manage store settings, products, and e-commerce functionality.
            </p>
            <Button asChild>
              <Link href="/admin/store">
                Manage Store
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border-royalGold/20">
          <CardHeader className="bg-royalGold/5 rounded-t-lg">
            <CardTitle className="flex items-center">
              <CreditCard className="mr-2 h-5 w-5 text-royalBlue" />
              Banking & Payments
            </CardTitle>
            <CardDescription>
              Manage financial settings
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <p className="text-sm mb-4">
              Configure payment processors, banking information, and financial settings.
            </p>
            <Button asChild>
              <Link href="/admin/settings?tab=banking">
                Banking Settings
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Super Admin Access</h3>
            <p className="text-sm mt-1 text-yellow-700">
              This section is restricted to super administrators only. The features available here
              are not visible to regular administrators. Please be cautious when making changes as
              they can affect critical system functionality.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
