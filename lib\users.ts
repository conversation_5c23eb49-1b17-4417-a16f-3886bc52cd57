import fs from 'fs';
import path from 'path';
import bcrypt from 'bcryptjs';

// Path to the JSON file that will store user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// This file should only be imported in server components or API routes
// Add this check to prevent client-side usage
if (typeof window !== 'undefined') {
  throw new Error('users.ts should only be used on the server side');
}

// Ensure the data directory exists
const ensureDataDir = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Create the users file if it doesn't exist
const ensureUsersFile = () => {
  ensureDataDir();
  if (!fs.existsSync(usersFilePath)) {
    fs.writeFileSync(usersFilePath, JSON.stringify([], null, 2));
  }
};

// Get all users
export const getAllUsers = () => {
  ensureUsersFile();
  const usersData = fs.readFileSync(usersFilePath, 'utf8');
  return JSON.parse(usersData);
};

// Get a user by ID
export const getUserById = (id: string) => {
  const users = getAllUsers();
  return users.find((user: any) => user.id === id);
};

// Get a user by email
export const getUserByEmail = (email: string) => {
  const users = getAllUsers();
  return users.find((user: any) => user.email === email);
};

// Create a new user
export const createUser = async (userData: {
  name: string;
  email: string;
  password: string;
  role: string;
}) => {
  const users = getAllUsers();

  // Check if user with this email already exists
  const existingUserIndex = users.findIndex((user: any) => user.email === userData.email);

  if (existingUserIndex !== -1) {
    // If user exists but is temporary, update it
    if (users[existingUserIndex].isTemporary) {
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Update the temporary user
      users[existingUserIndex] = {
        ...users[existingUserIndex],
        name: userData.name,
        password: hashedPassword,
        role: userData.role,
        isTemporary: false,
        updatedAt: new Date().toISOString(),
      };

      // Save to file
      fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));

      // Return user without password
      const { password, ...userWithoutPassword } = users[existingUserIndex];
      return userWithoutPassword;
    } else {
      throw new Error('User with this email already exists');
    }
  }

  // Hash the password
  const hashedPassword = await bcrypt.hash(userData.password, 10);

  // Create new user object
  const newUser = {
    id: Date.now().toString(),
    name: userData.name,
    email: userData.email,
    password: hashedPassword,
    role: userData.role,
    isTemporary: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastLogin: null,
  };

  // Add to users array
  users.push(newUser);

  // Save to file
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));

  // Return user without password
  const { password, ...userWithoutPassword } = newUser;
  return userWithoutPassword;
};

// Create a temporary user (for invitations)
export const createTemporaryUser = async (userData: {
  email: string;
  role: string;
  invitedBy: string;
}) => {
  const users = getAllUsers();

  // Check if user with this email already exists
  const existingUser = users.find((user: any) => user.email === userData.email);

  if (existingUser) {
    if (existingUser.isTemporary) {
      // If user is already temporary, just return it
      const { password, ...userWithoutPassword } = existingUser;
      return userWithoutPassword;
    } else {
      throw new Error('User with this email already exists');
    }
  }

  // Create temporary user object
  const tempUser = {
    id: Date.now().toString(),
    email: userData.email,
    role: userData.role,
    isTemporary: true,
    invitedBy: userData.invitedBy,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    password: '', // No password yet
    name: '', // No name yet
    lastLogin: null,
  };

  // Add to users array
  users.push(tempUser);

  // Save to file
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));

  // Return user without password
  const { password, ...userWithoutPassword } = tempUser;
  return userWithoutPassword;
};

// Update a user
export const updateUser = async (
  id: string,
  userData: {
    name?: string;
    email?: string;
    password?: string;
    role?: string;
  }
) => {
  console.log('updateUser called with:', { id, userData });

  try {
    // Read the users file directly
    const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));
    console.log('Found users:', usersData.length);

    // Find the user by ID
    const userIndex = usersData.findIndex((user: any) => user.id === id);
    console.log('User index:', userIndex);

    if (userIndex === -1) {
      console.error('User not found with ID:', id);
      throw new Error('User not found');
    }

    // Get the current user
    const currentUser = usersData[userIndex];
    console.log('Current user:', {
      id: currentUser.id,
      name: currentUser.name,
      email: currentUser.email
    });

    // Prepare updated user
    const updatedUser = {
      ...currentUser,
      name: userData.name || currentUser.name,
      email: userData.email || currentUser.email,
      role: userData.role || currentUser.role,
      updatedAt: new Date().toISOString(),
    };

    console.log('Updated user data:', {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email
    });

    // Update password if provided
    if (userData.password) {
      updatedUser.password = await bcrypt.hash(userData.password, 10);
    }

    // Update the user in the array
    usersData[userIndex] = updatedUser;

    // Write the updated data back to the file
    fs.writeFileSync(usersFilePath, JSON.stringify(usersData, null, 2));
    console.log('User data saved to file successfully');

    // Return user without password
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
};

// Delete a user
export const deleteUser = (id: string) => {
  const users = getAllUsers();
  const filteredUsers = users.filter((user: any) => user.id !== id);

  if (filteredUsers.length === users.length) {
    throw new Error('User not found');
  }

  // Save to file
  fs.writeFileSync(usersFilePath, JSON.stringify(filteredUsers, null, 2));

  return true;
};

// Update last login time
export const updateLastLogin = (id: string) => {
  const users = getAllUsers();
  const userIndex = users.findIndex((user: any) => user.id === id);

  if (userIndex === -1) {
    return false;
  }

  users[userIndex].lastLogin = new Date().toISOString();

  // Save to file
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));

  return true;
};
