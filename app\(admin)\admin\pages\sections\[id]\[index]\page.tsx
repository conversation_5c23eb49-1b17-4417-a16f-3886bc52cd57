'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Image as ImageIcon } from 'lucide-react';

interface SectionEditorParams {
  params: {
    id: string;
    index: string;
  };
}

export default function SectionEditor({ params }: SectionEditorParams) {
  const { id, index } = params;
  const router = useRouter();
  const [section, setSection] = useState<any>(null);
  const [pageTitle, setPageTitle] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const sectionIndex = parseInt(index);

  // Fetch section data
  useEffect(() => {
    const fetchSection = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/pages/${id}/sections/${index}`);
        const data = await response.json();
        
        if (!response.ok || !data.success) {
          toast.error(data.message || 'Failed to load section');
          router.push(`/admin/pages/${id}`);
          return;
        }
        
        setSection(data.data.section);
        setPageTitle(data.data.pageTitle);
      } catch (error) {
        console.error('Error fetching section:', error);
        toast.error('Failed to load section');
      } finally {
        setIsLoading(false);
      }
    };

    if (id && index) {
      fetchSection();
    }
  }, [id, index, router]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSection((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setSection((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setSection((prev: any) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const response = await fetch(`/api/pages/${id}/sections/${index}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to save section');
      }

      toast.success('Section saved successfully');
      
      // Navigate back to the page editor
      router.push(`/admin/pages/${id}`);
    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Render form fields based on section type
  const renderSectionFields = () => {
    if (!section) return null;

    switch (section._type) {
      case 'hero':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={section.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={section.tagline || ''}
                onChange={handleInputChange}
                placeholder="Enter tagline"
              />
            </div>
            <div>
              <Label>Background Image</Label>
              <div className="mt-2 p-4 border border-dashed rounded-md text-center">
                {section.backgroundImage ? (
                  <div className="relative h-40 w-full">
                    <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                      <p className="text-sm text-gray-500">Image preview (available in Sanity Studio)</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ImageIcon className="h-8 w-8 mx-auto text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">
                      Image upload is available in Sanity Studio
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>
        );
      case 'textSection':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={section.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Text Content</Label>
              <Textarea
                id="text"
                name="text"
                value={typeof section.text === 'string' 
                  ? section.text 
                  : Array.isArray(section.text) 
                    ? JSON.stringify(section.text) 
                    : ''}
                onChange={(e) => {
                  try {
                    // Try to parse as JSON if it looks like JSON
                    if (e.target.value.trim().startsWith('[') && e.target.value.trim().endsWith(']')) {
                      const parsedValue = JSON.parse(e.target.value);
                      setSection((prev: any) => ({
                        ...prev,
                        text: parsedValue,
                      }));
                    } else {
                      // Otherwise treat as plain text
                      setSection((prev: any) => ({
                        ...prev,
                        text: e.target.value,
                      }));
                    }
                  } catch (error) {
                    // If JSON parsing fails, just use the raw text
                    setSection((prev: any) => ({
                      ...prev,
                      text: e.target.value,
                    }));
                  }
                }}
                placeholder="Enter text content"
                rows={8}
              />
              <p className="text-xs text-gray-500 mt-1">
                Note: For rich text content, you can edit this in the Sanity Studio for more formatting options.
              </p>
            </div>
            <div>
              <Label htmlFor="backgroundStyle">Background Style</Label>
              <Select
                value={section.backgroundStyle || 'none'}
                onValueChange={(value) => handleSelectChange('backgroundStyle', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select background style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="royalBlue">Royal Blue</SelectItem>
                  <SelectItem value="royalGold">Royal Gold</SelectItem>
                  <SelectItem value="ivory">Ivory</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );
      case 'imageGallery':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={section.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Description</Label>
              <Textarea
                id="text"
                name="text"
                value={section.text || ''}
                onChange={handleInputChange}
                placeholder="Enter gallery description"
                rows={4}
              />
            </div>
            <div>
              <Label>Gallery Images</Label>
              <div className="mt-2 p-4 border border-dashed rounded-md text-center">
                <ImageIcon className="h-8 w-8 mx-auto text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">
                  Image management is available in Sanity Studio
                </p>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="p-4 text-center">
            <p>This section type ({section._type}) can be edited in Sanity Studio</p>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-royalBlue mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading section...</p>
        </div>
      </div>
    );
  }

  if (!section) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-semibold">Section not found</p>
          <Button 
            className="mt-4"
            onClick={() => router.push(`/admin/pages/${id}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Page Editor
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button 
            variant="outline" 
            onClick={() => router.push(`/admin/pages/${id}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Page Editor
          </Button>
          <h1 className="text-2xl font-bold mt-2">
            Editing {section._type} in "{pageTitle}"
          </h1>
        </div>
        
        <Button 
          onClick={handleSubmit}
          disabled={isSaving}
        >
          {isSaving ? (
            <>Saving...</>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Section
            </>
          )}
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Edit Section Content</CardTitle>
            <CardDescription>
              Customize this section of your page
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderSectionFields()}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.push(`/admin/pages/${id}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={isSaving}
            >
              {isSaving ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Section
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
