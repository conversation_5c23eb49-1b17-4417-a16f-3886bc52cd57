/**
 * Dashboard configuration for Sanity Studio
 * This file configures the dashboard widgets and their layout
 */

// Dashboard widgets configuration
import { PlausibleWidget } from './components/PlausibleWidget';

export const dashboardConfig = {
  // Configure the widgets that appear on the dashboard
  widgets: [
    // Analytics Dashboard
    {
      name: 'component',
      component: PlausibleWidget,
      options: {
        // Replace with your actual shared link from Plausible
        sharedLink: 'https://plausible.io/share/adukingdom-62986aa3239a.herokuapp.com?auth=REPLACE_WITH_YOUR_SHARED_LINK_TOKEN'
      },
      layout: { width: 'full', height: 'large' }
    },

    // Project information
    {
      name: 'project-info',
      layout: { width: 'medium' }
    },

    // Project users
    {
      name: 'project-users',
      layout: { width: 'medium' }
    },

    // Content insights - Recently edited
    {
      name: 'document-list',
      options: {
        title: 'Recently edited',
        order: '_updatedAt desc',
        limit: 10,
        types: ['page', 'news', 'event', 'gallery', 'product']
      },
      layout: { width: 'medium' }
    },

    // Content insights - Recently published
    {
      name: 'document-list',
      options: {
        title: 'Recently published',
        order: '_publishedAt desc',
        limit: 10,
        types: ['page', 'news', 'event', 'gallery', 'product']
      },
      layout: { width: 'medium' }
    }
  ],
};
