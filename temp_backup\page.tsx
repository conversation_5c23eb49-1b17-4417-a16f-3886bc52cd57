'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';

// Define product type
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  featured: boolean;
  inStock: boolean;
}

// Define cart item type
interface CartItem extends Product {
  quantity: number;
}

export default function StorePage() {
  // State for mobile menu
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // State for product categories
  const [activeCategory, setActiveCategory] = useState('all');

  // State for cart
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);

  // State for products
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [categories, setCategories] = useState<{_id: string, title: string, slug: {current: string}}[]>([]);

  // Fetch products and categories from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        const response = await fetch('/api/store/products');
        const data = await response.json();

        if (data.success && data.products) {
          // Transform Sanity products to match our Product interface
          const transformedProducts: Product[] = data.products.map((product: any) => ({
            id: product._id,
            name: product.name,
            description: product.description || '',
            price: product.price,
            // Use the first image or a placeholder
            image: product.images && product.images.length > 0
              ? product.images[0].asset._ref
                ? `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}/${process.env.NEXT_PUBLIC_SANITY_DATASET}/${product.images[0].asset._ref.replace('image-', '').replace('-jpg', '.jpg').replace('-png', '.png').replace('-webp', '.webp')}`
                : product.images[0].url || '/Website Images/placeholder.png'
              : '/Website Images/placeholder.png',
            category: product.category ? product.category.title.toLowerCase() : 'uncategorized',
            featured: product.featured || false,
            inStock: product.inventory ? product.inventory.quantity > 0 : true
          }));

          setProducts(transformedProducts);
        }

        // Fetch store settings to get categories
        const settingsResponse = await fetch('/api/store/settings');
        const settingsData = await settingsResponse.json();

        if (settingsData.success && settingsData.settings && settingsData.settings.categories) {
          setCategories(settingsData.settings.categories);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  // Filter products by category
  const filteredProducts = activeCategory === 'all'
    ? products
    : products.filter(product => product.category === activeCategory);

  // Featured products
  const featuredProducts = products.filter(product => product.featured);

  // Add to cart function
  const addToCart = (product: Product) => {
    setCart(prevCart => {
      // Check if product is already in cart
      const existingItem = prevCart.find(item => item.id === product.id);

      if (existingItem) {
        // Increase quantity if already in cart
        return prevCart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        // Add new item to cart
        return [...prevCart, { ...product, quantity: 1 }];
      }
    });

    // Open cart when adding item
    setIsCartOpen(true);
  };

  // Remove from cart function
  const removeFromCart = (productId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== productId));
  };

  // Update quantity function
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) return;

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === productId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  // Calculate cart total
  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

  return (
    <div className="min-h-screen flex flex-col bg-ivory">
      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <section className="py-16 royal-gradient">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">
                Royal Merchandise Store
              </h1>
              <p className="text-xl text-royalGold mb-8">
                Official merchandise from the Kingdom of Adukrom
              </p>

              {/* Category Navigation */}
              <div className="flex flex-wrap justify-center gap-2 mt-8">
                <button
                  onClick={() => setActiveCategory('all')}
                  className={`px-4 py-2 rounded-full transition-colors ${
                    activeCategory === 'all'
                      ? 'bg-royalGold text-royalBlue'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  All Products
                </button>

                {/* Dynamic category buttons */}
                {categories.map((category) => (
                  <button
                    key={category._id}
                    onClick={() => setActiveCategory(category.title.toLowerCase())}
                    className={`px-4 py-2 rounded-full transition-colors ${
                      activeCategory === category.title.toLowerCase()
                        ? 'bg-royalGold text-royalBlue'
                        : 'bg-white/10 text-white hover:bg-white/20'
                    }`}
                  >
                    {category.title}
                  </button>
                ))}
              </div>

              {/* Cart Button */}
              <div className="mt-8">
                <button
                  onClick={() => setIsCartOpen(!isCartOpen)}
                  className="relative inline-flex items-center px-6 py-3 bg-white text-royalBlue rounded-full hover:bg-royalGold transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  Cart ({cart.reduce((total, item) => total + item.quantity, 0)})
                  {cart.length > 0 && (
                    <span className="absolute -top-2 -right-2 bg-royalGold text-royalBlue w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                      {cart.length}
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Products Section */}
        <section className="py-16 bg-ivory">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-royalBlue text-center mb-12 section-title">
              {activeCategory === 'all' ? 'Featured Products' : `${activeCategory.charAt(0).toUpperCase() + activeCategory.slice(1)}`}
            </h2>

            {isLoadingProducts ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
                <p className="ml-3 text-royalBlue">Loading products...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-20">
                <p className="text-lg text-gray-600">No products found</p>
                <p className="text-sm text-gray-500 mt-2">Check back soon for new merchandise!</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {(activeCategory === 'all' ? featuredProducts : filteredProducts).map((product) => (
                  <motion.div
                    key={product.id}
                    className="bg-white rounded-lg shadow-lg overflow-hidden product-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    whileHover={{ y: -5, boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)' }}
                  >
                    <div className="relative h-64 bg-gray-100">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                      {product.featured && (
                        <div className="absolute top-2 right-2 bg-royalGold text-royalBlue text-xs font-bold px-2 py-1 rounded">
                          Featured
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-royalBlue mb-2">{product.name}</h3>
                      <p className="text-charcoal/80 mb-4 line-clamp-2">{product.description}</p>
                      <div className="flex justify-between items-center">
                        <span className="text-royalBlue font-bold">${product.price.toFixed(2)}</span>
                        <button
                          onClick={() => addToCart(product)}
                          className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-4 rounded-full hover:bg-yellow-500 transition-colors"
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* All Products Section */}
        {activeCategory === 'all' && (
          <section className="py-16 royal-gradient">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold text-white text-center mb-12 section-title">
                All Products
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {products.map((product) => (
                  <motion.div
                    key={product.id}
                    className="bg-white rounded-lg shadow-lg overflow-hidden product-card"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    whileHover={{ y: -5, boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)' }}
                  >
                    <div className="relative h-64 bg-gray-100">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                      {product.featured && (
                        <div className="absolute top-2 right-2 bg-royalGold text-royalBlue text-xs font-bold px-2 py-1 rounded">
                          Featured
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-royalBlue mb-2">{product.name}</h3>
                      <p className="text-charcoal/80 mb-4 line-clamp-2">{product.description}</p>
                      <div className="flex justify-between items-center">
                        <span className="text-royalBlue font-bold">${product.price.toFixed(2)}</span>
                        <button
                          onClick={() => addToCart(product)}
                          className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-4 rounded-full hover:bg-yellow-500 transition-colors"
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Shopping Cart Sidebar */}
        <div className={`fixed top-0 right-0 h-full w-full md:w-96 bg-white shadow-xl transform transition-transform z-50 ${isCartOpen ? 'translate-x-0' : 'translate-x-full'}`}>
          <div className="p-6 h-full flex flex-col">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-royalBlue">Your Cart</h3>
              <button
                onClick={() => setIsCartOpen(false)}
                className="text-charcoal hover:text-royalBlue"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            {cart.length === 0 ? (
              <div className="flex-grow flex flex-col items-center justify-center">
                <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <p className="text-gray-500">Your cart is empty</p>
                <button
                  onClick={() => setIsCartOpen(false)}
                  className="mt-4 royal-button bg-royalGold text-royalBlue font-bold py-2 px-4 rounded-full hover:bg-yellow-500 transition-colors"
                >
                  Continue Shopping
                </button>
              </div>
            ) : (
              <>
                <div className="flex-grow overflow-y-auto">
                  {cart.map((item) => (
                    <div key={item.id} className="flex items-center py-4 border-b border-gray-200">
                      <div className="relative w-16 h-16 bg-gray-100 rounded overflow-hidden">
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-4 flex-grow">
                        <h4 className="text-sm font-medium text-royalBlue">{item.name}</h4>
                        <p className="text-xs text-charcoal/80">${item.price.toFixed(2)}</p>
                        <div className="flex items-center mt-1">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-charcoal"
                          >
                            -
                          </button>
                          <span className="mx-2 text-sm">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-charcoal"
                          >
                            +
                          </button>
                        </div>
                      </div>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex justify-between mb-4">
                    <span className="text-charcoal">Subtotal</span>
                    <span className="text-royalBlue font-bold">${cartTotal.toFixed(2)}</span>
                  </div>
                  <button
                    className="w-full royal-button bg-royalGold text-royalBlue font-bold py-3 px-4 rounded-full hover:bg-yellow-500 transition-colors"
                  >
                    Proceed to Checkout
                  </button>
                  <button
                    onClick={() => setIsCartOpen(false)}
                    className="w-full mt-2 text-center text-charcoal hover:text-royalBlue"
                  >
                    Continue Shopping
                  </button>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Overlay for cart */}
        {isCartOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsCartOpen(false)}
          ></div>
        )}
      </main>
    </div>
  );
}
