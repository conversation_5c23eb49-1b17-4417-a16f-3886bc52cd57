import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export const metadata = {
  title: 'Uplift Ghana | Kingdom of Adukrom',
  description: 'A Royal Initiative for National Prosperity, Inclusion, and Innovation',
};

export default function UpliftGhanaPage() {
  return (
    <div className="min-h-screen bg-ivory">
      {/* Back Home Button */}
      <div className="bg-royalBlue py-4">
        <div className="container mx-auto px-4">
          <Link
            href="/"
            className="inline-flex items-center text-white hover:text-royalGold transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to Home
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative py-20 bg-royalBlue text-white">
        <div className="absolute inset-0 z-0 opacity-20">
          <Image
            src="/Website Images/Flags Ghana.png"
            alt="Ghana Flag Background"
            fill
            style={{ objectFit: 'cover' }}
            priority
          />
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">Uplift Ghana</h1>
            <p className="text-xl md:text-2xl font-medium mb-8">
              A Royal Initiative for National Prosperity, Inclusion, and Innovation
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                href="#join"
                className="bg-transparent border-2 border-white text-white font-bold py-3 px-8 rounded-full hover:bg-white hover:text-royalBlue transition-colors shadow-lg"
              >
                Join the Movement
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section id="overview" className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-8 text-center">Overview</h2>
            <div className="prose prose-lg max-w-none">
              <p className="text-lg mb-6">
                Uplift Ghana is a visionary initiative launched under the patronage of His Majesty Mpuntuhene Allen Ellison,
                designed to accelerate socio-economic transformation across Ghana through strategic investments in education,
                entrepreneurship, infrastructure, and cultural empowerment.
              </p>
              <p className="text-lg mb-6">
                Rooted in the values of unity, sustainability, and Pan-African excellence, Uplift Ghana is a call to action—uniting
                public and private sectors, diaspora leaders, global investors, and communities to build a thriving, future-ready Ghana.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto grid md:grid-cols-2 gap-12">
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Mission Statement</h3>
              <p className="text-lg">
                To empower communities across Ghana by promoting inclusive development, nurturing local talent,
                and mobilizing global partnerships that foster economic opportunity and elevate national pride.
              </p>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Vision Statement</h3>
              <p className="text-lg">
                A Ghana where every citizen has the tools, resources, and platform to rise—economically,
                educationally, and culturally—under the banner of unity, innovation, and royal-led advancement.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Pillars Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-12 text-center">Core Pillars of Uplift Ghana</h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Pillar 1 */}
            <div className="bg-white p-8 rounded-lg shadow-lg border-t-4 border-royalGold">
              <h3 className="text-xl font-bold text-royalBlue mb-4">1. Education for Empowerment</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Renovation of 11 high schools across the Eastern Region</li>
                <li>Deployment of digital learning labs and teacher development programs</li>
                <li>Scholarships and leadership academies for underserved youth</li>
              </ul>
            </div>

            {/* Pillar 2 */}
            <div className="bg-white p-8 rounded-lg shadow-lg border-t-4 border-royalGold">
              <h3 className="text-xl font-bold text-royalBlue mb-4">2. Entrepreneurship & Local Industry</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Microgrant programs for youth and women entrepreneurs</li>
                <li>Agro-processing and manufacturing cluster development</li>
                <li>Royal Business Incubator in Adukrom for startups and artisans</li>
              </ul>
            </div>

            {/* Pillar 3 */}
            <div className="bg-white p-8 rounded-lg shadow-lg border-t-4 border-royalGold">
              <h3 className="text-xl font-bold text-royalBlue mb-4">3. Sustainable Infrastructure & Energy</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Smart community development projects</li>
                <li>Expansion of solar-powered microgrids and eco-housing units</li>
                <li>Modernization of the Adukrom Business District as a model economic hub</li>
              </ul>
            </div>

            {/* Pillar 4 */}
            <div className="bg-white p-8 rounded-lg shadow-lg border-t-4 border-royalGold">
              <h3 className="text-xl font-bold text-royalBlue mb-4">4. Cultural Renaissance & Diaspora Engagement</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Cultural heritage preservation through digital archiving and festivals</li>
                <li>Diaspora "Return to Invest" campaign under royal leadership</li>
                <li>Arts and fashion collaborations showcasing Ghana's identity to the world</li>
              </ul>
            </div>

            {/* Pillar 5 */}
            <div className="bg-white p-8 rounded-lg shadow-lg border-t-4 border-royalGold">
              <h3 className="text-xl font-bold text-royalBlue mb-4">5. Health & Human Services</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Community clinics and maternal care initiatives</li>
                <li>Nutrition and clean water access in rural districts</li>
                <li>Royal Mobile Health Caravans for outreach</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-8 text-center">Led By</h2>
          <div className="max-w-3xl mx-auto">
            <div className="bg-white p-8 rounded-lg shadow-lg text-center">
              <p className="text-lg mb-4">The Royal Palace of Adukrom</p>
              <p className="text-lg mb-4">Under the leadership of His Majesty Mpuntuhene Allen Ellison</p>
              <p className="text-lg">
                In strategic alliance with the Ellison Royal Sovereign Wealth Fund, the Nifaman Council of the Akuapem State,
                and national and international development partners.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Join the Movement Section */}
      <section id="join" className="py-16 md:py-24 bg-royalBlue text-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Join the Movement</h2>
          <div className="max-w-3xl mx-auto text-center">
            <p className="text-xl mb-8">
              Whether you're an investor, institution, policymaker, or proud Ghanaian,
              Uplift Ghana offers a platform to create lasting impact.
            </p>
            <div className="grid sm:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <div className="bg-white/10 p-6 rounded-lg backdrop-blur-sm">
                <h3 className="text-xl font-bold mb-2">Become a Founding Partner</h3>
                <p>Join our network of strategic partners and help shape the future of Ghana.</p>
              </div>
              <div className="bg-white/10 p-6 rounded-lg backdrop-blur-sm">
                <h3 className="text-xl font-bold mb-2">Sponsor a Project</h3>
                <p>Support specific initiatives aligned with your organization's values and goals.</p>
              </div>
              <div className="bg-white/10 p-6 rounded-lg backdrop-blur-sm">
                <h3 className="text-xl font-bold mb-2">Engage Your Diaspora Network</h3>
                <p>Connect Ghanaians abroad with opportunities to contribute to national development.</p>
              </div>
              <div className="bg-white/10 p-6 rounded-lg backdrop-blur-sm">
                <h3 className="text-xl font-bold mb-2">Attend the Uplift Ghana Royal Summit 2025</h3>
                <p>Join leaders and changemakers at our inaugural summit to chart Ghana's future.</p>
              </div>
            </div>
            <div className="mt-12">
              <Link
                href="/#contact"
                className="bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg inline-block"
              >
                Contact Us to Get Involved
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
