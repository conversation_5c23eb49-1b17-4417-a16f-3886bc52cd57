import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';
import { createClient } from '@sanity/client';

// GET /api/news/[id] - Get a specific news article
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;


    // Get the Sanity client with fallback
    let client;
    try {
      client = getWriteClient();
    } catch (error) {

      // Fallback to direct client creation with hardcoded token
      const hardcodedToken = "skZvPBz8yZn4iRP2UemnqcRDAx7bEknpdNoUhwSpL0mkffSi7B5n83PBrPUVvxww6QgsXbFDETUDESuxJmmb0l51Hzl3NNIC6YdJ3C9lEKii2Ydn3yQN4pu7se2ZwayyOSKzsjYJLp295ypq1xvM9fidravifu92uhZAx7sjYXKcfCAyGGrb";

      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: hardcodedToken,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }

    // Fetch the news article by ID
    const article = await client.fetch(
      `*[_type == "news" && _id == $id][0]{
        _id,
        title,
        excerpt,
        body,
        mainImage,
        category->{_id, title, slug},
        publishedAt,
        status,
        slug,
        featured,
        gallery[] {
          image,
          alt,
          caption
        }
      }`,
      { id }
    );

    if (!article) {
      return NextResponse.json(
        { success: false, message: 'News article not found' },
        { status: 404 }
      );
    }

    // Ensure body is in the correct format
    if (article.body) {
      // If body is a string (from older articles), convert it to Portable Text format
      if (typeof article.body === 'string') {
        article.body = [{
          _type: 'block',
          _key: `body-${Date.now()}`,
          style: 'normal',
          markDefs: [],
          children: [
            {
              _type: 'span',
              _key: `text-${Date.now()}`,
              text: article.body,
              marks: []
            }
          ]
        }];
      }
    } else {
      // If body is missing, initialize with empty array
      article.body = [];
    }

    return NextResponse.json({
      success: true,
      article,
    });
  } catch (error) {
    console.error('Error fetching news article:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch news article',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/news/[id] - Update a news article
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update news articles' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Parse the request body
    const body = await request.json();


    // Get the Sanity client with fallback
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
      console.error('Error getting write client:', error);

      // Fallback to direct client creation with hardcoded token
      const hardcodedToken = "skZvPBz8yZn4iRP2UemnqcRDAx7bEknpdNoUhwSpL0mkffSi7B5n83PBrPUVvxww6QgsXbFDETUDESuxJmmb0l51Hzl3NNIC6YdJ3C9lEKii2Ydn3yQN4pu7se2ZwayyOSKzsjYJLp295ypq1xvM9fidravifu92uhZAx7sjYXKcfCAyGGrb";

      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: hardcodedToken,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }

    // Check if the news article exists
    const existingArticle = await client.fetch(
      `*[_type == "news" && _id == $id][0]`,
      { id }
    );

    if (!existingArticle) {
      return NextResponse.json(
        { success: false, message: 'News article not found' },
        { status: 404 }
      );
    }

    // Prepare the update data
    const updateData: Record<string, any> = {};

    // Only include fields that are provided in the request
    if (body.title !== undefined) updateData.title = body.title;
    if (body.excerpt !== undefined) updateData.excerpt = body.excerpt;

    // Handle body content - ensure it's in the correct format
    if (body.body !== undefined) {
      if (typeof body.body === 'string') {
        // Convert string to Portable Text format
        updateData.body = [{
          _type: 'block',
          _key: `body-${Date.now()}`,
          style: 'normal',
          markDefs: [],
          children: [
            {
              _type: 'span',
              _key: `text-${Date.now()}`,
              text: body.body,
              marks: []
            }
          ]
        }];
      } else if (Array.isArray(body.body)) {
        // Use the array directly if it's already in Portable Text format
        updateData.body = body.body;
      } else {
        // Initialize with empty array if body is null or invalid
        updateData.body = [];
      }
    }

    if (body.category !== undefined) updateData.category = body.category;
    if (body.status !== undefined) updateData.status = body.status;
    if (body.featured !== undefined) updateData.featured = body.featured;
    if (body.publishedAt !== undefined) updateData.publishedAt = body.publishedAt;
    if (body.mainImage !== undefined) updateData.mainImage = body.mainImage;
    if (body.gallery !== undefined) updateData.gallery = body.gallery;

    // If slug is provided, update it
    if (body.slug !== undefined) {
      updateData.slug = body.slug;
    }

    // Update the news article
    const updatedArticle = await client.patch(id)
      .set(updateData)
      .commit();

    return NextResponse.json({
      success: true,
      message: 'News article updated successfully',
      article: updatedArticle,
    });
  } catch (error) {
    console.error('Error updating news article:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update news article',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/news/[id] - Delete a news article
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete news articles' },
        { status: 401 }
      );
    }

    const id = params.id;


    // Get the Sanity client

    // Get the Sanity client with fallback
    let client;
    try {
      client = getWriteClient();
    } catch (error) {

      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );

      console.error('Error getting write client:', error);

      // Fallback to direct client creation with hardcoded token
      const hardcodedToken = "skZvPBz8yZn4iRP2UemnqcRDAx7bEknpdNoUhwSpL0mkffSi7B5n83PBrPUVvxww6QgsXbFDETUDESuxJmmb0l51Hzl3NNIC6YdJ3C9lEKii2Ydn3yQN4pu7se2ZwayyOSKzsjYJLp295ypq1xvM9fidravifu92uhZAx7sjYXKcfCAyGGrb";

      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: hardcodedToken,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }

    // Check if the news article exists
    const existingArticle = await client.fetch(
      `*[_type == "news" && _id == $id][0]`,
      { id }
    );

    if (!existingArticle) {
      return NextResponse.json(
        { success: false, message: 'News article not found' },
        { status: 404 }
      );
    }

    // Delete the news article
    await client.delete(id);

    return NextResponse.json({
      success: true,
      message: 'News article deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting news article:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete news article',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
