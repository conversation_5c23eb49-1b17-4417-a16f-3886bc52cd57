import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin } from '@/lib/auth-utils';
import bcrypt from 'bcryptjs';

// POST /api/users/sync - Sync a user between NextAuth and Sanity
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to sync users' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.id || !body.email || !body.name || !body.role) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Check if the user already exists in Sanity
    const existingUser = await client.fetch(`
      *[_type == "adminUser" && (email == $email || _id == $id)][0] {
        _id,
        email
      }
    `, {
      email: body.email,
      id: body.id
    });

    if (existingUser) {
      // Update the existing user
      const updateData: Record<string, any> = {
        name: body.name,
        email: body.email,
        role: body.role,
        updatedAt: new Date().toISOString()
      };

      // Only update password if provided
      if (body.password) {
        const salt = await bcrypt.genSalt(10);
        updateData.hashedPassword = await bcrypt.hash(body.password, salt);
      }

      // Update the user in Sanity
      const updatedUser = await client
        .patch(existingUser._id)
        .set(updateData)
        .commit();

      return NextResponse.json({
        success: true,
        message: 'User updated in Sanity',
        user: {
          id: updatedUser._id,
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role
        }
      });
    } else {
      // Create a new user in Sanity
      const username = body.email.split('@')[0];
      
      const userData: Record<string, any> = {
        _type: 'adminUser',
        _id: body.id,
        username,
        name: body.name,
        email: body.email,
        role: body.role,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add password if provided
      if (body.password) {
        const salt = await bcrypt.genSalt(10);
        userData.hashedPassword = await bcrypt.hash(body.password, salt);
      }

      // Create the user in Sanity
      const createdUser = await client.createOrReplace(userData);

      return NextResponse.json({
        success: true,
        message: 'User created in Sanity',
        user: {
          id: createdUser._id,
          name: createdUser.name,
          email: createdUser.email,
          role: createdUser.role
        }
      });
    }
  } catch (error) {
    console.error('Error syncing user:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to sync user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/users/sync - Check if a user exists in both systems
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to check user sync status' },
        { status: 401 }
      );
    }

    // Get the email from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const email = searchParams.get('email');
    const id = searchParams.get('id');

    if (!email && !id) {
      return NextResponse.json(
        { success: false, message: 'Missing email or id parameter' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Build the query based on the provided parameters
    let query = '*[_type == "adminUser" && ';
    const params: Record<string, any> = {};

    if (email) {
      query += 'email == $email';
      params.email = email;
    } else if (id) {
      query += '_id == $id';
      params.id = id;
    }

    query += '][0] { _id, username, name, email, role, isActive, createdAt, updatedAt, lastLogin }';

    // Check if the user exists in Sanity
    const sanityUser = await client.fetch(query, params);

    return NextResponse.json({
      success: true,
      exists: !!sanityUser,
      user: sanityUser || null
    });
  } catch (error) {
    console.error('Error checking user sync status:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to check user sync status',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
