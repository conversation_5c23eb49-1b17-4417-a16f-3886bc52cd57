'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { toast } from 'sonner';
import { PlusCircle, Trash2, Edit, ExternalLink, AlertCircle } from 'lucide-react';
// Using the existing admin layout, no need for separate header and container
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { urlFor } from '@/lib/sanity.client';
import { Skeleton } from '@/components/ui/skeleton';

// Define the partner type
interface Partner {
  _id: string;
  name: string;
  slug: { current: string };
  description: string;
  website?: string;
  logo?: any;
  partnershipType: string;
  featured: boolean;
  order: number;
  startDate?: string;
  active: boolean;
}

export default function PartnersPage() {
  const router = useRouter();
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [partnerToDelete, setPartnerToDelete] = useState<Partner | null>(null);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newPartner, setNewPartner] = useState({
    name: '',
    description: '',
    website: '',
    partnershipType: 'corporate',
    featured: false,
    order: 0,
    active: true
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Function to fetch partners
  const fetchPartners = async () => {
    try {
      const response = await axios.get('/api/partners');
      if (response.data.success) {
        setPartners(response.data.partners);
      } else {
        console.error('Failed to fetch partners');
        // Only show toast for actual errors, not initial loading
        if (!loading) {
          toast.error('Failed to fetch partners');
        }
      }
    } catch (error) {
      console.error('Error fetching partners:', error);
      // Only show toast for actual errors, not initial loading
      if (!loading) {
        toast.error('An error occurred while fetching partners');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch partners on component mount
  useEffect(() => {
    fetchPartners();
  }, []);

  // Function to handle partner deletion
  const handleDeletePartner = async () => {
    if (!partnerToDelete) return;

    try {
      const response = await axios.delete(`/api/partners/${partnerToDelete._id}`);
      if (response.data.success) {
        toast.success('Partner deleted successfully');
        setPartners(partners.filter(partner => partner._id !== partnerToDelete._id));
        setDeleteDialogOpen(false);
        setPartnerToDelete(null);
      } else {
        toast.error(response.data.message || 'Failed to delete partner');
      }
    } catch (error) {
      console.error('Error deleting partner:', error);
      toast.error('An error occurred while deleting the partner');
    }
  };

  // Function to handle partner creation
  const handleAddPartner = async () => {
    if (!newPartner.name || !newPartner.description || !newPartner.partnershipType) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);

    try {
      // First, upload the logo if provided
      let logoRef = null;
      if (logoFile) {
        const formData = new FormData();
        formData.append('file', logoFile);
        formData.append('type', 'image');

        const uploadResponse = await axios.post('/api/upload', formData);
        if (uploadResponse.data.success) {
          // Create a proper Sanity image reference with required alt text
          logoRef = {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: uploadResponse.data.asset._id
            },
            alt: newPartner.name + ' logo'
          };
          console.log('Logo reference created:', logoRef);
        } else {
          toast.error('Failed to upload logo');
          setSubmitting(false);
          return;
        }
      }

      // Create the partner
      const response = await axios.post('/api/partners', {
        ...newPartner,
        logo: logoRef
      });

      if (response.data.success) {
        toast.success('Partner added successfully');
        setAddDialogOpen(false);
        setNewPartner({
          name: '',
          description: '',
          website: '',
          partnershipType: 'corporate',
          featured: false,
          order: 0,
          active: true
        });
        setLogoFile(null);
        setLogoPreview(null);
        fetchPartners(); // Refresh the partners list
      } else {
        toast.error(response.data.message || 'Failed to add partner');
      }
    } catch (error) {
      console.error('Error adding partner:', error);
      toast.error('An error occurred while adding the partner');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-royalBlue">Strategic Partners</h1>
          <Button onClick={() => setAddDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Partner
          </Button>
        </div>

        {partners.length === 0 && !loading ? (
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No partners found</h3>
            <p className="text-gray-500 mt-2">Get started by adding your first strategic partner.</p>
            <Button className="mt-4" onClick={() => setAddDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Partner
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading && partners.length === 0 ? (
              // Show a simple loading spinner instead of skeletons
              <div className="col-span-full flex justify-center items-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue mb-4"></div>
                  <p className="text-gray-500">Loading partners...</p>
                </div>
              </div>
            ) : (
              // Show actual partners
              partners.map((partner) => (
                <Card key={partner._id} className={`overflow-hidden ${!partner.active ? 'opacity-60' : ''}`}>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      {partner.name}
                      {partner.featured && (
                        <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">
                          Featured
                        </span>
                      )}
                    </CardTitle>
                    <CardDescription>{partner.partnershipType.charAt(0).toUpperCase() + partner.partnershipType.slice(1)} Partner</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {partner.logo && urlFor(partner.logo).url() ? (
                      <div className="relative h-40 w-full mb-4 bg-gray-100 rounded-md overflow-hidden">
                        <Image
                          src={urlFor(partner.logo).url() || ''}
                          alt={partner.name}
                          fill
                          className="object-contain"
                          unoptimized={true}
                        />
                      </div>
                    ) : (
                      <div className="h-40 w-full mb-4 bg-gray-100 rounded-md flex items-center justify-center">
                        <p className="text-gray-400">No logo</p>
                      </div>
                    )}
                    <p className="text-sm text-gray-600 line-clamp-3">{partner.description}</p>
                    {partner.website && (
                      <a
                        href={partner.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline flex items-center mt-2"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Visit Website
                      </a>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        setPartnerToDelete(partner);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        console.log('Navigating to edit partner with ID:', partner._id);
                        // Make sure the ID is valid before navigating
                        if (partner._id && typeof partner._id === 'string') {
                          router.push(`/admin/partners/edit/${partner._id}`);
                        } else {
                          toast.error('Invalid partner ID');
                          console.error('Invalid partner ID:', partner._id);
                        }
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Partner</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete {partnerToDelete?.name}? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeletePartner}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Partner Dialog */}
        <Dialog
          open={addDialogOpen}
          onOpenChange={(open) => {
            setAddDialogOpen(open);
            if (!open) {
              // Reset the preview when dialog is closed
              setLogoPreview(null);
            }
          }}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Add Strategic Partner</DialogTitle>
              <DialogDescription>
                Add a new strategic partner to showcase on your website.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name *
                </Label>
                <Input
                  id="name"
                  value={newPartner.name}
                  onChange={(e) => setNewPartner({ ...newPartner, name: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description *
                </Label>
                <Textarea
                  id="description"
                  value={newPartner.description}
                  onChange={(e) => setNewPartner({ ...newPartner, description: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="website" className="text-right">
                  Website
                </Label>
                <Input
                  id="website"
                  type="url"
                  value={newPartner.website}
                  onChange={(e) => setNewPartner({ ...newPartner, website: e.target.value })}
                  className="col-span-3"
                  placeholder="https://example.com"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="logo" className="text-right">
                  Logo
                </Label>
                <Input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    setLogoFile(file);

                    // Create preview URL for the selected image
                    if (file) {
                      const reader = new FileReader();
                      reader.onloadend = () => {
                        setLogoPreview(reader.result as string);
                      };
                      reader.readAsDataURL(file);
                    } else {
                      setLogoPreview(null);
                    }
                  }}
                  className="col-span-3"
                />
                <div className="col-span-3 col-start-2 mt-2">
                  <div className="relative h-40 w-full bg-gray-100 rounded-md overflow-hidden">
                    {logoPreview ? (
                      <Image
                        src={logoPreview}
                        alt="Logo preview"
                        fill
                        className="object-contain"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-gray-400">No logo</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="partnershipType" className="text-right">
                  Type *
                </Label>
                <Select
                  value={newPartner.partnershipType}
                  onValueChange={(value) => setNewPartner({ ...newPartner, partnershipType: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select partnership type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="corporate">Corporate</SelectItem>
                    <SelectItem value="ngo">NGO</SelectItem>
                    <SelectItem value="government">Government</SelectItem>
                    <SelectItem value="educational">Educational</SelectItem>
                    <SelectItem value="media">Media</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="order" className="text-right">
                  Display Order
                </Label>
                <Input
                  id="order"
                  type="number"
                  value={newPartner.order.toString()}
                  onChange={(e) => setNewPartner({ ...newPartner, order: parseInt(e.target.value) || 0 })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="featured" className="text-right">
                  Featured
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch
                    id="featured"
                    checked={newPartner.featured}
                    onCheckedChange={(checked) => setNewPartner({ ...newPartner, featured: checked })}
                  />
                  <Label htmlFor="featured">Display prominently on the homepage</Label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="active" className="text-right">
                  Active
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch
                    id="active"
                    checked={newPartner.active}
                    onCheckedChange={(checked) => setNewPartner({ ...newPartner, active: checked })}
                  />
                  <Label htmlFor="active">Show on the website</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddPartner} disabled={submitting}>
                {submitting ? 'Adding...' : 'Add Partner'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
