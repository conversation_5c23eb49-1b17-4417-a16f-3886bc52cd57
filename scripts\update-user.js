const fs = require('fs');
const path = require('path');

// Path to the users.json file
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Read the current users data
const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

// Find the user with the specified email
const userEmail = '<EMAIL>';
const newName = 'Samantha <PERSON>';

const userIndex = usersData.findIndex(user => user.email === userEmail);

if (userIndex === -1) {
  console.error(`User with email ${userEmail} not found`);
  process.exit(1);
}

// Update the user's name
usersData[userIndex].name = newName;
usersData[userIndex].updatedAt = new Date().toISOString();

// Write the updated data back to the file
fs.writeFileSync(usersFilePath, JSON.stringify(usersData, null, 2));

console.log(`User ${userEmail} name updated to ${newName}`);
