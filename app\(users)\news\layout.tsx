import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate dynamic metadata for this page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Royal News',
    description: 'Latest news and announcements from the Kingdom of Adukrom and <PERSON>.',
    url: '/news',
    keywords: ['Royal News', 'Adukrom Announcements', 'Kingdom Updates', 'King <PERSON>'],
  });
}

export default function NewsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <div>{children}</div>;
}
