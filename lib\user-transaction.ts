/**
 * User Transaction Utility
 * 
 * This module provides transaction-like operations for user management,
 * ensuring that operations are either completed successfully in both
 * Sanity CMS and local JSON storage, or rolled back in case of failure.
 */

import { getWriteClient } from './sanity.client';
import * as localUserStore from './users';
import bcrypt from 'bcryptjs';
import { logError } from './errorHandling';

// Interface for user data
export interface UserData {
  id?: string;
  _id?: string;
  name?: string;
  email?: string;
  username?: string;
  password?: string;
  role?: string;
  isActive?: boolean;
  permissions?: Record<string, any>;
  isProtected?: boolean;
}

/**
 * Create a user in both Sanity CMS and local storage
 */
export async function createUserTransaction(userData: UserData): Promise<any> {
  // Generate a unique ID for the user if not provided
  const userId = userData.id || userData._id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  
  // Hash the password
  let hashedPassword = '';
  if (userData.password) {
    const salt = await bcrypt.genSalt(10);
    hashedPassword = await bcrypt.hash(userData.password, 10);
  }

  // Generate username from email if not provided
  const username = userData.username || (userData.email ? userData.email.split('@')[0] : '');

  try {
    // Step 1: Create user in Sanity
    const client = getWriteClient();
    
    // Check if user already exists in Sanity
    const existingUser = await client.fetch(`
      *[_type == "adminUser" && (username == $username || email == $email)][0] {
        _id,
        username,
        email
      }
    `, {
      username,
      email: userData.email
    });

    if (existingUser) {
      const field = existingUser.username === username ? 'username' : 'email';
      throw new Error(`A user with this ${field} already exists in Sanity`);
    }

    // Create the user document in Sanity
    const sanityUser = {
      _type: 'adminUser',
      _id: userId,
      username,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      hashedPassword,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: userData.isActive !== undefined ? userData.isActive : true,
      permissions: userData.permissions || {},
      isProtected: userData.isProtected || false
    };

    const createdSanityUser = await client.createOrReplace(sanityUser);
    console.log('User created in Sanity:', createdSanityUser._id);

    // Step 2: Create user in local storage
    try {
      // Check if user exists in local storage
      const localUser = localUserStore.getUserByEmail(userData.email || '');
      
      if (localUser) {
        throw new Error('User already exists in local storage');
      }

      // Create user in local storage
      const createdLocalUser = await localUserStore.createUser({
        name: userData.name || '',
        email: userData.email || '',
        password: userData.password || '',
        role: userData.role || 'user'
      });

      console.log('User created in local storage:', createdLocalUser.id);

      // Return the created user without sensitive data
      const { hashedPassword: _, ...userWithoutPassword } = createdSanityUser;
      return {
        success: true,
        user: userWithoutPassword,
        message: 'User created successfully in both systems'
      };
    } catch (localError) {
      // If local storage creation fails, delete from Sanity to maintain consistency
      console.error('Error creating user in local storage:', localError);
      
      try {
        await client.delete(userId);
        console.log('Rolled back Sanity user creation due to local storage error');
      } catch (rollbackError) {
        console.error('Error rolling back Sanity user creation:', rollbackError);
      }
      
      throw new Error(`Failed to create user in local storage: ${localError instanceof Error ? localError.message : 'Unknown error'}`);
    }
  } catch (error) {
    logError(error, 'createUserTransaction');
    throw error;
  }
}

/**
 * Update a user in both Sanity CMS and local storage
 */
export async function updateUserTransaction(userId: string, userData: UserData): Promise<any> {
  try {
    // Step 1: Update user in Sanity
    const client = getWriteClient();
    
    // Check if the user exists in Sanity
    const existingUser = await client.fetch(`
      *[_type == "adminUser" && _id == $id][0]
    `, { id: userId });

    if (!existingUser) {
      throw new Error('User not found in Sanity');
    }

    // Prepare the update object for Sanity
    const updateObj: Record<string, any> = {
      updatedAt: new Date().toISOString()
    };

    // Add fields to update
    if (userData.username) updateObj.username = userData.username;
    if (userData.email) updateObj.email = userData.email;
    if (userData.name) updateObj.name = userData.name;
    if (userData.role) updateObj.role = userData.role;
    if (userData.isActive !== undefined) updateObj.isActive = userData.isActive;
    if (userData.permissions) updateObj.permissions = userData.permissions;
    if (userData.isProtected !== undefined) updateObj.isProtected = userData.isProtected;

    // Handle password update if provided
    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      updateObj.hashedPassword = await bcrypt.hash(userData.password, salt);
    }

    // Update the user in Sanity
    const updatedSanityUser = await client
      .patch(userId)
      .set(updateObj)
      .commit();
    
    console.log('User updated in Sanity:', updatedSanityUser._id);

    // Step 2: Update user in local storage
    try {
      // Check if the user exists in local storage
      const localUser = localUserStore.getUserById(userId);
      
      if (localUser) {
        // Only include fields that should be updated
        const localUpdateData: Record<string, any> = {};
        if (userData.name) localUpdateData.name = userData.name;
        if (userData.email) localUpdateData.email = userData.email;
        if (userData.role) localUpdateData.role = userData.role;
        if (userData.password) localUpdateData.password = userData.password;

        // Update the user in local storage
        const updatedLocalUser = await localUserStore.updateUser(userId, localUpdateData);
        console.log('User updated in local storage:', updatedLocalUser.id);
      } else {
        console.log('User not found in local storage, skipping local update');
      }

      // Return the updated user without sensitive data
      const { hashedPassword, ...userWithoutPassword } = updatedSanityUser;
      return {
        success: true,
        user: userWithoutPassword,
        message: 'User updated successfully'
      };
    } catch (localError) {
      console.error('Error updating user in local storage:', localError);
      // Continue anyway since the user is updated in Sanity
      // We don't roll back Sanity updates because Sanity is the source of truth
      
      const { hashedPassword, ...userWithoutPassword } = updatedSanityUser;
      return {
        success: true,
        user: userWithoutPassword,
        message: 'User updated in Sanity but local update failed',
        localError: localError instanceof Error ? localError.message : 'Unknown error'
      };
    }
  } catch (error) {
    logError(error, 'updateUserTransaction');
    throw error;
  }
}

/**
 * Delete a user from both Sanity CMS and local storage
 */
export async function deleteUserTransaction(userId: string): Promise<any> {
  try {
    // Step 1: Check if user can be deleted
    const client = getWriteClient();
    
    // Check if the user exists in Sanity
    const existingUser = await client.fetch(`
      *[_type == "adminUser" && _id == $id][0]{
        _id,
        username,
        email,
        role,
        isProtected
      }
    `, { id: userId });

    if (!existingUser) {
      throw new Error('User not found in Sanity');
    }

    // Check if the user is protected
    if (existingUser.isProtected) {
      throw new Error('Cannot delete a protected user');
    }

    // Check if this is the last super_admin user
    if (existingUser.role === 'super_admin') {
      const superAdminCount = await client.fetch(
        `count(*[_type == "adminUser" && role == "super_admin"])`
      );

      if (superAdminCount <= 1) {
        throw new Error('Cannot delete the last super admin user');
      }
    }

    // Step 2: Delete user from Sanity
    await client.delete(userId);
    console.log('User deleted from Sanity:', userId);

    // Step 3: Delete user from local storage
    try {
      // Check if the user exists in local storage
      const localUser = localUserStore.getUserById(userId);
      
      if (localUser) {
        // Delete the user from local storage
        await localUserStore.deleteUser(userId);
        console.log('User deleted from local storage:', userId);
      } else {
        console.log('User not found in local storage, skipping local deletion');
      }

      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (localError) {
      console.error('Error deleting user from local storage:', localError);
      // Continue anyway since the user is deleted from Sanity
      
      return {
        success: true,
        message: 'User deleted from Sanity but local deletion failed',
        localError: localError instanceof Error ? localError.message : 'Unknown error'
      };
    }
  } catch (error) {
    logError(error, 'deleteUserTransaction');
    throw error;
  }
}
