import type { <PERSON>ada<PERSON> } from 'next';
import { Mont<PERSON><PERSON> } from 'next/font/google';
import './global.css';
import { Providers } from '@/lib/providers/Providers';
import AdminControls from '@/components/AdminControls';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata();
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${montserrat.className} bg-ivory`} suppressHydrationWarning>
        <Providers>
          {children}
          <AdminControls />
        </Providers>

      </body>
    </html>
  );
}
