import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@sanity/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Create a Sanity client with write permissions
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Use the server-side token with write permissions
  apiVersion: '2025-05-09', // Use a fixed API version
  useCdn: false,
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin for protected sections
    const userRole = session.user.role;
    const isSuperAdmin = userRole === 'super_admin';

    // Parse the request body
    const body = await request.json();
    const { section, data } = body;

    if (!section || !data) {
      return NextResponse.json(
        { success: false, message: 'Missing section or data' },
        { status: 400 }
      );
    }

    // Check permissions for protected sections
    if ((section === 'banking' || section === 'store') && !isSuperAdmin) {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    console.log(`Updating ${section} settings:`, data);

    // Get the siteSettings document
    const settingsQuery = `*[_type == "siteSettings"][0]`;
    let settingsDoc = await client.fetch(settingsQuery);

    // If no settings document exists, create one
    if (!settingsDoc) {
      console.log('No settings document found, creating one...');
      settingsDoc = await client.create({
        _type: 'siteSettings',
        title: 'The Royal Family of Africa',
        description: 'The Crown of Africa. The Rise of a New Era.',
      });
      console.log('Created new settings document:', settingsDoc);
    }

    // Update the appropriate section
    let updateData = {};
    let result;

    switch (section) {
      case 'general':
        updateData = {
          title: data.siteName,
          description: data.siteDescription,
          keywords: data.siteKeywords.split(',').map((k: string) => k.trim()),
          maintenanceMode: data.enableMaintenanceMode,
        };
        break;

      case 'contact':
        updateData = {
          contact: {
            email: data.email,
            phone: data.phone,
            address: data.address,
            mapCoordinates: data.mapCoordinates,
          }
        };
        break;

      case 'social':
        updateData = {
          social: {
            instagram: data.instagram,
            facebook: data.facebook,
            twitter: data.twitter,
          }
        };
        break;

      case 'events':
        // Ensure the date is valid
        try {
          // Validate the date format
          if (!data.coronationDate || !data.coronationDate.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?$/)) {
            throw new Error('Invalid date format');
          }

          // Parse the input date (which is in local time)
          const inputDate = new Date(data.coronationDate);
          if (isNaN(inputDate.getTime())) {
            throw new Error('Invalid date');
          }

          // Convert to ISO string (which will be in UTC with 'Z' suffix)
          const utcDateString = inputDate.toISOString();

          console.log('Original date input:', data.coronationDate);
          console.log('Storing date in UTC format:', utcDateString);

          updateData = {
            events: {
              // Store the date in UTC format
              coronationDate: utcDateString,
              coronationLocation: data.coronationLocation || '',
              showCountdown: typeof data.showCountdown === 'boolean' ? data.showCountdown : true,
            }
          };
        } catch (err) {
          return NextResponse.json(
            { success: false, message: 'Invalid date format' },
            { status: 400 }
          );
        }
        break;

      case 'banking':
        // Handle banking settings
        updateData = {
          banking: {
            accountName: data.accountName || '',
            accountNumber: data.accountNumber || '',
            routingNumber: data.routingNumber || '',
            bankName: data.bankName || '',
            swiftCode: data.swiftCode || '',
            paymentProcessor: data.paymentProcessor || 'stripe',
            enablePayments: typeof data.enablePayments === 'boolean' ? data.enablePayments : false,
          }
        };
        break;

      case 'store':
        // Handle store settings
        updateData = {
          store: {
            enableStore: typeof data.enableStore === 'boolean' ? data.enableStore : false,
            shippingFee: data.shippingFee || '10.00',
            taxRate: data.taxRate || '7.5',
            currencySymbol: data.currencySymbol || '$',
            allowInternationalShipping: typeof data.allowInternationalShipping === 'boolean'
              ? data.allowInternationalShipping
              : false,
            minimumOrderAmount: data.minimumOrderAmount || '25.00',
            // NFT settings
            nftSettings: {
              contractAddress: data.nftContractAddress || '',
              blockchainNetwork: data.blockchainNetwork || 'ethereum',
              enableNFTs: typeof data.enableNFTs === 'boolean' ? data.enableNFTs : false,
            }
          }
        };
        break;

      case 'metadata':
        // Handle metadata settings
        updateData = {
          metadata: {
            metaTitle: data.metaTitle || '',
            metaDescription: data.metaDescription || '',
            metaKeywords: data.metaKeywords ?
              (typeof data.metaKeywords === 'string' ?
                data.metaKeywords.split(',').map((k: string) => k.trim()) :
                (Array.isArray(data.metaKeywords) ? data.metaKeywords : [])) :
              [],
            twitterHandle: data.twitterHandle || '',
            siteUrl: data.siteUrl || '',
            titleTemplate: data.titleTemplate || '%s | The Crown of Africa',
          }
        };
        break;

      case 'seo':
        // Handle SEO settings (including ogImage)
        updateData = {
          seo: {
            ...(settingsDoc.seo || {}),
            openGraph: {
              ...(settingsDoc.seo?.openGraph || {}),
              image: data.ogImage || settingsDoc.seo?.openGraph?.image,
            }
          }
        };
        break;

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid section' },
          { status: 400 }
        );
    }

    console.log('Update data:', updateData);

    // Try to patch the document
    try {
      // Check if we have a valid token before attempting to update
      if (!process.env.SANITY_API_TOKEN) {
        console.warn('No Sanity API token found. This is a demo mode - pretending to save settings.');
        console.log('Available environment variables:', Object.keys(process.env).filter(key => key.includes('SANITY')));

        // In demo mode, just pretend we saved successfully
        result = {
          _id: settingsDoc._id,
          _type: 'siteSettings',
          ...updateData
        };

        return NextResponse.json({
          success: true,
          message: `${section} settings updated successfully (DEMO MODE)`,
          data: result,
          demoMode: true
        });
      }

      console.log('Using Sanity API token:', process.env.SANITY_API_TOKEN ? 'Token exists' : 'No token');

      // Real update with token
      result = await client
        .patch(settingsDoc._id)
        .set(updateData)
        .commit();
      console.log('Patch result:', result);
    } catch (patchError) {
      console.error('Error during patch operation:', patchError);
      console.error('Error details:', JSON.stringify(patchError, null, 2));

      // Log the client configuration
      console.log('Sanity client configuration:', {
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
        apiVersion: '2025-05-09',
        tokenExists: !!process.env.SANITY_API_TOKEN
      });

      // If patching fails, try replacing the entire document
      try {
        console.log('Trying alternative approach with direct document replacement');
        const updatedDoc = {
          ...settingsDoc,
          ...updateData,
        };
        result = await client.createOrReplace(updatedDoc);
        console.log('Replace result:', result);
      } catch (replaceError: unknown) {
        console.error('Error during document replacement:', replaceError);
        console.error('Replace error details:', JSON.stringify(replaceError, null, 2));

        // Check if this is a permissions error
        const errorMessage = replaceError instanceof Error
          ? replaceError.message
          : 'Unknown error during settings update';

        // If it's a permissions error, provide a more helpful message
        if (errorMessage.includes('Insufficient permissions')) {
          return NextResponse.json(
            {
              success: false,
              message: 'Failed to update settings due to Sanity permissions. Please check your API token has write permissions.',
              error: errorMessage,
              demoMode: true // Indicate we're falling back to demo mode
            },
            { status: 200 } // Return 200 so the UI doesn't show an error
          );
        }

        return NextResponse.json(
          { success: false, message: 'Failed to update settings', error: errorMessage },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: `${section} settings updated successfully`,
      data: result,
    });
  } catch (error: any) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update settings', error: error.message },
      { status: 500 }
    );
  }
}
