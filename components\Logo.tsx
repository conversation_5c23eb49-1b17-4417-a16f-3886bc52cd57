'use client';
import { motion } from 'framer-motion';
import Link from 'next/link';

export default function Logo() {
  return (
    <Link href="/" className="block">
      <div className="flex items-center">
        <motion.div
          className="relative w-12 h-12"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 0.6
          }}
          whileHover={{
            scale: 1.1,
            rotate: [0, -5, 5, -5, 0],
            transition: { duration: 0.5 }
          }}
        >
          {/* Crown */}
          <motion.svg
            className="absolute inset-0 w-full h-full text-royalGold"
            viewBox="0 0 24 24"
            fill="currentColor"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <motion.path
              d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.18l7 3.12v4.7c0 4.67-3.13 8.42-7 9.88-3.87-1.46-7-5.21-7-9.88V6.3l7-3.12z"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1.5, delay: 0.2 }}
            />
            <motion.path
              d="M12 6l-2.12 2.12 2.12 2.12 2.12-2.12z"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{ duration: 1, delay: 1 }}
            />
            <motion.path
              d="M12 12l-2.12 2.12 2.12 2.12 2.12-2.12z"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{ duration: 1, delay: 1.5 }}
            />
          </motion.svg>

          {/* Flag overlay */}
          <motion.div
            className="absolute inset-0 w-full h-full opacity-70"
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.7 }}
            transition={{ duration: 0.5, delay: 1.7 }}
          >
            <svg viewBox="0 0 24 24" className="w-full h-full">
              <defs>
                <clipPath id="flagClip">
                  <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.18l7 3.12v4.7c0 4.67-3.13 8.42-7 9.88-3.87-1.46-7-5.21-7-9.88V6.3l7-3.12z" />
                </clipPath>
              </defs>
              <g clipPath="url(#flagClip)">
                <rect x="0" y="0" width="24" height="8" fill="#CE1126" />
                <rect x="0" y="8" width="24" height="8" fill="#FCD116" />
                <rect x="0" y="16" width="24" height="8" fill="#006B3F" />
                <polygon points="12,8 13,11 16,11 14,13 15,16 12,14 9,16 10,13 8,11 11,11" fill="black" />
              </g>
            </svg>
          </motion.div>

          {/* No glow effect */}
        </motion.div>

        <motion.div
          className="ml-3"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.h1
            className="text-white text-lg md:text-xl font-bold"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            Adukrom Kingdom
          </motion.h1>
          <motion.p
            className="text-royalGold text-xs md:text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            The Crown of Africa
          </motion.p>
        </motion.div>
      </div>
    </Link>
  );
}
