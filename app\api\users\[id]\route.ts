import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getUserById, updateUser, deleteUser } from '@/lib/users';

// GET /api/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin
    const userRole = session.user.role;
    if (userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get user by ID
    const user = getUserById(id);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user;

    return NextResponse.json({ success: true, user: userWithoutPassword });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update a user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin
    const userRole = session.user.role;
    if (userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Parse request body
    const body = await request.json();
    const { name, email, password, role } = body;

    try {
      // Update user
      const updatedUser = await updateUser(id, {
        name,
        email,
        password,
        role,
      });

      return NextResponse.json({
        success: true,
        message: 'User updated successfully',
        user: updatedUser
      });
    } catch (error: any) {
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to update user' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin
    const userRole = session.user.role;
    if (userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Prevent deleting the current user
    if (id === session.user.id) {
      return NextResponse.json(
        { success: false, message: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    try {
      // Delete user
      deleteUser(id);
    } catch (error: any) {
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to delete user' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
