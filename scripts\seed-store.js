// Script to seed the Sanity database with sample store products and categories
const { createClient } = require('@sanity/client');
require('dotenv').config({ path: '.env.local' });

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Sample categories
const categories = [
  {
    _type: 'category',
    title: 'Apparel',
    slug: { _type: 'slug', current: 'apparel' },
    description: 'Clothing and wearable items featuring the royal emblem of Adukrom Kingdom',
    color: '#002366', // Royal blue
    order: 1,
  },
  {
    _type: 'category',
    title: 'Accessories',
    slug: { _type: 'slug', current: 'accessories' },
    description: 'Accessories and everyday items with the Adukrom Kingdom branding',
    color: '#FFD700', // Gold
    order: 2,
  },
  {
    _type: 'category',
    title: 'Collectibles',
    slug: { _type: 'slug', current: 'collectibles' },
    description: 'Limited edition collectible items commemorating special royal events',
    color: '#8B0000', // Dark red
    order: 3,
  },
];

// Sample products
const products = [
  {
    _type: 'product',
    name: 'Royal Crown T-Shirt',
    slug: { _type: 'slug', current: 'royal-crown-tshirt' },
    description: 'Premium cotton t-shirt featuring the royal crown emblem of Adukrom Kingdom.',
    price: 29.99,
    currency: 'USD',
    images: [
      {
        _type: 'image',
        asset: {
          _type: 'reference',
          _ref: 'image-placeholder-tshirt', // This will be replaced with actual image reference
        },
      },
    ],
    category: { _type: 'reference', _ref: 'category-apparel' }, // This will be replaced with actual category reference
    tags: ['clothing', 'royal', 'merchandise'],
    featured: true,
    inventory: { quantity: 25, trackInventory: true, allowBackorder: false },
    isDigital: false,
    publishedAt: new Date().toISOString(),
  },
  {
    _type: 'product',
    name: 'Adukrom Kingdom Hoodie',
    slug: { _type: 'slug', current: 'adukrom-hoodie' },
    description: 'Comfortable hoodie with the Adukrom Kingdom logo embroidered on the front.',
    price: 49.99,
    currency: 'USD',
    images: [
      {
        _type: 'image',
        asset: {
          _type: 'reference',
          _ref: 'image-placeholder-hoodie', // This will be replaced with actual image reference
        },
      },
    ],
    category: { _type: 'reference', _ref: 'category-apparel' }, // This will be replaced with actual category reference
    tags: ['clothing', 'royal', 'merchandise', 'hoodie'],
    featured: true,
    inventory: { quantity: 15, trackInventory: true, allowBackorder: false },
    isDigital: false,
    publishedAt: new Date().toISOString(),
  },
  {
    _type: 'product',
    name: 'Royal Crest Mug',
    slug: { _type: 'slug', current: 'royal-mug' },
    description: 'Ceramic mug featuring the royal crest of King Allen Ellison.',
    price: 19.99,
    currency: 'USD',
    images: [
      {
        _type: 'image',
        asset: {
          _type: 'reference',
          _ref: 'image-placeholder-mug', // This will be replaced with actual image reference
        },
      },
    ],
    category: { _type: 'reference', _ref: 'category-accessories' }, // This will be replaced with actual category reference
    tags: ['mug', 'royal', 'merchandise', 'kitchen'],
    featured: false,
    inventory: { quantity: 30, trackInventory: true, allowBackorder: true },
    isDigital: false,
    publishedAt: new Date().toISOString(),
  },
  {
    _type: 'product',
    name: 'Coronation Commemorative Plate',
    slug: { _type: 'slug', current: 'commemorative-plate' },
    description: 'Limited edition commemorative plate celebrating the coronation of King Allen Ellison.',
    price: 79.99,
    currency: 'USD',
    images: [
      {
        _type: 'image',
        asset: {
          _type: 'reference',
          _ref: 'image-placeholder-plate', // This will be replaced with actual image reference
        },
      },
    ],
    category: { _type: 'reference', _ref: 'category-collectibles' }, // This will be replaced with actual category reference
    tags: ['plate', 'royal', 'merchandise', 'collectible', 'limited edition'],
    featured: true,
    inventory: { quantity: 10, trackInventory: true, allowBackorder: false },
    isDigital: false,
    publishedAt: new Date().toISOString(),
  },
];

// Function to create site settings if they don't exist
async function createSiteSettings() {
  try {
    // Check if site settings already exist
    const existingSettings = await client.fetch('*[_type == "siteSettings"][0]');
    
    if (!existingSettings) {
      console.log('Creating site settings...');
      await client.create({
        _type: 'siteSettings',
        title: 'The Royal Family of Africa',
        description: 'The Crown of Africa. The Rise of a New Era.',
        store: {
          enableStore: true,
          shippingFee: '10.00',
          taxRate: '7.5',
          currencySymbol: '$',
          allowInternationalShipping: false,
          minimumOrderAmount: '25.00',
        },
      });
      console.log('Site settings created successfully');
    } else {
      console.log('Site settings already exist, updating store settings...');
      await client
        .patch(existingSettings._id)
        .set({
          store: {
            ...(existingSettings.store || {}),
            enableStore: true,
          },
        })
        .commit();
      console.log('Store settings updated successfully');
    }
  } catch (error) {
    console.error('Error creating/updating site settings:', error);
  }
}

// Main function to seed the database
async function seedDatabase() {
  try {
    console.log('Starting to seed the database...');
    
    // Create site settings
    await createSiteSettings();
    
    // Create categories
    console.log('Creating categories...');
    const createdCategories = [];
    for (const category of categories) {
      // Check if category already exists
      const existingCategory = await client.fetch(
        '*[_type == "category" && slug.current == $slug][0]',
        { slug: category.slug.current }
      );
      
      if (!existingCategory) {
        const createdCategory = await client.create(category);
        createdCategories.push(createdCategory);
        console.log(`Category created: ${category.title}`);
      } else {
        createdCategories.push(existingCategory);
        console.log(`Category already exists: ${category.title}`);
      }
    }
    
    // Create products
    console.log('Creating products...');
    for (const product of products) {
      // Check if product already exists
      const existingProduct = await client.fetch(
        '*[_type == "product" && slug.current == $slug][0]',
        { slug: product.slug.current }
      );
      
      if (!existingProduct) {
        // Find the correct category reference
        const categoryTitle = product.name.toLowerCase().includes('mug') || product.name.toLowerCase().includes('pen')
          ? 'Accessories'
          : product.name.toLowerCase().includes('plate') || product.name.toLowerCase().includes('book')
          ? 'Collectibles'
          : 'Apparel';
        
        const category = createdCategories.find(cat => cat.title === categoryTitle);
        
        if (category) {
          product.category._ref = category._id;
        }
        
        // Create the product
        const createdProduct = await client.create(product);
        console.log(`Product created: ${product.name}`);
      } else {
        console.log(`Product already exists: ${product.name}`);
      }
    }
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}

// Run the seed function
seedDatabase();
