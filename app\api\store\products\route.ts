import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/store/products - Get all products
export async function GET(request: NextRequest) {
  try {
    // Get the Sanity client
    const client = getWriteClient();

    // Get query parameters
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const featured = url.searchParams.get('featured');
    const limit = url.searchParams.get('limit');

    // Build the query
    let query = '*[_type == "product"]';
    const params: Record<string, any> = {};

    // Add filters
    if (category) {
      query += ' && category._ref == $category';
      params.category = category;
    }

    if (featured === 'true') {
      query += ' && featured == true';
    }

    // Add sorting
    query += ' | order(publishedAt desc)';

    // Add limit
    if (limit) {
      query += `[0...${parseInt(limit)}]`;
    }

    // Add projection
    query += ' { _id, name, slug, description, price, compareAtPrice, currency, images, featured, category->, tags, inventory, isDigital, publishedAt }';

    // Fetch products from Sanity
    const products = await client.fetch(query, params, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    return NextResponse.json({
      success: true,
      products,
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch products',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/store/products - Create a new product
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to create products' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.price) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields: name and price are required' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Generate a slug from the name if not provided
    if (!body.slug) {
      body.slug = {
        _type: 'slug',
        current: body.name
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim(),
      };
    }

    // Create the product document
    const productDoc = {
      _type: 'product',
      name: body.name,
      slug: body.slug,
      description: body.description || '',
      price: parseFloat(body.price),
      compareAtPrice: body.compareAtPrice ? parseFloat(body.compareAtPrice) : undefined,
      currency: body.currency || 'USD',
      images: body.images || [],
      category: body.category ? { _type: 'reference', _ref: body.category } : undefined,
      tags: body.tags || [],
      featured: body.featured || false,
      isDigital: body.isDigital || false,
      inventory: body.inventory || { quantity: 0, trackInventory: true },
      publishedAt: new Date().toISOString(),
    };

    // Create the document in Sanity
    const createdProduct = await client.create(productDoc);

    return NextResponse.json({
      success: true,
      message: 'Product created successfully',
      product: createdProduct,
    });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create product',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
