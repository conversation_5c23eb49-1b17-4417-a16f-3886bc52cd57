/**
 * Simple Express Server for Local Development
 * 
 * This script starts a simple Express server that serves the Next.js
 * static export files. It's much faster than running the Next.js
 * development server for simple testing.
 */

const express = require('express');
const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const PORT = process.env.PORT || 3000;
const ROOT_DIR = path.resolve(__dirname, '..');
const OUT_DIR = path.join(ROOT_DIR, 'out');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if the static export exists
 */
function checkStaticExport() {
  if (!fs.existsSync(OUT_DIR)) {
    console.log(`${COLORS.yellow}Static export not found. Building...${COLORS.reset}`);
    
    // Temporarily modify next.config.js to enable output export
    const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
    let originalConfig = '';
    
    if (fs.existsSync(nextConfigPath)) {
      originalConfig = fs.readFileSync(nextConfigPath, 'utf8');
      
      // Add output: 'export' to the config
      const modifiedConfig = originalConfig.replace(
        'const nextConfig = {',
        'const nextConfig = {\n  output: "export",'
      );
      
      fs.writeFileSync(nextConfigPath, modifiedConfig);
    }
    
    try {
      // Build the static export
      runCommand('npm run build');
      
      // Restore the original config
      if (originalConfig) {
        fs.writeFileSync(nextConfigPath, originalConfig);
      }
    } catch (error) {
      // Restore the original config on error
      if (originalConfig) {
        fs.writeFileSync(nextConfigPath, originalConfig);
      }
      throw error;
    }
  }
}

/**
 * Start the Express server
 */
function startServer() {
  const app = express();
  
  // Serve static files from the out directory
  app.use(express.static(OUT_DIR));
  
  // Serve index.html for all routes (SPA fallback)
  app.get('*', (req, res) => {
    // Check if the path exists as a HTML file
    const htmlPath = path.join(OUT_DIR, req.path, 'index.html');
    if (fs.existsSync(htmlPath)) {
      return res.sendFile(htmlPath);
    }
    
    // Otherwise, serve the main index.html
    res.sendFile(path.join(OUT_DIR, 'index.html'));
  });
  
  // Start the server
  app.listen(PORT, () => {
    console.log(`${COLORS.green}Server running at http://localhost:${PORT}${COLORS.reset}`);
    console.log(`${COLORS.cyan}Press Ctrl+C to stop${COLORS.reset}`);
  });
}

/**
 * Main function
 */
function main() {
  console.log(`${COLORS.cyan}=== Simple Express Server ====${COLORS.reset}`);
  
  // Check if the static export exists
  checkStaticExport();
  
  // Start the Express server
  startServer();
}

// Run the main function
main();
