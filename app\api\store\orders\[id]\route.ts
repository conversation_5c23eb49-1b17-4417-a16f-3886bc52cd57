import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/store/orders/[id] - Get a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Check authentication for admin-only access
    const session = await getServerSession(authOptions);
    const isAdminUser = session?.user && (isAdmin(session) || isSuperAdmin(session));
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Fetch the order
    const order = await client.fetch(`
      *[_type == "order" && (_id == $id || orderNumber == $id)][0]
    `, { id });
    
    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }
    
    // Check if the user is authorized to view this order
    // Admin users can view any order
    // Non-admin users can only view their own orders
    if (!isAdminUser) {
      // Get customer email from the request (e.g., from a token or session)
      const customerEmail = session?.user?.email;
      
      if (!customerEmail || order.customer.email !== customerEmail) {
        return NextResponse.json(
          { success: false, message: 'Unauthorized: You do not have permission to view this order' },
          { status: 403 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      order,
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch order',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/store/orders/[id] - Update a specific order (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update orders' },
        { status: 401 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Check if the order exists
    const existingOrder = await client.fetch(`
      *[_type == "order" && _id == $id][0]
    `, { id });
    
    if (!existingOrder) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }
    
    // Prepare the update object
    const updateObj: Record<string, any> = {
      updatedAt: new Date().toISOString(),
    };
    
    // Add fields to update
    if (body.status !== undefined) updateObj.status = body.status;
    if (body.notes !== undefined) updateObj.notes = body.notes;
    if (body.payment !== undefined) updateObj.payment = {
      ...existingOrder.payment,
      ...body.payment,
    };
    if (body.shippingInfo !== undefined) updateObj.shippingInfo = {
      ...existingOrder.shippingInfo,
      ...body.shippingInfo,
    };
    
    // Update the order in Sanity
    const updatedOrder = await client
      .patch(id)
      .set(updateObj)
      .commit();
    
    return NextResponse.json({
      success: true,
      message: 'Order updated successfully',
      order: updatedOrder,
    });
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update order',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/store/orders/[id] - Delete a specific order (super admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to delete orders' },
        { status: 401 }
      );
    }
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Check if the order exists
    const existingOrder = await client.fetch(`
      *[_type == "order" && _id == $id][0]
    `, { id });
    
    if (!existingOrder) {
      return NextResponse.json(
        { success: false, message: 'Order not found' },
        { status: 404 }
      );
    }
    
    // Delete the order from Sanity
    await client.delete(id);
    
    return NextResponse.json({
      success: true,
      message: 'Order deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting order:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete order',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
