// Simple script to test authentication configuration
// Run with: node scripts/test-auth.js

console.log('Testing authentication configuration...');

// Check for required environment variables
const requiredVars = [
  'NEXTAUTH_SECRET',
  'ADMIN_USERNAME',
  'ADMIN_PASSWORD',
  'NEXTAUTH_URL'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPlease check your .env file and make sure these variables are set.');
} else {
  console.log('✅ All required environment variables are set.');
}

// Check NEXTAUTH_SECRET
const secret = process.env.NEXTAUTH_SECRET;
if (secret && secret.length < 32) {
  console.warn('⚠️ NEXTAUTH_SECRET is too short. It should be at least 32 characters long.');
} else if (secret) {
  console.log('✅ NEXTAUTH_SECRET has sufficient length.');
}

// Check NEXTAUTH_URL
const url = process.env.NEXTAUTH_URL;
if (url && !url.startsWith('http')) {
  console.warn('⚠️ NEXTAUTH_URL should start with http:// or https://');
} else if (url) {
  console.log('✅ NEXTAUTH_URL format looks correct.');
}

console.log('\nAuthentication configuration test complete.');
