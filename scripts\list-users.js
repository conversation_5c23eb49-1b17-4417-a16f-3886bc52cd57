// This script lists all users in the Sanity database
// Run with: node scripts/list-users.js

const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Function to list all users
async function listUsers() {
  try {
    console.log('Fetching all users from Sanity...');
    
    const users = await client.fetch(
      `*[_type == "adminUser"] | order(role asc, username asc) {
        _id,
        username,
        name,
        email,
        role,
        isActive,
        isProtected,
        createdAt,
        updatedAt
      }`
    );
    
    console.log(`Found ${users.length} users\n`);
    
    // Group users by role
    const usersByRole = {};
    
    users.forEach(user => {
      const role = user.role || 'unknown';
      if (!usersByRole[role]) {
        usersByRole[role] = [];
      }
      usersByRole[role].push(user);
    });
    
    // Display users by role
    Object.keys(usersByRole).sort().forEach(role => {
      console.log(`=== ${role.toUpperCase()} USERS (${usersByRole[role].length}) ===`);
      
      usersByRole[role].forEach(user => {
        console.log(`- ${user.name} (${user.email})`);
        console.log(`  ID: ${user._id}`);
        console.log(`  Username: ${user.username}`);
        console.log(`  Active: ${user.isActive ? 'Yes' : 'No'}`);
        console.log(`  Protected: ${user.isProtected ? 'Yes' : 'No'}`);
        console.log(`  Created: ${user.createdAt ? new Date(user.createdAt).toLocaleString() : 'Unknown'}`);
        console.log('');
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error listing users:', error);
    return [];
  }
}

// Main function
async function main() {
  console.log('Starting user listing...');
  
  await listUsers();
  
  console.log('\nUser listing complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
