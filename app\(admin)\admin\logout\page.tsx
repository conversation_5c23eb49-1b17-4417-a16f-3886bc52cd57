'use client';

import { useEffect, Suspense } from 'react';
import { signOut } from 'next-auth/react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

// Component that uses useSearchParams
function LogoutContent() {
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/admin/login';

  // Perform logout immediately when the page loads
  useEffect(() => {
    // Clear any legacy tokens
    if (typeof window !== 'undefined') {
      localStorage.removeItem('adminToken');
    }

    const performLogout = async () => {
      try {
        // First, try to clear cookies via API
        await fetch('/api/auth/logout', {
          method: 'GET',
        });

        // Then use signOut from next-auth
        await signOut({ redirect: false });

        // Finally, redirect to the specified page
        window.location.href = redirect;
      } catch (error) {
        console.error('Error during logout:', error);
        // Fallback to direct redirect
        window.location.href = redirect;
      }
    };

    performLogout();
  }, [redirect]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-royalBlue to-royalBlue/70 p-4">
      <div className="text-center text-white">
        <h1 className="text-3xl font-bold mb-4">Logging Out</h1>
        <p className="text-lg mb-6">You are being logged out of the admin panel...</p>
        <Loader2 className="h-12 w-12 animate-spin mx-auto text-white mb-8" />

        <div className="mt-4">
          <p className="text-sm mb-4">If you're not redirected automatically:</p>
          <Button asChild variant="outline" className="bg-white/10 hover:bg-white/20 text-white border-white/30">
            <Link href="/admin/login">
              Return to Login
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

// Loading fallback for Suspense
function LogoutFallback() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-royalBlue to-royalBlue/70 p-4">
      <div className="text-center text-white">
        <h1 className="text-3xl font-bold mb-4">Logging Out</h1>
        <p className="text-lg mb-6">Preparing to log you out...</p>
        <Loader2 className="h-12 w-12 animate-spin mx-auto text-white mb-8" />
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function LogoutPage() {
  return (
    <Suspense fallback={<LogoutFallback />}>
      <LogoutContent />
    </Suspense>
  );
}