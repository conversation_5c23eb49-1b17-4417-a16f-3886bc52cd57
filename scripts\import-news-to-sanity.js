/**
 * <PERSON><PERSON><PERSON> to import hardcoded news articles into Sanity
 *
 * Usage:
 * node scripts/import-news-to-sanity.js
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
const slugify = require('slugify');

// Log environment variables (without the token for security)
console.log('Using Sanity configuration:');
console.log(`- Project ID: ${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'not set'}`);
console.log(`- Dataset: ${process.env.NEXT_PUBLIC_SANITY_DATASET || 'not set'}`);
console.log(`- API Token: ${process.env.SANITY_API_TOKEN ? 'set (hidden)' : 'not set'}`);

// Sanity client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // You need a write token
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Hardcoded articles to import
const articles = [
  {
    title: "Sacred Crown Jewels Unveiled for Upcoming Ceremony",
    excerpt: "The historic crown jewels, kept in sacred storage for decades, have been revealed in preparation for the ceremony.",
    publishedAt: "2023-08-12T00:00:00.000Z",
    category: "CEREMONY",
    content: "<p>The historic crown jewels, kept in sacred storage for decades, have been revealed in preparation for the ceremony. These priceless artifacts represent the rich cultural heritage of the Adukrom Kingdom.</p><p>Master craftsmen have been working tirelessly to restore these jewels to their former glory, ensuring they shine brightly during the upcoming coronation ceremony.</p>",
    imageUrl: "/Website Images/Prince Allen.png",
    featured: true,
    status: "published"
  },
  {
    title: "Traditional Artisans Create Ceremonial Garments",
    excerpt: "Master weavers and craftspeople have been working for months to create the elaborate ceremonial attire.",
    publishedAt: "2023-08-10T00:00:00.000Z",
    category: "CULTURE",
    content: "<p>Master weavers and craftspeople have been working for months to create the elaborate ceremonial attire that will be worn during the upcoming events. Using traditional techniques passed down through generations, these artisans are preserving cultural heritage through their craft.</p><p>The garments feature intricate patterns and symbols that tell the story of the Adukrom Kingdom's history and values.</p>",
    imageUrl: "/Website Images/vibrant_kente_design_2.png",
    featured: false,
    status: "published"
  },
  {
    title: "Exclusive: King Allen Ellison Shares Vision for Adukrom Kingdom",
    excerpt: "In a rare interview, King Allen Ellison discusses his plans for cultural preservation and economic growth.",
    publishedAt: "2023-08-08T00:00:00.000Z",
    category: "INTERVIEW",
    content: "<p>In a rare interview, King Allen Ellison discusses his plans for cultural preservation and economic growth in the Adukrom Kingdom. His vision includes strengthening ties with the diaspora, investing in education, and promoting sustainable development.</p><p>\"Our goal is to honor our traditions while embracing the opportunities of the modern world,\" says King Ellison. \"We want to create a future where our children can thrive while staying connected to their heritage.\"</p>",
    imageUrl: "/Website Images/KingAllenEllison.png",
    featured: false,
    status: "published"
  },
  {
    title: "The Legacy of Adukrom Kingdom: A Historical Timeline",
    excerpt: "Exploring the rich history and traditions of Adukrom Kingdom as it prepares for its first major event in 25 years.",
    publishedAt: "2023-08-05T00:00:00.000Z",
    category: "HISTORY",
    content: "<p>Exploring the rich history and traditions of Adukrom Kingdom as it prepares for its first major event in 25 years. From its founding centuries ago to its current revival, the kingdom has played an important role in preserving cultural heritage.</p><p>This article traces the key moments in the kingdom's history, highlighting the resilience and wisdom of its leaders throughout the generations.</p>",
    imageUrl: "/Website Images/ghana-wildlife-viewing.png",
    featured: false,
    status: "published"
  }
];

// Function to create a Sanity image asset from a local file
async function createSanityImageAsset(imagePath) {
  if (!imagePath) return null;

  try {
    console.log(`Creating Sanity image asset from ${imagePath}...`);

    // Try different possible paths for the image
    const possiblePaths = [
      path.join(process.cwd(), 'public', imagePath),
      path.join(process.cwd(), imagePath),
      // Remove leading slash if present
      path.join(process.cwd(), 'public', imagePath.replace(/^\//, '')),
      path.join(process.cwd(), imagePath.replace(/^\//, '')),
      // Try with 'Website Images' folder at different locations
      path.join(process.cwd(), 'public', 'Website Images', path.basename(imagePath)),
      path.join(process.cwd(), 'Website Images', path.basename(imagePath))
    ];

    let imageBuffer = null;
    let foundPath = null;

    // Try each path until we find one that exists
    for (const tryPath of possiblePaths) {
      console.log(`Trying path: ${tryPath}`);
      if (fs.existsSync(tryPath)) {
        foundPath = tryPath;
        imageBuffer = fs.readFileSync(tryPath);
        console.log(`Found image at: ${tryPath}`);
        break;
      }
    }

    // If no image found, create a placeholder
    if (!imageBuffer) {
      console.log('No image found, creating placeholder...');
      const { createCanvas } = require('canvas');
      const canvas = createCanvas(300, 200);
      const ctx = canvas.getContext('2d');

      // Fill with blue background
      ctx.fillStyle = '#3b5998';
      ctx.fillRect(0, 0, 300, 200);

      // Add text
      ctx.fillStyle = 'white';
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Kingdom of Adukrom', 150, 100);

      // Convert to buffer
      imageBuffer = canvas.toBuffer('image/jpeg');
      console.log('Created placeholder image');
    }

    // Upload to Sanity
    const asset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath) || 'placeholder.jpg',
    });

    console.log(`Created Sanity image asset: ${asset._id}`);

    return {
      _type: 'image',
      asset: {
        _type: 'reference',
        _ref: asset._id,
      },
    };
  } catch (error) {
    console.error(`Error creating Sanity image asset:`, error);
    return null;
  }
}

// Function to create a Sanity block content from HTML
function createSanityBlockContent(html) {
  if (!html) return [];

  // This is a simplified version - in a real implementation,
  // you would need to parse the HTML and convert it to Sanity's block content format
  return [
    {
      _type: 'block',
      style: 'normal',
      _key: Date.now().toString(),
      markDefs: [],
      children: [
        {
          _type: 'span',
          _key: `${Date.now().toString()}-span`,
          text: html.replace(/<[^>]*>/g, ' ').trim(), // Strip HTML tags
          marks: [],
        },
      ],
    },
  ];
}

// Function to import articles to Sanity
async function importArticlesToSanity() {
  try {
    console.log(`Starting import of ${articles.length} articles to Sanity...`);

    // Create categories for each unique category in the articles
    const uniqueCategories = [...new Set(articles.map(article => article.category))];
    const categoryMap = {};

    for (const categoryTitle of uniqueCategories) {
      try {
        // Check if category already exists
        const existingCategory = await client.fetch(
          `*[_type == "category" && title == $title][0]`,
          { title: categoryTitle }
        );

        if (existingCategory) {
          console.log(`Category "${categoryTitle}" already exists with ID: ${existingCategory._id}`);

          // Check if the category has a slug, if not, update it
          if (!existingCategory.slug || !existingCategory.slug.current) {
            console.log(`Adding slug to category "${categoryTitle}"...`);
            await client.patch(existingCategory._id).set({
              slug: {
                _type: 'slug',
                current: slugify(categoryTitle, { lower: true, strict: true })
              }
            }).commit();
            console.log(`Updated category "${categoryTitle}" with slug`);
          }

          categoryMap[categoryTitle] = existingCategory._id;
        } else {
          // Create new category with slug
          const newCategory = await client.create({
            _type: 'category',
            title: categoryTitle,
            description: `Articles about ${categoryTitle.toLowerCase()}`,
            slug: {
              _type: 'slug',
              current: slugify(categoryTitle, { lower: true, strict: true })
            }
          });
          console.log(`Created new category "${categoryTitle}" with ID: ${newCategory._id}`);
          categoryMap[categoryTitle] = newCategory._id;
        }
      } catch (error) {
        console.error(`Error processing category "${categoryTitle}":`, error);
      }
    }

    // Process each article
    for (const article of articles) {
      try {
        console.log(`Processing article: ${article.title}`);

        // Generate a slug
        const slug = slugify(article.title, { lower: true, strict: true });

        // Create Sanity image asset if image URL is available
        let mainImage = null;
        if (article.imageUrl) {
          mainImage = await createSanityImageAsset(article.imageUrl);
        }

        // Get category reference
        const categoryRef = categoryMap[article.category];

        // Create the article document in Sanity
        const sanityArticle = {
          _type: 'post',
          title: article.title,
          slug: {
            _type: 'slug',
            current: slug,
          },
          excerpt: article.excerpt,
          mainImage: mainImage,
          body: createSanityBlockContent(article.content),
          publishedAt: article.publishedAt,
          featured: article.featured || false,
          status: article.status || "published",
          category: categoryRef ? {
            _type: 'reference',
            _ref: categoryRef,
          } : undefined,
        };

        // Check if article already exists
        const existingArticle = await client.fetch(
          `*[_type == "post" && slug.current == $slug][0]`,
          { slug }
        );

        if (existingArticle) {
          console.log(`Article "${article.title}" already exists, updating...`);
          await client.patch(existingArticle._id).set(sanityArticle).commit();
          console.log(`Updated article: ${article.title}`);
        } else {
          console.log(`Creating new article: ${article.title}`);
          const result = await client.create(sanityArticle);
          console.log(`Created article: ${article.title} with ID: ${result._id}`);
        }
      } catch (error) {
        console.error(`Error processing article "${article.title}":`, error);
      }
    }

    console.log('Import completed!');
  } catch (error) {
    console.error('Error importing articles to Sanity:', error);
  }
}

// Run the import
importArticlesToSanity();
