'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { LogIn } from 'lucide-react';
import { toast } from 'sonner';

export default function ForceLoginButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleAdminLogin = async () => {
    setIsLoading(true);
    try {
      await signIn('credentials', {
        username: 'admin',
        password: 'kingdom2024',
        redirect: false,
      });
      toast.success('Logged in as Admin');
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuperAdminLogin = async () => {
    setIsLoading(true);
    try {
      await signIn('credentials', {
        username: 'superadmin',
        password: 'kingdom2024super',
        redirect: false,
      });
      toast.success('Logged in as Super Admin');
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed top-4 left-4 z-50 flex space-x-2">
      <Button
        variant="default"
        size="sm"
        onClick={handleAdminLogin}
        disabled={isLoading}
        className="bg-blue-600 hover:bg-blue-700 text-white"
      >
        <LogIn className="h-4 w-4 mr-2" />
        Login as Admin
      </Button>
      
      <Button
        variant="default"
        size="sm"
        onClick={handleSuperAdminLogin}
        disabled={isLoading}
        className="bg-purple-600 hover:bg-purple-700 text-white"
      >
        <LogIn className="h-4 w-4 mr-2" />
        Login as Super Admin
      </Button>
    </div>
  );
}
