import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

interface SectionEditorProps {
  pageId: string;
  sectionIndex: number;
}

export default function SectionEditor({ pageId, sectionIndex }: SectionEditorProps) {
  const handleEditClick = () => {
    // Open the section editor in a new tab
    window.open(`/admin/pages/${pageId}?section=${sectionIndex}`, '_blank');
  };

  return (
    <div className="py-4">
      <Button 
        onClick={handleEditClick}
        variant="default"
        className="bg-royalBlue hover:bg-royalBlue/90 text-white"
      >
        Edit Section Content
      </Button>
    </div>
  );
}
