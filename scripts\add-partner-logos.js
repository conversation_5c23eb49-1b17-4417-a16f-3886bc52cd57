/**
 * <PERSON><PERSON><PERSON> to add partner logos to existing strategic partners in Sanity
 *
 * This script uploads logo images for the strategic partners and updates
 * the partner documents in Sanity with the logo references.
 *
 * Run with: node scripts/add-partner-logos.js
 */

// Import the Sanity client
const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Configure the client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Need to provide this when running the script
  apiVersion: '2023-05-03', // Use a consistent API version
  useCdn: false
});

// Partner logos data
const partnerLogos = [
  {
    slug: 'remit-global',
    logoPath: 'public/Website Images/remit-global-logo.png',
    altText: 'Remit Global Logo'
  },
  {
    slug: 'royal-lion',
    logoPath: 'public/Website Images/royal_lion_logo.png',
    altText: 'Royal Lion Logo'
  },
  {
    slug: 'tef',
    logoPath: 'public/Website Images/tef-logo2-transparent.png',
    altText: 'TEF Logo'
  },
  {
    slug: 'lightace-global',
    logoPath: 'public/Website Images/lightace-global-logo.png',
    altText: 'Lightace Global Logo'
  },
  {
    slug: 'akuapem-nifaman-council',
    logoPath: 'public/Website Images/akuaapem_nifaman_council_logo.png',
    altText: 'Akuapem Nifaman Council Logo'
  }
];

// Function to upload an image to Sanity
async function uploadImage(imagePath, altText) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.error(`Image not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const imageAsset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath),
      contentType: `image/${path.extname(imagePath).substring(1)}`
    });

    return {
      _type: 'image',
      asset: {
        _type: 'reference',
        _ref: imageAsset._id
      },
      alt: altText
    };
  } catch (error) {
    console.error(`Error uploading image ${imagePath}:`, error);
    return null;
  }
}

// Function to update a partner with a logo
async function updatePartnerLogo(slug, logoPath, altText) {
  try {
    // Find the partner by slug
    const partner = await client.fetch(
      `*[_type == "strategicPartner" && slug.current == $slug][0]`,
      { slug }
    );

    if (!partner) {
      console.error(`Partner with slug "${slug}" not found`);
      return null;
    }

    // Upload the logo
    const logo = await uploadImage(logoPath, altText);
    if (!logo) {
      console.error(`Failed to upload logo for partner "${slug}"`);
      return null;
    }

    // Update the partner with the logo
    const updatedPartner = await client
      .patch(partner._id)
      .set({ logo })
      .commit();

    console.log(`Updated partner "${updatedPartner.name}" with logo`);
    return updatedPartner;
  } catch (error) {
    console.error(`Error updating partner logo for "${slug}":`, error);
    return null;
  }
}

// Main function to add logos to all partners
async function addPartnerLogos() {
  console.log('Starting to add partner logos to Sanity...');

  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  for (const partnerLogo of partnerLogos) {
    await updatePartnerLogo(
      partnerLogo.slug,
      partnerLogo.logoPath,
      partnerLogo.altText
    );
  }

  console.log('Finished adding partner logos to Sanity');
}

// Run the script
addPartnerLogos().catch(console.error);
