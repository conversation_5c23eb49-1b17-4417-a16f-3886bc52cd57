/**
 * Hydration Issue Checker
 * 
 * This script scans your codebase for common patterns that might cause hydration issues
 * and provides suggestions for fixing them.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const COMPONENTS_DIR = path.join(ROOT_DIR, 'components');
const APP_DIR = path.join(ROOT_DIR, 'app');
const LIB_DIR = path.join(ROOT_DIR, 'lib');

// Patterns that might cause hydration issues
const PATTERNS = {
  // Check for window/document usage without proper guards
  WINDOW_CHECK: {
    pattern: /typeof\s+window\s*!==\s*['"]undefined['"]/g,
    risk: 'LOW',
    suggestion: 'This is a proper check, but ensure it\'s used in useEffect or event handlers.'
  },
  WINDOW_DIRECT: {
    pattern: /window\./g,
    risk: 'HIGH',
    suggestion: 'Direct window access should be moved to useEffect or wrapped in typeof window !== "undefined" check.'
  },
  DOCUMENT_DIRECT: {
    pattern: /document\./g,
    risk: 'HIGH',
    suggestion: 'Direct document access should be moved to useEffect or wrapped in typeof document !== "undefined" check.'
  },
  
  // Check for Date.now() or Math.random() in render
  DATE_NOW: {
    pattern: /Date\.now\(\)/g,
    risk: 'HIGH',
    suggestion: 'Move Date.now() to useEffect or useState initialization to avoid hydration mismatches.'
  },
  MATH_RANDOM: {
    pattern: /Math\.random\(\)/g,
    risk: 'HIGH',
    suggestion: 'Move Math.random() to useEffect or useState initialization to avoid hydration mismatches.'
  },
  
  // Check for locale-specific formatting
  INTL_DATETIME: {
    pattern: /new\s+Intl\.DateTimeFormat/g,
    risk: 'MEDIUM',
    suggestion: 'Use suppressHydrationWarning on elements with locale-specific formatting or move to useEffect.'
  },
  
  // Check for conditional rendering based on mounted state
  MOUNTED_CHECK: {
    pattern: /\[\s*isMounted\s*\]/g,
    risk: 'MEDIUM',
    suggestion: 'Use suppressHydrationWarning on the parent element or consider a different approach.'
  },
  
  // Check for missing suppressHydrationWarning
  SUPPRESS_HYDRATION: {
    pattern: /suppressHydrationWarning/g,
    risk: 'INFO',
    suggestion: 'Good practice for elements with dynamic content that might differ between server and client.'
  },
};

// Files to ignore
const IGNORE_FILES = [
  'node_modules',
  '.next',
  'public',
  '.git',
  'test',
  '__tests__',
  'jest',
  'cypress',
  'scripts',
];

// Counter for issues found
const stats = {
  filesScanned: 0,
  issuesFound: 0,
  highRiskIssues: 0,
  mediumRiskIssues: 0,
  lowRiskIssues: 0,
  infoIssues: 0,
};

// Store issues by file
const issuesByFile = {};

/**
 * Check if a file should be ignored
 */
function shouldIgnoreFile(filePath) {
  return IGNORE_FILES.some(ignore => filePath.includes(ignore));
}

/**
 * Scan a file for potential hydration issues
 */
function scanFile(filePath) {
  if (shouldIgnoreFile(filePath)) return;
  
  // Only scan JavaScript/TypeScript files
  if (!['.js', '.jsx', '.ts', '.tsx'].includes(path.extname(filePath))) return;
  
  stats.filesScanned++;
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileIssues = [];
    
    // Check for each pattern
    for (const [name, { pattern, risk, suggestion }] of Object.entries(PATTERNS)) {
      const matches = content.match(pattern);
      if (matches) {
        fileIssues.push({
          type: name,
          count: matches.length,
          risk,
          suggestion,
        });
        
        stats.issuesFound += matches.length;
        
        // Update risk counters
        if (risk === 'HIGH') stats.highRiskIssues += matches.length;
        else if (risk === 'MEDIUM') stats.mediumRiskIssues += matches.length;
        else if (risk === 'LOW') stats.lowRiskIssues += matches.length;
        else if (risk === 'INFO') stats.infoIssues += matches.length;
      }
    }
    
    // If issues found, store them
    if (fileIssues.length > 0) {
      issuesByFile[filePath] = fileIssues;
    }
  } catch (error) {
    console.error(`Error scanning ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan a directory for files
 */
function scanDirectory(dir) {
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        if (!shouldIgnoreFile(fullPath)) {
          scanDirectory(fullPath);
        }
      } else {
        scanFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error.message);
  }
}

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Main execution
console.log(`${colors.cyan}=== Hydration Issue Checker ====${colors.reset}`);
console.log('Scanning for potential hydration issues...');

// Scan directories
scanDirectory(COMPONENTS_DIR);
scanDirectory(APP_DIR);
scanDirectory(LIB_DIR);

// Print results
console.log(`\n${colors.cyan}=== Scan Results ====${colors.reset}`);
console.log(`Files scanned: ${stats.filesScanned}`);
console.log(`Issues found: ${stats.issuesFound}`);
console.log(`  - High risk: ${colors.red}${stats.highRiskIssues}${colors.reset}`);
console.log(`  - Medium risk: ${colors.yellow}${stats.mediumRiskIssues}${colors.reset}`);
console.log(`  - Low risk: ${colors.blue}${stats.lowRiskIssues}${colors.reset}`);
console.log(`  - Info: ${colors.green}${stats.infoIssues}${colors.reset}`);

// Print issues by file
if (stats.issuesFound > 0) {
  console.log(`\n${colors.cyan}=== Issues by File ====${colors.reset}`);
  
  for (const [filePath, issues] of Object.entries(issuesByFile)) {
    const relativePath = path.relative(ROOT_DIR, filePath);
    console.log(`\n${colors.cyan}${relativePath}${colors.reset}:`);
    
    issues.forEach(issue => {
      let riskColor = colors.blue;
      if (issue.risk === 'HIGH') riskColor = colors.red;
      else if (issue.risk === 'MEDIUM') riskColor = colors.yellow;
      else if (issue.risk === 'INFO') riskColor = colors.green;
      
      console.log(`  - ${issue.type}: ${issue.count} occurrences (${riskColor}${issue.risk}${colors.reset})`);
      console.log(`    ${issue.suggestion}`);
    });
  }
  
  // Print general advice
  console.log(`\n${colors.cyan}=== General Advice for Fixing Hydration Issues ====${colors.reset}`);
  console.log('1. Move all random values (Math.random, Date.now) to useEffect');
  console.log('2. Add suppressHydrationWarning to elements with dynamic content');
  console.log('3. Use useEffect for browser-specific code (window, document)');
  console.log('4. For animations with random values, pre-calculate them in useEffect');
  console.log('5. Consider using dynamic imports with { ssr: false } for purely client components');
}

console.log(`\n${colors.green}Hydration issue check complete!${colors.reset}`);
