import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/categories/[id] - Get a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Fetch the category by ID
    const category = await client.fetch(
      `*[_type == "category" && _id == $id][0]{
        _id,
        title,
        slug,
        description,
        color,
        icon,
        order
      }`,
      { id }
    );

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      category,
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch category',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/categories/[id] - Update a category
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update categories' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Parse the request body
    const body = await request.json();

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Check if the category exists
    const existingCategory = await client.fetch(
      `*[_type == "category" && _id == $id][0]`,
      { id }
    );

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: 'Category not found' },
        { status: 404 }
      );
    }

    // Prepare the update data
    const updateData: Record<string, any> = {};

    // Only include fields that are provided in the request
    if (body.title !== undefined) {
      updateData.title = body.title;
      
      // If title is updated, also update the slug
      if (body.title) {
        const slug = body.title
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
        
        updateData.slug = {
          _type: 'slug',
          current: slug
        };
      }
    }
    
    if (body.description !== undefined) updateData.description = body.description;
    if (body.color !== undefined) updateData.color = body.color;
    if (body.icon !== undefined) updateData.icon = body.icon;
    if (body.order !== undefined) updateData.order = body.order;

    // Update the category
    const updatedCategory = await client.patch(id)
      .set(updateData)
      .commit();

    return NextResponse.json({
      success: true,
      message: 'Category updated successfully',
      category: updatedCategory,
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update category',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete categories' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        {
          success: false,
          message: 'Server configuration error',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Check if the category exists
    const existingCategory = await client.fetch(
      `*[_type == "category" && _id == $id][0]`,
      { id }
    );

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, message: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if the category is in use
    const usageCount = await client.fetch(
      `count(*[_type in ["news", "gallery"] && references($id)])`,
      { id }
    );

    if (usageCount > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Cannot delete category that is in use', 
          usageCount 
        },
        { status: 400 }
      );
    }

    // Delete the category
    await client.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete category',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
