import { Metadata } from 'next';
import { generateDynamicGalleryMetadata } from '@/lib/metadata-generator';

// Generate metadata for the main gallery page
export async function generateMetadata(): Promise<Metadata> {
  try {
    // Generate metadata for the gallery index page
    return generateDynamicGalleryMetadata();
  } catch (error) {
    console.error('Error generating gallery index metadata:', error);

    // Fallback metadata
    return {
      title: 'Royal Gallery',
      description: 'Explore the royal gallery of the Kingdom of Adukrom.',
    };
  }
}
