'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

export default function SanityTestPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [newName, setNewName] = useState<string>('');
  
  // Fetch users on page load
  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/sanity-test');
        const data = await response.json();
        
        if (data.success) {
          setUsers(data.users);
          toast.success('Sanity connection test successful');
        } else {
          toast.error(data.message);
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error('Failed to fetch users');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUsers();
  }, []);
  
  // Handle update
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser || !newName) {
      toast.error('Please select a user and enter a new name');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/sanity-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedUser,
          name: newName,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Sanity update test successful');
        
        // Update the user in the list
        setUsers(users.map(user => 
          user._id === selectedUser ? { ...user, name: newName } : user
        ));
        
        // Clear the form
        setSelectedUser('');
        setNewName('');
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Sanity Test</h1>
        <p className="text-muted-foreground">
          Test the Sanity connection and update functionality
        </p>
      </div>
      
      <Separator />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Sanity Users</CardTitle>
            <CardDescription>
              Users found in Sanity
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center p-4">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : users.length > 0 ? (
              <div className="space-y-2">
                {users.map(user => (
                  <div key={user._id} className="p-2 border rounded">
                    <p><strong>ID:</strong> {user._id}</p>
                    <p><strong>Name:</strong> {user.name}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Username:</strong> {user.username}</p>
                    <p><strong>Role:</strong> {user.role}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p>No users found</p>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Update User</CardTitle>
            <CardDescription>
              Test updating a user in Sanity
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleUpdate}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="user">Select User</Label>
                <select
                  id="user"
                  value={selectedUser}
                  onChange={(e) => setSelectedUser(e.target.value)}
                  className="w-full p-2 border rounded"
                  disabled={isLoading}
                >
                  <option value="">Select a user</option>
                  {users.map(user => (
                    <option key={user._id} value={user._id}>
                      {user.name} ({user.email})
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="name">New Name</Label>
                <Input
                  id="name"
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  placeholder="Enter new name"
                  disabled={isLoading}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isLoading || !selectedUser || !newName}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update User'
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
