/**
 * Cart utility functions for the store
 * 
 * This file contains functions for managing the shopping cart,
 * including adding, updating, and removing items, as well as
 * calculating totals.
 */

// Define cart item type
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  category?: string;
  sku?: string;
  isDigital?: boolean;
  variantId?: string;
  variantOptions?: {
    color?: string;
    size?: string;
    material?: string;
    style?: string;
  };
}

// Define cart type
export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
}

// Tax rate (7.5%)
const TAX_RATE = 0.075;

// Minimum order amount for free shipping
const FREE_SHIPPING_THRESHOLD = 100;

// Standard shipping cost
const STANDARD_SHIPPING_COST = 10;

/**
 * Load cart from localStorage
 * @returns Cart items array
 */
export function loadCart(): CartItem[] {
  if (typeof window === 'undefined') {
    return [];
  }
  
  try {
    const savedCart = localStorage.getItem('cart');
    return savedCart ? JSON.parse(savedCart) : [];
  } catch (err) {
    console.error('Error loading cart from localStorage:', err);
    return [];
  }
}

/**
 * Save cart to localStorage
 * @param items Cart items to save
 */
export function saveCart(items: CartItem[]): void {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    localStorage.setItem('cart', JSON.stringify(items));
  } catch (err) {
    console.error('Error saving cart to localStorage:', err);
  }
}

/**
 * Add item to cart
 * @param items Current cart items
 * @param item Item to add
 * @returns Updated cart items
 */
export function addToCart(items: CartItem[], item: CartItem): CartItem[] {
  // Check if item already exists in cart
  const existingItemIndex = items.findIndex(
    (cartItem) => 
      cartItem.id === item.id && 
      cartItem.variantId === item.variantId
  );

  if (existingItemIndex >= 0) {
    // Update quantity if item exists
    const updatedItems = [...items];
    updatedItems[existingItemIndex].quantity += item.quantity;
    saveCart(updatedItems);
    return updatedItems;
  } else {
    // Add new item
    const updatedItems = [...items, item];
    saveCart(updatedItems);
    return updatedItems;
  }
}

/**
 * Update item quantity in cart
 * @param items Current cart items
 * @param id Item ID to update
 * @param variantId Optional variant ID
 * @param quantity New quantity
 * @returns Updated cart items
 */
export function updateCartItemQuantity(
  items: CartItem[],
  id: string,
  quantity: number,
  variantId?: string
): CartItem[] {
  if (quantity < 1) {
    return items;
  }

  const updatedItems = items.map((item) => {
    if (item.id === id && item.variantId === variantId) {
      return { ...item, quantity };
    }
    return item;
  });

  saveCart(updatedItems);
  return updatedItems;
}

/**
 * Remove item from cart
 * @param items Current cart items
 * @param id Item ID to remove
 * @param variantId Optional variant ID
 * @returns Updated cart items
 */
export function removeFromCart(
  items: CartItem[],
  id: string,
  variantId?: string
): CartItem[] {
  const updatedItems = items.filter(
    (item) => !(item.id === id && item.variantId === variantId)
  );
  
  saveCart(updatedItems);
  return updatedItems;
}

/**
 * Clear cart
 * @returns Empty cart
 */
export function clearCart(): CartItem[] {
  saveCart([]);
  return [];
}

/**
 * Calculate cart totals
 * @param items Cart items
 * @returns Cart with calculated totals
 */
export function calculateCartTotals(items: CartItem[]): Cart {
  const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);
  const tax = subtotal * TAX_RATE;
  const shipping = subtotal >= FREE_SHIPPING_THRESHOLD || subtotal === 0 ? 0 : STANDARD_SHIPPING_COST;
  const total = subtotal + tax + shipping;

  return {
    items,
    subtotal,
    tax,
    shipping,
    total
  };
}
