import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/store/settings - Get store settings
export async function GET(request: NextRequest) {
  try {
    // Get the Sanity client
    const client = getWriteClient();

    // Check if we need to include protected fields
    const session = await getServerSession(authOptions);
    const includeProtected = session?.user && (isAdmin(session) || isSuperAdmin(session));

    // Query to fetch site settings
    const query = `*[_type == "siteSettings"][0] {
      store,
      ${includeProtected ? 'banking,' : ''}
      "categories": *[_type == "category"] | order(order asc) {
        _id,
        title,
        slug,
        description,
        color
      }
    }`;

    // Fetch settings from Sanity
    const settings = await client.fetch(query, {}, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    // If no settings document exists, return empty object
    if (!settings) {
      return NextResponse.json({
        success: true,
        settings: {},
        message: 'No store settings found'
      });
    }

    return NextResponse.json({
      success: true,
      settings,
    });
  } catch (error) {
    console.error('Error fetching store settings:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch store settings',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/store/settings - Update store settings
export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to update store settings' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Get the Sanity client
    const client = getWriteClient();

    // Get the siteSettings document
    const settingsQuery = `*[_type == "siteSettings"][0]`;
    let settingsDoc = await client.fetch(settingsQuery);

    // If no settings document exists, create one
    if (!settingsDoc) {
      console.log('No settings document found, creating one...');
      settingsDoc = await client.create({
        _type: 'siteSettings',
        title: 'The Royal Family of Africa',
        description: 'The Crown of Africa. The Rise of a New Era.',
      });
      console.log('Created new settings document:', settingsDoc);
    }

    // Prepare the update object
    const updateObj: Record<string, any> = {};

    // Update store settings
    if (body.store) {
      updateObj.store = {
        ...(settingsDoc.store || {}),
        ...body.store,
      };
    }

    // Update banking settings (super admin only)
    if (body.banking) {
      updateObj.banking = {
        ...(settingsDoc.banking || {}),
        ...body.banking,
      };
    }

    // Update the document in Sanity
    const result = await client
      .patch(settingsDoc._id)
      .set(updateObj)
      .commit();

    return NextResponse.json({
      success: true,
      message: 'Store settings updated successfully',
      settings: result,
    });
  } catch (error) {
    console.error('Error updating store settings:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update store settings',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
