import type { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Royal Merchandise Store',
    description: 'Official merchandise from the Kingdom of Adukrom',
    url: '/store',
    keywords: ['Royal Merchandise', 'Adukrom Store', 'Kingdom Products', 'Royal Gifts'],
  });
}

export default function StoreLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen">
      {children}
    </div>
  );
}
