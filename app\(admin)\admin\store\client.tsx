'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  ShoppingBag,
  CreditCard,
  Package,
  AlertTriangle,
  Setting<PERSON>,
  Wallet,
  Coins,
  Bar<PERSON>hart4,
  Tag,
  Plus,
  Trash2,
  Edit,
  Loader2,
  ImagePlus,
  Palette
} from 'lucide-react';

export default function StoreClient() {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [storeSettings, setStoreSettings] = useState<any>({});
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [categories, setCategories] = useState<any[]>([]);
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState(false);
  const [isSubmittingCategory, setIsSubmittingCategory] = useState(false);
  const [categoryFormData, setCategoryFormData] = useState({
    title: '',
    description: '',
    color: '#002366',
    order: '0'
  });
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const router = useRouter();

  // Get session directly from next-auth
  const { data: session, status } = useSession();

  // Fetch store settings
  const fetchStoreSettings = async () => {
    try {
      const response = await fetch('/api/store/settings');
      const data = await response.json();

      if (data.success && data.settings) {
        console.log('Fetched store settings:', data.settings);
        setStoreSettings(data.settings);
      }
    } catch (error) {
      console.error('Error fetching store settings:', error);
    }
  };

  // Fetch store categories
  const fetchCategories = async () => {
    try {
      console.log('Fetching store categories...');
      const response = await fetch('/api/store/categories');
      const data = await response.json();

      console.log('Categories API response:', data);

      if (data.success && data.categories) {
        console.log('Found categories:', data.categories);
        setCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching store categories:', error);
      toast.error('Failed to load store categories');
    }
  };

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedImage(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCategoryFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmittingCategory(true);

    try {
      // First, upload the image to Sanity if selected
      let imageAsset = null;
      if (selectedImage) {
        try {
          const formData = new FormData();
          formData.append('file', selectedImage);

          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          const uploadData = await uploadResponse.json();

          if (uploadData.success && uploadData.asset) {
            imageAsset = {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: uploadData.asset._id
              }
            };
          } else {
            // If upload failed, show error but continue with category creation
            console.error('Image upload failed:', uploadData.message);
            toast.error('Image upload failed, but continuing with category creation');
          }
        } catch (error) {
          // If upload API is not available, show error but continue with category creation
          console.error('Error uploading image:', error);
          toast.error('Image upload failed, but continuing with category creation');
        }
      }

      // Prepare category data
      const categoryData = {
        title: categoryFormData.title,
        description: categoryFormData.description,
        color: categoryFormData.color,
        order: parseInt(categoryFormData.order),
        // Only include icon if we have a valid asset
        ...(imageAsset && { icon: imageAsset })
      };

      // Create the category
      const response = await fetch('/api/store/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(categoryData),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Category created successfully');
        // Reset form
        setCategoryFormData({
          title: '',
          description: '',
          color: '#002366',
          order: '0'
        });
        setSelectedImage(null);
        setImagePreview(null);
        setIsAddCategoryDialogOpen(false);
        // Refresh categories list
        fetchCategories();
      } else {
        toast.error(data.message || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category');
    } finally {
      setIsSubmittingCategory(false);
    }
  };

  // Check for tab parameter in URL
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab');

      if (tabParam && ['general', 'stripe', 'nft', 'products', 'categories'].includes(tabParam)) {
        setActiveTab(tabParam);
      }
    }
  }, []);

  useEffect(() => {
    if (status === 'loading') {
      return; // Still loading, don't do anything yet
    }

    // Check if the user is a super admin
    if (session?.user?.role === 'super_admin') {
      setIsAuthorized(true);
      // Fetch store settings
      fetchStoreSettings();
      // Fetch categories
      fetchCategories();
    }

    // Set loading to false
    setIsLoading(false);
  }, [session, status]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <ShoppingBag className="mr-2 h-6 w-6 text-royalBlue" />
          Store Management
        </h1>
        <p className="text-muted-foreground">
          Configure your online store, products, and payment methods.
        </p>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General Settings</TabsTrigger>
          <TabsTrigger value="stripe">Stripe Payments</TabsTrigger>
          <TabsTrigger value="nft">NFT Marketplace</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>

        {/* General Store Settings */}
        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                Store Configuration
              </CardTitle>
              <CardDescription>
                Configure general store settings and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="store-name">Store Name</Label>
                  <Input
                    id="store-name"
                    value={storeSettings?.store?.name || ''}
                    onChange={(e) => setStoreSettings({
                      ...storeSettings,
                      store: {
                        ...storeSettings.store,
                        name: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="store-currency">Default Currency</Label>
                  <Input
                    id="store-currency"
                    value={storeSettings?.store?.currency || 'USD'}
                    onChange={(e) => setStoreSettings({
                      ...storeSettings,
                      store: {
                        ...storeSettings.store,
                        currency: e.target.value
                      }
                    })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-description">Store Description</Label>
                <Input
                  id="store-description"
                  value={storeSettings?.store?.description || ''}
                  onChange={(e) => setStoreSettings({
                    ...storeSettings,
                    store: {
                      ...storeSettings.store,
                      description: e.target.value
                    }
                  })}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="store-active"
                  checked={storeSettings?.store?.isActive !== false}
                  onCheckedChange={(checked) => setStoreSettings({
                    ...storeSettings,
                    store: {
                      ...storeSettings.store,
                      isActive: checked
                    }
                  })}
                />
                <Label htmlFor="store-active">Store Active</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="tax-enabled"
                  checked={storeSettings?.store?.enableTax !== false}
                  onCheckedChange={(checked) => setStoreSettings({
                    ...storeSettings,
                    store: {
                      ...storeSettings.store,
                      enableTax: checked
                    }
                  })}
                />
                <Label htmlFor="tax-enabled">Enable Tax Calculations</Label>
              </div>

              <Button
                onClick={async () => {
                  try {
                    setIsSaving(true);
                    const response = await fetch('/api/store/settings', {
                      method: 'PATCH',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({
                        store: storeSettings.store
                      }),
                    });

                    const data = await response.json();

                    if (data.success) {
                      toast.success('Store settings saved successfully');
                    } else {
                      toast.error(data.message || 'Failed to save settings');
                    }
                  } catch (error) {
                    console.error('Error saving store settings:', error);
                    toast.error('Failed to save settings');
                  } finally {
                    setIsSaving(false);
                  }
                }}
                disabled={isSaving}
              >
                {isSaving ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stripe Payment Settings */}
        <TabsContent value="stripe" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Stripe Integration
              </CardTitle>
              <CardDescription>
                Configure Stripe payment gateway for processing credit card payments.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stripe-public-key">Stripe Public Key</Label>
                <Input id="stripe-public-key" type="password" defaultValue="pk_test_51..." />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stripe-secret-key">Stripe Secret Key</Label>
                <Input id="stripe-secret-key" type="password" defaultValue="sk_test_51..." />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stripe-webhook-secret">Webhook Secret</Label>
                <Input id="stripe-webhook-secret" type="password" defaultValue="whsec_..." />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="stripe-test-mode" defaultChecked />
                <Label htmlFor="stripe-test-mode">Test Mode</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="stripe-enabled" defaultChecked />
                <Label htmlFor="stripe-enabled">Enable Stripe Payments</Label>
              </div>

              <Button>Save Stripe Settings</Button>
              <Button variant="outline" className="ml-2">Test Connection</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart4 className="mr-2 h-5 w-5" />
                Payment Analytics
              </CardTitle>
              <CardDescription>
                View payment statistics and transaction history.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8 text-muted-foreground">
                Payment analytics dashboard will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* NFT Marketplace Settings */}
        <TabsContent value="nft" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Wallet className="mr-2 h-5 w-5" />
                NFT Marketplace Configuration
              </CardTitle>
              <CardDescription>
                Configure blockchain and NFT marketplace settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contract-address">Smart Contract Address</Label>
                <Input id="contract-address" defaultValue="0x1234..." />
              </div>

              <div className="space-y-2">
                <Label htmlFor="blockchain-network">Blockchain Network</Label>
                <Input id="blockchain-network" defaultValue="Ethereum Mainnet" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="wallet-address">Treasury Wallet Address</Label>
                <Input id="wallet-address" defaultValue="0xabcd..." />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="nft-marketplace-enabled" defaultChecked />
                <Label htmlFor="nft-marketplace-enabled">Enable NFT Marketplace</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="nft-test-mode" defaultChecked />
                <Label htmlFor="nft-test-mode">Test Mode (Testnet)</Label>
              </div>

              <Button>Save NFT Settings</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Coins className="mr-2 h-5 w-5" />
                NFT Collections
              </CardTitle>
              <CardDescription>
                Manage your NFT collections and digital assets.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8 text-muted-foreground">
                NFT collections management interface will be displayed here.
              </div>
              <Button className="mt-4">Create New Collection</Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Products Settings */}
        <TabsContent value="products" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Product Management
              </CardTitle>
              <CardDescription>
                Manage physical and digital products in your store.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-muted-foreground">
                  Manage your store products
                </div>
                <div className="flex gap-2">
                  <Button onClick={() => router.push('/admin/store/products')}>
                    <Tag className="mr-2 h-4 w-4" />
                    Manage Products
                  </Button>
                  <Button variant="outline" onClick={() => router.push('/admin/store/orders')}>
                    <Package className="mr-2 h-4 w-4" />
                    View Orders
                  </Button>
                </div>
              </div>

              <div className="text-center p-8 text-muted-foreground">
                Product management interface will be displayed here.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Categories Settings */}
        <TabsContent value="categories" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center">
                    <Tag className="mr-2 h-5 w-5" />
                    Category Management
                  </CardTitle>
                  <CardDescription>
                    Organize your products with categories.
                  </CardDescription>
                </div>
                <Dialog open={isAddCategoryDialogOpen} onOpenChange={setIsAddCategoryDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add New Category
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                      <DialogTitle>Add New Category</DialogTitle>
                      <DialogDescription>
                        Create a new category for your store products.
                      </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleCategorySubmit}>
                      <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="title">Category Name</Label>
                          <Input
                            id="title"
                            name="title"
                            value={categoryFormData.title}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="description">Description</Label>
                          <Textarea
                            id="description"
                            name="description"
                            value={categoryFormData.description}
                            onChange={handleInputChange}
                            rows={3}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="color">Color</Label>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-6 h-6 rounded-full border"
                                style={{ backgroundColor: categoryFormData.color }}
                              />
                              <Input
                                id="color"
                                name="color"
                                type="text"
                                value={categoryFormData.color}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="order">Display Order</Label>
                            <Input
                              id="order"
                              name="order"
                              type="number"
                              min="0"
                              value={categoryFormData.order}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="image">Category Icon (Optional)</Label>
                          <div className="flex items-center gap-4">
                            <div className="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                              onClick={() => document.getElementById('image-upload')?.click()}
                            >
                              <ImagePlus className="h-8 w-8 text-gray-400 mb-2" />
                              <p className="text-sm text-gray-500">Click to upload</p>
                              <input
                                id="image-upload"
                                type="file"
                                accept="image/*"
                                onChange={handleImageChange}
                                className="hidden"
                              />
                            </div>
                            {imagePreview && (
                              <div className="relative w-24 h-24 border rounded-lg overflow-hidden">
                                <Image
                                  src={imagePreview}
                                  alt="Preview"
                                  fill
                                  className="object-cover"
                                />
                                <button
                                  type="button"
                                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1"
                                  onClick={() => {
                                    setSelectedImage(null);
                                    setImagePreview(null);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsAddCategoryDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isSubmittingCategory}>
                          {isSubmittingCategory ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            'Create Category'
                          )}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-20">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
                  <p className="ml-3 text-royalBlue">Loading categories...</p>
                </div>
              ) : categories.length === 0 ? (
                <div className="text-center py-20">
                  <Tag className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-lg text-gray-600">No categories found</p>
                  <p className="text-sm text-gray-500 mt-2">Create your first category to get started</p>
                  <Button className="mt-4" onClick={() => setIsAddCategoryDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Category
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categories.map((category) => (
                    <div key={category._id} className="border rounded-lg overflow-hidden">
                      <div className="p-4 flex items-center gap-3">
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center text-white"
                          style={{ backgroundColor: category.color || '#002366' }}
                        >
                          {category.icon ? (
                            <div className="relative w-full h-full">
                              <Image
                                src={category.icon}
                                alt={category.title}
                                fill
                                className="object-cover"
                                unoptimized
                              />
                            </div>
                          ) : (
                            <Tag className="h-5 w-5" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{category.title}</h3>
                          {category.description && (
                            <p className="text-sm text-gray-500 line-clamp-1">{category.description}</p>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-500 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Store Configuration</h3>
            <p className="text-sm mt-1 text-yellow-700">
              Changes to payment gateways and NFT settings may affect your customers' ability to make purchases.
              Always test your configuration in a test environment before enabling in production.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
