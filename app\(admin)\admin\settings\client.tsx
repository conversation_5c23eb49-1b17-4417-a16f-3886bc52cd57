'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Save, RefreshCw, Globe, Mail, Phone, MapPin, Instagram, Facebook, Twitter, Calendar, Clock, Loader2, CreditCard, ShoppingBag, Shield } from 'lucide-react';
import { format } from 'date-fns';
import { getSiteSettings } from '@/lib/sanity';
import { isSuperAdmin } from '@/lib/auth';

export default function SettingsClient() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const userIsSuperAdmin = isSuperAdmin(session);

  // Determine which tab to show by default
  const defaultTab = userIsSuperAdmin ? "general" : "general";

  // General settings
  const [generalSettings, setGeneralSettings] = useState({
    siteName: '',
    siteDescription: '',
    siteKeywords: '',
    enableMaintenanceMode: false,
  });

  // SEO & Metadata settings
  const [metadataSettings, setMetadataSettings] = useState({
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    twitterHandle: '',
    siteUrl: '',
    titleTemplate: '',
  });

  // Contact settings
  const [contactSettings, setContactSettings] = useState({
    email: '',
    phone: '',
    address: '',
    mapCoordinates: '',
  });

  // Social media settings
  const [socialSettings, setSocialSettings] = useState({
    instagram: '',
    facebook: '',
    twitter: '',
  });

  // Events settings
  const [eventsSettings, setEventsSettings] = useState({
    coronationDate: '',
    coronationLocation: '',
    showCountdown: false,
  });

  // Banking settings (super admin only)
  const [bankingSettings, setBankingSettings] = useState({
    accountName: '',
    accountNumber: '',
    routingNumber: '',
    bankName: '',
    swiftCode: '',
    paymentProcessor: '',
    enablePayments: false,
  });

  // Store settings (super admin only)
  const [storeSettings, setStoreSettings] = useState({
    enableStore: false,
    shippingFee: '',
    taxRate: '',
    currencySymbol: '',
    allowInternationalShipping: false,
    minimumOrderAmount: '',
  });

  // Fetch settings from Sanity
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching site settings...');

        // Force a fresh fetch by adding a timestamp to bypass cache
        const timestamp = new Date().getTime();
        const settings = await getSiteSettings(`?t=${timestamp}`);
        console.log('Fetched settings:', settings);

        if (settings) {
          // Update general settings
          setGeneralSettings({
            siteName: settings.title || '',
            siteDescription: settings.description || '',
            siteKeywords: settings.keywords?.join(', ') || '',
            enableMaintenanceMode: settings.maintenanceMode || false,
          });

          // Update metadata settings
          if (settings.metadata) {
            setMetadataSettings({
              metaTitle: settings.metadata.metaTitle || '',
              metaDescription: settings.metadata.metaDescription || '',
              metaKeywords: settings.metadata.metaKeywords?.join(', ') || '',
              twitterHandle: settings.metadata.twitterHandle || '',
              siteUrl: settings.metadata.siteUrl || '',
              titleTemplate: settings.metadata.titleTemplate || '%s | The Crown of Africa',
            });
          }

          // Update contact settings
          if (settings.contact) {
            setContactSettings({
              email: settings.contact.email || '',
              phone: settings.contact.phone || '',
              address: settings.contact.address || '',
              mapCoordinates: settings.contact.mapCoordinates || '',
            });
          }

          // Update social settings
          if (settings.social) {
            setSocialSettings({
              instagram: settings.social.instagram || '',
              facebook: settings.social.facebook || '',
              twitter: settings.social.twitter || '',
            });
          }

          // Update events settings
          try {
            if (settings.events) {
              console.log('Fetched events settings:', settings.events);

              // Parse the date safely
              let formattedDate = '';
              if (settings.events.coronationDate) {
                try {
                  console.log('Parsing date from settings:', settings.events.coronationDate);

                  // Parse the date string from UTC to local time
                  const dateString = settings.events.coronationDate;
                  console.log('Raw date string from settings:', dateString);

                  // Create a date object - this will automatically convert from UTC to local time
                  const date = new Date(dateString);
                  console.log('Date parsed in local time:', date);

                  if (!isNaN(date.getTime())) {
                    // Format for datetime-local input (YYYY-MM-DDThh:mm)
                    formattedDate = date.toISOString().slice(0, 16);
                    console.log('Formatted date for input:', formattedDate);
                  } else {
                    console.warn('Invalid date from settings:', settings.events.coronationDate);
                  }
                } catch (err) {
                  console.error('Error parsing date:', err);
                }
              } else {
                console.warn('No coronation date in settings');
              }

              setEventsSettings({
                coronationDate: formattedDate,
                coronationLocation: settings.events.coronationLocation || '',
                showCountdown: typeof settings.events.showCountdown === 'boolean'
                  ? settings.events.showCountdown
                  : false,
              });
            } else {
              console.log('No events settings found, using defaults');
            }
          } catch (err) {
            console.error('Error processing events settings:', err);
          }

          // Update banking settings (super admin only)
          if (settings.banking && userIsSuperAdmin) {
            console.log('Fetched banking settings:', settings.banking);
            setBankingSettings({
              accountName: settings.banking.accountName || '',
              accountNumber: settings.banking.accountNumber || '',
              routingNumber: settings.banking.routingNumber || '',
              bankName: settings.banking.bankName || '',
              swiftCode: settings.banking.swiftCode || '',
              paymentProcessor: settings.banking.paymentProcessor || '',
              enablePayments: typeof settings.banking.enablePayments === 'boolean'
                ? settings.banking.enablePayments
                : false,
            });
          }

          // Update store settings (super admin only)
          if (settings.store && userIsSuperAdmin) {
            console.log('Fetched store settings:', settings.store);
            setStoreSettings({
              enableStore: typeof settings.store.enableStore === 'boolean'
                ? settings.store.enableStore
                : false,
              shippingFee: settings.store.shippingFee || '',
              taxRate: settings.store.taxRate || '',
              currencySymbol: settings.store.currencySymbol || '',
              allowInternationalShipping: typeof settings.store.allowInternationalShipping === 'boolean'
                ? settings.store.allowInternationalShipping
                : false,
              minimumOrderAmount: settings.store.minimumOrderAmount || '',
            });
          }
        }
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        toast.error('Failed to load settings');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [userIsSuperAdmin]);

  // Handle form submissions
  const handleGeneralSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting general settings:', generalSettings);

      // Prepare data for API
      const generalData = {
        siteName: generalSettings.siteName,
        siteDescription: generalSettings.siteDescription,
        siteKeywords: generalSettings.siteKeywords,
        enableMaintenanceMode: generalSettings.enableMaintenanceMode
      };

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'general',
          data: generalData
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('General settings saved successfully');

        // Refresh settings after saving
        const timestamp = new Date().getTime();
        const updatedSettings = await getSiteSettings(`?t=${timestamp}`);

        if (updatedSettings) {
          // Update general settings
          setGeneralSettings({
            siteName: updatedSettings.title || '',
            siteDescription: updatedSettings.description || '',
            siteKeywords: updatedSettings.keywords?.join(', ') || '',
            enableMaintenanceMode: updatedSettings.maintenanceMode || false,
          });
        }
      } else {
        throw new Error(result.message || 'Failed to save general settings');
      }
    } catch (error) {
      console.error('Failed to save general settings:', error);
      toast.error('Failed to save general settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting contact settings:', contactSettings);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'contact',
          data: contactSettings
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Contact settings saved successfully');
      } else {
        throw new Error(result.message || 'Failed to save contact settings');
      }
    } catch (error) {
      console.error('Failed to save contact settings:', error);
      toast.error('Failed to save contact settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSocialSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting social settings:', socialSettings);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'social',
          data: socialSettings
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Social media settings saved successfully');
      } else {
        throw new Error(result.message || 'Failed to save social media settings');
      }
    } catch (error) {
      console.error('Failed to save social media settings:', error);
      toast.error('Failed to save social media settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Metadata settings submit handler
  const handleMetadataSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting metadata settings:', metadataSettings);

      // Convert comma-separated keywords to array
      const keywordsArray = metadataSettings.metaKeywords
        ? metadataSettings.metaKeywords.split(',').map(k => k.trim()).filter(Boolean)
        : [];

      // Prepare data for API
      const metadataData = {
        metaTitle: metadataSettings.metaTitle,
        metaDescription: metadataSettings.metaDescription,
        metaKeywords: keywordsArray, // Send as array, not string
        twitterHandle: metadataSettings.twitterHandle,
        siteUrl: metadataSettings.siteUrl,
        titleTemplate: metadataSettings.titleTemplate,
      };

      console.log('Prepared metadata data:', metadataData);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'metadata',
          data: metadataData
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('SEO & Metadata settings saved successfully');

        // Refresh settings after saving
        const timestamp = new Date().getTime();
        const updatedSettings = await getSiteSettings(`?t=${timestamp}`);

        if (updatedSettings && updatedSettings.metadata) {
          // Update metadata settings
          setMetadataSettings({
            metaTitle: updatedSettings.metadata.metaTitle || '',
            metaDescription: updatedSettings.metadata.metaDescription || '',
            metaKeywords: updatedSettings.metadata.metaKeywords?.join(', ') || '',
            twitterHandle: updatedSettings.metadata.twitterHandle || '',
            siteUrl: updatedSettings.metadata.siteUrl || '',
            titleTemplate: updatedSettings.metadata.titleTemplate || '%s | The Crown of Africa',
          });
        }
      } else {
        throw new Error(result.message || 'Failed to save metadata settings');
      }
    } catch (error) {
      console.error('Failed to save metadata settings:', error);
      toast.error('Failed to save metadata settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEventsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting events settings:', eventsSettings);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'events',
          data: eventsSettings
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Events settings saved successfully');
      } else {
        throw new Error(result.message || 'Failed to save events settings');
      }
    } catch (error) {
      console.error('Failed to save events settings:', error);
      toast.error('Failed to save events settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Banking settings submit handler (super admin only)
  const handleBankingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting banking settings:', bankingSettings);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'banking',
          data: bankingSettings
        }),
      });

      const result = await response.json();
      console.log('API response:', result);

      if (result.success) {
        if (result.demoMode) {
          toast.success('Banking settings saved successfully (Demo Mode)');
          toast.info('Note: In demo mode, settings are not actually saved to the database');
        } else {
          toast.success('Banking settings saved successfully');
        }
      } else if (result.demoMode) {
        toast.warning('Settings not saved due to permissions issue (Demo Mode)');
        toast.info(result.message || 'The Sanity API token may not have write permissions');
      } else {
        throw new Error(result.message || 'Failed to save banking settings');
      }
    } catch (error) {
      console.error('Failed to save banking settings:', error);
      toast.error('Failed to save banking settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Store settings submit handler (super admin only)
  const handleStoreSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      console.log('Submitting store settings:', storeSettings);

      // Get NFT settings from the form
      const nftContractAddress = (document.getElementById('nftContractAddress') as HTMLInputElement)?.value || '';
      const blockchainNetwork = (document.getElementById('blockchainNetwork') as HTMLSelectElement)?.value || 'ethereum';
      const enableNFTs = (document.getElementById('enableNFTs') as HTMLInputElement)?.checked || false;

      // Add NFT settings to the store settings
      const storeSettingsWithNFT = {
        ...storeSettings,
        nftContractAddress,
        blockchainNetwork,
        enableNFTs
      };

      console.log('Store settings with NFT:', storeSettingsWithNFT);

      // Use the API endpoint
      const response = await fetch('/api/settings/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'store',
          data: storeSettingsWithNFT
        }),
      });

      const result = await response.json();
      console.log('API response:', result);

      if (result.success) {
        if (result.demoMode) {
          toast.success('Store settings saved successfully (Demo Mode)');
          toast.info('Note: In demo mode, settings are not actually saved to the database');
        } else {
          toast.success('Store settings saved successfully');
        }
      } else if (result.demoMode) {
        toast.warning('Settings not saved due to permissions issue (Demo Mode)');
        toast.info(result.message || 'The Sanity API token may not have write permissions');
      } else {
        throw new Error(result.message || 'Failed to save store settings');
      }
    } catch (error) {
      console.error('Failed to save store settings:', error);
      toast.error('Failed to save store settings');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your website settings and configurations.
        </p>
      </div>

      <Separator />

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      ) : (
        <Tabs defaultValue={defaultTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="metadata">SEO & Metadata</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
            <TabsTrigger value="social">Social Media</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure the basic settings for your website.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleGeneralSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={generalSettings.siteName}
                      onChange={(e) => setGeneralSettings({ ...generalSettings, siteName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteDescription">Site Description</Label>
                    <Textarea
                      id="siteDescription"
                      value={generalSettings.siteDescription}
                      onChange={(e) => setGeneralSettings({ ...generalSettings, siteDescription: e.target.value })}
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteKeywords">Site Keywords (comma separated)</Label>
                    <Input
                      id="siteKeywords"
                      value={generalSettings.siteKeywords}
                      onChange={(e) => setGeneralSettings({ ...generalSettings, siteKeywords: e.target.value })}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="maintenanceMode"
                      checked={generalSettings.enableMaintenanceMode}
                      onCheckedChange={(checked) => setGeneralSettings({ ...generalSettings, enableMaintenanceMode: checked })}
                    />
                    <Label htmlFor="maintenanceMode">Enable Maintenance Mode</Label>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" type="button">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="metadata" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>SEO & Metadata Settings</CardTitle>
                <CardDescription>
                  Configure search engine optimization and social sharing settings.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleMetadataSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="metaTitle">Default Meta Title</Label>
                    <Input
                      id="metaTitle"
                      value={metadataSettings.metaTitle}
                      onChange={(e) => setMetadataSettings({ ...metadataSettings, metaTitle: e.target.value })}
                      placeholder="The Royal Family of Africa"
                    />
                    <p className="text-xs text-muted-foreground">If left empty, Site Name will be used</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="metaDescription">Default Meta Description</Label>
                    <Textarea
                      id="metaDescription"
                      value={metadataSettings.metaDescription}
                      onChange={(e) => setMetadataSettings({ ...metadataSettings, metaDescription: e.target.value })}
                      rows={3}
                      placeholder="The Crown of Africa. The Rise of a New Era."
                    />
                    <p className="text-xs text-muted-foreground">If left empty, Site Description will be used</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="metaKeywords">Default Meta Keywords (comma separated)</Label>
                    <Input
                      id="metaKeywords"
                      value={metadataSettings.metaKeywords}
                      onChange={(e) => setMetadataSettings({ ...metadataSettings, metaKeywords: e.target.value })}
                      placeholder="royalty, crown, africa, rise, new era"
                    />
                    <p className="text-xs text-muted-foreground">If left empty, Site Keywords will be used</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="titleTemplate">Title Template</Label>
                    <Input
                      id="titleTemplate"
                      value={metadataSettings.titleTemplate}
                      onChange={(e) => setMetadataSettings({ ...metadataSettings, titleTemplate: e.target.value })}
                      placeholder="%s | The Crown of Africa"
                    />
                    <p className="text-xs text-muted-foreground">Use %s to insert the page title</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteUrl">Site URL</Label>
                    <Input
                      id="siteUrl"
                      value={metadataSettings.siteUrl}
                      onChange={(e) => setMetadataSettings({ ...metadataSettings, siteUrl: e.target.value })}
                      placeholder="https://kingdomadukrom.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitterHandle">X Handle (formerly Twitter)</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground">
                        @
                      </span>
                      <Input
                        id="twitterHandle"
                        value={metadataSettings.twitterHandle}
                        onChange={(e) => setMetadataSettings({ ...metadataSettings, twitterHandle: e.target.value })}
                        className="rounded-l-none"
                        placeholder="KingdomAdukrom"
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Your X (formerly Twitter) username without the @ symbol
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ogImage">Default Social Sharing Image</Label>
                    <div className="border rounded-md p-4 bg-muted/20">
                      <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            className="flex gap-2"
                            onClick={() => {
                              // Open file dialog
                              const input = document.createElement('input');
                              input.type = 'file';
                              input.accept = 'image/*';
                              input.onchange = async (e) => {
                                const file = (e.target as HTMLInputElement).files?.[0];
                                if (!file) return;

                                try {
                                  setIsSaving(true);

                                  // Create FormData for image upload
                                  const formData = new FormData();
                                  formData.append('image', file);
                                  formData.append('alt', 'Default social sharing image');
                                  formData.append('type', 'seo');

                                  // Upload the image through our server-side API route
                                  const imageUploadResponse = await fetch(`/api/upload`, {
                                    method: 'POST',
                                    body: formData,
                                  });

                                  if (!imageUploadResponse.ok) {
                                    throw new Error('Failed to upload image');
                                  }

                                  const imageData = await imageUploadResponse.json();

                                  // Update metadata settings with the new image
                                  const response = await fetch('/api/settings/update', {
                                    method: 'POST',
                                    headers: {
                                      'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                      section: 'seo',
                                      data: {
                                        ogImage: imageData.image
                                      }
                                    }),
                                  });

                                  const result = await response.json();

                                  if (result.success) {
                                    toast.success('Social sharing image updated successfully');
                                    // Refresh settings
                                    const updatedSettings = await getSiteSettings(`?t=${new Date().getTime()}`);
                                  } else {
                                    throw new Error(result.message || 'Failed to update settings');
                                  }
                                } catch (error) {
                                  console.error('Error uploading image:', error);
                                  toast.error('Failed to upload image');
                                } finally {
                                  setIsSaving(false);
                                }
                              };
                              input.click();
                            }}
                          >
                            <Upload className="h-4 w-4" />
                            Upload Image
                          </Button>
                          <p className="text-xs text-muted-foreground">
                            Recommended size: 1200×630 pixels
                          </p>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          This image will be used when your site is shared on social media platforms.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" type="button">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="contact" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  Update your contact details displayed on the website.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleContactSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <div className="flex">
                      <Mail className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        value={contactSettings.email}
                        onChange={(e) => setContactSettings({ ...contactSettings, email: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <div className="flex">
                      <Phone className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="phone"
                        value={contactSettings.phone}
                        onChange={(e) => setContactSettings({ ...contactSettings, phone: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <div className="flex">
                      <MapPin className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Textarea
                        id="address"
                        value={contactSettings.address}
                        onChange={(e) => setContactSettings({ ...contactSettings, address: e.target.value })}
                        rows={2}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="mapCoordinates">Map Coordinates</Label>
                    <div className="flex">
                      <Globe className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="mapCoordinates"
                        value={contactSettings.mapCoordinates}
                        onChange={(e) => setContactSettings({ ...contactSettings, mapCoordinates: e.target.value })}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" type="button">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="social" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Links</CardTitle>
                <CardDescription>
                  Update your social media profiles linked on the website.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSocialSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <div className="flex">
                      <Instagram className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="instagram"
                        value={socialSettings.instagram}
                        onChange={(e) => setSocialSettings({ ...socialSettings, instagram: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <div className="flex">
                      <Facebook className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="facebook"
                        value={socialSettings.facebook}
                        onChange={(e) => setSocialSettings({ ...socialSettings, facebook: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitter">X (formerly Twitter)</Label>
                    <div className="flex">
                      <svg className="mr-2 h-4 w-4 mt-3 text-muted-foreground" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                      </svg>
                      <Input
                        id="twitter"
                        value={socialSettings.twitter}
                        onChange={(e) => setSocialSettings({ ...socialSettings, twitter: e.target.value })}
                        placeholder="https://x.com/yourusername"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" type="button">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Event Settings</CardTitle>
                <CardDescription>
                  Manage events and countdown timers.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleEventsSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="coronationDate">Coronation Date & Time</Label>
                    <div className="flex">
                      <Calendar className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="coronationDate"
                        type="datetime-local"
                        value={eventsSettings.coronationDate}
                        onChange={(e) => setEventsSettings({ ...eventsSettings, coronationDate: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="coronationLocation">Coronation Location</Label>
                    <div className="flex">
                      <MapPin className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="coronationLocation"
                        value={eventsSettings.coronationLocation}
                        onChange={(e) => setEventsSettings({ ...eventsSettings, coronationLocation: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showCountdown"
                      checked={eventsSettings.showCountdown}
                      onCheckedChange={(checked) => setEventsSettings({ ...eventsSettings, showCountdown: checked })}
                    />
                    <Label htmlFor="showCountdown">Show Countdown Timer</Label>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" type="button">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {userIsSuperAdmin && (
        <div className="mt-8 pt-8 border-t border-gray-200">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <Shield className="mr-2 h-5 w-5 text-royalGold" />
            Super Admin Settings
          </h2>
          <p className="text-muted-foreground mb-6">
            These settings are only visible to super administrators.
          </p>

          <Tabs defaultValue="banking" className="space-y-4">
            <TabsList>
              <TabsTrigger value="banking" className="bg-royalGold/10">Banking</TabsTrigger>
              <TabsTrigger value="store" className="bg-royalGold/10">Store</TabsTrigger>
            </TabsList>

            <TabsContent value="banking" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="mr-2 h-5 w-5 text-royalBlue" />
                    Banking Information
                  </CardTitle>
                  <CardDescription>
                    Manage banking and payment processing settings for the store.
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleBankingSubmit}>
                  <CardContent className="space-y-4">
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
                      <div className="flex items-start gap-2">
                        <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h3 className="font-medium text-yellow-800">Super Admin Only</h3>
                          <p className="text-sm text-yellow-700">
                            Banking information is restricted to super administrators only.
                            This information is used for payment processing and financial operations.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="accountName">Account Name</Label>
                      <Input
                        id="accountName"
                        value={bankingSettings.accountName}
                        onChange={(e) => setBankingSettings({ ...bankingSettings, accountName: e.target.value })}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="accountNumber">Account Number</Label>
                        <Input
                          id="accountNumber"
                          type="password"
                          value={bankingSettings.accountNumber}
                          onChange={(e) => setBankingSettings({ ...bankingSettings, accountNumber: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="routingNumber">Routing Number</Label>
                        <Input
                          id="routingNumber"
                          type="password"
                          value={bankingSettings.routingNumber}
                          onChange={(e) => setBankingSettings({ ...bankingSettings, routingNumber: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="bankName">Bank Name</Label>
                        <Input
                          id="bankName"
                          value={bankingSettings.bankName}
                          onChange={(e) => setBankingSettings({ ...bankingSettings, bankName: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="swiftCode">SWIFT/BIC Code</Label>
                        <Input
                          id="swiftCode"
                          value={bankingSettings.swiftCode}
                          onChange={(e) => setBankingSettings({ ...bankingSettings, swiftCode: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paymentProcessor">Payment Processor</Label>
                      <select
                        id="paymentProcessor"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={bankingSettings.paymentProcessor}
                        onChange={(e) => setBankingSettings({ ...bankingSettings, paymentProcessor: e.target.value })}
                      >
                        <option value="stripe">Stripe</option>
                        <option value="paypal">PayPal</option>
                        <option value="square">Square</option>
                        <option value="manual">Manual Processing</option>
                      </select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enablePayments"
                        checked={bankingSettings.enablePayments}
                        onCheckedChange={(checked) => setBankingSettings({ ...bankingSettings, enablePayments: checked })}
                      />
                      <Label htmlFor="enablePayments">Enable Payment Processing</Label>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" type="button">Reset</Button>
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? 'Saving...' : 'Save Banking Settings'}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            <TabsContent value="store" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ShoppingBag className="mr-2 h-5 w-5 text-royalBlue" />
                    Store Settings
                  </CardTitle>
                  <CardDescription>
                    Configure the online store and e-commerce settings.
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleStoreSubmit}>
                  <CardContent className="space-y-4">
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
                      <div className="flex items-start gap-2">
                        <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h3 className="font-medium text-yellow-800">Super Admin Only</h3>
                          <p className="text-sm text-yellow-700">
                            Store settings are restricted to super administrators only.
                            These settings control the e-commerce functionality of the website.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enableStore"
                        checked={storeSettings.enableStore}
                        onCheckedChange={(checked) => setStoreSettings({ ...storeSettings, enableStore: checked })}
                      />
                      <Label htmlFor="enableStore">Enable Online Store</Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="currencySymbol">Currency Symbol</Label>
                        <Input
                          id="currencySymbol"
                          value={storeSettings.currencySymbol}
                          onChange={(e) => setStoreSettings({ ...storeSettings, currencySymbol: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="shippingFee">Default Shipping Fee</Label>
                        <Input
                          id="shippingFee"
                          type="number"
                          step="0.01"
                          value={storeSettings.shippingFee}
                          onChange={(e) => setStoreSettings({ ...storeSettings, shippingFee: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="taxRate">Tax Rate (%)</Label>
                        <Input
                          id="taxRate"
                          type="number"
                          step="0.1"
                          value={storeSettings.taxRate}
                          onChange={(e) => setStoreSettings({ ...storeSettings, taxRate: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="minimumOrderAmount">Minimum Order Amount</Label>
                      <Input
                        id="minimumOrderAmount"
                        type="number"
                        step="0.01"
                        value={storeSettings.minimumOrderAmount}
                        onChange={(e) => setStoreSettings({ ...storeSettings, minimumOrderAmount: e.target.value })}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="allowInternationalShipping"
                        checked={storeSettings.allowInternationalShipping}
                        onCheckedChange={(checked) => setStoreSettings({ ...storeSettings, allowInternationalShipping: checked })}
                      />
                      <Label htmlFor="allowInternationalShipping">Allow International Shipping</Label>
                    </div>

                    <Separator className="my-4" />

                    <div>
                      <h3 className="text-lg font-medium mb-2">NFT Marketplace Integration</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Configure NFT marketplace settings for digital assets.
                      </p>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="nftContractAddress">Smart Contract Address</Label>
                          <Input
                            id="nftContractAddress"
                            placeholder="0x1234..."
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="blockchainNetwork">Blockchain Network</Label>
                          <select
                            id="blockchainNetwork"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <option value="ethereum">Ethereum Mainnet</option>
                            <option value="polygon">Polygon</option>
                            <option value="optimism">Optimism</option>
                            <option value="arbitrum">Arbitrum</option>
                            <option value="base">Base</option>
                          </select>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch id="enableNFTs" />
                          <Label htmlFor="enableNFTs">Enable NFT Marketplace</Label>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" type="button">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Reset
                    </Button>
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Store Settings
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
