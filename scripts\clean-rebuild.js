/**
 * Clean and Rebuild Script
 * 
 * This script:
 * 1. Cleans the Next.js cache (.next folder)
 * 2. Optionally removes node_modules and reinstalls dependencies
 * 3. Rebuilds the Next.js application
 * 4. Starts the development server
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Parse command line arguments
const args = process.argv.slice(2);
const fullClean = args.includes('--full') || args.includes('-f');
const skipInstall = args.includes('--skip-install') || args.includes('-s');
const skipBuild = args.includes('--skip-build') || args.includes('-b');
const devMode = !args.includes('--prod') && !args.includes('-p');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if a directory exists
 */
function directoryExists(dirPath) {
  try {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Next.js Clean and Rebuild ====${COLORS.reset}`);
  
  // Step 1: Clean the Next.js cache
  console.log(`\n${COLORS.yellow}Step 1: Cleaning Next.js cache...${COLORS.reset}`);
  if (directoryExists('.next')) {
    console.log(`Removing .next directory...`);
    try {
      fs.rmSync('.next', { recursive: true, force: true });
      console.log(`${COLORS.green}✓ .next directory removed${COLORS.reset}`);
    } catch (error) {
      console.error(`${COLORS.red}Failed to remove .next directory: ${error.message}${COLORS.reset}`);
      console.log('Trying with command line...');
      runCommand('npx rimraf .next', { ignoreError: true });
    }
  } else {
    console.log(`${COLORS.green}✓ .next directory does not exist${COLORS.reset}`);
  }
  
  // Step 2: Clean node_modules (if full clean is requested)
  if (fullClean) {
    console.log(`\n${COLORS.yellow}Step 2: Performing full clean...${COLORS.reset}`);
    if (directoryExists('node_modules')) {
      console.log(`Removing node_modules directory...`);
      try {
        fs.rmSync('node_modules', { recursive: true, force: true });
        console.log(`${COLORS.green}✓ node_modules directory removed${COLORS.reset}`);
      } catch (error) {
        console.error(`${COLORS.red}Failed to remove node_modules directory: ${error.message}${COLORS.reset}`);
        console.log('Trying with command line...');
        runCommand('npx rimraf node_modules', { ignoreError: true });
      }
    } else {
      console.log(`${COLORS.green}✓ node_modules directory does not exist${COLORS.reset}`);
    }
  }
  
  // Step 3: Install dependencies (unless skipped)
  if (!skipInstall && (fullClean || !directoryExists('node_modules'))) {
    console.log(`\n${COLORS.yellow}Step 3: Installing dependencies...${COLORS.reset}`);
    runCommand('npm install');
    console.log(`${COLORS.green}✓ Dependencies installed${COLORS.reset}`);
  } else if (skipInstall) {
    console.log(`\n${COLORS.yellow}Step 3: Skipping dependency installation${COLORS.reset}`);
  } else {
    console.log(`\n${COLORS.yellow}Step 3: Dependencies already installed${COLORS.reset}`);
  }
  
  // Step 4: Build the application (unless skipped)
  if (!skipBuild) {
    console.log(`\n${COLORS.yellow}Step 4: Building the application...${COLORS.reset}`);
    runCommand('npm run build');
    console.log(`${COLORS.green}✓ Application built${COLORS.reset}`);
  } else {
    console.log(`\n${COLORS.yellow}Step 4: Skipping application build${COLORS.reset}`);
  }
  
  // Step 5: Start the development server (in dev mode)
  if (devMode) {
    console.log(`\n${COLORS.yellow}Step 5: Starting development server...${COLORS.reset}`);
    console.log(`${COLORS.green}✓ Clean and rebuild complete!${COLORS.reset}`);
    console.log(`${COLORS.cyan}Starting development server...${COLORS.reset}`);
    runCommand('npm run dev');
  } else {
    console.log(`\n${COLORS.yellow}Step 5: Starting production server...${COLORS.reset}`);
    console.log(`${COLORS.green}✓ Clean and rebuild complete!${COLORS.reset}`);
    console.log(`${COLORS.cyan}Starting production server...${COLORS.reset}`);
    runCommand('npm run start');
  }
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
