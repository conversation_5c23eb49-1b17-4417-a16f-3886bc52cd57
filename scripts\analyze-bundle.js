/**
 * Bundle Analyzer Script
 * 
 * This script analyzes the Next.js bundle size and composition.
 * Run with: node scripts/analyze-bundle.js
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Update Next.js configuration to enable bundle analyzer
 */
function enableBundleAnalyzer() {
  console.log(`\n${COLORS.yellow}Enabling bundle analyzer in Next.js config...${COLORS.reset}`);
  
  const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    console.log(`${COLORS.red}next.config.js not found, skipping...${COLORS.reset}`);
    return false;
  }
  
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  let configUpdated = false;
  
  // Check if bundle analyzer is already configured
  if (nextConfig.includes('@next/bundle-analyzer')) {
    console.log(`${COLORS.green}✓ Bundle analyzer already configured${COLORS.reset}`);
  } else {
    // Add bundle analyzer configuration
    nextConfig = `const withBundleAnalyzer = process.env.ANALYZE === 'true' 
  ? require('@next/bundle-analyzer')({ enabled: true })
  : (config) => config;

${nextConfig}`;

    // Update module.exports to wrap with withBundleAnalyzer
    nextConfig = nextConfig.replace(
      'module.exports = nextConfig;',
      'module.exports = withBundleAnalyzer(nextConfig);'
    );
    
    // Write updated config
    fs.writeFileSync(nextConfigPath, nextConfig);
    console.log(`${COLORS.green}✓ Enabled bundle analyzer in Next.js config${COLORS.reset}`);
    configUpdated = true;
  }
  
  return configUpdated;
}

/**
 * Restore Next.js configuration after analysis
 */
function restoreNextConfig(configUpdated) {
  if (!configUpdated) return;
  
  console.log(`\n${COLORS.yellow}Restoring Next.js configuration...${COLORS.reset}`);
  
  const nextConfigPath = path.join(ROOT_DIR, 'next.config.js');
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Remove bundle analyzer configuration
  nextConfig = nextConfig.replace(/const withBundleAnalyzer.*?;(\r?\n)+/s, '');
  
  // Restore module.exports
  nextConfig = nextConfig.replace(
    'module.exports = withBundleAnalyzer(nextConfig);',
    'module.exports = nextConfig;'
  );
  
  // Write updated config
  fs.writeFileSync(nextConfigPath, nextConfig);
  console.log(`${COLORS.green}✓ Restored Next.js configuration${COLORS.reset}`);
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Next.js Bundle Analyzer ====${COLORS.reset}`);
  
  // Install required packages
  console.log(`\n${COLORS.yellow}Installing required packages...${COLORS.reset}`);
  runCommand('npm install --no-save cross-env @next/bundle-analyzer', { ignoreError: true });
  
  // Enable bundle analyzer in Next.js config
  const configUpdated = enableBundleAnalyzer();
  
  // Run the build with bundle analyzer
  console.log(`\n${COLORS.yellow}Analyzing bundle...${COLORS.reset}`);
  try {
    runCommand('cross-env ANALYZE=true next build', { 
      env: {
        ...process.env,
        ANALYZE: 'true',
      }
    });
    
    console.log(`\n${COLORS.green}✓ Bundle analysis complete!${COLORS.reset}`);
    console.log(`Check the report in your browser.`);
  } catch (error) {
    console.error(`${COLORS.red}Error analyzing bundle: ${error.message}${COLORS.reset}`);
  } finally {
    // Restore Next.js config
    restoreNextConfig(configUpdated);
  }
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
