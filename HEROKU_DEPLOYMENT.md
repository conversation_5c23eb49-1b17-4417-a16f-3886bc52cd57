# Heroku Deployment Guide for Kingdom of Adukrom Website

This guide provides step-by-step instructions for deploying the Kingdom of Adukrom website to Heroku.

## Prerequisites

1. [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) installed
2. Heroku account
3. Git installed

## Deployment Steps

### 1. Login to <PERSON><PERSON>

```bash
heroku login
```

### 2. Create a new Heroku app (if you haven't already)

```bash
heroku create kingdomadukrom
```

### 3. Set Heroku buildpacks

```bash
heroku buildpacks:clear -a kingdomadukrom
heroku buildpacks:set heroku/nodejs -a kingdomadukrom
```

### 4. Set environment variables

```bash
heroku config:set NODE_ENV=production -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_DATASET=production -a kingdomadukrom
heroku config:set NEXT_PUBLIC_SANITY_API_VERSION=2025-05-09 -a kingdomadukrom
```

### 5. Deploy to <PERSON><PERSON>

```bash
git push heroku main
```

## Troubleshooting

### Node.js Version Issues

If you encounter Node.js version compatibility issues, make sure your package.json has the correct engines field:

```json
"engines": {
  "node": ">=20.0.0",
  "npm": ">=10.0.0"
}
```

### Tailwind CSS Issues

If you encounter issues with Tailwind CSS, make sure you have the following files:

1. `postcss.config.js` with the following content:
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

2. `tailwind.config.js` properly configured

### Sanity Client Issues

If you encounter issues with Sanity client, make sure you have the correct versions:

```json
"@sanity/client": "^6.15.7",
"@sanity/visual-editing": "^1.8.0",
```

## Monitoring Your Deployment

After deployment, you can monitor your app using:

```bash
heroku logs --tail -a kingdomadukrom
```

## Scaling Your App

To scale your app, use:

```bash
heroku ps:scale web=1 -a kingdomadukrom
```

## Custom Domain Setup

To set up a custom domain:

1. Purchase a domain
2. Add it to your Heroku app:
```bash
heroku domains:add www.kingdomadukrom.com -a kingdomadukrom
```
3. Configure DNS settings with your domain provider
