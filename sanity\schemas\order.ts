import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'order',
  title: 'Order',
  type: 'document',
  fields: [
    defineField({
      name: 'orderNumber',
      title: 'Order Number',
      type: 'string',
      readOnly: true,
    }),
    defineField({
      name: 'customer',
      title: 'Customer',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' },
        { name: 'email', title: 'Email', type: 'string' },
        { name: 'phone', title: 'Phone', type: 'string' },
      ],
    }),
    defineField({
      name: 'items',
      title: 'Order Items',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'product',
              title: 'Product',
              type: 'reference',
              to: [{ type: 'product' }],
            },
            { name: 'quantity', title: 'Quantity', type: 'number' },
            { name: 'price', title: 'Price at Time of Purchase', type: 'number' },
            { name: 'variantSku', title: 'Variant SKU', type: 'string' },
            {
              name: 'variantOptions',
              title: 'Variant Options',
              type: 'object',
              fields: [
                { name: 'color', title: 'Color', type: 'string' },
                { name: 'size', title: 'Size', type: 'string' },
                { name: 'material', title: 'Material', type: 'string' },
                { name: 'style', title: 'Style', type: 'string' },
              ],
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'shippingAddress',
      title: 'Shipping Address',
      type: 'object',
      fields: [
        { name: 'line1', title: 'Address Line 1', type: 'string' },
        { name: 'line2', title: 'Address Line 2', type: 'string' },
        { name: 'city', title: 'City', type: 'string' },
        { name: 'state', title: 'State/Province', type: 'string' },
        { name: 'postalCode', title: 'Postal/Zip Code', type: 'string' },
        { name: 'country', title: 'Country', type: 'string' },
      ],
    }),
    defineField({
      name: 'billingAddress',
      title: 'Billing Address',
      type: 'object',
      fields: [
        { name: 'sameAsShipping', title: 'Same as Shipping', type: 'boolean' },
        { name: 'line1', title: 'Address Line 1', type: 'string' },
        { name: 'line2', title: 'Address Line 2', type: 'string' },
        { name: 'city', title: 'City', type: 'string' },
        { name: 'state', title: 'State/Province', type: 'string' },
        { name: 'postalCode', title: 'Postal/Zip Code', type: 'string' },
        { name: 'country', title: 'Country', type: 'string' },
      ],
    }),
    defineField({
      name: 'payment',
      title: 'Payment Information',
      type: 'object',
      fields: [
        {
          name: 'method',
          title: 'Payment Method',
          type: 'string',
          options: {
            list: [
              { title: 'Credit Card', value: 'credit_card' },
              { title: 'PayPal', value: 'paypal' },
              { title: 'Crypto', value: 'crypto' },
              { title: 'Bank Transfer', value: 'bank_transfer' },
            ],
          },
        },
        { name: 'transactionId', title: 'Transaction ID', type: 'string' },
        { name: 'amountPaid', title: 'Amount Paid', type: 'number' },
        { name: 'currency', title: 'Currency', type: 'string' },
        {
          name: 'status',
          title: 'Payment Status',
          type: 'string',
          options: {
            list: [
              { title: 'Pending', value: 'pending' },
              { title: 'Completed', value: 'completed' },
              { title: 'Failed', value: 'failed' },
              { title: 'Refunded', value: 'refunded' },
            ],
          },
        },
        { name: 'paidAt', title: 'Paid At', type: 'datetime' },
      ],
    }),
    defineField({
      name: 'totals',
      title: 'Order Totals',
      type: 'object',
      fields: [
        { name: 'subtotal', title: 'Subtotal', type: 'number' },
        { name: 'tax', title: 'Tax', type: 'number' },
        { name: 'shipping', title: 'Shipping', type: 'number' },
        { name: 'discount', title: 'Discount', type: 'number' },
        { name: 'total', title: 'Total', type: 'number' },
      ],
    }),
    defineField({
      name: 'status',
      title: 'Order Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Processing', value: 'processing' },
          { title: 'Shipped', value: 'shipped' },
          { title: 'Delivered', value: 'delivered' },
          { title: 'Cancelled', value: 'cancelled' },
          { title: 'Refunded', value: 'refunded' },
        ],
      },
      initialValue: 'pending',
    }),
    defineField({
      name: 'notes',
      title: 'Order Notes',
      type: 'text',
    }),
    defineField({
      name: 'createdAt',
      title: 'Created At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      readOnly: true,
    }),
    defineField({
      name: 'updatedAt',
      title: 'Updated At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    }),
    defineField({
      name: 'shippingInfo',
      title: 'Shipping Information',
      type: 'object',
      fields: [
        { name: 'carrier', title: 'Carrier', type: 'string' },
        { name: 'trackingNumber', title: 'Tracking Number', type: 'string' },
        { name: 'estimatedDelivery', title: 'Estimated Delivery', type: 'date' },
        { name: 'shippedAt', title: 'Shipped At', type: 'datetime' },
        { name: 'deliveredAt', title: 'Delivered At', type: 'datetime' },
      ],
    }),
  ],
  preview: {
    select: {
      title: 'orderNumber',
      subtitle: 'status',
      customer: 'customer.name',
      total: 'totals.total',
    },
    prepare({ title, subtitle, customer, total }) {
      return {
        title: `Order #${title}`,
        subtitle: `${subtitle} - ${customer} - $${total || 0}`,
      };
    },
  },
});
