'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { getNavigationPages } from '@/lib/sanity';
import { USE_LOCAL_DATA } from '@/lib/config';

type NavPage = {
  _id: string;
  title: string;
  slug: { current: string };
  navOrder: number;
};

export default function Header({ isGalleryPage = false }: { isGalleryPage?: boolean }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [isMounted, setIsMounted] = useState(false);
  const [navPages, setNavPages] = useState<NavPage[]>([]);

  useEffect(() => {
    setIsMounted(true);

    // Fetch navigation pages from Sanity
    const fetchNavPages = async () => {
      try {
        console.log('Header: Attempting to fetch navigation pages...');

        // Use a timeout to prevent hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Navigation request timeout')), 5000);
        });

        // Try to fetch the pages with a timeout
        const pagesPromise = getNavigationPages();
        const pages = await Promise.race([pagesPromise, timeoutPromise]);

        if (Array.isArray(pages)) {
          console.log(`Header: Successfully fetched ${pages.length} navigation pages`);
          setNavPages(pages);
        } else {
          console.warn('Header: Navigation pages not returned as array, using default navigation');
          setNavPages([]);
        }
      } catch (error) {
        console.error('Header: Error fetching navigation pages:', error);
        // Continue with default navigation on error
        setNavPages([]);
      }
    };

    fetchNavPages();
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  useEffect(() => {
    if (!isMounted) return;

    // Only track sections on the home page
    if (isGalleryPage) return;

    // Check for hash in URL when component mounts
    const checkHash = () => {
      const hash = window.location.hash;
      if (hash) {
        // Remove the # character
        const sectionId = hash.substring(1);
        // Set the active section based on the hash
        setActiveSection(sectionId);

        // Scroll to the section
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    };

    // Run once when component mounts
    checkHash();

    const handleScroll = () => {
      const sections = document.querySelectorAll('section[id]');
      let currentSection = 'home';

      sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top + window.scrollY;
        const sectionHeight = section.clientHeight;
        if (window.scrollY >= sectionTop - 200 && window.scrollY < sectionTop + sectionHeight - 200) {
          currentSection = section.getAttribute('id') || 'home';
        }
      });

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isGalleryPage, isMounted]);

  if (!isMounted) {
    return null; // Return null on server-side and first render
  }

  // Static nav items that should always be included
  const staticNavItems = [
    { _id: 'home', title: 'Home', slug: { current: '/' }, navOrder: 0 },
    { _id: 'gallery', title: 'Gallery', slug: { current: '/gallery' }, navOrder: 90 },
    { _id: 'contact', title: 'Contact', slug: { current: '/contact' }, navOrder: 100 }
  ];

  // Filter out duplicate Home links from navPages
  const filteredNavPages = navPages.filter(page => page.title.toLowerCase() !== 'home');

  // Combine static and dynamic nav items
  // Note: This is kept for future use if needed
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const allNavItems = [...staticNavItems, ...filteredNavPages];

  // Define default navigation items for the site
  const defaultNavItems = [
    { _id: 'home', title: 'Home', slug: { current: '/' }, navOrder: 0 },
    { _id: 'about', title: 'About', slug: { current: '#about' }, navOrder: 10 },
    { _id: 'coronation', title: 'Coronation', slug: { current: '#coronation' }, navOrder: 20 },
    { _id: 'rsvp', title: 'RSVP', slug: { current: '#rsvp' }, navOrder: 30 },
    { _id: 'initiatives', title: 'Initiatives', slug: { current: '#initiatives' }, navOrder: 40 },
    { _id: 'events', title: 'Events', slug: { current: '/events' }, navOrder: 45 },
    { _id: 'gallery', title: 'Gallery', slug: { current: '/gallery' }, navOrder: 50 },
    { _id: 'partners', title: 'Partners', slug: { current: '/partners' }, navOrder: 55 },
    { _id: 'contact', title: 'Contact', slug: { current: '#contact' }, navOrder: 60 },
    { _id: 'news', title: 'News', slug: { current: '/news' }, navOrder: 70 }
  ];

  // Determine which navigation items to use
  let navItems: NavPage[] = [];

  // If we have pages from Sanity, use those, otherwise use default items
  if (navPages.length > 0) {
    console.log('Using navigation pages from Sanity');
    // Start with the default items
    navItems = [...defaultNavItems];

    // Add any additional pages from Sanity that aren't already in the default items
    navPages.forEach(page => {
      // Check if this page is already in our default items
      const existingItem = navItems.find(item =>
        item.title.toLowerCase() === page.title.toLowerCase() ||
        item.slug.current === page.slug.current
      );

      // If not found, add it to our navigation items
      if (!existingItem) {
        navItems.push({
          _id: page._id,
          title: page.title,
          slug: page.slug,
          navOrder: page.navOrder
        });
      }
    });
  } else {
    console.log('Using default navigation items');
    navItems = [...defaultNavItems];
  }

  // Sort all items by navOrder
  navItems.sort((a, b) => a.navOrder - b.navOrder);

  // Define main navigation items that will be shown directly in the navbar
  const mainNavItems = ['Home', 'About', 'Contact'];

  // Filter items that should appear in the main navigation
  const topLevelNav = navItems.filter(item => mainNavItems.includes(item.title));

  // Create 4 specific dropdown categories
  const navGroups: Record<string, NavPage[]> = {
    'Initiatives': [],
    'Events': [],
    'Gallery': [],
    'Resources': []
  };

  // Group remaining items into the 4 categories
  navItems.forEach(item => {
    if (!mainNavItems.includes(item.title)) {
      const title = item.title.toLowerCase();

      // Initiatives category
      if (title.includes('initiative') ||
          title.includes('project') ||
          title.includes('program') ||
          title.includes('mission') ||
          title.includes('vision') ||
          title.includes('goal') ||
          title.includes('development') ||
          title.includes('community')) {
        navGroups['Initiatives'].push(item);
      }
      // Events category
      else if (title.includes('event') ||
               title.includes('ceremony') ||
               title.includes('festival') ||
               title.includes('celebration') ||
               title.includes('coronation') ||
               title.includes('calendar') ||
               title.includes('rsvp')) {
        navGroups['Events'].push(item);
      }
      // Gallery category
      else if (title.includes('gallery') ||
               title.includes('photo') ||
               title.includes('image') ||
               title.includes('media') ||
               title.includes('video')) {
        navGroups['Gallery'].push(item);
      }
      // Resources category (default for anything else)
      else {
        navGroups['Resources'].push(item);
      }
    }
  });

  // Sort items in each category alphabetically
  Object.keys(navGroups).forEach(key => {
    navGroups[key].sort((a, b) => a.title.localeCompare(b.title));
  });

  // Remove empty categories
  Object.keys(navGroups).forEach(key => {
    if (navGroups[key].length === 0) {
      delete navGroups[key];
    }
  });

  const groupOrder = Object.keys(navGroups);

  return (
    <header className="sticky top-0 z-50 royal-gradient shadow-lg">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          {/* Logo and Title */}
          <Link href="/" className="flex items-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
              animate={{
                opacity: 1,
                scale: 1,
                rotate: 0,
                y: [0, -5, 0]
              }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
                duration: 1.2,
                y: {
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }
              }}
              whileHover={{
                scale: 1.05,
                rotate: [0, -5, 5, -5, 0],
                transition: { duration: 0.5 }
              }}
              className="relative"
            >
              {/* No glow effect */}
              <Image
                src="/images/ai-logo1a.png"
                alt="Adukrom Kingdom Logo"
                width={100}
                height={100}
                className="object-contain relative z-10"
              />
            </motion.div>
            <motion.div
              className="ml-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <motion.h1
                className="text-white text-lg md:text-xl font-bold"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.0 }}
                whileHover={{
                  color: "#D4AF37",
                  transition: { duration: 0.3 }
                }}
              >
                Adukrom Kingdom
              </motion.h1>
              <motion.p
                className="text-royalGold text-xs md:text-sm"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.2 }}
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
              >
                The Crown of Africa
              </motion.p>
            </motion.div>
          </Link>

          {/* Desktop Navigation: Top-level links first, then dropdowns */}
          <nav className="hidden lg:flex items-center space-x-8">
            {topLevelNav.map(item => {
              // Handle different types of links correctly
              let href = '';
              if (item.title.toLowerCase() === 'home') {
                href = '/';
              } else if (item.slug.current.startsWith('#')) {
                // For anchor links on the homepage
                href = isGalleryPage ? '/' : item.slug.current;
              } else if (item.slug.current.startsWith('/')) {
                // For absolute paths
                href = item.slug.current;
              } else {
                // For regular pages
                href = `/${item.slug.current}`;
              }

              // Determine if this link is active - improved detection for all pages
              const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
              const isActive = currentPath === href ||
                              (href.startsWith('#') && activeSection === item.title.toLowerCase()) ||
                              (isGalleryPage && href === '/gallery') ||
                              (currentPath.includes('partners') && href.includes('partners')) ||
                              (currentPath.includes(item.slug?.current) && href.includes(item.slug?.current));

              return (
               {groupOrder.map(group => (
  <div key={group} className="mb-4">
    <div className="font-bold mb-2 border-b border-white/20 pb-1 flex items-center text-white">
      {group}
    </div>
    <div className="pl-3">
      <div className="grid grid-cols-2 gap-x-2">
        {navGroups[group].map((item: NavPage) => {
          let href = '';
          if (item.title.toLowerCase() === 'home') {
            href = '/';
          } else if (item.slug.current.startsWith('#')) {
            href = isGalleryPage ? '/' + item.slug.current.substring(1) : item.slug.current;
          } else if (item.slug.current.startsWith('/')) {
            href = item.slug.current;
          } else {
            href = `/${item.slug.current}`;
          }

          const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
          const isActive = currentPath === href ||
                          (href.startsWith('#') && activeSection === item.title.toLowerCase()) ||
                          (isGalleryPage && href === '/gallery') ||
                          (currentPath.includes('partners') && href.includes('partners')) ||
                          (currentPath.includes(item.slug?.current) && href.includes(item.slug?.current));

          return (
            <Link
              key={item._id}
              href={href}
              className={`block text-sm font-medium text-white mb-2 flex items-center`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className="w-1.5 h-1.5 rounded-full mr-2 opacity-70 bg-white"></span>
              {item.title}
            </Link>
          );
        })}
      </div>
    </div>
  </div>
))}

