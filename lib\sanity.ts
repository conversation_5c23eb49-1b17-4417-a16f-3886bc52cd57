/**
 * Sanity data fetching functions
 *
 * This module provides functions to fetch data from <PERSON>ity.
 * It uses the centralized Sanity client from sanity.client.ts.
 */

import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { readClient, urlFor, sanityFetch } from './sanity.client';
import { logError } from './errorHandling';
import { SiteSettings, NewsArticle, GalleryItem, Category, Event } from './types/sanity';

// Re-export the Sanity client and urlFor function
export { readClient as sanityClient, urlFor };

// Fetch all news articles
export async function getNews() {
  console.log('Fetching all news articles...');
  const result = await sanityFetch(`
    *[_type == "post" || _type == "news"] | order(publishedAt desc) {
      _id,
      title,
      slug,
      excerpt,
      mainImage,
      publishedAt,
      category->{
        title,
        slug
      },
      featured,
      status
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });

  console.log(`Found ${result.length} news articles`);
  if (result.length > 0) {
    console.log('First article:', JSON.stringify(result[0], null, 2));
  }

  return result;
}

// Fetch a single news article by slug
export async function getNewsBySlug(slug: string) {
  return sanityFetch(`
    *[(_type == "post" || _type == "news") && slug.current == $slug][0] {
      _id,
      title,
      slug,
      excerpt,
      mainImage,
      body,
      publishedAt,
      category->{
        title,
        slug
      },
      author->{
        name,
        image,
        bio
      },
      featured,
      status,
      gallery[] {
        image,
        alt,
        caption
      }
    }
  `, { slug }, {
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch featured news articles
export async function getFeaturedNews() {
  console.log('Fetching featured news articles...');
  const result = await sanityFetch(`
    *[(_type == "post" || _type == "news") && (featured == true || status == "published")] | order(publishedAt desc)[0...3] {
      _id,
      title,
      slug,
      excerpt,
      mainImage,
      publishedAt,
      category->{
        title,
        slug
      }
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });

  console.log(`Found ${result.length} featured news articles`);
  if (result.length > 0) {
    console.log('First featured article:', JSON.stringify(result[0], null, 2));
  }

  return result;
}

// Fetch all gallery images and videos
export async function getGallery() {
  return sanityFetch(`
    *[_type == "gallery"] | order(order asc) {
      _id,
      title,
      slug,
      description,
      "image": images[0].image, // For backward compatibility
      images[]{
        mediaType,
        image {
          asset->{
            _id,
            url
          },
          alt,
          caption,
          hotspot,
          crop
        },
        video {
          asset->{
            _id,
            url
          }
        },
        thumbnail {
          asset->{
            _id,
            url
          },
          hotspot,
          crop
        },
        alt,
        caption
      },
      category->{
        _id,
        title,
        slug
      },
      tags,
      publishedAt,
      featured,
      order
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch featured gallery images
export async function getFeaturedGallery() {
  return sanityFetch(`
    *[_type == "gallery" && featured == true] | order(order asc)[0...6] {
      _id,
      title,
      slug,
      description,
      image,
      category->{
        title,
        slug
      }
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch a gallery by slug
export async function getGalleryBySlug(slug: string) {
  return sanityFetch(`
    *[_type == "gallery" && slug.current == $slug][0] {
      _id,
      title,
      description,
      images[] {
        image,
        caption,
        alt
      },
      displayStyle,
      backgroundStyle,
      animation,
      category->,
      tags,
      publishedAt
    }
  `, { slug }, {
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch gallery images by category
export async function getGalleryByCategory(categorySlug: string) {
  return sanityFetch(`
    *[_type == "gallery" && category->slug.current == $categorySlug] | order(order asc) {
      _id,
      title,
      slug,
      description,
      image,
      category->{
        title,
        slug
      },
      tags,
      publishedAt
    }
  `, { categorySlug }, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch all categories
export async function getCategories() {
  return sanityFetch(`
    *[_type == "category"] | order(order asc) {
      _id,
      title,
      slug,
      description,
      color,
      icon,
      order
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch site settings
export async function getSiteSettings(cacheParam?: string): Promise<SiteSettings> {
  return sanityFetch<SiteSettings>(`
    *[_type == "siteSettings"][0] {
      title,
      description,
      keywords,
      logo,
      favicon,
      mainNavigation,
      contact,
      social,
      footer,
      maintenanceMode,
      metadata {
        metaTitle,
        metaDescription,
        metaKeywords,
        ogImage,
        twitterHandle,
        siteUrl,
        titleTemplate
      },
      seo {
        _type,
        metaTitle,
        metaDescription,
        seoKeywords,
        nofollowAttributes,
        openGraph {
          _type,
          title,
          description,
          url,
          siteName,
          image {
            _type,
            asset->,
            crop,
            hotspot
          }
        },
        twitter {
          _type,
          site,
          creator,
          cardType,
          handle
        },
        additionalMetaTags[] {
          _type,
          metaAttributes[] {
            _type,
            attributeKey,
            attributeType,
            attributeValueString,
            attributeValueImage {
              _type,
              asset->,
              crop,
              hotspot
            }
          }
        }
      },
      about {
        kingBio,
        kingVision,
        kingMission,
        kingPurpose,
        kingQuote,
        adukromDescription,
        adukromLocation,
        nifaheneDescription
      },
      events {
        coronationDate,
        coronationLocation,
        showCountdown
      },
      banking {
        accountName,
        accountNumber,
        routingNumber,
        bankName,
        swiftCode,
        paymentProcessor,
        enablePayments
      },
      store {
        enableStore,
        shippingFee,
        taxRate,
        currencySymbol,
        allowInternationalShipping,
        minimumOrderAmount,
        nftSettings {
          contractAddress,
          blockchainNetwork,
          enableNFTs
        }
      }
    }
  `, {}, {
    fallback: null,
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch all RSVP submissions
export async function getRsvpSubmissions() {
  return sanityFetch(`
    *[_type == "rsvp"] | order(submittedAt desc) {
      _id,
      firstName,
      lastName,
      email,
      phone,
      country,
      events,
      attendanceType,
      reminderPreference,
      notes,
      submittedAt
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch all dynamic pages
export async function getPages() {
  return sanityFetch(`
    *[_type == "page"] | order(navOrder asc) {
      _id,
      title,
      slug,
      navMenu,
      navOrder,
      description,
      accessLevel
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch a single page by slug
export async function getPageBySlug(slug: string) {
  return sanityFetch(`
    *[_type == "page" && slug.current == $slug][0] {
      _id,
      title,
      slug,
      description,
      pageBuilder[],
      openGraphImage,
      accessLevel
    }
  `, { slug }, {
    fallback: null,
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch navigation pages
export async function getNavigationPages() {
  console.log('Fetching navigation pages...');

  // Define default navigation pages to use as fallback
  const defaultNavPages = [
    { _id: 'home', title: 'Home', slug: { current: '/' }, navOrder: 0 },
    { _id: 'about', title: 'About', slug: { current: '#about' }, navOrder: 10 },
    { _id: 'coronation', title: 'Coronation', slug: { current: '#coronation' }, navOrder: 20 },
    { _id: 'rsvp', title: 'RSVP', slug: { current: '#rsvp' }, navOrder: 30 },
    { _id: 'initiatives', title: 'Initiatives', slug: { current: '#initiatives' }, navOrder: 40 },
    { _id: 'events', title: 'Events', slug: { current: '/events' }, navOrder: 45 },
    { _id: 'gallery', title: 'Gallery', slug: { current: '/gallery' }, navOrder: 50 },
    { _id: 'partners', title: 'Partners', slug: { current: '/partners' }, navOrder: 55 },
    { _id: 'contact', title: 'Contact', slug: { current: '#contact' }, navOrder: 60 },
    { _id: 'news', title: 'News', slug: { current: '/news' }, navOrder: 70 }
  ];

  // Simplified query to reduce complexity
  const query = `*[_type == "page" && navMenu == true] | order(navOrder asc) {
    _id,
    title,
    slug,
    navOrder
  }`;

  try {
    // Try direct fetch to Sanity API
    const apiUrl = `https://${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}.api.sanity.io/v2025-05-09/data/query/${process.env.NEXT_PUBLIC_SANITY_DATASET}?query=${encodeURIComponent(query)}`;

    const headers = {
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SANITY_API_TOKEN}`,
      'Content-Type': 'application/json',
    };

    console.log('Making direct fetch to Sanity API for navigation pages...');
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
      next: { revalidate: 60 }
    });

    if (response.ok) {
      const data = await response.json();
      const pages = data.result;

      if (pages && pages.length > 0) {
        console.log(`Successfully fetched ${pages.length} navigation pages`);
        return pages;
      } else {
        console.log('No navigation pages found, using default pages');
        return defaultNavPages;
      }
    } else {
      console.error('Error response from Sanity API:', response.status, response.statusText);
      return defaultNavPages;
    }
  } catch (fetchError) {
    console.error('Error fetching navigation pages directly:', fetchError);

    // Fall back to using sanityFetch
    try {
      const pages = await sanityFetch(query, {}, {
        fallback: defaultNavPages,
        next: { revalidate: 60 },
        timeout: 15000 // Longer timeout
      });

      console.log(`Found ${pages.length} navigation pages from sanityFetch`);

      // If no pages found in Sanity, use the default pages
      if (!pages || pages.length === 0) {
        console.log('No navigation pages found in Sanity, using default pages');
        return defaultNavPages;
      }

      return pages;
    } catch (queryError) {
      console.error('Error in Sanity query for navigation pages:', queryError);
      return defaultNavPages;
    }
  }
}

// Fetch all events
export async function getEvents() {
  try {
    console.log('Executing Sanity query for events...');
    const events = await sanityFetch(`
      *[_type == "event"] | order(date asc) {
        _id,
        title,
        slug,
        date,
        endDate,
        location,
        description,
        "imageUrl": image.asset->url,
        "imageAlt": image.alt,
        "imageCaption": image.caption,
        image {
          asset->{
            _id,
            url
          },
          alt,
          caption,
          hotspot,
          crop
        },
        isCountdownTarget,
        isHighlighted,
        showRsvp,
        eventType,
        order
      }
    `, {}, { fallback: [], next: { revalidate: 60 } });

    console.log(`Found ${events?.length || 0} events`);

    // If no events found, try to get the coronation event from site settings
    if (!events || events.length === 0) {
      try {
        console.log('No events found, checking site settings for coronation date...');
        const settings = await getSiteSettings();

        if (settings && settings.events && settings.events.coronationDate) {
          console.log('Found coronation date in site settings:', settings.events.coronationDate);

          // Create a virtual event from the site settings
          const coronationEvent = {
            _id: 'virtual-coronation',
            title: 'Royal Coronation Ceremony',
            slug: { current: 'royal-coronation' },
            date: settings.events.coronationDate,
            location: settings.events.coronationLocation || 'Adukrom Palace, Ghana',
            description: 'The official coronation ceremony of King Allen Ellison.',
            isCountdownTarget: true,
            isHighlighted: true,
            showRsvp: true,
            eventType: 'ceremony',
            order: 1
          };

          console.log('Created virtual coronation event from site settings');
          return [coronationEvent];
        }
      } catch (settingsError) {
        console.error('Error fetching site settings:', settingsError);
      }
    }

    return events || [];
  } catch (error) {
    console.error('Error fetching events from Sanity:', error);
    return [];
  }
}

// Fetch a single event by ID
export async function getEventById(id: string) {
  try {
    console.log(`Fetching event with ID: ${id}`);
    return await sanityFetch(`
      *[_type == "event" && _id == $id][0] {
        _id,
        title,
        slug,
        date,
        endDate,
        location,
        description,
        "imageUrl": image.asset->url,
        "imageAlt": image.alt,
        "imageCaption": image.caption,
        image {
          asset->{
            _id,
            url
          },
          alt,
          caption,
          hotspot,
          crop
        },
        isCountdownTarget,
        isHighlighted,
        showRsvp,
        eventType,
        order
      }
    `, { id }, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });
  } catch (error) {
    console.error(`Error fetching event with ID ${id}:`, error);
    return null;
  }
}

// Fetch the countdown target event
export async function getCountdownTargetEvent() {
  try {
    console.log('Executing Sanity query for countdown target event...');

    // Create a default event in case of failure
    const defaultEvent = {
      _id: 'default-coronation',
      title: 'Royal Coronation Ceremony',
      date: '2025-08-29T07:30:00.000Z',
      location: 'Adukrom Palace, Ghana',
      description: 'The official coronation ceremony of King Allen Ellison.',
      isCountdownTarget: true,
      isHighlighted: true,
      showRsvp: true,
      eventType: 'ceremony'
    };

    // Simplified query to reduce complexity
    const query = `*[_type == "event" && isCountdownTarget == true][0] {
      _id,
      title,
      date,
      location,
      description,
      isCountdownTarget
    }`;

    try {
      // Try direct fetch to Sanity API
      const apiUrl = `https://${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}.api.sanity.io/v2025-05-09/data/query/${process.env.NEXT_PUBLIC_SANITY_DATASET}?query=${encodeURIComponent(query)}`;

      const headers = {
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SANITY_API_TOKEN}`,
        'Content-Type': 'application/json',
      };

      console.log('Making direct fetch to Sanity API for countdown event...');
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers,
        next: { revalidate: 60 }
      });

      if (response.ok) {
        const data = await response.json();
        const event = data.result;

        if (event) {
          console.log('Successfully fetched countdown event:', event.title);
          return event;
        } else {
          console.log('No countdown event found, using default');
          return defaultEvent;
        }
      } else {
        console.error('Error response from Sanity API:', response.status, response.statusText);
        return defaultEvent;
      }
    } catch (fetchError) {
      console.error('Error fetching countdown event directly:', fetchError);

      // Fall back to using sanityFetch
      try {
        const event = await sanityFetch(query, {}, {
          next: { revalidate: 60 },
          fallback: defaultEvent,
          timeout: 15000 // Longer timeout
        });

        console.log('Countdown event from sanityFetch:', event || 'No event found, using default');
        return event || defaultEvent;
      } catch (queryError) {
        console.error('Error in Sanity query for countdown event:', queryError);
        return defaultEvent;
      }
    }
  } catch (error) {
    console.error('Critical error fetching countdown target event:', error);
    // Return a hardcoded event as fallback
    return {
      _id: 'fallback-coronation',
      title: 'Royal Coronation Ceremony',
      date: '2025-08-29T07:30:00.000Z',
      location: 'Adukrom Palace, Ghana',
      description: 'The official coronation ceremony of King Allen Ellison.',
      isCountdownTarget: true,
      isHighlighted: true,
      showRsvp: true,
      eventType: 'ceremony'
    };
  }
}

// Fetch all strategic partners
export async function getStrategicPartners() {
  return sanityFetch(`
    *[_type == "strategicPartner"] | order(order asc) {
      _id,
      name,
      slug,
      description,
      website,
      logo,
      partnershipType,
      featured,
      order,
      startDate,
      active
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch featured strategic partners
export async function getFeaturedPartners() {
  console.log('Fetching featured strategic partners...');

  // Define default partners to use as fallback
  const defaultPartners = [
    {
      _id: 'remit-global',
      name: 'Remit Global',
      slug: { current: 'remit-global' },
      description: 'Strategic Financial Partner',
      partnershipType: 'corporate',
      logo: '/Website Images/remit-global-logo.png'
    },
    {
      _id: 'royal-lion',
      name: 'Royal Lion',
      slug: { current: 'royal-lion' },
      description: 'Heritage & Cultural Partner',
      partnershipType: 'heritage',
      logo: '/Website Images/royal_lion_logo.png'
    },
    {
      _id: 'tef',
      name: 'TEF',
      slug: { current: 'tef' },
      description: 'Educational Development Partner',
      partnershipType: 'educational',
      logo: '/Website Images/tef-logo2-transparent.png'
    },
    {
      _id: 'lightace-global',
      name: 'Lightace Global',
      slug: { current: 'lightace-global' },
      description: 'Innovation & Technology Partner',
      partnershipType: 'technology',
      logo: '/Website Images/lightace-global-logo.png'
    },
    {
      _id: 'akuapem-nifaman-council',
      name: 'Akuapem Nifaman Council',
      slug: { current: 'akuapem-nifaman-council' },
      description: 'Traditional Governance Partner',
      partnershipType: 'governance',
      logo: '/Website Images/akuaapem_nifaman_council_logo.png'
    }
  ];

  // Simplified query to reduce complexity
  const query = `*[_type == "strategicPartner" && featured == true && active == true] | order(order asc) {
    _id,
    name,
    slug,
    description,
    website,
    logo,
    partnershipType,
    featured,
    order,
    active
  }`;

  try {
    // Try direct fetch to Sanity API
    const apiUrl = `https://${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}.api.sanity.io/v2025-05-09/data/query/${process.env.NEXT_PUBLIC_SANITY_DATASET}?query=${encodeURIComponent(query)}`;

    const headers = {
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SANITY_API_TOKEN}`,
      'Content-Type': 'application/json',
    };

    console.log('Making direct fetch to Sanity API for strategic partners...');
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers,
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    if (response.ok) {
      const data = await response.json();
      const partners = data.result;

      if (partners && partners.length > 0) {
        console.log(`Successfully fetched ${partners.length} strategic partners`);
        return partners;
      } else {
        console.log('No strategic partners found, using default partners');
        return defaultPartners;
      }
    } else {
      console.error('Error response from Sanity API:', response.status, response.statusText);
      return defaultPartners;
    }
  } catch (fetchError) {
    console.error('Error fetching strategic partners directly:', fetchError);

    // Fall back to using sanityFetch
    try {
      const partners = await sanityFetch(query, {}, {
        fallback: defaultPartners,
        next: { revalidate: 60 }, // Revalidate every 60 seconds
        timeout: 15000 // Longer timeout
      });

      console.log(`Found ${partners.length} strategic partners from sanityFetch`);

      // If no partners found in Sanity, use the default partners
      if (!partners || partners.length === 0) {
        console.log('No partners found in Sanity, using default partners');
        return defaultPartners;
      }

      return partners;
    } catch (queryError) {
      console.error('Error in Sanity query for strategic partners:', queryError);
      return defaultPartners;
    }
  }
}

// Fetch a strategic partner by ID
export async function getPartnerById(id: string) {
  return sanityFetch(`
    *[_type == "strategicPartner" && _id == $id][0] {
      _id,
      name,
      slug,
      description,
      website,
      logo,
      partnershipType,
      featured,
      order,
      startDate,
      active
    }
  `, { id }, {
    fallback: null,
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch a product by slug
export async function getProductBySlug(slug: string) {
  return sanityFetch(`
    *[_type == "product" && slug.current == $slug][0] {
      _id,
      name,
      slug,
      description,
      longDescription,
      price,
      compareAtPrice,
      currency,
      images,
      category->,
      tags,
      featured,
      inventory,
      variants,
      isDigital,
      publishedAt,
      seo
    }
  `, { slug }, {
    fallback: null,
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch store categories
export async function getStoreCategories() {
  return sanityFetch(`
    *[_type == "productCategory"] | order(order asc) {
      _id,
      title,
      slug,
      description,
      image,
      order,
      featured,
      seo
    }
  `, {}, {
    fallback: [],
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}

// Fetch a store category by slug
export async function getStoreCategoryBySlug(slug: string) {
  return sanityFetch(`
    *[_type == "productCategory" && slug.current == $slug][0] {
      _id,
      title,
      slug,
      description,
      image,
      order,
      featured,
      seo
    }
  `, { slug }, {
    fallback: null,
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
}