import { createClient } from 'next-sanity'
import { apiVersion, dataset, projectId } from '../env'

// This client is used specifically for Sanity Studio integration
export const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false, // Set to false for Studio integration
})

// Re-export the client from lib/sanity.ts to ensure consistency
export { sanityClient } from '../../lib/sanity'
