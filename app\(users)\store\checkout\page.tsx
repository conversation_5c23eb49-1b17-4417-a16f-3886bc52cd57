'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ChevronLeft, CreditCard, ShoppingBag, Check, AlertTriangle } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { 
  CartItem, 
  Cart as CartType,
  loadCart, 
  calculateCartTotals 
} from '@/lib/cart';
import {
  CheckoutData,
  processCheckout,
  processPayment
} from '@/lib/checkout';

export default function CheckoutPage() {
  const router = useRouter();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cartTotals, setCartTotals] = useState<CartType>({
    items: [],
    subtotal: 0,
    tax: 0,
    shipping: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<'information' | 'shipping' | 'payment' | 'confirmation'>('information');
  const [orderNumber, setOrderNumber] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<CheckoutData>({
    customer: {
      name: '',
      email: '',
      phone: ''
    },
    shippingAddress: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    },
    billingAddress: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    },
    sameAsBilling: true,
    paymentMethod: 'credit_card',
    notes: ''
  });

  // Load cart from localStorage
  useEffect(() => {
    setIsLoading(true);
    const loadedCart = loadCart();
    
    // Redirect to cart if empty
    if (loadedCart.length === 0) {
      router.push('/store/cart');
      return;
    }
    
    setCart(loadedCart);
    setCartTotals(calculateCartTotals(loadedCart));
    setIsLoading(false);
  }, [router]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle nested properties
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section as keyof CheckoutData],
          [field]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);
    setError(null);

    try {
      // Process payment (simulation)
      const paymentResult = await processPayment(
        cartTotals.total,
        formData.paymentMethod
      );

      if (!paymentResult.success) {
        setError(paymentResult.error || 'Payment processing failed');
        setIsProcessing(false);
        return;
      }

      // Process checkout
      const checkoutResult = await processCheckout(
        cartTotals,
        {
          ...formData,
          // Add transaction ID from payment
          payment: {
            ...formData.payment,
            transactionId: paymentResult.transactionId
          }
        }
      );

      if (!checkoutResult.success) {
        setError(checkoutResult.error || 'Checkout processing failed');
        setIsProcessing(false);
        return;
      }

      // Set order number and move to confirmation
      setOrderNumber(checkoutResult.order?.orderNumber || 'KA-ORDER');
      setCheckoutStep('confirmation');
    } catch (error) {
      console.error('Error during checkout:', error);
      setError('An unexpected error occurred during checkout');
    } finally {
      setIsProcessing(false);
    }
  };

  // Navigate to next step
  const goToNextStep = () => {
    if (checkoutStep === 'information') {
      setCheckoutStep('shipping');
    } else if (checkoutStep === 'shipping') {
      setCheckoutStep('payment');
    }
  };

  // Navigate to previous step
  const goToPreviousStep = () => {
    if (checkoutStep === 'payment') {
      setCheckoutStep('shipping');
    } else if (checkoutStep === 'shipping') {
      setCheckoutStep('information');
    } else if (checkoutStep === 'information') {
      router.push('/store/cart');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-ivory">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
        <p className="ml-3 text-royalBlue">Loading checkout...</p>
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-ivory">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <button
              onClick={goToPreviousStep}
              className="flex items-center text-royalBlue hover:text-royalGold transition-colors"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              {checkoutStep === 'information' ? 'Back to Cart' : 'Back'}
            </button>
          </div>

          <h1 className="text-3xl font-bold text-royalBlue mb-8 flex items-center">
            <ShoppingBag className="mr-2 h-6 w-6" />
            Checkout
          </h1>

          {/* Checkout steps */}
          <div className="mb-8">
            <div className="flex justify-between">
              <div className={`flex flex-col items-center ${checkoutStep === 'information' ? 'text-royalBlue' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${checkoutStep === 'information' ? 'bg-royalBlue text-white' : 'bg-gray-200'}`}>1</div>
                <span className="text-sm mt-1">Information</span>
              </div>
              <div className="flex-1 border-t border-gray-300 self-center mx-2"></div>
              <div className={`flex flex-col items-center ${checkoutStep === 'shipping' ? 'text-royalBlue' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${checkoutStep === 'shipping' ? 'bg-royalBlue text-white' : 'bg-gray-200'}`}>2</div>
                <span className="text-sm mt-1">Shipping</span>
              </div>
              <div className="flex-1 border-t border-gray-300 self-center mx-2"></div>
              <div className={`flex flex-col items-center ${checkoutStep === 'payment' ? 'text-royalBlue' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${checkoutStep === 'payment' ? 'bg-royalBlue text-white' : 'bg-gray-200'}`}>3</div>
                <span className="text-sm mt-1">Payment</span>
              </div>
              <div className="flex-1 border-t border-gray-300 self-center mx-2"></div>
              <div className={`flex flex-col items-center ${checkoutStep === 'confirmation' ? 'text-royalBlue' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${checkoutStep === 'confirmation' ? 'bg-royalBlue text-white' : 'bg-gray-200'}`}>4</div>
                <span className="text-sm mt-1">Confirmation</span>
              </div>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded-lg text-red-700 flex items-start">
              <AlertTriangle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Error</p>
                <p>{error}</p>
              </div>
            </div>
          )}

          {/* Checkout form */}
          {checkoutStep !== 'confirmation' ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Checkout form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <form onSubmit={handleSubmit}>
                    {/* Information step */}
                    {checkoutStep === 'information' && (
                      <div className="p-6">
                        <h2 className="text-xl font-semibold text-royalBlue mb-4">Customer Information</h2>
                        
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="customer.name" className="block text-sm font-medium text-charcoal mb-1">
                              Full Name
                            </label>
                            <input
                              type="text"
                              id="customer.name"
                              name="customer.name"
                              value={formData.customer.name}
                              onChange={handleInputChange}
                              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-royalBlue"
                              required
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="customer.email" className="block text-sm font-medium text-charcoal mb-1">
                              Email Address
                            </label>
                            <input
                              type="email"
                              id="customer.email"
                              name="customer.email"
                              value={formData.customer.email}
                              onChange={handleInputChange}
                              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-royalBlue"
                              required
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="customer.phone" className="block text-sm font-medium text-charcoal mb-1">
                              Phone Number
                            </label>
                            <input
                              type="tel"
                              id="customer.phone"
                              name="customer.phone"
                              value={formData.customer.phone}
                              onChange={handleInputChange}
                              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-royalBlue"
                            />
                          </div>
                        </div>
                        
                        <div className="mt-6 flex justify-end">
                          <button
                            type="button"
                            onClick={goToNextStep}
                            className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-6 rounded-full hover:bg-yellow-500 transition-colors"
                          >
                            Continue to Shipping
                          </button>
                        </div>
                      </div>
                    )}
                  </form>
                </div>
              </div>

              {/* Order summary */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-lg font-semibold text-royalBlue mb-4">Order Summary</h2>
                  
                  <div className="space-y-4 mb-4">
                    {cart.map((item) => (
                      <div key={item.id} className="flex items-center">
                        <div className="relative w-16 h-16 bg-gray-100 rounded overflow-hidden">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="ml-4 flex-grow">
                          <h3 className="text-sm font-medium text-royalBlue">{item.name}</h3>
                          <p className="text-xs text-charcoal/80">Qty: {item.quantity}</p>
                          <p className="text-sm text-charcoal">${(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="border-t border-gray-200 pt-4 space-y-2">
                    <div className="flex justify-between text-sm text-charcoal">
                      <span>Subtotal</span>
                      <span>${cartTotals.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-charcoal">
                      <span>Tax</span>
                      <span>${cartTotals.tax.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-charcoal">
                      <span>Shipping</span>
                      <span>${cartTotals.shipping.toFixed(2)}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-2 mt-2">
                      <div className="flex justify-between font-semibold text-royalBlue">
                        <span>Total</span>
                        <span>${cartTotals.total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Confirmation step
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-royalBlue mb-2">Order Confirmed!</h2>
              <p className="text-charcoal mb-4">Thank you for your purchase.</p>
              <p className="text-charcoal mb-6">Your order number is: <span className="font-semibold">{orderNumber}</span></p>
              
              <p className="text-sm text-charcoal/70 mb-8">
                This is a demo store. No actual payment has been processed and no products will be shipped.
              </p>
              
              <Link
                href="/store"
                className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-6 rounded-full hover:bg-yellow-500 transition-colors inline-block"
              >
                Continue Shopping
              </Link>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </>
  );
}
