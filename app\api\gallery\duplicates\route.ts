import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin, isAdmin } from '@/lib/auth-utils';
import { v4 as uuidv4 } from 'uuid';

// GET /api/gallery/duplicates - Find duplicate media in the gallery
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to access this endpoint' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Fetch all gallery items with their images
    const galleryItems = await client.fetch(`
      *[_type == "gallery"] {
        _id,
        title,
        slug,
        images[] {
          _key,
          mediaType,
          image,
          video,
          alt,
          caption
        }
      }
    `);

    // Extract all media items
    const allMediaItems: any[] = [];
    galleryItems.forEach((item: any) => {
      if (item.images && item.images.length > 0) {
        item.images.forEach((mediaItem: any) => {
          // Add the gallery item ID to each media item
          allMediaItems.push({
            ...mediaItem,
            galleryId: item._id,
            galleryTitle: item.title,
            _id: item._id // Use the gallery item ID
          });
        });
      }
    });

    // Find duplicates based on asset references
    const duplicateGroups: any[] = [];
    const processedAssets = new Map<string, any[]>();

    // Group by asset reference
    allMediaItems.forEach((item) => {
      let assetRef = '';
      
      if (item.mediaType === 'video' && item.video && item.video.asset && item.video.asset._ref) {
        assetRef = item.video.asset._ref;
      } else if (item.image && item.image.asset && item.image.asset._ref) {
        assetRef = item.image.asset._ref;
      }
      
      if (assetRef) {
        if (!processedAssets.has(assetRef)) {
          processedAssets.set(assetRef, []);
        }
        
        processedAssets.get(assetRef)!.push(item);
      }
    });

    // Create duplicate groups
    processedAssets.forEach((items, assetRef) => {
      if (items.length > 1) {
        duplicateGroups.push({
          id: uuidv4(),
          assetRef,
          items
        });
      }
    });

    return NextResponse.json({
      success: true,
      duplicates: duplicateGroups,
      count: duplicateGroups.length,
      totalItems: allMediaItems.length
    });
  } catch (error) {
    console.error('Error finding duplicates:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to find duplicates',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
