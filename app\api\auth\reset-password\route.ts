import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, updateUser } from '@/lib/users';
import { verifyToken } from '@/lib/tokens';

// POST /api/auth/reset-password - Reset password with token
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { token, password } = body;
    
    // Validate required fields
    if (!token || !password) {
      return NextResponse.json(
        { success: false, message: 'Token and password are required' },
        { status: 400 }
      );
    }
    
    try {
      // Verify the reset token
      const tokenData = verifyToken(token);
      
      // Check if token is for password reset
      if (tokenData.type !== 'reset-password') {
        return NextResponse.json(
          { success: false, message: 'Invalid or expired reset token' },
          { status: 400 }
        );
      }
      
      const { email, userId } = tokenData;
      
      // Check if user exists
      const user = getUserByEmail(email);
      if (!user || user.id !== userId) {
        return NextResponse.json(
          { success: false, message: 'Invalid or expired reset token' },
          { status: 400 }
        );
      }
      
      // Update user's password
      try {
        await updateUser(userId, { password });
        
        return NextResponse.json({
          success: true,
          message: 'Password reset successful',
        });
      } catch (updateError: any) {
        return NextResponse.json(
          { success: false, message: updateError.message || 'Failed to reset password' },
          { status: 400 }
        );
      }
    } catch (tokenError) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error during password reset:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to reset password' },
      { status: 500 }
    );
  }
}
