import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the checkout page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Checkout',
    description: 'Complete your purchase of royal merchandise.',
    url: '/store/checkout',
    keywords: ['Checkout', 'Royal Merchandise', 'Payment'],
    noIndex: true, // We don't want search engines to index checkout pages
  });
}
