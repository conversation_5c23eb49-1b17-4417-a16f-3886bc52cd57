'use client';

import { useState, useRef, useEffect } from 'react';
import { FileVideo } from 'lucide-react';

interface AdminVideoPlayerProps {
  src: string;
  poster?: string;
  title?: string;
  className?: string;
  width?: number;
  height?: number;
  onError?: (error: any) => void;
  onClick?: () => void;
}

/**
 * A direct video player component for the admin section
 * Shows the video with native controls directly in the gallery
 */
export default function AdminVideoPlayer({
  src,
  poster,
  title = 'Video',
  className = '',
  width = 400,
  height = 300,
  onError,
  onClick
}: AdminVideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle video loaded
  const handleLoadedData = () => {
    setIsLoading(false);
    console.log('AdminVideoPlayer: Video loaded successfully');
  };

  // Handle video error
  const handleError = (e: any) => {
    setIsLoading(false);
    setError('Failed to load video');
    console.error('AdminVideoPlayer: Error loading video:', e);

    if (onError) {
      onError(e);
    }
  };

  // Handle click
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`} style={{ width, height }}>
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <div className="animate-pulse flex flex-col items-center">
            <FileVideo className="h-8 w-8 text-white/70 mb-2" />
            <p className="text-white text-sm">Loading video...</p>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <div className="flex flex-col items-center text-center p-4">
            <FileVideo className="h-8 w-8 text-red-500 mb-2" />
            <p className="text-white text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Video element with controls */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        src={src}
        poster={poster}
        controls
        preload="auto"
        playsInline
        onClick={handleClick}
        onLoadedData={handleLoadedData}
        onError={handleError}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Video badge */}
      <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
        Video
      </div>
    </div>
  );
}
