import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const redirectTo = searchParams.get('redirectTo') || '/';

  // Disable Draft Mode
  draftMode().disable();

  // Redirect to the path the user was on
  redirect(redirectTo);
}
