import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';
import { createClient } from '@sanity/client';
import { sendRsvpConfirmationNotifications } from '@/lib/notifications';

// POST /api/rsvp - Submit an RSVP
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'country', 'events', 'attendanceType'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, message: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Get the Sanity write client (with server-side token)
    let client;
    try {
      client = getWriteClient();
      console.log('Successfully created Sanity write client');
    } catch (error) {
      console.error('Error creating write client:', error);

      // Fallback to direct client creation
      const token = process.env.SANITY_API_TOKEN;
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Server configuration error: Missing Sanity API token' },
          { status: 500 }
        );
      }

      console.log('Using fallback Sanity client');
      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }

    // Prepare the RSVP document
    const rsvpDocument = {
      _type: 'rsvp',
      firstName: body.firstName,
      lastName: body.lastName,
      email: body.email,
      phone: body.phone,
      country: body.country,
      events: body.events,
      attendanceType: body.attendanceType,
      reminderPreference: body.reminder || [],
      notes: body.notes || '',
      submittedAt: new Date().toISOString(),
    };

    console.log('Creating RSVP document in Sanity:', {
      name: `${body.firstName} ${body.lastName}`,
      email: body.email,
      events: body.events.length
    });

    // Create the RSVP document in Sanity
    const document = await client.create(rsvpDocument);

    console.log('RSVP document created successfully with ID:', document._id);

    // Send confirmation notifications based on user preferences
    try {
      await sendRsvpConfirmationNotifications({
        firstName: body.firstName,
        lastName: body.lastName,
        email: body.email,
        phone: body.phone,
        events: body.events,
        reminderPreference: body.reminder || ['email']
      });
      console.log('RSVP confirmation notifications sent successfully');
    } catch (notificationError) {
      console.error('Error sending RSVP confirmation notifications:', notificationError);
      // Continue with the response even if notifications fail
    }

    return NextResponse.json({
      success: true,
      message: 'RSVP submitted successfully',
      id: document._id
    });
  } catch (error) {
    console.error('Error submitting RSVP:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to submit RSVP',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
