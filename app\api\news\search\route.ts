import { NextResponse } from 'next/server';
import { readClient as client } from '@/lib/sanity.client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Sanitize the query to prevent GROQ injection
    const sanitizedQuery = query.replace(/[\\'"]/g, '');

    // Search for news articles that match the query in title, excerpt, or body
    const newsArticles = await client.fetch(`
      *[_type == "news" && (
        title match "*${sanitizedQuery}*" ||
        excerpt match "*${sanitizedQuery}*" ||
        pt::text(body) match "*${sanitizedQuery}*"
      )] | order(publishedAt desc) {
        _id,
        title,
        slug,
        excerpt,
        publishedAt,
        mainImage,
        category->{
          _id,
          title,
          slug
        }
      }
    `);

    return NextResponse.json({ articles: newsArticles });
  } catch (error) {
    console.error('Error searching news:', error);
    return NextResponse.json(
      { error: 'Failed to search news articles' },
      { status: 500 }
    );
  }
}
