import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'formSubmission',
  title: 'Form Submission',
  type: 'document',
  fields: [
    defineField({
      name: 'formType',
      title: 'Form Type',
      type: 'string',
      options: {
        list: [
          { title: 'Contact', value: 'contact' },
          { title: 'RSVP', value: 'rsvp' },
          { title: 'Newsletter', value: 'newsletter' },
          { title: 'Other', value: 'other' },
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'formData',
      title: 'Form Data',
      type: 'object',
      fields: [
        defineField({
          name: 'name',
          title: 'Name',
          type: 'string',
        }),
        defineField({
          name: 'email',
          title: 'Email',
          type: 'string',
        }),
        defineField({
          name: 'phone',
          title: 'Phone',
          type: 'string',
        }),
        defineField({
          name: 'message',
          title: 'Message',
          type: 'text',
        }),
        defineField({
          name: 'additionalFields',
          title: 'Additional Fields',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'key', type: 'string', title: 'Field Name' },
                { name: 'value', type: 'string', title: 'Field Value' },
              ],
            },
          ],
        }),
      ],
    }),
    defineField({
      name: 'submittedAt',
      title: 'Submitted At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    }),
    defineField({
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'New', value: 'new' },
          { title: 'Processed', value: 'processed' },
          { title: 'Archived', value: 'archived' },
        ],
      },
      initialValue: 'new',
    }),
    defineField({
      name: 'ipAddress',
      title: 'IP Address',
      type: 'string',
    }),
    defineField({
      name: 'userAgent',
      title: 'User Agent',
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'formType',
      subtitle: 'formData.name',
      date: 'submittedAt',
    },
    prepare({ title, subtitle, date }) {
      return {
        title: `${title} Form: ${subtitle || 'Unknown'}`,
        subtitle: date ? new Date(date).toLocaleString() : 'Unknown date',
      };
    },
  },
});
