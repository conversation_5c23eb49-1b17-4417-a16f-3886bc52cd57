'use client';

import { useState, useEffect, useRef } from 'react';
import { FileVideo, AlertCircle, Play, Volume2, VolumeX } from 'lucide-react';
import Image from 'next/image';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  title?: string;
  mode?: 'full' | 'preview' | 'thumbnail';  // Context-aware modes
  autoPlay?: boolean;
  controls?: boolean;
  loop?: boolean;
  muted?: boolean;
  className?: string;
  width?: number;
  height?: number;
  onError?: (error: any) => void;
  onLoad?: () => void;
  onClick?: () => void;
}

export default function VideoPlayer({
  src,
  poster,
  title = 'Video',
  mode = 'full',  // Default to full mode
  autoPlay = false,
  controls = true,
  loop = false,
  muted = false,
  className = '',
  width = 1200,
  height = 800,
  onError,
  onLoad,
  onClick
}: VideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isMuted, setIsMuted] = useState(muted);
  const [generatedThumbnail, setGeneratedThumbnail] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const thumbnailVideoRef = useRef<HTMLVideoElement>(null);

  // Effect to generate thumbnail if needed
  useEffect(() => {
    // Only try to generate thumbnail if in thumbnail mode and no poster is provided
    if (mode === 'thumbnail' && !poster && src) {
      console.log('Attempting to generate thumbnail for video:', src);

      // Create a hidden video element to extract the thumbnail
      const generateThumbnail = async () => {
        try {
          // Create a new video element if we don't have one
          const video = thumbnailVideoRef.current || document.createElement('video');

          if (!thumbnailVideoRef.current) {
            console.log('Creating new video element for thumbnail generation');
            video.style.display = 'none';
            document.body.appendChild(video);
          }

          // Set up video properties
          video.muted = true;
          video.playsInline = true;
          video.crossOrigin = 'anonymous';
          video.preload = 'metadata';

          // Set up event listeners before setting src
          const metadataLoaded = new Promise<void>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              console.warn('Metadata loading timed out after 5 seconds');
              reject(new Error('Metadata loading timeout'));
            }, 5000);

            video.onloadedmetadata = () => {
              clearTimeout(timeoutId);
              console.log('Video metadata loaded, duration:', video.duration);
              resolve();
            };

            video.onerror = (e) => {
              clearTimeout(timeoutId);
              console.error('Error loading video for thumbnail:', video.error);
              reject(new Error('Video loading error'));
            };
          });

          // Set the source
          video.src = src;

          try {
            // Wait for metadata to load
            await metadataLoaded;

            // Seek to 25% of the video for thumbnail
            if (video.duration && isFinite(video.duration)) {
              video.currentTime = Math.min(video.duration * 0.25, 3); // Use 25% or max 3 seconds
              console.log('Seeking to time:', video.currentTime);
            } else {
              // If duration is not available, use 1 second
              video.currentTime = 1;
              console.log('Duration not available, seeking to 1 second');
            }

            // Wait for seek to complete
            await new Promise<void>((resolve, reject) => {
              const seekTimeout = setTimeout(() => {
                console.warn('Seek timed out after 5 seconds');
                resolve(); // Continue anyway
              }, 5000);

              video.onseeked = () => {
                clearTimeout(seekTimeout);
                console.log('Seek completed');
                resolve();
              };

              video.onerror = () => {
                clearTimeout(seekTimeout);
                console.error('Error seeking video');
                reject(new Error('Seek error'));
              };
            });

            // Create canvas and draw video frame
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth || 640;
            canvas.height = video.videoHeight || 360;
            const ctx = canvas.getContext('2d');

            if (ctx) {
              // Draw the current frame to canvas
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

              try {
                // Convert to data URL
                const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);
                console.log('Successfully generated thumbnail');
                setGeneratedThumbnail(thumbnailUrl);
              } catch (canvasError) {
                console.error('Canvas toDataURL error:', canvasError);
                // This might be a CORS issue
                console.log('CORS issue detected when generating thumbnail');
              }
            }
          } catch (processingError) {
            console.error('Error during thumbnail processing:', processingError);
          } finally {
            // Clean up
            video.pause();
            video.removeAttribute('src');
            video.load();

            // Remove the video element if we created it
            if (!thumbnailVideoRef.current && document.body.contains(video)) {
              document.body.removeChild(video);
            }
          }
        } catch (error) {
          console.error('Failed to generate thumbnail:', error);
        }
      };

      // Try to generate the thumbnail
      generateThumbnail();
    }
  }, [src, poster, mode]);

  // Effect to handle video loading and cleanup
  useEffect(() => {
    // Reset state when src changes
    setIsLoading(true);
    setError(null);

    // Log the source for debugging
    console.log('VideoPlayer: Loading video from source:', src);

    return () => {
      // Clean up when component unmounts
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.src = '';
        videoRef.current.load();
      }
    };
  }, [src]);

  const handleLoadedData = () => {
    setIsLoading(false);
    console.log('VideoPlayer: Video loaded successfully');
    if (onLoad) onLoad();
  };

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const videoElement = e.currentTarget;
    setIsLoading(false);

    let errorMessage = 'Failed to load video';
    if (videoElement.error) {
      switch (videoElement.error.code) {
        case 1: errorMessage = 'Video loading aborted'; break;
        case 2: errorMessage = 'Network error while loading video'; break;
        case 3: errorMessage = 'Video decoding failed'; break;
        case 4: errorMessage = 'Video format not supported'; break;
      }
    }

    setError(errorMessage);
    if (onError) onError(videoElement.error);
    console.error('VideoPlayer error:', errorMessage, videoElement.error);
  };

  const handleThumbnailClick = () => {
    if (mode === 'thumbnail') {
      if (onClick) {
        onClick();
      } else {
        setIsPlaying(true);
      }
    }
  };

  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(!isMuted);
    }
  };

  const togglePlay = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        setIsPlaying(true);
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  // Thumbnail mode (shows video thumbnail with play button overlay)
  if (mode === 'thumbnail' && !isPlaying) {
    // Create a video thumbnail URL from the source if possible
    // This works for some video hosting services by adding a parameter
    const getVideoThumbnailUrl = (videoSrc: string): string | null => {
      try {
        const url = new URL(videoSrc);

        // YouTube thumbnail
        if (url.hostname.includes('youtube.com') || url.hostname.includes('youtu.be')) {
          const videoId = url.hostname.includes('youtu.be')
            ? url.pathname.slice(1)
            : url.searchParams.get('v');
          if (videoId) {
            return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
          }
        }

        // Vimeo thumbnail - would require an API call, not implemented here

        // For Sanity CDN videos, try to generate a thumbnail URL
        if (url.hostname.includes('cdn.sanity.io') && url.pathname.includes('/files/')) {
          // Extract project ID and dataset from the URL
          // Format: https://cdn.sanity.io/files/{projectId}/{dataset}/{fileId}.{format}
          const pathParts = url.pathname.split('/');
          if (pathParts.length >= 5) {
            const projectId = pathParts[2];
            const dataset = pathParts[3];
            const fileIdWithExt = pathParts[4];

            // Check if this is an MP4 file
            if (fileIdWithExt.endsWith('.mp4')) {
              // Extract the file ID without extension
              const fileId = fileIdWithExt.replace('.mp4', '');

              // Try to construct an image URL with the same ID but as an image
              // This is a guess - it might work if there's a thumbnail image with the same ID
              return `https://cdn.sanity.io/images/${projectId}/${dataset}/${fileId}.jpg`;
            }
          }
        }

        return null;
      } catch (e) {
        return null;
      }
    };

    // Try to get a thumbnail URL from the video source
    const videoThumbnailUrl = getVideoThumbnailUrl(src);

    return (
      <div
        className={`relative bg-black rounded-lg overflow-hidden cursor-pointer ${className}`}
        onClick={handleThumbnailClick}
      >
        {/* Use poster image if provided */}
        {poster ? (
          <Image
            src={poster}
            alt={`${title} thumbnail`}
            width={width || 400}
            height={height || 300}
            className="w-full h-full object-cover"
          />
        ) : generatedThumbnail ? (
          /* Use generated thumbnail if available */
          <Image
            src={generatedThumbnail}
            alt={`${title} thumbnail`}
            width={width || 400}
            height={height || 300}
            className="w-full h-full object-cover"
          />
        ) : videoThumbnailUrl ? (
          /* Use service-specific thumbnail if available */
          <Image
            src={videoThumbnailUrl}
            alt={`${title} thumbnail`}
            width={width || 400}
            height={height || 300}
            className="w-full h-full object-cover"
          />
        ) : (
          /* Fallback to video icon */
          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
            <FileVideo className="h-8 w-8 text-white/70" />
          </div>
        )}

        {/* Semi-transparent overlay with play button */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
          {/* Play button in center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 shadow-lg transform transition-transform hover:scale-110">
              <Play className="h-8 w-8 text-white drop-shadow-lg" />
            </div>
          </div>

          {/* Video title at bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-3">
            <h3 className="text-white font-medium text-sm truncate drop-shadow-md">{title}</h3>
          </div>

          {/* Video badge */}
          <div className="absolute top-2 right-2 bg-red-500/80 text-white text-xs px-2 py-1 rounded-full font-medium">
            Video
          </div>
        </div>
      </div>
    );
  }

  // Preview mode (smaller, simpler controls)
  const previewMode = mode === 'preview';
  const fullMode = mode === 'full';

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
          <div className="animate-pulse flex flex-col items-center">
            <FileVideo className={`${previewMode ? 'h-8 w-8' : 'h-12 w-12'} text-white/70 mb-2`} />
            <p className="text-white text-sm">Loading video...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-10">
          <div className="flex flex-col items-center text-center p-4">
            <AlertCircle className={`${previewMode ? 'h-8 w-8' : 'h-12 w-12'} text-red-500 mb-2`} />
            <p className="text-white font-medium">{error}</p>
            {!previewMode && (
              <p className="text-white/70 text-sm mt-1">Please try again later</p>
            )}
          </div>
        </div>
      )}

      {/* Main video player */}
      <video
        ref={videoRef}
        key={src} // Force re-render when src changes
        className={`w-full h-full ${mode === 'full' ? 'object-contain' : 'object-cover'}`}
        poster={poster || generatedThumbnail || undefined}
        controls={controls && !previewMode}
        autoPlay={autoPlay || (mode === 'thumbnail' && isPlaying)}
        playsInline
        loop={loop}
        muted={isMuted || previewMode} // Mute in preview mode by default
        crossOrigin="anonymous"
        onLoadedData={handleLoadedData}
        onError={handleError}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Hidden video element for thumbnail generation */}
      {mode === 'thumbnail' && !poster && (
        <video
          ref={thumbnailVideoRef}
          className="hidden"
          muted
          playsInline
          crossOrigin="anonymous"
        />
      )}

      {/* Custom controls for preview mode */}
      {previewMode && !error && !isLoading && (
        <div className="absolute bottom-2 right-2 flex space-x-2">
          <button
            onClick={togglePlay}
            className="bg-black/60 hover:bg-black/80 text-white rounded-full p-1.5 transition-colors"
          >
            {isPlaying ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
            ) : (
              <Play size={16} />
            )}
          </button>
          <button
            onClick={toggleMute}
            className="bg-black/60 hover:bg-black/80 text-white rounded-full p-1.5 transition-colors"
          >
            {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
          </button>
        </div>
      )}
    </div>
  );
}
