'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Check, X, Loader2, FileEdit } from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '@sanity/client';
import { apiVersion, dataset, projectId, token } from '@/sanity/env';

interface SimpleOverlayEditorProps {
  sectionType: string;
  sectionId?: string;
  onClose: () => void;
}

export default function SimpleOverlayEditor({
  sectionType,
  sectionId,
  onClose
}: SimpleOverlayEditorProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [isFixingKeys, setIsFixingKeys] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<Record<string, string>>({});

  // Load data from Sanity when the component mounts
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Create a Sanity client
        const client = createClient({
          projectId,
          dataset,
          apiVersion,
          token,
          useCdn: false,
        });

        // Determine which document to fetch based on section type
        let query = '';
        let params = {};

        if (sectionType === 'hero' || sectionType === 'about' || sectionType === 'contact') {
          query = `*[_type == "siteSettings"][0]`;
        } else if (sectionType === 'gallery') {
          query = `*[_type == "gallery"]`;
        } else if (sectionId) {
          query = `*[_id == $id][0]`;
          params = { id: sectionId };
        }

        if (query) {
          const result = await client.fetch(query, params);

          // Extract the relevant section data
          let sectionData = {};
          if (sectionType === 'hero' && result?.hero) {
            sectionData = result.hero;
          } else if (sectionType === 'about' && result?.about) {
            sectionData = result.about;
          } else if (sectionType === 'contact' && result?.contact) {
            sectionData = result.contact;
          } else {
            sectionData = result;
          }

          // Set the form data
          setFormData(sectionData || {});
        }
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [sectionType, sectionId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const fixMissingKeys = async () => {
    setIsFixingKeys(true);
    try {
      // Determine which document type to fix
      let documentToFix = sectionId;
      let typeToFix = '';

      if (sectionType === 'hero' || sectionType === 'about' || sectionType === 'contact') {
        typeToFix = 'siteSettings';
      } else if (sectionType === 'events') {
        typeToFix = 'event';
      } else if (sectionType === 'gallery') {
        typeToFix = 'gallery';
      } else if (sectionType === 'news') {
        typeToFix = 'post';
      }

      // Call the API to fix missing keys
      const response = await fetch('/api/sanity/fix-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: documentToFix,
          documentType: typeToFix,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || 'Fixed missing keys');
      } else {
        throw new Error(data.error || 'Failed to fix missing keys');
      }
    } catch (error) {
      console.error('Error fixing missing keys:', error);
      toast.error('Failed to fix missing keys');
    } finally {
      setIsFixingKeys(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Create a Sanity client
      const client = createClient({
        projectId,
        dataset,
        apiVersion,
        token,
        useCdn: false,
      });

      // Determine which document to update based on section type
      let documentId = '';
      let patchObject = {};

      if (sectionType === 'hero' || sectionType === 'about' || sectionType === 'contact') {
        // Get the site settings document
        const siteSettings = await client.fetch(`*[_type == "siteSettings"][0]`);
        documentId = siteSettings._id;

        // Create the patch object based on section type
        if (sectionType === 'hero') {
          patchObject = {
            hero: {
              ...siteSettings.hero,
              heading: formData.heading || siteSettings.hero?.heading,
              tagline: formData.tagline || siteSettings.hero?.tagline,
            }
          };
        } else if (sectionType === 'about') {
          patchObject = {
            about: {
              ...siteSettings.about,
              kingBio: formData.kingBio || siteSettings.about?.kingBio,
              kingVision: formData.kingVision || siteSettings.about?.kingVision,
              kingMission: formData.kingMission || siteSettings.about?.kingMission,
            }
          };
        } else if (sectionType === 'contact') {
          patchObject = {
            contact: {
              ...siteSettings.contact,
              email: formData.email || siteSettings.contact?.email,
              phone: formData.phone || siteSettings.contact?.phone,
              address: formData.address || siteSettings.contact?.address,
            }
          };
        }
      } else if (sectionId) {
        // For other section types with an ID
        documentId = sectionId;
        patchObject = formData;
      }

      // Update the document
      if (documentId) {
        await client.patch(documentId).set(patchObject).commit();
        toast.success('Changes saved successfully');

        // Reload the page to see the changes
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        throw new Error('Could not determine which document to update');
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setIsSaving(false);
    }
  };

  // Render different form fields based on section type
  const renderFormFields = () => {
    switch (sectionType) {
      case 'hero':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={formData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={formData.tagline || ''}
                onChange={handleInputChange}
                placeholder="Enter tagline"
              />
            </div>
          </>
        );

      case 'about':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="kingBio">King Bio</Label>
              <Textarea
                id="kingBio"
                name="kingBio"
                value={formData.kingBio || ''}
                onChange={handleInputChange}
                placeholder="Enter king bio"
                rows={4}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="kingVision">King Vision</Label>
              <Textarea
                id="kingVision"
                name="kingVision"
                value={formData.kingVision || ''}
                onChange={handleInputChange}
                placeholder="Enter king vision"
                rows={4}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="kingMission">King Mission</Label>
              <Textarea
                id="kingMission"
                name="kingMission"
                value={formData.kingMission || ''}
                onChange={handleInputChange}
                placeholder="Enter king mission"
                rows={4}
              />
            </div>
          </>
        );

      case 'contact':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                value={formData.email || ''}
                onChange={handleInputChange}
                placeholder="Enter email"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleInputChange}
                placeholder="Enter phone"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Textarea
                id="address"
                name="address"
                value={formData.address || ''}
                onChange={handleInputChange}
                placeholder="Enter address"
                rows={3}
              />
            </div>
          </>
        );

      case 'gallery':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title || ''}
                onChange={handleInputChange}
                placeholder="Enter gallery title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleInputChange}
                placeholder="Enter gallery description"
                rows={3}
              />
            </div>
            <div className="p-4 bg-gray-100 rounded-md mt-4">
              <p className="text-sm text-gray-500 mb-2">
                Note: To edit gallery images, please use the Sanity Studio interface.
              </p>
              <Button
                variant="outline"
                onClick={() => window.open('/studio/desk/gallery', '_blank')}
                className="w-full"
                type="button"
              >
                <FileEdit className="h-4 w-4 mr-2" />
                Edit Gallery in Sanity Studio
              </Button>
            </div>
          </>
        );

      default:
        return (
          <div className="space-y-2">
            <p className="text-gray-500">
              This section type doesn't have a custom editor yet. Please use the Sanity Studio for more complex edits.
            </p>
            <Button
              variant="outline"
              onClick={() => window.open('/studio', '_blank')}
              className="w-full"
            >
              Open Sanity Studio
            </Button>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>Edit {sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} Section</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-royalGold mb-4" />
              <p className="text-gray-500">Loading content...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {renderFormFields()}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={fixMissingKeys}
            disabled={isFixingKeys || isSaving || isLoading}
            size="sm"
          >
            {isFixingKeys ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Fixing Keys...
              </>
            ) : (
              <>
                Fix Missing Keys
              </>
            )}
          </Button>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose} disabled={isSaving || isFixingKeys || isLoading}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button variant="default" onClick={handleSave} disabled={isSaving || isFixingKeys || isLoading}>
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
