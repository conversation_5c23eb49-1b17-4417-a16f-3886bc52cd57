import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, getAllUsers } from '@/lib/users';
import bcrypt from 'bcryptjs';

// POST /api/auth/verify-user - Verify user credentials
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { email, password } = body;

    console.log('Verifying user with email/username:', email);

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Check for built-in admin users first
    if (email === '<EMAIL>' && password === 'kingdom2024') {
      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        user: {
          id: '1',
          name: 'Admin',
          email: '<EMAIL>',
          role: 'admin',
          lastLogin: new Date().toISOString()
        }
      });
    }

    if (email === '<EMAIL>' && password === 'kingdom2024super') {
      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        user: {
          id: '2',
          name: 'Super Admin',
          email: '<EMAIL>',
          role: 'super_admin',
          lastLogin: new Date().toISOString()
        }
      });
    }

    // For other users, check the database
    try {
      // Get all users
      const allUsers = getAllUsers();

      // Try to find the user by email
      let user = allUsers.find((u: any) => u.email === email);

      // If not found by email, check if the input might be a username
      if (!user) {
        console.log('User not found by email, checking if it might be a username');
        // Check if any user has this email as username (for backward compatibility)
        user = allUsers.find((u: any) =>
          u.email.split('@')[0].toLowerCase() === email.toLowerCase() ||
          (u.username && u.username.toLowerCase() === email.toLowerCase())
        );
      }

      // If user still not found
      if (!user) {
        console.log('User not found by email or username');
        return NextResponse.json(
          { success: false, message: 'Invalid credentials' },
          { status: 401 }
        );
      }

      console.log('User found:', { id: user.id, email: user.email, name: user.name });

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return NextResponse.json(
          { success: false, message: 'Invalid credentials' },
          { status: 401 }
        );
      }

      // Return user data without password
      const { password: _, ...userWithoutPassword } = user;

      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        user: userWithoutPassword
      });
    } catch (err) {
      console.warn('Error getting user from database:', err);
      return NextResponse.json(
        { success: false, message: 'Invalid credentials' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error verifying user:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred' },
      { status: 500 }
    );
  }
}
