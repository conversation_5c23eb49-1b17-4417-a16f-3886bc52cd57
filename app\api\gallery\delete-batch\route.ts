import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin, isAdmin } from '@/lib/auth-utils';

// POST /api/gallery/delete-batch - Delete multiple gallery items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete gallery items' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { ids } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No IDs provided for deletion' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Delete each gallery item
    const results = [];
    let successCount = 0;
    let failedCount = 0;

    for (const id of ids) {
      try {
        // First, fetch the gallery item to get its images
        const galleryItem = await client.fetch(`*[_type == "gallery" && _id == $id][0]`, { id });
        
        if (!galleryItem) {
          results.push({
            id,
            success: false,
            message: 'Gallery item not found'
          });
          failedCount++;
          continue;
        }
        
        // Delete the gallery item
        await client.delete(id);
        
        // Track the result
        results.push({
          id,
          success: true
        });
        successCount++;
      } catch (error) {
        console.error(`Error deleting gallery item ${id}:`, error);
        results.push({
          id,
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error'
        });
        failedCount++;
      }
    }

    return NextResponse.json({
      success: successCount > 0,
      message: `Deleted ${successCount} out of ${ids.length} gallery items`,
      results,
      deleted: successCount,
      failed: failedCount
    });
  } catch (error) {
    console.error('Error deleting gallery items:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete gallery items',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
