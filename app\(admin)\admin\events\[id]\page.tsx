'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { format } from 'date-fns';
import { toast } from 'sonner';
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  Clock,
  MapPin,
  Edit,
  Trash,
  ArrowLeft,
  Loader2,
  Timer,
  Star,
  Users,
} from 'lucide-react';
import { useEventStore } from '@/lib/stores/eventStore';
import { useEventQuery, useDeleteEventMutation } from '@/lib/hooks/useEvents';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';

interface Event {
  _id: string;
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
}

export default function EventDetailsPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const eventId = unwrappedParams.id;

  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Use React Query to fetch the event data
  const { data: event, isLoading, error } = useEventQuery(eventId);

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      console.error('Failed to fetch event:', error);
      toast.error('Failed to load event details');
    }
  }, [error]);

  // Redirect if event not found after loading is complete
  useEffect(() => {
    if (!isLoading && !event) {
      toast.error('Event not found');
      router.push('/admin/events');
    }
  }, [isLoading, event, router]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMMM d, yyyy h:mm a');
    } catch (error) {
      console.error('Error formatting date:', error, dateString);
      return 'Invalid date';
    }
  };

  // Delete event
  // Use the delete event mutation
  const deleteEventMutation = useDeleteEventMutation();

  const deleteEvent = async () => {
    if (!event) return;

    setIsDeleting(true);

    try {
      console.log(`Attempting to delete event with ID: ${event._id}`);

      // Use the mutation to delete the event
      await deleteEventMutation.mutateAsync(event._id);

      // Force a hard navigation to refresh data
      window.location.href = '/admin/events';
    } catch (error) {
      console.error('Failed to delete event:', error);
      toast.error('Failed to delete event: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2">Loading event details...</span>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6 text-center">
        <h1 className="text-3xl font-bold mb-4">Event Not Found</h1>
        <p className="mb-6">The event you&apos;re looking for doesn&apos;t exist or has been deleted.</p>
        <Button onClick={() => router.push('/admin/events')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Events
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Event"
        description="Are you sure you want to delete this event? This action cannot be undone."
        onConfirm={deleteEvent}
        confirmText="Delete"
        variant="destructive"
      />

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push('/admin/events')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">{event.title}</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push(`/admin/events/${event._id}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Event Details</CardTitle>
          <CardDescription>
            View the details of this event.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6">
            {event.imageUrl ? (
              <div className="relative w-full md:w-1/3 aspect-video rounded-lg overflow-hidden">
                <Image
                  src={event.imageUrl}
                  alt={event.imageAlt || event.title}
                  fill
                  className="object-cover"
                />
              </div>
            ) : (
              <div className="w-full md:w-1/3 aspect-video bg-muted rounded-lg flex items-center justify-center">
                <Calendar className="h-12 w-12 text-muted-foreground" />
              </div>
            )}

            <div className="md:w-2/3 space-y-4">
              <div className="flex flex-wrap gap-2">
                {event.isCountdownTarget && (
                  <Badge variant="secondary">
                    <Timer className="mr-1 h-3 w-3" />
                    Countdown Target
                  </Badge>
                )}
                {event.isHighlighted && (
                  <Badge variant="secondary">
                    <Star className="mr-1 h-3 w-3" />
                    Highlighted
                  </Badge>
                )}
                {event.showRsvp && (
                  <Badge variant="outline">
                    <Users className="mr-1 h-3 w-3" />
                    RSVP Enabled
                  </Badge>
                )}
                {event.eventType && (
                  <Badge variant="outline">
                    {event.eventType.charAt(0).toUpperCase() + event.eventType.slice(1)}
                  </Badge>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span>{formatDate(event.date)}</span>
                </div>

                {event.endDate && (
                  <div className="flex items-center text-muted-foreground">
                    <Clock className="mr-2 h-4 w-4" />
                    <span>Until {formatDate(event.endDate)}</span>
                  </div>
                )}

                <div className="flex items-center text-muted-foreground">
                  <MapPin className="mr-2 h-4 w-4" />
                  <span>{event.location}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-2">Description</h3>
            <p className="whitespace-pre-line">{event.description}</p>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Slug</h3>
              <p>{event.slug.current}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Display Order</h3>
              <p>{event.order}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
