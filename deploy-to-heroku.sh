#!/bin/bash

# Deploy to Heroku script
# This script automates the process of deploying to Heroku

echo "=== Starting deployment to Heroku ==="

# 1. Clean up for Heroku deployment
echo "Step 1: Running clean-for-heroku.js"
node clean-for-heroku.js

# 2. Install production dependencies
echo "Step 2: Installing production dependencies"
npm install --production --legacy-peer-deps

# 3. Push to Heroku
echo "Step 3: Pushing to Heroku"
git push heroku store-functionality:main

echo "=== Deployment complete ==="
echo "Your app should now be available at: https://adukingdom-62986aa3239a.herokuapp.com/"
