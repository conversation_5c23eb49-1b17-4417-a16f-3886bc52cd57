import type { Metadata } from 'next';
import '../global.css';
import AdminLayoutWrapper from './AdminLayoutWrapper';

export const metadata: Metadata = {
  title: 'Kingdom Admin',
  description: 'Admin panel for Kingdom website',
};

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AdminLayoutWrapper>
      {children}
    </AdminLayoutWrapper>
  );
}

