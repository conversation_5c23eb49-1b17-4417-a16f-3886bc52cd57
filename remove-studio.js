// Script to remove Sanity Studio from the build
const fs = require('fs');
const path = require('path');

console.log('Removing Sanity Studio from the build...');

// Function to recursively delete a directory
function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        // Recursive call
        deleteFolderRecursive(curPath);
      } else {
        // Delete file
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

// Paths to remove
const pathsToRemove = [
  './app/studio',
  './sanity',
  './sanity.config.js',
  './sanity.cli.js',
];

// Remove each path
pathsToRemove.forEach((pathToRemove) => {
  const fullPath = path.resolve(pathToRemove);
  console.log(`Removing ${fullPath}...`);
  
  try {
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        deleteFolderRecursive(fullPath);
        console.log(`Removed directory: ${fullPath}`);
      } else {
        fs.unlinkSync(fullPath);
        console.log(`Removed file: ${fullPath}`);
      }
    } else {
      console.log(`Path does not exist: ${fullPath}`);
    }
  } catch (error) {
    console.error(`Error removing ${fullPath}:`, error);
  }
});

console.log('Sanity Studio removal completed!');
