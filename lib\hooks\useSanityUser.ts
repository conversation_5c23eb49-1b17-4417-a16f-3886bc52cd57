'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

// Define the user data type
interface SanityUser {
  _id: string;
  name: string;
  email: string;
  role: string;
  username?: string;
  updatedAt?: string;
}

// Create a custom hook to fetch user data from Sanity
export function useSanityUser() {
  const { data: session } = useSession();
  const [user, setUser] = useState<SanityUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());

  // Function to refresh the user data
  const refreshUser = () => {
    setLastRefresh(Date.now());
  };

  useEffect(() => {
    // Only fetch if we have a session with an email
    if (!session?.user?.email) {
      setLoading(false);
      return;
    }

    const fetchUserData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch user data from our API
        const response = await fetch(`/api/users/get-user?email=${encodeURIComponent(session.user.email)}`, {
          next: { revalidate: 60 }, // Revalidate every 60 seconds
          headers: {
            'Cache-Control': 'max-age=0, s-maxage=60',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const data = await response.json();

        if (data.success && data.user) {
          setUser(data.user);
        } else {
          // If no user found, create a fallback user from session
          setUser({
            _id: session.user.id || 'unknown',
            name: session.user.name || 'Unknown User',
            email: session.user.email,
            role: (session.user as any).role || 'admin',
          });
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));

        // Use session data as fallback
        setUser({
          _id: session.user.id || 'unknown',
          name: session.user.name || 'Unknown User',
          email: session.user.email,
          role: (session.user as any).role || 'admin',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [session, lastRefresh]);

  return { user, loading, error, refreshUser };
}
