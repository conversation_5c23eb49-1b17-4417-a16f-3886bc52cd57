import { NextRequest, NextResponse } from 'next/server';
import { fixMissingKeys, fixMissingKeysForType } from '@/lib/sanity.fixKeys';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if the user is authenticated and has admin role
    const session = await getServerSession(authOptions);
    if (!session || (session.user?.role !== 'admin' && session.user?.role !== 'super_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();
    const { documentId, documentType } = body;

    if (!documentId && !documentType) {
      return NextResponse.json(
        { error: 'Either documentId or documentType is required' },
        { status: 400 }
      );
    }

    // Fix missing keys
    if (documentId) {
      await fixMissingKeys(documentId);
      return NextResponse.json({ success: true, message: `Fixed missing keys in document ${documentId}` });
    } else if (documentType) {
      await fixMissingKeysForType(documentType);
      return NextResponse.json({ success: true, message: `Fixed missing keys in documents of type ${documentType}` });
    }
  } catch (error) {
    console.error('Error fixing missing keys:', error);
    return NextResponse.json(
      { error: 'Failed to fix missing keys', details: (error as Error).message },
      { status: 500 }
    );
  }
}
