/**
 * Centralized Sanity client configuration
 *
 * This module provides different Sanity clients for different use cases:
 * - readClient: For read-only operations, can be used on both client and server
 * - writeClient: For write operations, should only be used on the server
 * - previewClient: For preview mode
 */

import { createClient } from 'next-sanity';
import imageUrlBuilder from '@sanity/image-url';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { env } from './env';
import { logError } from './errorHandling';

// Validate required configuration
if (!env.NEXT_PUBLIC_SANITY_PROJECT_ID || !env.NEXT_PUBLIC_SANITY_DATASET) {
  throw new Error('Missing required Sanity configuration. Please check your environment variables.');
}

// Default configuration
const config = {
  projectId: env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: '2025-05-09', // Updated to match Sanity Studio version (removed 'v' prefix)
  useCdn: false, // Disable CDN to ensure fresh data
  perspective: 'published',
  token: env.NEXT_PUBLIC_SANITY_API_TOKEN, // Add token for authentication
};

// Client for read operations (can be used on client-side)
export const readClient = createClient(config);

// Client for write operations (should only be used on server-side)
export const getWriteClient = () => {
  // This should only be called server-side
  if (typeof window !== 'undefined') {
    console.warn('Warning: getWriteClient() should only be called server-side');
  }

  // Use the hardcoded token directly
  // This is a workaround for environments where process.env is not properly loaded
  const hardcodedToken = "skZvPBz8yZn4iRP2UemnqcRDAx7bEknpdNoUhwSpL0mkffSi7B5n83PBrPUVvxww6QgsXbFDETUDESuxJmmb0l51Hzl3NNIC6YdJ3C9lEKii2Ydn3yQN4pu7se2ZwayyOSKzsjYJLp295ypq1xvM9fidravifu92uhZAx7sjYXKcfCAyGGrb";

  // First try to get the token from environment variables
  let token = process.env.SANITY_API_TOKEN;

  // If token is not available from environment, use the hardcoded token
  if (!token) {
    console.warn('SANITY_API_TOKEN not found in environment variables, using hardcoded token');
    token = hardcodedToken;
  }

  try {
    return createClient({
      ...config,
      useCdn: false, // Never use CDN for write operations
      token,
      perspective: 'raw', // Use raw perspective for write operations
    });
  } catch (error) {
    logError(error, 'getWriteClient');
    throw new Error('Failed to initialize Sanity write client: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
};

// Client for preview mode
export const getPreviewClient = (token: string) => {
  if (!token) {
    throw new Error('Preview client requires a token');
  }

  return createClient({
    ...config,
    useCdn: false,
    token,
    perspective: 'previewDrafts', // Use preview perspective
  });
};

// Set up image URL builder
const builder = imageUrlBuilder(readClient);

// Helper function to generate image URLs
export function urlFor(source: SanityImageSource) {
  if (!source) {
    return {
      url: () => '', // Return empty string instead of fallback image
    };
  }

  try {
    // Log the source for debugging
    console.log('Image source:', JSON.stringify(source, null, 2));

    // Handle case where source is already a string URL
    if (typeof source === 'string') {
      return {
        url: () => source,
      };
    }

    // Handle case where source has a direct URL property
    if (source && typeof source === 'object' && 'url' in source && typeof source.url === 'string') {
      return {
        url: () => source.url,
      };
    }

    // Check if source has asset._ref but no asset._id (common issue)
    if (source && typeof source === 'object' && 'asset' in source &&
        typeof source.asset === 'object' && source.asset &&
        '_ref' in source.asset) {

      // Extract the image ID from the _ref
      const ref = source.asset._ref;

      // If it's already a valid Sanity image URL, use it directly
      if (typeof ref === 'string' && ref.startsWith('image-')) {
        // Format: image-{imageId}-{dimensions}-{format}
        // Example: image-3cdbf6604cd9d73a4ebdc674265c12cc720a059a-453x680-png
        const parts = ref.split('-');
        if (parts.length >= 4) {
          const imageId = parts[1];
          const dimensions = parts[2];
          const format = parts[3];

          // Construct a direct URL to the Sanity CDN
          const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID;
          const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production';

          return {
            url: () => `https://cdn.sanity.io/images/${projectId}/${dataset}/${imageId}-${dimensions}.${format}`
          };
        }
      }

      // Copy _ref to _id to make it work with the image builder
      const fixedSource = {
        ...source,
        asset: {
          ...source.asset,
          _id: source.asset._ref
        }
      };

      return builder.image(fixedSource);
    }

    return builder.image(source);
  } catch (error) {
    console.error('Error in urlFor:', error);
    logError(error, 'urlFor');
    return {
      url: () => '', // Return empty string instead of fallback image
    };
  }
}

// Function to get the URL for a file (video, document, etc.)
export function fileUrlFor(source: any): string {
  if (!source) {
    console.warn('fileUrlFor called with empty source');
    return '';
  }

  try {
    // Handle case where source is already a string URL
    if (typeof source === 'string') {
      console.log('fileUrlFor: Source is already a string URL:', source);
      return source;
    }

    // Log the source for debugging
    console.log('fileUrlFor source:', JSON.stringify(source, null, 2));

    // Check if source has asset._ref
    if (source && typeof source === 'object' && 'asset' in source) {
      // Handle case where asset is a string (direct reference)
      if (typeof source.asset === 'string' && source.asset.startsWith('file-')) {
        const parts = source.asset.split('-');
        if (parts.length >= 3) {
          const fileId = parts[1];
          const format = parts[2];
          const projectId = env.NEXT_PUBLIC_SANITY_PROJECT_ID;
          const dataset = env.NEXT_PUBLIC_SANITY_DATASET;
          const url = `https://cdn.sanity.io/files/${projectId}/${dataset}/${fileId}.${format}`;
          console.log('fileUrlFor: Generated URL from string asset:', url);
          return url;
        }
      }

      // Handle case where asset is an object with _ref
      if (typeof source.asset === 'object' && source.asset && '_ref' in source.asset) {
        // Extract the file ID from the _ref
        const ref = source.asset._ref;

        // If it's a valid Sanity file reference
        if (typeof ref === 'string' && ref.startsWith('file-')) {
          // Format: file-{fileId}-{format}
          // Example: file-a1b2c3d4e5f6-mp4
          const parts = ref.split('-');
          if (parts.length >= 3) {
            const fileId = parts[1];
            const format = parts[2];

            // Construct a direct URL to the Sanity CDN
            const projectId = env.NEXT_PUBLIC_SANITY_PROJECT_ID;
            const dataset = env.NEXT_PUBLIC_SANITY_DATASET;

            const url = `https://cdn.sanity.io/files/${projectId}/${dataset}/${fileId}.${format}`;
            console.log('fileUrlFor: Generated URL from _ref:', url);
            return url;
          }
        }
      }
    }

    // Handle case where source has a direct URL property
    if (source && typeof source === 'object' && 'url' in source && typeof source.url === 'string') {
      console.log('fileUrlFor: Using direct URL property:', source.url);
      return source.url;
    }

    console.warn('Invalid file source:', source);
    return '';
  } catch (error) {
    console.error('Error in fileUrlFor:', error);
    logError(error, 'fileUrlFor');
    return '';
  }
}

// Function to check if a file is a video
export function isVideoFile(source: any): boolean {
  if (!source) {
    return false;
  }

  try {
    // Handle case where source is a string URL
    if (typeof source === 'string') {
      return source.endsWith('.mp4');
    }

    // Check if source has asset._ref
    if (source && typeof source === 'object' && 'asset' in source &&
        typeof source.asset === 'object' && source.asset &&
        '_ref' in source.asset) {

      const ref = source.asset._ref;
      return typeof ref === 'string' && ref.startsWith('file-') && ref.endsWith('-mp4');
    }

    return false;
  } catch (error) {
    console.error('Error in isVideoFile:', error);
    logError(error, 'isVideoFile');
    return false;
  }
}

// Helper function to safely execute Sanity queries with error handling
export async function sanityFetch<T>(
  query: string,
  params = {},
  options: {
    cache?: RequestCache,
    next?: { revalidate?: number | false },
    fallback?: T,
    perspective?: 'published' | 'previewDrafts' | 'raw',
    timeout?: number
  } = {}
): Promise<T> {
  try {
    // Always use the read client with token for authentication
    const client = readClient;

    // Create a timeout promise
    const timeout = options.timeout || 10000; // Increase timeout to 10 seconds
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Sanity query timeout after ${timeout}ms`)), timeout);
    });

    // Create the fetch promise with direct fetch to Sanity API
    const fetchPromise = client.fetch<T>(query, params, {
      ...options,
      perspective: options.perspective || 'published',
      useCdn: false, // Always bypass CDN
      next: {
        revalidate: options.next?.revalidate !== undefined ? options.next.revalidate : 60
      }
    });

    // Race the promises
    const result = await Promise.race([fetchPromise, timeoutPromise]);

    // Log success for debugging
    console.log(`Sanity query successful: ${query.substring(0, 50)}...`);

    return result;
  } catch (error) {
    console.error(`Sanity query error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    logError(error, `sanityFetch: ${query.substring(0, 50)}...`);

    // Try a direct fetch as a fallback if the client fetch fails
    if (!options.fallback) {
      try {
        console.log(`Attempting direct fetch to Sanity API for: ${query.substring(0, 50)}...`);

        // Construct a direct fetch URL
        const apiUrl = `https://${env.NEXT_PUBLIC_SANITY_PROJECT_ID}.api.sanity.io/v2025-05-09/data/query/${env.NEXT_PUBLIC_SANITY_DATASET}?query=${encodeURIComponent(query)}`;

        const headers = {
          'Authorization': `Bearer ${env.NEXT_PUBLIC_SANITY_API_TOKEN}`,
          'Content-Type': 'application/json',
        };

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers,
          next: { revalidate: 60 } // Revalidate every 60 seconds instead of no-store
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`Direct fetch successful: ${query.substring(0, 50)}...`);
          return data.result as T;
        }
      } catch (directError) {
        console.error(`Direct fetch failed: ${directError instanceof Error ? directError.message : 'Unknown error'}`);
      }
    }

    if (options.fallback !== undefined) {
      console.log(`Using fallback data for query: ${query.substring(0, 50)}...`);
      return options.fallback;
    }

    throw error;
  }
}
