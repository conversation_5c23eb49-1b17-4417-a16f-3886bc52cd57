// Deployment script for both GitHub and Heroku
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Function to execute shell commands and log output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Function to get user input
function getUserInput(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer);
    });
  });
}

// Main deployment process
async function deploy() {
  console.log('Starting deployment process...');

  // Check if GitHub remote exists
  console.log('Checking GitHub remote...');
  try {
    const remotes = execSync('git remote -v', { encoding: 'utf8' });
    if (!remotes.includes('github')) {
      console.log('GitHub remote not found. Adding it now...');
      runCommand('git remote add github https://github.com/joelgriiyo/kingdomadukrom.git');
    }
  } catch (error) {
    console.error('Error checking git remotes:', error);
    process.exit(1);
  }

  // Push to GitHub
  console.log('Pushing to GitHub repository...');
  try {
    // Ask which branch to push
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    const branch = await getUserInput(`Which branch do you want to push to GitHub? (default: ${currentBranch}): `) || currentBranch;

    // Push to GitHub
    runCommand(`git push github ${branch}:main`);
    console.log('Successfully pushed to GitHub!');
  } catch (error) {
    console.error('Error pushing to GitHub:', error);
    const continueDeployment = await getUserInput('Continue with Heroku deployment? (y/n): ');
    if (continueDeployment.toLowerCase() !== 'y') {
      process.exit(1);
    }
  }

  // Heroku Deployment
  console.log('Starting Heroku deployment process...');

  // Check if we're logged in to Heroku
  console.log('Checking Heroku login status...');
  try {
    execSync('heroku whoami', { stdio: 'pipe' });
    console.log('Already logged in to Heroku');
  } catch (error) {
    console.log('Not logged in to Heroku. Please log in:');
    runCommand('heroku login');
  }

  // Check if the Heroku app exists
  console.log('Checking if Heroku app exists...');
  try {
    execSync('heroku apps:info kingdomadukrom', { stdio: 'pipe' });
    console.log('Heroku app exists');
  } catch (error) {
    console.log('Creating Heroku app...');
    runCommand('heroku create kingdomadukrom');
  }

  // Set Heroku buildpacks
  console.log('Setting Heroku buildpacks...');
  runCommand('heroku buildpacks:clear -a kingdomadukrom');
  runCommand('heroku buildpacks:set heroku/nodejs -a kingdomadukrom');

  // Set Heroku config variables
  console.log('Setting Heroku config variables...');
  runCommand('heroku config:set NODE_ENV=production -a kingdomadukrom');
  runCommand('heroku config:set NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt -a kingdomadukrom');
  runCommand('heroku config:set NEXT_PUBLIC_SANITY_DATASET=production -a kingdomadukrom');
  runCommand('heroku config:set NEXT_PUBLIC_SANITY_API_VERSION=2025-05-09 -a kingdomadukrom');

  // Deploy to Heroku
  console.log('Deploying to Heroku...');
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  runCommand(`git push https://git.heroku.com/kingdomadukrom.git ${currentBranch}:main -f`);

  // Open the app in the browser
  console.log('Opening app in browser...');
  runCommand('heroku open -a kingdomadukrom');

  console.log('Deployment process completed!');
}

// Run the deployment process
deploy();
