// This script ensures that super admin users always exist in the system
// Run with: node scripts/ensure-super-admins.js

const { createClient } = require('@sanity/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Define the super admin users that should always exist
// These users will be created if they don't exist, and will never be deleted
const PROTECTED_SUPER_ADMINS = [
  {
    _id: 'super-admin-1',
    username: 'admin1',
    name: 'Super Admin 1',
    email: '<EMAIL>',
    role: 'super_admin',
    password: 'Admin123!', // This will be hashed before storing
    isActive: true
  },
  {
    _id: 'super-admin-2',
    username: 'admin2',
    name: 'Super Admin 2',
    email: '<EMAIL>',
    role: 'super_admin',
    password: 'Admin123!', // This will be hashed before storing
    isActive: true
  },
  {
    _id: 'user-1747416453821-ex9fhql',
    username: 'joel-rodriguz',
    name: 'Joel Rodriguez',
    email: '<EMAIL>', // Replace with actual email
    role: 'super_admin',
    password: 'YourSecurePassword123!', // Replace with actual password
    isActive: true
  },
  {
    _id: 'user-1747416491468-m19tdcq',
    username: 'eliusumersmom',
    name: 'Elius Umers Mom',
    email: '<EMAIL>',
    role: 'super_admin',
    password: 'Sum34l331!',
    isActive: true
  }
];

// Function to ensure a super admin user exists
async function ensureSuperAdmin(superAdmin) {
  try {
    console.log(`Checking if super admin ${superAdmin.username} exists...`);

    // Check if the user already exists
    const existingUser = await client.fetch(
      `*[_type == "adminUser" && (_id == $id || email == $email)][0]`,
      {
        id: superAdmin._id,
        email: superAdmin.email
      }
    );

    if (existingUser) {
      console.log(`Super admin ${superAdmin.username} already exists with ID: ${existingUser._id}`);

      // Ensure the user has super_admin role and is active
      if (existingUser.role !== 'super_admin' || !existingUser.isActive) {
        console.log(`Updating ${superAdmin.username} to ensure super_admin role and active status`);

        await client
          .patch(existingUser._id)
          .set({
            role: 'super_admin',
            isActive: true,
            updatedAt: new Date().toISOString()
          })
          .commit();

        console.log(`Updated ${superAdmin.username} to super_admin role and active status`);
      }

      return existingUser;
    }

    // User doesn't exist, create it
    console.log(`Creating super admin ${superAdmin.username}...`);

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(superAdmin.password, salt);

    // Create the user document
    const newUser = {
      _type: 'adminUser',
      _id: superAdmin._id,
      username: superAdmin.username,
      name: superAdmin.name,
      email: superAdmin.email,
      role: 'super_admin',
      hashedPassword,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    // Create the user in Sanity
    const createdUser = await client.createOrReplace(newUser);
    console.log(`Created super admin ${superAdmin.username} with ID: ${createdUser._id}`);

    return createdUser;
  } catch (error) {
    console.error(`Error ensuring super admin ${superAdmin.username}:`, error);
    return null;
  }
}

// Function to add protection to super admin users
async function addProtectionToSuperAdmins() {
  try {
    console.log('Adding protection to super admin users...');

    // Get all super admin users
    const superAdmins = await client.fetch(
      `*[_type == "adminUser" && role == "super_admin"]{
        _id,
        username,
        email,
        isProtected
      }`
    );

    console.log(`Found ${superAdmins.length} super admin users`);

    // Add protection flag to each super admin
    for (const admin of superAdmins) {
      if (!admin.isProtected) {
        console.log(`Adding protection to ${admin.username} (${admin._id})`);

        await client
          .patch(admin._id)
          .set({
            isProtected: true,
            updatedAt: new Date().toISOString()
          })
          .commit();

        console.log(`Added protection to ${admin.username}`);
      } else {
        console.log(`${admin.username} is already protected`);
      }
    }
  } catch (error) {
    console.error('Error adding protection to super admin users:', error);
  }
}

// Function to create a listener for user deletion
async function createUserDeletionListener() {
  try {
    console.log('Creating user deletion listener...');

    // This is a conceptual example - in a real implementation, you would use Sanity's
    // webhooks or listeners to prevent deletion of protected users

    // For now, we'll just log a message
    console.log('NOTE: To fully protect super admin users from deletion, you should:');
    console.log('1. Add a validation rule in your Sanity schema');
    console.log('2. Add a check in your API routes that handle user deletion');
    console.log('3. Consider using Sanity webhooks for additional protection');
  } catch (error) {
    console.error('Error creating user deletion listener:', error);
  }
}

// Main function
async function main() {
  console.log('Starting super admin protection...');

  // Ensure all protected super admins exist
  for (const superAdmin of PROTECTED_SUPER_ADMINS) {
    await ensureSuperAdmin(superAdmin);
  }

  // Add protection to all super admin users
  await addProtectionToSuperAdmins();

  // Create a listener for user deletion
  await createUserDeletionListener();

  console.log('\nSuper admin protection complete!');
  console.log('\nIMPORTANT: Update the script with your actual super admin details before running in production.');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
