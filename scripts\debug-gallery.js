// This script debugs gallery images in Sanity
// Run with: node scripts/debug-gallery.js

const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Function to fetch all gallery items
async function fetchGalleryItems() {
  try {
    console.log('Fetching gallery items from Sanity...');
    
    const query = `
      *[_type == "gallery"] {
        _id,
        title,
        slug,
        description,
        images,
        category->{
          _id,
          title,
          slug
        },
        publishedAt,
        featured,
        order
      }
    `;
    
    const galleryItems = await client.fetch(query);
    
    console.log(`Found ${galleryItems.length} gallery items`);
    
    // Check each gallery item for images
    galleryItems.forEach((item, index) => {
      console.log(`\nItem ${index + 1}: ${item.title} (${item._id})`);
      console.log(`Category: ${item.category ? item.category.title : 'None'}`);
      
      if (!item.images || item.images.length === 0) {
        console.log('WARNING: No images found for this item');
      } else {
        console.log(`Images: ${item.images.length}`);
        
        // Check each image
        item.images.forEach((imageObj, imgIndex) => {
          if (!imageObj.image || !imageObj.image.asset) {
            console.log(`  Image ${imgIndex + 1}: INVALID - Missing image or asset reference`);
          } else {
            console.log(`  Image ${imgIndex + 1}: Valid - Asset ID: ${imageObj.image.asset._ref}`);
          }
        });
      }
    });
    
    return galleryItems;
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return [];
  }
}

// Function to create a sample gallery item with image
async function createSampleGalleryItem() {
  try {
    console.log('\nCreating a sample gallery item...');
    
    // First, check if we have any categories
    const categories = await client.fetch(`*[_type == "category"][0...1]`);
    
    if (categories.length === 0) {
      console.log('No categories found. Creating a sample category first...');
      
      // Create a sample category
      const categoryDoc = {
        _type: 'category',
        title: 'Sample Category',
        slug: { _type: 'slug', current: 'sample-category' },
        description: 'A sample category for testing',
        color: '#002366',
        order: 0
      };
      
      const category = await client.create(categoryDoc);
      console.log(`Created sample category: ${category.title} (${category._id})`);
      
      // Use this category for the gallery item
      const galleryDoc = {
        _type: 'gallery',
        title: 'Sample Gallery Item',
        slug: { _type: 'slug', current: 'sample-gallery-item' },
        description: 'A sample gallery item for testing',
        images: [
          {
            _type: 'object',
            image: {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: 'image-6a9fbc0d9c8e8b9f9c8e8b9f9c8e8b9f9c8e8b9f-1200x800-jpg'
              }
            },
            alt: 'Sample Image',
            caption: 'This is a sample image'
          }
        ],
        category: {
          _type: 'reference',
          _ref: category._id
        },
        publishedAt: new Date().toISOString(),
        featured: false,
        order: 0
      };
      
      const galleryItem = await client.create(galleryDoc);
      console.log(`Created sample gallery item: ${galleryItem.title} (${galleryItem._id})`);
    } else {
      console.log(`Found existing category: ${categories[0].title} (${categories[0]._id})`);
      
      // Use the existing category for the gallery item
      const galleryDoc = {
        _type: 'gallery',
        title: 'Sample Gallery Item',
        slug: { _type: 'slug', current: 'sample-gallery-item-' + Date.now() },
        description: 'A sample gallery item for testing',
        images: [
          {
            _type: 'object',
            image: {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: 'image-6a9fbc0d9c8e8b9f9c8e8b9f9c8e8b9f9c8e8b9f-1200x800-jpg'
              }
            },
            alt: 'Sample Image',
            caption: 'This is a sample image'
          }
        ],
        category: {
          _type: 'reference',
          _ref: categories[0]._id
        },
        publishedAt: new Date().toISOString(),
        featured: false,
        order: 0
      };
      
      try {
        const galleryItem = await client.create(galleryDoc);
        console.log(`Created sample gallery item: ${galleryItem.title} (${galleryItem._id})`);
      } catch (error) {
        console.error('Error creating sample gallery item:', error);
      }
    }
  } catch (error) {
    console.error('Error creating sample gallery item:', error);
  }
}

// Main function
async function main() {
  console.log('Starting gallery debugging...');
  
  // Fetch all gallery items
  const galleryItems = await fetchGalleryItems();
  
  // If no gallery items found, create a sample one
  if (galleryItems.length === 0) {
    await createSampleGalleryItem();
    
    // Fetch again to verify
    await fetchGalleryItems();
  }
  
  console.log('\nDebugging complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
