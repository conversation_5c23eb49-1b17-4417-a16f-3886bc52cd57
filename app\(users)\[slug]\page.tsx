import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getPageBySlug } from '@/lib/sanity';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import PageBuilder from '@/components/PageBuilder';

interface PageData {
  _id: string;
  title: string;
  slug: { current: string };
  description?: string;
  pageBuilder?: any[];
  openGraphImage?: string;
  accessLevel?: string;
}

type Props = {
  params: {
    slug: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const page = await getPageBySlug(params.slug) as PageData | null;

  if (!page) {
    return {
      title: 'Page Not Found',
      description: 'The page you are looking for does not exist.',
    };
  }

  return {
    title: `${page.title} | Kingdom of Adukrom`,
    description: page.description || '',
    openGraph: page.openGraphImage ? {
      images: [{ url: page.openGraphImage, alt: page.title }],
    } : undefined,
  };
}

export default async function DynamicPage({ params }: Props) {
  const page = await getPageBySlug(params.slug) as PageData | null;

  if (!page) {
    notFound();
  }

  return (
    <>
      <Header />
      <main>
        {page.pageBuilder && <PageBuilder sections={page.pageBuilder} pageId={page._id} />}
      </main>
      <Footer />
    </>
  );
}
