'use client';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import ParallaxBackground from './ParallaxBackground';
import CountdownTimer from './CountdownTimer';
import { getCountdownTargetEvent } from '@/lib/sanity';
import { Event } from '@/lib/types/sanity';
import { USE_LOCAL_DATA, DEFAULT_CORONATION_TITLE, DEFAULT_CORONATION_DATE } from '@/lib/config';


export default function Hero() {
  const [eventTitle, setEventTitle] = useState('Royal Coronation');
  const [eventDate, setEventDate] = useState<string>('');

  useEffect(() => {
    // Set default values immediately to ensure something is displayed
    setEventTitle(DEFAULT_CORONATION_TITLE);
    try {
      const date = new Date(DEFAULT_CORONATION_DATE);
      setEventDate(date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }));
    } catch (error) {
      console.error('Hero: Error formatting default date:', error);
      setEventDate('August 29, 2025');
    }

    // Only run the fetch on the client side
    if (typeof window === 'undefined') return;

    const fetchEvent = async () => {
      // If using local data only, skip the API call
      if (USE_LOCAL_DATA) {
        console.log('Hero: Using local data (Sanity API disabled)');
        return;
      }

      try {
        console.log('Hero: Attempting to fetch countdown target event...');

        // Use a timeout to prevent hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 5000);
        });

        // Try to fetch the event with a timeout
        const eventPromise = getCountdownTargetEvent();
        const event = await Promise.race([eventPromise, timeoutPromise]) as Event | null;

        if (event && event.title) {
          console.log('Hero: Successfully fetched event:', event.title);
          setEventTitle(event.title);
          if (event.date) {
            try {
              const date = new Date(event.date);
              setEventDate(date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }));
            } catch (dateError) {
              console.error('Hero: Error formatting event date:', dateError);
              // Keep the default date that was already set
            }
          }
        } else {
          console.log('Hero: No event returned or missing title, using default values');
          // Default values already set above
        }
      } catch (error) {
        console.error('Hero: Error fetching event:', error);
        // Default values already set above
      }
    };

    fetchEvent();
  }, []);

  // No state needed for editing anymore

  return (
    <section id="home" className="min-h-screen flex items-center relative overflow-hidden">
      <ParallaxBackground />

      <div className="container mx-auto px-4 py-20 text-center relative z-30">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.h1
            className="text-4xl md:text-6xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Welcome to Adukrom Kingdom
          </motion.h1>
          <motion.p
            className="text-xl md:text-2xl text-royalGold font-semibold mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            The Rise of a New Era
          </motion.p>
          {eventDate && (
            <motion.p
              className="text-lg md:text-xl text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
            >
              <span className="bg-royalGold/20 px-4 py-1 rounded-full text-royalGold font-semibold">
                {eventTitle} • {eventDate}
              </span>
            </motion.p>
          )}
          <motion.p
            className="text-lg text-white mb-12 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            Join us in celebrating a new era of prosperity, heritage, and visionary leadership for our
            people and the global community.
          </motion.p>
          <motion.div
            className="flex flex-col md:flex-row justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="#coronation"
                className="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg inline-block"
              >
                {eventTitle} Details
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="#rsvp"
                className="royal-button bg-transparent text-white border-2 border-royalGold py-3 px-8 rounded-full hover:bg-royalGold/20 transition-colors inline-block"
              >
                RSVP Now
              </Link>
            </motion.div>

          </motion.div>
        </motion.div>

        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <CountdownTimer
            key="hero-countdown"
            size="lg"
            showTitle={true}
            showLabels={true}
            textColor="text-white"
            numberColor="text-royalGold"
          />
        </motion.div>
      </div>
    </section>
  );
}
