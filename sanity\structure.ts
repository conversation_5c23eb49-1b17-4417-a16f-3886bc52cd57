import type {StructureResolver} from 'sanity/structure'
import {DocumentIcon, DocumentPdfIcon, ImageIcon, CalendarIcon, EditIcon, TagIcon, UserIcon, CogIcon, LinkIcon} from '@sanity/icons'

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items([
      // Content types
      // Pages - using a more direct approach to avoid schema errors
      S.listItem()
        .title('Pages')
        .icon(DocumentIcon)
        .child(
          S.documentList()
            .title('Pages')
            .filter('_type == "page"')
            .defaultOrdering([{field: 'title', direction: 'asc'}])
        ),

      S.listItem()
        .title('News')
        .icon(DocumentPdfIcon)
        .child(
          S.documentTypeList('news')
            .title('News Articles')
        ),

      S.listItem()
        .title('Gallery')
        .icon(ImageIcon)
        .child(
          S.documentTypeList('gallery')
            .title('Gallery Items')
        ),

      S.listItem()
        .title('Events')
        .icon(CalendarIcon)
        .child(
          S.documentTypeList('event')
            .title('Events')
        ),

      S.listItem()
        .title('Strategic Partners')
        .icon(LinkIcon)
        .child(
          S.documentTypeList('strategicPartner')
            .title('Strategic Partners')
        ),

      S.listItem()
        .title('RSVP Submissions')
        .icon(EditIcon)
        .child(
          S.documentTypeList('rsvp')
            .title('RSVP Submissions')
        ),

      // Taxonomy
      S.divider(),

      S.listItem()
        .title('Categories')
        .icon(TagIcon)
        .child(
          S.documentTypeList('category')
            .title('Categories')
        ),

      S.listItem()
        .title('Authors')
        .icon(UserIcon)
        .child(
          S.documentTypeList('author')
            .title('Authors')
        ),

      // Settings
      S.divider(),

      S.listItem()
        .title('Site Settings')
        .icon(CogIcon)
        .child(
          S.editor()
            .id('siteSettings')
            .schemaType('siteSettings')
            .documentId('siteSettings')
        )
    ])
