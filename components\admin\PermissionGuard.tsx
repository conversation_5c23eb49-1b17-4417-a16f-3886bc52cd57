'use client';

import { ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { AdminRole } from '@/lib/types/admin';
import { Shield, AlertCircle } from 'lucide-react';

interface PermissionGuardProps {
  /**
   * The children to render if the user has permission
   */
  children: ReactNode;

  /**
   * The permission required to access this component
   */
  requiredPermission: keyof (typeof import('../../lib/types/admin').ROLE_PERMISSIONS)[AdminRole];

  /**
   * Optional fallback component to render if the user doesn't have permission
   */
  fallback?: ReactNode;

  /**
   * Whether to show a message if the user doesn't have permission
   */
  showMessage?: boolean;
}

/**
 * A component that only renders its children if the user has the required permission
 */
export function PermissionGuard({
  children,
  requiredPermission,
  fallback,
  showMessage = true,
}: PermissionGuardProps) {
  const { data: session } = useSession();

  // If no session, don't render anything
  if (!session?.user) {
    return null;
  }

  const userRole = session.user.role as AdminRole;

  // Define permissions for each role
  const rolePermissions = {
    [AdminRole.ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: false,
      canManageUsers: false,
      canManageStore: false,
      canManageBankingInfo: false,
    },
    [AdminRole.SUPER_ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: true,
      canManageUsers: true,
      canManageStore: true,
      canManageBankingInfo: true,
    }
  };

  // Check if user has permission
  const hasPermission = rolePermissions[userRole]?.[requiredPermission] === true;

  if (hasPermission) {
    return <div>{children}</div>;
  }

  // If user doesn't have permission, show fallback or message
  if (fallback) {
    return <>{fallback}</>;
  }

  if (showMessage) {
    return (
      <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800">
        <div className="flex items-center">
          <Shield className="mr-2 h-5 w-5 text-yellow-600" />
          <h3 className="font-medium">Permission Required</h3>
        </div>
        <p className="mt-2 text-sm">
          You don't have permission to access this feature. Please contact a super administrator if you need access.
        </p>
      </div>
    );
  }

  return null;
}

/**
 * A component that only renders its children if the user is a super admin
 */
export function SuperAdminGuard({
  children,
  fallback,
  showMessage = true,
}: Omit<PermissionGuardProps, 'requiredPermission'>) {
  const { data: session, status } = useSession();

  // During loading, return a div with suppressHydrationWarning
  if (status === 'loading') {
    return <div suppressHydrationWarning>{null}</div>;
  }

  const isSuperAdmin = session?.user?.role === AdminRole.SUPER_ADMIN;

  if (isSuperAdmin) {
    return <div suppressHydrationWarning>{children}</div>;
  }

  if (fallback) {
    return <div suppressHydrationWarning>{fallback}</div>;
  }

  if (showMessage) {
    return (
      <div suppressHydrationWarning className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800">
        <div className="flex items-center">
          <AlertCircle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
      </div>
    );
  }

  return null;
}
