import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@sanity/client';

interface EventData {
  title: string;
  slug?: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  isCountdownTarget?: boolean;
  isHighlighted?: boolean;
  showRsvp?: boolean;
  eventType?: string;
  order?: number;
}

// Create a Sanity client for server-side operations
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Server-side token, not exposed to client
  apiVersion: '2025-05-09',
  useCdn: false,
});

// DELETE handler for /api/events/[id]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('DELETE API route called with params:', params);

    if (!params.id) {
      console.error('No event ID provided');
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Validate token exists
    if (!process.env.SANITY_API_TOKEN) {
      console.error('SANITY_API_TOKEN is missing');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    console.log('SANITY_API_TOKEN exists:', !!process.env.SANITY_API_TOKEN);
    console.log('Token length:', process.env.SANITY_API_TOKEN?.length);
    console.log('Sanity project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt');
    console.log('Sanity dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET || 'production');

    console.log(`Attempting to delete event with ID: ${params.id}`);

    try {
      // Delete the document from Sanity
      const result = await client.delete(params.id);
      console.log('Sanity delete result:', result);

      if (!result) {
        console.error('No result from Sanity delete operation');
        return NextResponse.json(
          { error: 'Failed to delete event' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Event deleted successfully',
        result: result
      });
    } catch (sanityError) {
      console.error('Sanity delete operation error:', sanityError);
      return NextResponse.json(
        {
          error: 'Sanity delete operation failed',
          details: sanityError instanceof Error ? sanityError.message : 'Unknown Sanity error',
          stack: sanityError instanceof Error ? sanityError.stack : undefined
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in DELETE handler:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete event',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// PATCH handler for /api/events/[id]
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!params.id) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Validate token exists
    if (!process.env.SANITY_API_TOKEN) {
      console.error('SANITY_API_TOKEN is missing');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Parse the request body
    const eventData: EventData = await req.json();

    // Validate required fields
    if (!eventData.title || !eventData.date || !eventData.location) {
      return NextResponse.json(
        { error: 'Missing required fields: title, date, and location are required' },
        { status: 400 }
      );
    }

    console.log(`Attempting to update event with ID: ${params.id}`);

    // Update the document in Sanity
    const result = await client.patch(params.id)
      .set(eventData)
      .commit();

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to update event' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Event updated successfully',
      event: result
    });
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json(
      { error: 'Failed to update event', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
