'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { urlFor } from '@/lib/sanity';
import { fileUrlFor } from '@/lib/sanity.client';
import VideoPlayer from '@/components/VideoPlayer';
import AdminVideoPlayer from '@/components/AdminVideoPlayer';

interface Category {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
}

interface GalleryItem {
  _id: string;
  title: string;
  description?: string;
  image?: any; // For backward compatibility
  images?: Array<{
    mediaType?: 'image' | 'video';
    image?: any;
    video?: any;
    thumbnail?: any;
    alt?: string;
    caption?: string;
  }>;
  category?: {
    _id: string;
    title: string;
    slug: {
      current: string;
    };
  };
  publishedAt: string;
  featured?: boolean;
  order?: number;
}

export default function EditGalleryPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  // Use React.use to unwrap params (fixing the warning)
  const unwrappedParams = React.use(params as any);
  const id = unwrappedParams.id;
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [galleryItem, setGalleryItem] = useState<GalleryItem | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
  });

  // Fetch gallery item and categories
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch gallery item
        const itemResponse = await fetch(`/api/gallery/${id}`);
        const itemData = await itemResponse.json();

        if (itemData.success) {
          setGalleryItem(itemData.item);
          setFormData({
            title: itemData.item.title || '',
            description: itemData.item.description || '',
            category: itemData.item.category?._id || '',
          });
        } else {
          console.error('Failed to fetch gallery item:', itemData.message);
          toast.error('Failed to load gallery item');
        }

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories');
        const categoriesData = await categoriesResponse.json();

        if (categoriesData.success) {
          setCategories(categoriesData.categories || []);

          // If no categories are available, create a default one
          if (categoriesData.categories && categoriesData.categories.length === 0) {
            console.log('No categories found, creating a default category');
            try {
              const createResponse = await fetch('/api/categories', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  title: 'General',
                  description: 'General gallery images',
                  color: '#002366',
                  order: 0
                }),
              });

              const createData = await createResponse.json();

              if (createData.success && createData.category) {
                console.log('Default category created:', createData.category);
                setCategories([createData.category]);
              }
            } catch (createError) {
              console.error('Failed to create default category:', createError);
            }
          }
        } else {
          console.error('Failed to fetch categories:', categoriesData.message);
          toast.error('Failed to load categories');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const response = await fetch(`/api/gallery/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Gallery item updated successfully');
        router.push('/admin/gallery');
      } else {
        toast.error(data.message || 'Failed to update gallery item');
      }
    } catch (error) {
      console.error('Error updating gallery item:', error);
      toast.error('Failed to update gallery item');
    } finally {
      setIsSaving(false);
    }
  };

  // Get media info from Sanity
  const getMediaInfo = () => {
    try {
      // Try to get media from the images array first
      if (galleryItem?.images && galleryItem.images.length > 0) {
        const mediaItem = galleryItem.images[0];

        // Check if this is a video
        if (mediaItem.mediaType === 'video' && mediaItem.video) {
          return {
            type: 'video',
            url: fileUrlFor(mediaItem.video),
            thumbnailUrl: mediaItem.thumbnail ? urlFor(mediaItem.thumbnail).url() : null,
            alt: mediaItem.alt || galleryItem.title
          };
        }
        // Otherwise treat as image
        else if (mediaItem.image) {
          return {
            type: 'image',
            url: urlFor(mediaItem.image).url(),
            alt: mediaItem.alt || galleryItem.title
          };
        }
      }

      // Fall back to the legacy image field
      if (galleryItem?.image) {
        return {
          type: 'image',
          url: urlFor(galleryItem.image).url(),
          alt: galleryItem.title
        };
      }

      // Default fallback
      return {
        type: 'image',
        url: '/images/placeholder.jpg',
        alt: 'Placeholder'
      };
    } catch (error) {
      console.error('Error generating media info:', error);
      return {
        type: 'image',
        url: '/images/placeholder.jpg',
        alt: 'Error loading media'
      };
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading gallery item...</span>
      </div>
    );
  }

  if (!galleryItem) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/gallery">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Gallery Item Not Found</h1>
        </div>
        <p>The gallery item you are looking for could not be found.</p>
        <Button asChild>
          <Link href="/admin/gallery">Back to Gallery</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" asChild>
          <Link href="/admin/gallery">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Edit Gallery Item</h1>
      </div>

      <Separator />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Media Preview</CardTitle>
            <CardDescription>Current media in the gallery</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative aspect-video overflow-hidden rounded-md">
              {getMediaInfo().type === 'image' ? (
                <Image
                  src={getMediaInfo().url}
                  alt={getMediaInfo().alt}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="relative w-full h-full bg-black overflow-hidden">
                  {/* Direct video element with controls */}
                  <video
                    className="w-full h-full object-contain"
                    src={getMediaInfo().url}
                    poster={getMediaInfo().thumbnailUrl || '/images/video-thumbnail.png'}
                    controls
                    preload="auto"
                    onError={(e) => {
                      console.error('Video error:', e);
                    }}
                  />

                  {/* Video badge */}
                  <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
                    Video
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Media Details</CardTitle>
              <CardDescription>Edit the details of this gallery item</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="title" className="text-sm font-medium">Title</label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter image title"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">Description</label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter image description"
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="category" className="text-sm font-medium">Category</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">-- No Category --</option>
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.title}
                    </option>
                  ))}
                </select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" asChild>
                <Link href="/admin/gallery">Cancel</Link>
              </Button>
              <Button type="submit" disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
