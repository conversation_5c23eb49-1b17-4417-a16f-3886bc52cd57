const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute shell commands and log output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Main build process
console.log('Starting custom Heroku build process for Kingdom Adukrom website...');
console.log('This script ensures all dependencies are properly installed and configured.');
console.log(`Build timestamp: ${new Date().toISOString()}`);

// Create a .env file with Sanity configuration
console.log('Creating .env file with Sanity configuration...');
const envContent = `
# Sanity Configuration
NEXT_PUBLIC_SANITY_PROJECT_ID=${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt'}
NEXT_PUBLIC_SANITY_DATASET=${process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'}
NEXT_PUBLIC_SANITY_API_VERSION=${process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2025-05-09'}
NEXT_PUBLIC_SANITY_API_TOKEN=${process.env.NEXT_PUBLIC_SANITY_API_TOKEN || ''}
SANITY_API_TOKEN=${process.env.SANITY_API_TOKEN || ''}
NEXT_PUBLIC_APP_URL=${process.env.NEXT_PUBLIC_APP_URL || 'https://adukingdom-62986aa3239a.herokuapp.com'}
NEXTAUTH_URL=${process.env.NEXTAUTH_URL || 'https://adukingdom-62986aa3239a.herokuapp.com'}
NEXTAUTH_URL_INTERNAL=${process.env.NEXTAUTH_URL_INTERNAL || 'https://adukingdom-62986aa3239a.herokuapp.com'}
NEXTAUTH_SECRET=${process.env.NEXTAUTH_SECRET || ''}
ADMIN_USERNAME=${process.env.ADMIN_USERNAME || 'admin'}
ADMIN_PASSWORD=${process.env.ADMIN_PASSWORD || 'kingdom2024'}
`;

fs.writeFileSync('.env.local', envContent);
console.log('.env.local file created successfully');

// Check Node.js version
console.log('Checking Node.js version...');
const nodeVersion = process.version;
console.log(`Current Node.js version: ${nodeVersion}`);

// Verify Node.js version is 20 or higher
const versionMatch = nodeVersion.match(/^v(\d+)\./);
if (versionMatch && Number(versionMatch[1]) < 20) {
  console.warn(`WARNING: Current Node.js version ${nodeVersion} is less than the required version 20.`);
  console.warn('This may cause compatibility issues with @sanity/client.');
  console.warn('Consider upgrading Node.js to version 20 or higher.');
}

// Create a simple next-env.d.ts file if it doesn't exist
const nextEnvPath = path.join(__dirname, 'next-env.d.ts');
if (!fs.existsSync(nextEnvPath)) {
  console.log('Creating next-env.d.ts file...');
  fs.writeFileSync(
    nextEnvPath,
    '/// <reference types="next" />\n/// <reference types="next/image-types/global" />\n\n// NOTE: This file should not be edited\n// see https://nextjs.org/docs/basic-features/typescript for more information.\n'
  );
}

// Check for missing CSS files and fix paths if needed
console.log('Checking for CSS import issues...');
const secretLayoutPath = path.join(__dirname, 'app', '(users)', 'secret', 'layout.tsx');
if (fs.existsSync(secretLayoutPath)) {
  const content = fs.readFileSync(secretLayoutPath, 'utf8');
  if (content.includes("import '../globals.css'")) {
    console.log('Fixing CSS import path in secret layout...');
    const fixedContent = content.replace("import '../globals.css'", "import '../../globals.css'");
    fs.writeFileSync(secretLayoutPath, fixedContent);
    console.log('Fixed CSS import path in secret layout');
  }
}

// Check for other potential CSS import issues
const adminLayoutPath = path.join(__dirname, 'app', '(admin)', 'admin', 'layout.tsx');
if (fs.existsSync(adminLayoutPath)) {
  const content = fs.readFileSync(adminLayoutPath, 'utf8');
  if (content.includes("import '../globals.css'")) {
    console.log('Fixing CSS import path in admin layout...');
    const fixedContent = content.replace("import '../globals.css'", "import '../../globals.css'");
    fs.writeFileSync(adminLayoutPath, fixedContent);
    console.log('Fixed CSS import path in admin layout');
  }
}

// Check for dependencies
console.log('Checking dependencies...');

// Update next.config.js to fix dynamic server usage errors
console.log('Updating Next.js configuration for production...');
const nextConfigPath = path.join(__dirname, 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');

  // Add export configuration if not present
  if (!nextConfig.includes('output:')) {
    nextConfig = nextConfig.replace(
      'const nextConfig = {',
      'const nextConfig = {\n  output: "standalone",'
    );
    fs.writeFileSync(nextConfigPath, nextConfig);
    console.log('Added standalone output configuration to next.config.js');
  }
}

// Fix Node.js protocol imports in user files
console.log('Fixing Node.js protocol imports...');
const usersFilePath = path.join(__dirname, 'lib', 'users.ts');
if (fs.existsSync(usersFilePath)) {
  let usersContent = fs.readFileSync(usersFilePath, 'utf8');

  // Replace node:fs and node:path imports
  if (usersContent.includes("import fs from 'node:fs'") || usersContent.includes('import fs from "node:fs"')) {
    usersContent = usersContent.replace(/import fs from ['"]node:fs['"]/, "import fs from 'fs'");
    usersContent = usersContent.replace(/import path from ['"]node:path['"]/, "import path from 'path'");
    fs.writeFileSync(usersFilePath, usersContent);
    console.log('Fixed Node.js protocol imports in users.ts');
  }
}

// Fix Node.js protocol imports in migration script
const migrationScriptPath = path.join(__dirname, 'scripts', 'migrate-users-to-sanity.js');
if (fs.existsSync(migrationScriptPath)) {
  let scriptContent = fs.readFileSync(migrationScriptPath, 'utf8');

  // Replace node:fs and node:path requires
  if (scriptContent.includes("require('node:fs')") || scriptContent.includes('require("node:fs")')) {
    scriptContent = scriptContent.replace(/require\(['"]node:fs['"]\)/, "require('fs')");
    scriptContent = scriptContent.replace(/require\(['"]node:path['"]\)/, "require('path')");
    fs.writeFileSync(migrationScriptPath, scriptContent);
    console.log('Fixed Node.js protocol imports in migrate-users-to-sanity.js');
  }
}

// Build the Next.js application
console.log('Building Next.js application...');
// Set environment variables to optimize the build
process.env.NEXT_MINIMAL_BUILD = 'true';
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.NODE_ENV = 'production';
process.env.NEXT_OPTIMIZE_FONTS = 'true';
process.env.NEXT_OPTIMIZE_IMAGES = 'true';
process.env.NEXT_OPTIMIZE_CSS = 'true';
// Use a more optimized build command
runCommand('npm run build --legacy-peer-deps -- --no-lint');

console.log('Custom build process completed successfully!');
