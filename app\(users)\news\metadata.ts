import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the main news page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'News & Updates',
    description: 'Stay updated with the latest news and announcements from the Kingdom of Adukrom.',
    url: '/news',
    keywords: ['Adukrom News', 'Royal News', 'Kingdom Updates', 'African Royalty News'],
  });
}
