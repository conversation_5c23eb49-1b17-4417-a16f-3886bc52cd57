'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { FileText, Image as ImageIcon, Users, Calendar, ArrowRight, Mail, ShoppingBag } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalNews: 0,
    totalGalleryItems: 0,
    totalVisitors: 0,
    upcomingEvents: 0,
    totalRsvps: 0,
  });

  // State for recent data
  const [recentNews, setRecentNews] = useState([]);
  const [recentGallery, setRecentGallery] = useState([]);
  const [recentRsvp, setRecentRsvp] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Fetch real data from the API
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Fetch dashboard statistics from the API
        const response = await fetch('/api/dashboard');
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch dashboard data');
        }

        // Update state with real data
        setStats({
          totalNews: data.stats.totalNews || 0,
          totalGalleryItems: data.stats.totalGalleryItems || 0,
          totalVisitors: data.stats.totalVisitors || 0,
          upcomingEvents: data.stats.upcomingEvents || 0,
          totalRsvps: data.stats.totalRsvps || 0,
        });

        // Update recent data
        setRecentNews(data.recentNews || []);
        setRecentGallery(data.recentGallery || []);
        setRecentRsvp(data.recentRsvp || []);
        setRecentActivity(data.recentActivity || []);

        toast.success('Dashboard data loaded successfully');
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to the Kingdom admin dashboard.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/admin/news/create">
              Create News
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/gallery/upload">
              Upload Images
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/pages">
              Manage Pages
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/store">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Store
            </Link>
          </Button>
        </div>
      </div>

      <Separator />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total News</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalNews}</div>
            <p className="text-xs text-muted-foreground">
              Articles published
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gallery Items</CardTitle>
            <ImageIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalGalleryItems}</div>
            <p className="text-xs text-muted-foreground">
              Images in gallery
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">RSVP Submissions</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRsvps}</div>
            <p className="text-xs text-muted-foreground">
              Event registrations
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Visitors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVisitors}</div>
            <p className="text-xs text-muted-foreground">
              Monthly website visitors
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingEvents}</div>
            <p className="text-xs text-muted-foreground">
              Events scheduled
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent">
        <TabsList>
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
          <TabsTrigger value="news">Latest News</TabsTrigger>
          <TabsTrigger value="gallery">Gallery Updates</TabsTrigger>
          <TabsTrigger value="rsvp">RSVP Submissions</TabsTrigger>
        </TabsList>
        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest actions performed in the admin panel
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  <p className="ml-3">Loading activity...</p>
                </div>
              ) : recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity: any, index: number) => (
                    <div key={activity.id || index}>
                      {index > 0 && <Separator />}
                      <div className="flex items-center justify-between pt-4">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{activity.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {activity.description}
                          </p>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {new Date(activity.updatedAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric',
                          })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No recent activity found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="news" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Latest News</CardTitle>
              <CardDescription>
                Recently published news articles
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  <p className="ml-3">Loading news...</p>
                </div>
              ) : recentNews.length > 0 ? (
                <div className="space-y-4">
                  {recentNews.map((article: any, index: number) => (
                    <div key={article._id}>
                      {index > 0 && <Separator />}
                      <div className="flex items-center justify-between pt-4">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{article.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {article.publishedAt ? (
                              `Published on ${new Date(article.publishedAt).toLocaleDateString('en-US', {
                                month: 'long',
                                day: 'numeric',
                                year: 'numeric',
                              })}`
                            ) : (
                              `Status: ${article.status || 'Draft'}`
                            )}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/news/edit/${article._id}`}>
                            <ArrowRight className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No news articles found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="gallery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gallery Updates</CardTitle>
              <CardDescription>
                Recently added gallery images
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  <p className="ml-3">Loading gallery items...</p>
                </div>
              ) : recentGallery.length > 0 ? (
                <div className="space-y-4">
                  {recentGallery.map((item: any, index: number) => (
                    <div key={item._id}>
                      {index > 0 && <Separator />}
                      <div className="flex items-center justify-between pt-4">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{item.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {item._createdAt ? (
                              `Added on ${new Date(item._createdAt).toLocaleDateString('en-US', {
                                month: 'long',
                                day: 'numeric',
                                year: 'numeric',
                              })}`
                            ) : 'Recently added'}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/gallery/edit/${item._id}`}>
                            <ArrowRight className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No gallery items found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="rsvp" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent RSVP Submissions</CardTitle>
              <CardDescription>
                Latest event registrations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  <p className="ml-3">Loading RSVP submissions...</p>
                </div>
              ) : recentRsvp.length > 0 ? (
                <div className="space-y-4">
                  {recentRsvp.map((submission: any, index: number) => (
                    <div key={submission._id}>
                      {index > 0 && <Separator />}
                      <div className="flex items-center justify-between pt-4">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">
                            {submission.firstName} {submission.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {submission.events ? (
                              `Registered for ${Array.isArray(submission.events)
                                ? submission.events.join(', ')
                                : submission.events}`
                            ) : (
                              'Event registration'
                            )}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href="/admin/rsvp">
                            <ArrowRight className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No RSVP submissions found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>
    </div>
  );
}