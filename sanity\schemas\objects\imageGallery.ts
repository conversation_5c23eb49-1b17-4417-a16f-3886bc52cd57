import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'imageGallery',
  title: 'Image Gallery',
  type: 'object',
  fields: [
    defineField({
      name: 'heading',
      title: 'Heading',
      type: 'string',
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 2,
    }),
    defineField({
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'image',
              title: 'Image',
              type: 'image',
              options: {
                hotspot: true,
              },
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'caption',
              title: 'Caption',
              type: 'string',
            },
            {
              name: 'altText',
              title: 'Alt Text',
              type: 'string',
              description: 'Important for SEO and accessibility',
            },
          ],
          preview: {
            select: {
              title: 'caption',
              media: 'image',
            },
          },
        },
      ],
      options: {
        layout: 'grid',
      },
    }),
    defineField({
      name: 'displayStyle',
      title: 'Display Style',
      type: 'string',
      options: {
        list: [
          { title: 'Grid', value: 'grid' },
          { title: 'Carousel', value: 'carousel' },
          { title: 'Masonry', value: 'masonry' },
        ],
      },
      initialValue: 'grid',
    }),
    defineField({
      name: 'backgroundStyle',
      title: 'Background Style',
      type: 'string',
      options: {
        list: [
          { title: 'None', value: 'none' },
          { title: 'Light', value: 'light' },
          { title: 'Dark', value: 'dark' },
          { title: 'Royal Blue', value: 'royalBlue' },
          { title: 'Royal Gold', value: 'royalGold' },
          { title: 'Ivory', value: 'ivory' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      name: 'animation',
      title: 'Animation Settings',
      type: 'object',
      fields: [
        { name: 'duration', type: 'number', title: 'Duration', initialValue: 0.6 },
        { name: 'delay', type: 'number', title: 'Delay', initialValue: 0 },
        { name: 'stagger', type: 'number', title: 'Stagger Children', initialValue: 0 },
        { name: 'type', type: 'string', title: 'Type', initialValue: 'spring' }
      ]
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      images: 'images',
    },
    prepare({ title, images }) {
      return {
        title: `Gallery: ${title || 'Untitled'}`,
        subtitle: `${images?.length || 0} image${images?.length === 1 ? '' : 's'}`,
        media: images?.[0]?.image,
      };
    },
  },
});
