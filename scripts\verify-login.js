// This script verifies login credentials against Sanity
// Run with: node scripts/verify-login.js <email> <password>

const { createClient } = require('@sanity/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Function to verify login credentials
async function verifyLogin(email, password) {
  try {
    console.log(`Verifying login for email: ${email}`);
    
    // Check if the user exists
    const user = await client.fetch(
      `*[_type == "adminUser" && email == $email][0]{
        _id,
        username,
        name,
        email,
        hashedPassword,
        role,
        isActive
      }`,
      { email }
    );
    
    if (!user) {
      console.log('User not found');
      return { success: false, message: 'User not found' };
    }
    
    console.log(`Found user: ${user.username} (${user._id})`);
    
    // Check if the user is active
    if (!user.isActive) {
      console.log('User account is not active');
      return { success: false, message: 'User account is not active' };
    }
    
    // Check if the user has a password
    if (!user.hashedPassword) {
      console.log('User has no password set');
      return { success: false, message: 'User has no password set' };
    }
    
    // Verify the password
    const isPasswordValid = await bcrypt.compare(password, user.hashedPassword);
    
    if (!isPasswordValid) {
      console.log('Invalid password');
      return { success: false, message: 'Invalid password' };
    }
    
    console.log('Login successful!');
    console.log(`User: ${user.name} (${user.email})`);
    console.log(`Role: ${user.role}`);
    
    return {
      success: true,
      user: {
        id: user._id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role
      }
    };
  } catch (error) {
    console.error('Error verifying login:', error);
    return { success: false, message: 'Error verifying login' };
  }
}

// Main function
async function main() {
  // Get email and password from command line arguments
  const email = process.argv[2];
  const password = process.argv[3];
  
  if (!email || !password) {
    console.error('Usage: node scripts/verify-login.js <email> <password>');
    process.exit(1);
  }
  
  const result = await verifyLogin(email, password);
  
  if (result.success) {
    console.log('\nLogin verification successful!');
  } else {
    console.error('\nLogin verification failed:', result.message);
  }
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
