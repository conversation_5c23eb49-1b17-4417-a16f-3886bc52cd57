'use client';

// Simple event system for client-side communication
type EventCallback = (data?: any) => void;

interface EventMap {
  [key: string]: EventCallback[];
}

class EventBus {
  private events: EventMap = {};

  // Subscribe to an event
  on(event: string, callback: EventCallback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);

    // Return unsubscribe function
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    };
  }

  // Emit an event
  emit(event: string, data?: any) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }

  // Remove all event listeners
  clear(event?: string) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

// Create a singleton instance
const eventBus = typeof window !== 'undefined' ? new EventBus() : null;

// Event names
export const EVENTS = {
  PROFILE_UPDATED: 'profile_updated',
};

// Helper functions
export const onProfileUpdated = (callback: (userData: any) => void) => {
  return eventBus?.on(EVENTS.PROFILE_UPDATED, callback);
};

export const emitProfileUpdated = (userData: any) => {
  eventBus?.emit(EVENTS.PROFILE_UPDATED, userData);
};

export default eventBus;
