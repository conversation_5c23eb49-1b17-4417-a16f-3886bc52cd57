import { format, formatDistance, parseISO } from 'date-fns';

/**
 * Safely format a date string using date-fns
 * This helps prevent hydration errors by handling invalid dates
 * 
 * @param dateString - ISO date string to format
 * @param formatString - date-fns format string (default: 'MMMM d, yyyy')
 * @param fallback - fallback string if date is invalid (default: 'Invalid date')
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string | Date | undefined | null,
  formatString: string = 'MMMM d, yyyy',
  fallback: string = 'Invalid date'
): string {
  if (!dateString) return fallback;
  
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
    return format(date, formatString);
  } catch (error) {
    console.error('Error formatting date:', error);
    return fallback;
  }
}

/**
 * Format a date as a relative time (e.g., "2 days ago")
 * 
 * @param dateString - ISO date string
 * @param fallback - fallback string if date is invalid (default: 'Invalid date')
 * @returns Relative time string
 */
export function formatRelativeTime(
  dateString: string | Date | undefined | null,
  fallback: string = 'Invalid date'
): string {
  if (!dateString) return fallback;
  
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
    return formatDistance(date, new Date(), { addSuffix: true });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return fallback;
  }
}

/**
 * Client-side only date formatter
 * Use this in useEffect or event handlers to avoid hydration mismatches
 * 
 * @param dateString - ISO date string
 * @param formatString - date-fns format string
 * @param fallback - fallback string if date is invalid
 * @returns Formatted date string
 */
export function clientSideFormatDate(
  dateString: string | Date | undefined | null,
  formatString: string = 'MMMM d, yyyy',
  fallback: string = 'Invalid date'
): string {
  // Only run on client side
  if (typeof window === 'undefined') return fallback;
  
  return formatDate(dateString, formatString, fallback);
}

/**
 * Format a date range (e.g., "January 1 - January 5, 2023")
 * 
 * @param startDateString - Start date ISO string
 * @param endDateString - End date ISO string
 * @param fallback - Fallback string if dates are invalid
 * @returns Formatted date range string
 */
export function formatDateRange(
  startDateString: string | Date | undefined | null,
  endDateString: string | Date | undefined | null,
  fallback: string = 'Invalid date range'
): string {
  if (!startDateString) return fallback;
  
  try {
    const startDate = typeof startDateString === 'string' ? parseISO(startDateString) : startDateString;
    
    // If no end date, just format the start date
    if (!endDateString) {
      return format(startDate, 'MMMM d, yyyy');
    }
    
    const endDate = typeof endDateString === 'string' ? parseISO(endDateString) : endDateString;
    
    // If same day, format as single date
    if (format(startDate, 'yyyy-MM-dd') === format(endDate, 'yyyy-MM-dd')) {
      return format(startDate, 'MMMM d, yyyy');
    }
    
    // If same month and year, format as "January 1-5, 2023"
    if (format(startDate, 'yyyy-MM') === format(endDate, 'yyyy-MM')) {
      return `${format(startDate, 'MMMM d')} - ${format(endDate, 'd, yyyy')}`;
    }
    
    // If same year, format as "January 1 - February 5, 2023"
    if (format(startDate, 'yyyy') === format(endDate, 'yyyy')) {
      return `${format(startDate, 'MMMM d')} - ${format(endDate, 'MMMM d, yyyy')}`;
    }
    
    // Different years, format as "January 1, 2023 - February 5, 2024"
    return `${format(startDate, 'MMMM d, yyyy')} - ${format(endDate, 'MMMM d, yyyy')}`;
  } catch (error) {
    console.error('Error formatting date range:', error);
    return fallback;
  }
}
