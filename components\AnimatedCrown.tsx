'use client';
import { motion } from 'framer-motion';

export default function AnimatedCrown() {
  return (
    <motion.svg
      className="w-10 h-10 text-royalGold crown-icon"
      viewBox="0 0 24 24"
      fill="currentColor"
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 260, 
        damping: 20,
        duration: 0.6 
      }}
      whileHover={{ 
        scale: 1.1,
        rotate: [0, -5, 5, -5, 0],
        transition: { duration: 0.5 }
      }}
    >
      <motion.path 
        d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.18l7 3.12v4.7c0 4.67-3.13 8.42-7 9.88-3.87-1.46-7-5.21-7-9.88V6.3l7-3.12z"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 1.5, delay: 0.2 }}
      />
      <motion.path 
        d="M12 6l-2.12 2.12 2.12 2.12 2.12-2.12z"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
      />
      <motion.path 
        d="M12 12l-2.12 2.12 2.12 2.12 2.12-2.12z"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
      />
    </motion.svg>
  );
}
