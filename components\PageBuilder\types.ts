export interface SectionProps {
  _type: string;
  _key: string;
}

export interface HeroProps extends SectionProps {
  heading?: string;
  tagline?: string;
  backgroundImage?: any;
  ctas?: {
    title: string;
    link: string;
    isPrimary: boolean;
  }[];
}

export interface TextSectionProps extends SectionProps {
  heading?: string;
  text?: any[];
  backgroundStyle?: 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory';
  textAlign?: 'left' | 'center' | 'right';
  image?: any;
  imagePosition?: 'none' | 'left' | 'right' | 'background';
}

export interface ImageGalleryProps extends SectionProps {
  heading?: string;
  description?: string;
  images?: {
    _key?: string;
    image: any;
    caption?: string;
    altText?: string;
    alt?: string; // Support both altText and alt for flexibility
  }[];
  displayStyle?: 'grid' | 'carousel' | 'masonry';
  backgroundStyle?: 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory';
  animation?: {
    duration?: number;
    delay?: number;
    stagger?: number;
    type?: string;
  };
}

export interface FeaturedContentProps extends SectionProps {
  heading?: string;
  description?: string;
  items?: {
    _key: string;
    title: string;
    description?: string;
    image?: any;
    link?: string;
    linkText?: string;
    icon?: string;
  }[];
  layout?: 'cards' | 'list' | 'grid';
  backgroundStyle?: 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory';
}

export interface ContactFormProps extends SectionProps {
  heading?: string;
  description?: string;
  formFields?: {
    _key: string;
    name: string;
    label: string;
    type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio';
    options?: string[];
    required?: boolean;
    placeholder?: string;
  }[];
  submitButtonText?: string;
  successMessage?: string;
  backgroundStyle?: 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory';
}
