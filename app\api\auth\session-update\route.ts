import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import fs from 'node:fs';
import path from 'node:path';
import { getWriteClient } from '@/lib/sanity.client';

// Path to the users file
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// POST /api/auth/session-update - Update the session data
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'No active session' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, message: 'Name is required' },
        { status: 400 }
      );
    }

    // Get the user ID and email from the session
    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log('Updating session for user:', {
      id: userId,
      email: userEmail,
      currentName: session.user.name,
      newName: name
    });

    // Return the updated session data
    return NextResponse.json({
      success: true,
      message: 'Session data updated',
      user: {
        id: userId,
        name: name,
        email: userEmail,
        role: (session.user as any).role,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating session:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update session' },
      { status: 500 }
    );
  }
}
