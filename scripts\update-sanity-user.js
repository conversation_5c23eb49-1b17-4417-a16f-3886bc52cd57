// This script updates a user in Sanity directly
// Run with: node scripts/update-sanity-user.js

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2023-05-03',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
});

// Email of the user to update
const userEmail = '<EMAIL>';
// New name to set
const newName = 'Samantha Updated';

async function updateUser() {
  try {
    console.log('Sanity config:', {
      projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
      dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
      apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
      token: process.env.SANITY_API_TOKEN ? 'Set' : 'Not set'
    });
    
    // Log the full token for debugging (first 10 chars only)
    if (process.env.SANITY_API_TOKEN) {
      console.log('Token starts with:', process.env.SANITY_API_TOKEN.substring(0, 10) + '...');
    }
    
    console.log(`Looking for user with email: ${userEmail}`);
    
    // Find the user by email
    const users = await client.fetch(`*[_type == "adminUser" && email == $email]`, { email: userEmail });
    
    console.log(`Found ${users.length} users`);
    
    if (users.length === 0) {
      console.log('No users found with that email');
      
      // Try to find all users
      const allUsers = await client.fetch(`*[_type == "adminUser"]{_id, name, email, username}`);
      console.log('All users:', allUsers);
      
      return;
    }
    
    const user = users[0];
    console.log('User found:', user);
    
    // Update the user
    console.log(`Updating user ${user._id} with new name: ${newName}`);
    
    const result = await client
      .patch(user._id)
      .set({
        name: newName,
        updatedAt: new Date().toISOString()
      })
      .commit();
    
    console.log('Update successful:', result);
  } catch (error) {
    console.error('Error updating user:', error);
  }
}

// Run the function
updateUser();
