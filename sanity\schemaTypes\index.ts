import { type SchemaTypeDefinition } from 'sanity'
import author from '../schemas/author'
import category from '../schemas/category'
import news from '../schemas/news'
import gallery from '../schemas/gallery'
import event from '../schemas/event'
import rsvp from '../schemas/rsvp'
import siteSettings from '../schemas/siteSettings'

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    // Documents
    news,
    author,
    category,
    gallery,
    event,
    rsvp,
    siteSettings,
  ],
}
