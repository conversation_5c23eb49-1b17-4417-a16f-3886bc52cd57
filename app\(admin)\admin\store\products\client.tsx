'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  Package,
  AlertTriangle,
  Plus,
  Trash2,
  Edit,
  Eye,
  ImagePlus,
  Tag,
  DollarSign,
  Loader2
} from 'lucide-react';

// Define product type
interface Product {
  _id: string;
  name: string;
  slug: { current: string };
  description?: string;
  price: number;
  compareAtPrice?: number;
  currency?: string;
  images?: any[];
  category?: { _id: string; title: string };
  tags?: string[];
  featured?: boolean;
  isDigital?: boolean;
  inventory?: { quantity: number; trackInventory: boolean };
  publishedAt: string;
}

// Define category type
interface Category {
  _id: string;
  title: string;
  slug: { current: string };
}

export default function ProductsClient() {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    compareAtPrice: '',
    currency: 'USD',
    category: '',
    featured: false,
    isDigital: false,
    inventoryQuantity: '0',
    trackInventory: true
  });
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const router = useRouter();

  // Get session directly from next-auth
  const { data: session, status } = useSession();

  // Fetch products
  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/store/products');
      const data = await response.json();

      if (data.success && data.products) {
        setProducts(data.products);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch store categories
  const fetchCategories = async () => {
    try {
      console.log('Fetching store categories...');
      const response = await fetch('/api/store/categories');
      const data = await response.json();

      console.log('Categories API response:', data);

      if (data.success && data.categories && data.categories.length > 0) {
        console.log('Found categories:', data.categories);
        setCategories(data.categories);
      } else {
        // If no categories found, create some default ones
        console.log('No store categories found, creating defaults...');
        await createDefaultCategories();
      }
    } catch (error) {
      console.error('Error fetching store categories:', error);
      toast.error('Failed to load store categories');
      // Create default categories as fallback
      await createDefaultCategories();
    }
  };

  // Create default store categories if none exist
  const createDefaultCategories = async () => {
    try {
      const defaultCategories = [
        { title: 'Apparel', description: 'Clothing and wearable items', color: '#3B82F6' },
        { title: 'Accessories', description: 'Bags, jewelry, and other accessories', color: '#10B981' },
        { title: 'Collectibles', description: 'Limited edition and commemorative items', color: '#F59E0B' },
        { title: 'Digital', description: 'Digital products and downloads', color: '#8B5CF6' }
      ];

      toast.info('Creating default store categories...');

      // Create categories one by one
      for (const category of defaultCategories) {
        try {
          const response = await fetch('/api/store/categories', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(category),
          });

          const data = await response.json();
          if (data.success) {
            console.log(`Created category: ${category.title}`);
          } else {
            console.error(`Failed to create category ${category.title}:`, data.message);
          }
        } catch (categoryError) {
          console.error(`Error creating category ${category.title}:`, categoryError);
        }

        // Add a small delay between requests to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Fetch the newly created categories
      const response = await fetch('/api/store/categories');
      const data = await response.json();

      if (data.success && data.categories) {
        setCategories(data.categories);
        toast.success('Default store categories created');
      }
    } catch (error) {
      console.error('Error creating default categories:', error);
      toast.error('Failed to create default categories');
    }
  };

  useEffect(() => {
    if (status === 'loading') {
      return; // Still loading, don't do anything yet
    }

    // Check if the user is an admin or super admin
    if (session?.user?.role === 'admin' || session?.user?.role === 'super_admin') {
      setIsAuthorized(true);
      // Fetch products and categories
      fetchProducts();
      fetchCategories();
    }

    // Set loading to false
    setIsLoading(false);
  }, [session, status]);

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedImage(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // First, upload the image to Sanity if selected
      let imageAsset = null;
      if (selectedImage) {
        try {
          const formData = new FormData();
          formData.append('file', selectedImage);

          toast.info('Uploading product image...');

          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          const uploadData = await uploadResponse.json();

          if (uploadData.success && uploadData.asset) {
            toast.success('Image uploaded successfully');
            imageAsset = {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: uploadData.asset._id
              }
            };
          } else {
            // If upload failed, show error but continue with product creation
            console.error('Image upload failed:', uploadData.message);
            toast.error(`Image upload failed: ${uploadData.message || 'Unknown error'}`);
          }
        } catch (error) {
          // If upload API is not available, show error but continue with product creation
          console.error('Error uploading image:', error);
          toast.error(`Error uploading image: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Prepare product data
      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        compareAtPrice: formData.compareAtPrice ? parseFloat(formData.compareAtPrice) : undefined,
        currency: formData.currency,
        // Only include category if it's not "none"
        category: formData.category && formData.category !== "none" ? formData.category : undefined,
        featured: formData.featured,
        isDigital: formData.isDigital,
        inventory: {
          quantity: parseInt(formData.inventoryQuantity),
          trackInventory: formData.trackInventory,
          allowBackorder: false // Default value
        },
        // Only include images if we have a valid asset
        ...(imageAsset ? { images: [imageAsset] } : { images: [] })
      };

      let response;
      let successMessage;

      if (editingProductId) {
        // Update existing product
        response = await fetch(`/api/store/products/${editingProductId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(productData),
        });
        successMessage = 'Product updated successfully';
      } else {
        // Create new product
        response = await fetch('/api/store/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(productData),
        });
        successMessage = 'Product created successfully';
      }

      const data = await response.json();

      if (data.success) {
        toast.success(successMessage);
        // Reset form
        setFormData({
          name: '',
          description: '',
          price: '',
          compareAtPrice: '',
          currency: 'USD',
          category: '',
          featured: false,
          isDigital: false,
          inventoryQuantity: '0',
          trackInventory: true
        });
        setSelectedImage(null);
        setImagePreview(null);
        setIsAddDialogOpen(false);
        setIsEditDialogOpen(false);
        setEditingProductId(null);
        // Refresh products list
        fetchProducts();
      } else {
        toast.error(data.message || 'Failed to save product');
      }
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error('Failed to save product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit product
  const handleEditProduct = (product: Product) => {
    setEditingProductId(product._id);
    setFormData({
      name: product.name,
      description: product.description || '',
      price: product.price.toString(),
      compareAtPrice: product.compareAtPrice ? product.compareAtPrice.toString() : '',
      currency: product.currency || 'USD',
      category: product.category?._id || '',
      featured: product.featured || false,
      isDigital: product.isDigital || false,
      inventoryQuantity: product.inventory?.quantity.toString() || '0',
      trackInventory: product.inventory?.trackInventory || true
    });

    // Set image preview if product has images
    if (product.images && product.images.length > 0 && product.images[0].asset?._ref) {
      const imageUrl = `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt'}/${process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'}/${product.images[0].asset._ref
        .replace('image-', '')
        .replace('-jpg', '.jpg')
        .replace('-png', '.png')
        .replace('-webp', '.webp')}`;
      setImagePreview(imageUrl);
    }

    setIsEditDialogOpen(true);
  };

  // Handle delete product
  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      toast.info('Deleting product...');

      const response = await fetch(`/api/store/products/${productId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Product deleted successfully');
        // Refresh products list
        fetchProducts();
      } else {
        toast.error(data.message || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to administrators only.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Package className="mr-2 h-6 w-6 text-royalBlue" />
          Product Management
        </h1>
        <p className="text-muted-foreground">
          Create, edit, and manage products for your online store.
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center">
              <Tag className="mr-2 h-5 w-5" />
              Products
            </CardTitle>
            {/* Add Product Dialog */}
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Product
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Product</DialogTitle>
                  <DialogDescription>
                    Create a new product for your store. Fill in the details below.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Product Name</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="price">Price</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                          <Input
                            id="price"
                            name="price"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.price}
                            onChange={handleInputChange}
                            className="pl-8"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="compareAtPrice">Compare At Price (Optional)</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                          <Input
                            id="compareAtPrice"
                            name="compareAtPrice"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.compareAtPrice}
                            onChange={handleInputChange}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          value={formData.currency}
                          onValueChange={(value) => handleSelectChange('currency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD ($)</SelectItem>
                            <SelectItem value="EUR">EUR (€)</SelectItem>
                            <SelectItem value="GBP">GBP (£)</SelectItem>
                            <SelectItem value="CAD">CAD (C$)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="category">Category</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => router.push('/admin/store?tab=categories')}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          New Category
                        </Button>
                      </div>
                      <Select
                        value={formData.category}
                        onValueChange={(value) => handleSelectChange('category', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {categories && categories.length > 0 ? (
                            categories.map((category) => (
                              <SelectItem key={category._id} value={category._id}>
                                {category.title}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="image">Product Image</Label>
                      <div className="flex items-center gap-4">
                        <div className="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                          onClick={() => document.getElementById('image-upload')?.click()}
                        >
                          <ImagePlus className="h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-sm text-gray-500">Click to upload</p>
                          <input
                            id="image-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleImageChange}
                            className="hidden"
                          />
                        </div>
                        {imagePreview && (
                          <div className="relative w-24 h-24 border rounded-lg overflow-hidden">
                            <Image
                              src={imagePreview}
                              alt="Preview"
                              fill
                              className="object-cover"
                            />
                            <button
                              type="button"
                              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1"
                              onClick={() => {
                                setSelectedImage(null);
                                setImagePreview(null);
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="inventoryQuantity">Inventory Quantity</Label>
                        <Input
                          id="inventoryQuantity"
                          name="inventoryQuantity"
                          type="number"
                          min="0"
                          value={formData.inventoryQuantity}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="space-y-2 flex flex-col justify-end">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="trackInventory"
                            checked={formData.trackInventory}
                            onCheckedChange={(checked) => handleSwitchChange('trackInventory', checked)}
                          />
                          <Label htmlFor="trackInventory">Track Inventory</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="featured"
                        checked={formData.featured}
                        onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                      />
                      <Label htmlFor="featured">Featured Product</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isDigital"
                        checked={formData.isDigital}
                        onCheckedChange={(checked) => handleSwitchChange('isDigital', checked)}
                      />
                      <Label htmlFor="isDigital">Digital Product</Label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Product'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>

            {/* Edit Product Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Edit Product</DialogTitle>
                  <DialogDescription>
                    Update the product details below.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Product Name</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="price">Price</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                          <Input
                            id="price"
                            name="price"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.price}
                            onChange={handleInputChange}
                            className="pl-8"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="compareAtPrice">Compare At Price (Optional)</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                          <Input
                            id="compareAtPrice"
                            name="compareAtPrice"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.compareAtPrice}
                            onChange={handleInputChange}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          value={formData.currency}
                          onValueChange={(value) => handleSelectChange('currency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD ($)</SelectItem>
                            <SelectItem value="EUR">EUR (€)</SelectItem>
                            <SelectItem value="GBP">GBP (£)</SelectItem>
                            <SelectItem value="CAD">CAD (C$)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="category">Category</Label>
                      </div>
                      <Select
                        value={formData.category}
                        onValueChange={(value) => handleSelectChange('category', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {categories && categories.length > 0 ? (
                            categories.map((category) => (
                              <SelectItem key={category._id} value={category._id}>
                                {category.title}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="image">Product Image</Label>
                      <div className="flex items-center gap-4">
                        <div className="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                          onClick={() => document.getElementById('image-upload-edit')?.click()}
                        >
                          <ImagePlus className="h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-sm text-gray-500">Click to upload</p>
                          <input
                            id="image-upload-edit"
                            type="file"
                            accept="image/*"
                            onChange={handleImageChange}
                            className="hidden"
                          />
                        </div>
                        {imagePreview && (
                          <div className="relative w-24 h-24 border rounded-lg overflow-hidden">
                            <Image
                              src={imagePreview}
                              alt="Preview"
                              fill
                              className="object-cover"
                              unoptimized={imagePreview.includes('cdn.sanity.io')}
                            />
                            <button
                              type="button"
                              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1"
                              onClick={() => {
                                setSelectedImage(null);
                                setImagePreview(null);
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="inventoryQuantity">Inventory Quantity</Label>
                        <Input
                          id="inventoryQuantity"
                          name="inventoryQuantity"
                          type="number"
                          min="0"
                          value={formData.inventoryQuantity}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 h-full pt-6">
                          <Switch
                            id="trackInventory"
                            checked={formData.trackInventory}
                            onCheckedChange={(checked) => handleSwitchChange('trackInventory', checked)}
                          />
                          <Label htmlFor="trackInventory">Track Inventory</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="featured"
                        checked={formData.featured}
                        onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                      />
                      <Label htmlFor="featured">Featured Product</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isDigital"
                        checked={formData.isDigital}
                        onCheckedChange={(checked) => handleSwitchChange('isDigital', checked)}
                      />
                      <Label htmlFor="isDigital">Digital Product</Label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => {
                      setIsEditDialogOpen(false);
                      setEditingProductId(null);
                    }}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        'Update Product'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          <CardDescription>
            Manage your store's products, inventory, and pricing.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
              <p className="ml-3 text-royalBlue">Loading products...</p>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-20">
              <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-lg text-gray-600">No products found</p>
              <p className="text-sm text-gray-500 mt-2">Create your first product to get started</p>
              <Button className="mt-4" onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add New Product
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="py-3 px-4 text-left">Product</th>
                    <th className="py-3 px-4 text-left">Price</th>
                    <th className="py-3 px-4 text-left">Category</th>
                    <th className="py-3 px-4 text-left">Inventory</th>
                    <th className="py-3 px-4 text-left">Status</th>
                    <th className="py-3 px-4 text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {products.map((product) => (
                    <tr key={product._id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="relative w-10 h-10 bg-gray-100 rounded overflow-hidden mr-3">
                            {product.images && product.images.length > 0 ? (
                              <Image
                                src={`https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt'}/${process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'}/${product.images[0].asset._ref.replace('image-', '').replace('-jpg', '.jpg').replace('-png', '.png').replace('-webp', '.webp')}`}
                                alt={product.name}
                                fill
                                className="object-cover"
                                unoptimized
                              />
                            ) : (
                              <Package className="h-6 w-6 text-gray-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-royalBlue">{product.name}</p>
                            <p className="text-xs text-gray-500">{product.slug.current}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <p className="font-medium">${product.price.toFixed(2)}</p>
                        {product.compareAtPrice && (
                          <p className="text-xs text-gray-500 line-through">${product.compareAtPrice.toFixed(2)}</p>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        {product.category ? (
                          <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">
                            {product.category.title}
                          </span>
                        ) : (
                          <span className="text-gray-400 text-xs">No category</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        {product.inventory ? (
                          <span className={`font-medium ${product.inventory.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {product.inventory.quantity} in stock
                          </span>
                        ) : (
                          <span className="text-gray-400">Not tracked</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        {product.featured ? (
                          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                            Featured
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                            Standard
                          </span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0" onClick={() => window.open(`/store/${product.slug.current}`, '_blank')}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0" onClick={() => handleEditProduct(product)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0 text-red-500 hover:text-red-700" onClick={() => handleDeleteProduct(product._id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
