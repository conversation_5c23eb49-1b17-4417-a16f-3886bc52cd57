/**
 * Notification service
 * 
 * This module provides functions to send notifications via different channels.
 */

import { sendEmail, sendRsvpConfirmationEmail } from './email';
import { sendSms, sendRsvpConfirmationSms } from './sms';
import { logError } from './errorHandling';

/**
 * RSVP submission data
 */
export interface RsvpData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  events: string[];
  reminderPreference?: string[];
}

/**
 * Send RSVP confirmation notifications based on user preferences
 * @param rsvpData - RSVP submission data
 */
export async function sendRsvpConfirmationNotifications(rsvpData: RsvpData): Promise<void> {
  const { firstName, lastName, email, phone, events, reminderPreference = ['email'] } = rsvpData;
  
  try {
    const notifications: Promise<void>[] = [];
    
    // Send email notification if preferred
    if (reminderPreference.includes('email')) {
      notifications.push(
        sendRsvpConfirmationEmail(email, firstName, lastName, events)
          .catch(error => {
            logError(error, 'sendRsvpConfirmationEmail');
            console.error('Failed to send confirmation email:', error);
          })
      );
    }
    
    // Send SMS notification if preferred
    if (reminderPreference.includes('sms')) {
      notifications.push(
        sendRsvpConfirmationSms(phone, firstName, events)
          .catch(error => {
            logError(error, 'sendRsvpConfirmationSms');
            console.error('Failed to send confirmation SMS:', error);
          })
      );
    }
    
    // Wait for all notifications to be sent
    await Promise.all(notifications);
    
    console.log(`RSVP confirmation notifications sent to ${firstName} ${lastName}`);
  } catch (error) {
    logError(error, 'sendRsvpConfirmationNotifications');
    console.error('Failed to send RSVP confirmation notifications:', error);
  }
}

/**
 * Send event reminder notifications
 * @param rsvpData - RSVP submission data
 * @param eventCode - Event code
 * @param daysUntilEvent - Number of days until the event
 */
export async function sendEventReminderNotifications(
  rsvpData: RsvpData,
  eventCode: string,
  daysUntilEvent: number
): Promise<void> {
  const { firstName, lastName, email, phone, reminderPreference = ['email'] } = rsvpData;
  
  // Only send reminders for events the user is attending
  if (!rsvpData.events.includes(eventCode)) {
    return;
  }
  
  try {
    const notifications: Promise<void>[] = [];
    
    // Get event name
    const eventMap: Record<string, string> = {
      'coronation': 'Royal Coronation Ceremony',
      'gala': 'Royal Gala Dinner',
      'forum': 'Global Economic Forum',
    };
    const eventName = eventMap[eventCode] || eventCode;
    
    // Send email reminder if preferred
    if (reminderPreference.includes('email')) {
      const subject = `Reminder: ${eventName} in ${daysUntilEvent} day${daysUntilEvent === 1 ? '' : 's'}`;
      const text = `
Dear ${firstName} ${lastName},

This is a friendly reminder that the ${eventName} will take place in ${daysUntilEvent} day${daysUntilEvent === 1 ? '' : 's'}.

We look forward to seeing you there!

Warm regards,
The Royal Court of Adukrom Kingdom
      `;
      
      notifications.push(
        sendEmail({ to: email, subject, text })
          .catch(error => {
            logError(error, 'sendEventReminderEmail');
            console.error('Failed to send event reminder email:', error);
          })
      );
    }
    
    // Send SMS reminder if preferred
    if (reminderPreference.includes('sms')) {
      const body = `Reminder: The ${eventName} will take place in ${daysUntilEvent} day${daysUntilEvent === 1 ? '' : 's'}. We look forward to seeing you there! - Kingdom of Adukrom`;
      
      notifications.push(
        sendSms({ to: phone, body })
          .catch(error => {
            logError(error, 'sendEventReminderSms');
            console.error('Failed to send event reminder SMS:', error);
          })
      );
    }
    
    // Wait for all notifications to be sent
    await Promise.all(notifications);
    
    console.log(`Event reminder notifications sent to ${firstName} ${lastName} for ${eventName}`);
  } catch (error) {
    logError(error, 'sendEventReminderNotifications');
    console.error('Failed to send event reminder notifications:', error);
  }
}
