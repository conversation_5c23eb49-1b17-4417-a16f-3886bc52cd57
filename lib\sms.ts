/**
 * SMS sending utility (STUB VERSION)
 *
 * This is a temporary stub version that doesn't require T<PERSON><PERSON>.
 * The actual implementation will be restored when <PERSON><PERSON><PERSON> is added back.
 */

import { logError } from './errorHandling';

interface SmsOptions {
  to: string;
  body: string;
  from?: string;
}

/**
 * Send an SMS message (stub version)
 * @param options - SMS options
 */
export async function sendSms(options: SmsOptions): Promise<void> {
  const { to, body, from = process.env.TWILIO_PHONE_NUMBER || '+15555555555' } = options;

  // Just log the SMS
  console.log('==== SMS SENT (STUB) ====');
  console.log(`From: ${from}`);
  console.log(`To: ${to}`);
  console.log(`Body: ${body}`);
  console.log('==== END SMS ====');
  return;
}

/**
 * Format phone number to E.164 format (stub version)
 * @param phoneNumber - Phone number to format
 */
function formatPhoneNumber(phoneNumber: string): string {
  // Simple formatting for stub version
  return phoneNumber.startsWith('+') ? phoneNumber : `+1${phoneNumber.replace(/\D/g, '')}`;
}

/**
 * Send an RSVP confirmation SMS
 * @param phoneNumber - Recipient's phone number
 * @param firstName - Recipient's first name
 * @param events - Events the recipient is attending
 */
export async function sendRsvpConfirmationSms(
  phoneNumber: string,
  firstName: string,
  events: string[]
): Promise<void> {
  // Format event names for display
  const eventNames = formatEventNames(events);

  // Create the SMS body
  const body = `Thank you, ${firstName}! Your RSVP for ${eventNames} has been confirmed. We look forward to seeing you at the event(s). - Kingdom of Adukrom`;

  // Send the SMS
  await sendSms({
    to: phoneNumber,
    body,
  });
}

/**
 * Format event names for display in messages
 * @param events - Array of event codes
 */
function formatEventNames(events: string[]): string {
  const eventMap: Record<string, string> = {
    'coronation': 'Royal Coronation Ceremony',
    'gala': 'Royal Gala Dinner',
    'forum': 'Global Economic Forum',
  };

  const formattedEvents = events.map(event => eventMap[event] || event);

  if (formattedEvents.length === 1) {
    return formattedEvents[0];
  } else if (formattedEvents.length === 2) {
    return `${formattedEvents[0]} and ${formattedEvents[1]}`;
  } else {
    const lastEvent = formattedEvents.pop();
    return `${formattedEvents.join(', ')}, and ${lastEvent}`;
  }
}
