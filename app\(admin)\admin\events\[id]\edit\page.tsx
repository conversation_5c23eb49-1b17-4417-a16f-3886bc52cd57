'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar as CalendarIcon, Loader2, Upload, AlertCircle } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { useEventQuery, useUpdateEventMutation, eventKeys } from '@/lib/hooks/useEvents';
import { logError } from '@/lib/errorHandling';
import SeoForm from '@/components/admin/SeoForm';

interface Event {
  _id: string;
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    seoKeywords?: string[];
    nofollowAttributes?: boolean;
    openGraph?: {
      title?: string;
      description?: string;
      image?: any;
    };
    twitter?: {
      title?: string;
      description?: string;
      cardType?: string;
    };
  };
}

export default function EditEventPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use()
  const unwrappedParams = React.use(params);
  const eventId = unwrappedParams.id;

  const router = useRouter();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [event, setEvent] = useState<Event | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageAlt, setImageAlt] = useState('');

  // Form state
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [isCountdownTarget, setIsCountdownTarget] = useState(false);
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [showRsvp, setShowRsvp] = useState(true);
  const [eventType, setEventType] = useState('other');
  const [order, setOrder] = useState(0);
  const [seoData, setSeoData] = useState({
    metaTitle: '',
    metaDescription: '',
    seoKeywords: [],
    nofollowAttributes: false,
    openGraph: {
      title: '',
      description: '',
      image: null
    },
    twitter: {
      title: '',
      description: '',
      cardType: 'summary_large_image'
    }
  });

  // Use React Query to fetch the event
  const { data: eventData, isLoading: isEventLoading, error: eventError } = useEventQuery(eventId);

  // Use the update event mutation
  const updateEventMutation = useUpdateEventMutation();

  // Set loading state based on query loading state
  useEffect(() => {
    setIsLoading(isEventLoading);
  }, [isEventLoading]);

  // Show error toast if there's an error
  useEffect(() => {
    if (eventError) {
      logError(eventError, 'fetchEvent');
      toast.error('Failed to load event details');
    }
  }, [eventError]);

  // Populate form fields when event data is loaded
  useEffect(() => {
    if (eventData) {
      setEvent(eventData);

      // Populate form fields
      setTitle(eventData.title || '');
      setSlug(eventData.slug?.current || '');
      setDate(eventData.date ? new Date(eventData.date) : undefined);
      setEndDate(eventData.endDate ? new Date(eventData.endDate) : undefined);
      setLocation(eventData.location || '');
      setDescription(eventData.description || '');
      setIsCountdownTarget(eventData.isCountdownTarget || false);
      setIsHighlighted(eventData.isHighlighted || false);
      setShowRsvp(eventData.showRsvp !== undefined ? eventData.showRsvp : true);
      setEventType(eventData.eventType || 'other');
      setOrder(eventData.order || 0);

      // Set image preview if available
      if (eventData.imageUrl) {
        setImagePreview(eventData.imageUrl);
        setImageAlt(eventData.imageAlt || '');
      }

      // Set SEO data if available
      if (eventData.seo) {
        setSeoData(eventData.seo);
      }
    }
  }, [eventData]);

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    console.log('Image file selected:', file.name, file.type, file.size);
    setImageFile(file);

    // Create a preview URL
    const reader = new FileReader();
    reader.onloadend = () => {
      const result = reader.result as string;
      console.log('Image preview created');
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title || !date || !location || !description) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSaving(true);

    try {
      // Format the event data for Sanity
      const eventData = {
        title,
        slug: { _type: 'slug', current: slug },
        date: date?.toISOString(),
        endDate: endDate?.toISOString(),
        location,
        description,
        imageAlt,
        isCountdownTarget,
        isHighlighted,
        showRsvp,
        eventType,
        order,
        seo: seoData,
      };

      console.log(`Updating event with ID: ${eventId}`, eventData);

      // First update the basic event data via the API
      const updateResponse = await fetch(`/api/events/${eventId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to update event');
      }

      console.log('Basic event data updated successfully');

      // If there's a new image file, upload it via the server-side API route
      if (imageFile) {
        try {
          console.log('Attempting to upload image file:', imageFile.name);

          // Create a FormData object to send the image file
          const formData = new FormData();
          formData.append('image', imageFile);
          formData.append('alt', imageAlt || title);

          console.log('Uploading image via API route...');

          // Upload the image through our server-side API route
          const imageUploadResponse = await fetch(`/api/events/${eventId}/image`, {
            method: 'POST',
            body: formData,
          });

          if (!imageUploadResponse.ok) {
            const errorData = await imageUploadResponse.json();
            throw new Error(errorData.error || 'Failed to upload image');
          }

          const imageResult = await imageUploadResponse.json();
          console.log('Image uploaded successfully:', imageResult);

          if (!imageResult.imageUrl) {
            throw new Error('No image URL returned from server');
          }

          // No need to patch the event document separately as the API route will handle that

          console.log('Image upload successful');
          toast.success('Image uploaded successfully');
        } catch (imageError) {
          console.error('Failed to upload image:', imageError);
          toast.error('Event updated but image upload failed');
        }
      }

      // Add a small delay before navigation to ensure Sanity has time to process the updates
      toast.success('Event updated successfully');

      // Force a refresh of the query cache to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      await queryClient.invalidateQueries({ queryKey: eventKeys.detail(eventId) });

      setTimeout(() => {
        router.push('/admin/events');
      }, 1000);
    } catch (error) {
      // Error is handled by the mutation's onError callback
      logError(error, 'updateEvent');
    } finally {
      setIsSaving(false);
    }
  };

  const generateSlug = () => {
    if (!title) return;

    const slugified = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    setSlug(slugified);
  };

  // Handle SEO data changes
  const handleSeoChange = (newSeoData: any) => {
    setSeoData(newSeoData);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2">Loading event details...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Edit Event</h1>
        <Button variant="outline" onClick={() => router.push('/admin/events')}>
          Cancel
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Event Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Event Details</TabsTrigger>
                <TabsTrigger value="seo">SEO & Social Sharing</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    onBlur={() => !slug && generateSlug()}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug *</Label>
                  <div className="flex gap-2">
                    <Input
                      id="slug"
                      value={slug}
                      onChange={(e) => setSlug(e.target.value)}
                      required
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={generateSlug}
                      className="shrink-0"
                    >
                      Generate
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date">Event Date & Time *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !date && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, "PPP p") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={setDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date & Time (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "PPP p") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="eventType">Event Type *</Label>
                  <Select value={eventType} onValueChange={setEventType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select event type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ceremony">Ceremony</SelectItem>
                      <SelectItem value="dinner">Dinner</SelectItem>
                      <SelectItem value="conference">Conference</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">Event Image</Label>
                  <div className="grid gap-4">
                    <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-lg border border-border">
                      {imagePreview ? (
                        // Show the new image preview if a file is selected
                        <Image
                          src={imagePreview}
                          alt={imageAlt || title}
                          fill
                          className="object-cover"
                        />
                      ) : event?.imageUrl ? (
                        // Show the existing image if available
                        <Image
                          src={event.imageUrl}
                          alt={imageAlt || title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        // Show "No image" message if no image is available
                        <div className="flex items-center justify-center h-full bg-gray-100">
                          <p className="text-gray-400">No image</p>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <Label htmlFor="imageAlt">Image Alt Text</Label>
                      <Input
                        id="imageAlt"
                        value={imageAlt}
                        onChange={(e) => setImageAlt(e.target.value)}
                        placeholder="Descriptive text for the image"
                      />
                    </div>
                    <div className="flex items-center gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex gap-2"
                        onClick={() => document.getElementById('imageUpload')?.click()}
                      >
                        <Upload className="h-4 w-4" />
                        {imagePreview ? 'Change Image' : 'Upload Image'}
                      </Button>
                      <input
                        type="file"
                        id="imageUpload"
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                      {imagePreview && (
                        <p className="text-sm text-muted-foreground">
                          Image will be updated when you save changes
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isCountdownTarget"
                      checked={isCountdownTarget}
                      onCheckedChange={(checked) => setIsCountdownTarget(checked === true)}
                    />
                    <Label htmlFor="isCountdownTarget">Use as Countdown Target</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isHighlighted"
                      checked={isHighlighted}
                      onCheckedChange={(checked) => setIsHighlighted(checked === true)}
                    />
                    <Label htmlFor="isHighlighted">Highlight Event</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="showRsvp"
                      checked={showRsvp}
                      onCheckedChange={(checked) => setShowRsvp(checked === true)}
                    />
                    <Label htmlFor="showRsvp">Show RSVP Button</Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="order">Display Order</Label>
                  <Input
                    id="order"
                    type="number"
                    value={order}
                    onChange={(e) => setOrder(parseInt(e.target.value) || 0)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Events with lower numbers will be displayed first.
                  </p>
                </div>

                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push('/admin/events')}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="seo" className="pt-4">
                <SeoForm
                  title={title}
                  description={description}
                  slug={slug}
                  image={imagePreview || event?.imageUrl}
                  contentType="event"
                  seoData={seoData}
                  onSeoChange={handleSeoChange}
                  onImageUpload={async (file) => {
                    try {
                      // Create FormData for image upload
                      const formData = new FormData();
                      formData.append('image', file);
                      formData.append('alt', title || 'Event image');

                      // Upload the image through our server-side API route
                      const imageUploadResponse = await fetch(`/api/upload`, {
                        method: 'POST',
                        body: formData,
                      });

                      if (!imageUploadResponse.ok) {
                        const errorData = await imageUploadResponse.json();
                        throw new Error(errorData.error || 'Failed to upload image');
                      }

                      const imageResult = await imageUploadResponse.json();
                      console.log('SEO Image uploaded successfully:', imageResult);

                      if (!imageResult.asset) {
                        throw new Error('No image asset returned from server');
                      }

                      // Return the image asset reference
                      return {
                        _type: 'image',
                        asset: {
                          _type: 'reference',
                          _ref: imageResult.asset._id
                        }
                      };
                    } catch (error) {
                      console.error('Error uploading SEO image:', error);
                      throw error;
                    }
                  }}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
