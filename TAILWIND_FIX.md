# Tailwind CSS Fix for Heroku Deployment

This document explains the fix for the "Cannot find module 'tailwindcss'" error during Heroku deployment.

## The Problem

When deploying to Heroku, the build process was failing with the following error:

```
Failed to compile.
app/(admin)/layout.tsx
An error occurred in `next/font`.
Error: Cannot find module 'tailwindcss'
Require stack:
- /tmp/build_4fe0cb6e/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js
...
```

This error occurs because Hero<PERSON> doesn't install devDependencies by default in production builds, but tailwindcss was listed in devDependencies.

## The Solution

The solution was to move tailwindcss and related packages from devDependencies to dependencies in package.json:

```json
"dependencies": {
  // ... other dependencies
  "autoprefixer": "^10.4.18",
  "postcss": "^8.4.35",
  "tailwindcss": "^3.4.1",
  // ... other dependencies
},
"devDependencies": {
  // ... other devDependencies (without tailwindcss, autoprefixer, and postcss)
}
```

This ensures that tailwindcss is installed during the Heroku build process.

## Additional Steps

1. We also updated the heroku-build.js file to remove the separate installation of tailwindcss since it's now in dependencies.

2. We made sure that the postcss.config.js file is correctly configured:

```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

3. We verified that the tailwind.config.js file is correctly configured.

## Why This Works

Heroku's build process has two phases:
1. **Install dependencies**: Only packages listed in the dependencies section of package.json are installed.
2. **Build the application**: The build script is run.

By moving tailwindcss from devDependencies to dependencies, we ensure that it's installed during the first phase, so it's available during the build phase.

## Testing

After making these changes, the Heroku build should succeed without the "Cannot find module 'tailwindcss'" error.

## References

- [Heroku Node.js Support](https://devcenter.heroku.com/articles/nodejs-support)
- [Tailwind CSS Installation](https://tailwindcss.com/docs/installation)
- [Next.js with Tailwind CSS](https://nextjs.org/docs/app/building-your-application/styling/tailwind-css)
