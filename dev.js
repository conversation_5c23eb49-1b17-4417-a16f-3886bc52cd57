// Simple development server script
const { execSync } = require('child_process');

console.log('Starting Next.js development server...');

try {
  // Run the Next.js development server
  execSync('npx next dev', {
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_OPTIONS: '--no-warnings'
    }
  });
} catch (error) {
  console.error('Error running Next.js development server:', error);
  process.exit(1);
}
