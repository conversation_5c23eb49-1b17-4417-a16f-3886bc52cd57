'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronLeft, ShoppingCart, Heart, Share2 } from 'lucide-react';
import { CartItem, loadCart, addToCart as addItemToCart } from '@/lib/cart';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface Product {
  _id: string;
  name: string;
  description: string;
  longDescription?: any[];
  price: number;
  compareAtPrice?: number;
  currency: string;
  images: any[];
  category: {
    _id: string;
    title: string;
    slug: { current: string };
  };
  tags: string[];
  featured: boolean;
  inventory: {
    quantity: number;
    trackInventory: boolean;
    allowBackorder: boolean;
  };
  variants?: any[];
  isDigital: boolean;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

export default function ProductPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setIsLoading(true);

        try {
          // Fetch product from Sanity API
          const response = await fetch(`/api/store/products/${params.slug}`);

          if (!response.ok) {
            throw new Error('Product not found');
          }

          const data = await response.json();

          if (data.success && data.product) {
            setProduct(data.product);
          } else {
            throw new Error(data.message || 'Failed to load product');
          }
        } catch (apiError) {
          console.error('Error fetching from API:', apiError);

          // Fallback to sample products if API fails
          const sampleProducts = {
            'royal-crown-tshirt': {
              _id: 'royal-crown-tshirt',
              name: 'Royal Crown T-Shirt',
              description: 'Premium cotton t-shirt featuring the royal crown emblem of Adukrom Kingdom.',
              price: 29.99,
              currency: 'USD',
              images: [{ asset: { _ref: '' }, url: '/Website Images/tshirt-mockup.png' }],
              category: { _id: 'apparel', title: 'Apparel', slug: { current: 'apparel' } },
              tags: ['clothing', 'royal', 'merchandise'],
              featured: true,
              inventory: { quantity: 25, trackInventory: true, allowBackorder: false },
              isDigital: false
            },
            'adukrom-hoodie': {
              _id: 'adukrom-hoodie',
              name: 'Adukrom Kingdom Hoodie',
              description: 'Comfortable hoodie with the Adukrom Kingdom logo embroidered on the front.',
              price: 49.99,
              currency: 'USD',
              images: [{ asset: { _ref: '' }, url: '/Website Images/hoodie-mockup.png' }],
              category: { _id: 'apparel', title: 'Apparel', slug: { current: 'apparel' } },
              tags: ['clothing', 'royal', 'merchandise', 'hoodie'],
              featured: true,
              inventory: { quantity: 15, trackInventory: true, allowBackorder: false },
              isDigital: false
            },
            'royal-mug': {
              _id: 'royal-mug',
              name: 'Royal Crest Mug',
              description: 'Ceramic mug featuring the royal crest of King Allen Ellison.',
              price: 19.99,
              currency: 'USD',
              images: [{ asset: { _ref: '' }, url: '/Website Images/mug-mockup.png' }],
              category: { _id: 'accessories', title: 'Accessories', slug: { current: 'accessories' } },
              tags: ['mug', 'royal', 'merchandise', 'kitchen'],
              featured: false,
              inventory: { quantity: 30, trackInventory: true, allowBackorder: true },
              isDigital: false
            },
            'commemorative-plate': {
              _id: 'commemorative-plate',
              name: 'Coronation Commemorative Plate',
              description: 'Limited edition commemorative plate celebrating the coronation of King Allen Ellison.',
              price: 79.99,
              currency: 'USD',
              images: [{ asset: { _ref: '' }, url: '/Website Images/plate-mockup.png' }],
              category: { _id: 'collectibles', title: 'Collectibles', slug: { current: 'collectibles' } },
              tags: ['plate', 'royal', 'merchandise', 'collectible', 'limited edition'],
              featured: true,
              inventory: { quantity: 10, trackInventory: true, allowBackorder: false },
              isDigital: false
            }
          };

          // Check if the requested product exists in our sample data
          if (sampleProducts[params.slug]) {
            setProduct(sampleProducts[params.slug]);
          } else {
            throw new Error('Product not found');
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching product:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [params.slug]);

  // Load cart from localStorage
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (err) {
        console.error('Error parsing cart from localStorage:', err);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cart));
  }, [cart]);

  // Handle quantity change
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < 1) return;
    if (product?.inventory?.trackInventory && newQuantity > (product?.inventory?.quantity || 0)) {
      if (!product?.inventory?.allowBackorder) {
        return; // Don't allow exceeding inventory if backorders not allowed
      }
    }
    setQuantity(newQuantity);
  };

  // Add to cart function
  const addToCart = () => {
    if (!product) return;

    setIsAddingToCart(true);

    setTimeout(() => {
      setCart(prevCart => {
        // Check if product is already in cart
        const existingItemIndex = prevCart.findIndex(item => item.id === product._id);

        if (existingItemIndex >= 0) {
          // Update quantity if already in cart
          const updatedCart = [...prevCart];
          updatedCart[existingItemIndex].quantity += quantity;
          return updatedCart;
        } else {
          // Add new item to cart
          return [...prevCart, {
            id: product._id,
            name: product.name,
            price: product.price,
            quantity: quantity,
            image: getProductImageUrl(product.images[0])
          }];
        }
      });

      setIsAddingToCart(false);

      // Show success message or open cart
      alert('Product added to cart!');
    }, 500);
  };

  // Helper function to get image URL from Sanity image object
  const getProductImageUrl = (image: any) => {
    if (!image) return '/Website Images/placeholder.png';

    if (image.asset && image.asset._ref) {
      return `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt'}/${process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'}/${image.asset._ref
        .replace('image-', '')
        .replace('-jpg', '.jpg')
        .replace('-png', '.png')
        .replace('-webp', '.webp')}`;
    }

    return image.url || '/Website Images/placeholder.png';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-ivory">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
        <p className="ml-3 text-royalBlue">Loading product...</p>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-ivory">
        <div className="text-center max-w-md p-8 bg-white rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold text-royalBlue mb-4">Product Not Found</h1>
          <p className="text-charcoal mb-6">{error || 'The requested product could not be found.'}</p>
          <Link href="/store" className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-4 rounded-full hover:bg-yellow-500 transition-colors">
            Return to Store
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-ivory">
        <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center text-royalBlue hover:text-royalGold transition-colors"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back to Store
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative h-96 bg-white rounded-lg overflow-hidden shadow-md">
              <Image
                src={getProductImageUrl(product.images[selectedImage])}
                alt={product.name}
                fill
                className="object-contain"
                unoptimized={getProductImageUrl(product.images[selectedImage]).includes('cdn.sanity.io')}
              />
            </div>

            {/* Thumbnail Gallery */}
            {product.images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`relative w-20 h-20 rounded-md overflow-hidden border-2 ${
                      selectedImage === index ? 'border-royalGold' : 'border-transparent'
                    }`}
                  >
                    <Image
                      src={getProductImageUrl(image)}
                      alt={`${product.name} - Image ${index + 1}`}
                      fill
                      className="object-cover"
                      unoptimized={getProductImageUrl(image).includes('cdn.sanity.io')}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-royalBlue">{product.name}</h1>

              {product.category && (
                <Link
                  href={`/store?category=${product.category.title.toLowerCase()}`}
                  className="text-sm text-royalGold hover:underline"
                >
                  {product.category.title}
                </Link>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-royalBlue">
                ${product.price.toFixed(2)}
              </span>

              {product.compareAtPrice && product.compareAtPrice > product.price && (
                <span className="text-lg text-gray-500 line-through">
                  ${product.compareAtPrice.toFixed(2)}
                </span>
              )}
            </div>

            <div className="prose text-charcoal">
              <p>{product.description}</p>
            </div>

            {/* Inventory Status */}
            <div className="text-sm">
              {product.inventory?.trackInventory ? (
                product.inventory.quantity > 0 ? (
                  <span className="text-green-600">
                    In Stock ({product.inventory.quantity} available)
                  </span>
                ) : product.inventory.allowBackorder ? (
                  <span className="text-amber-600">
                    Backordered - Will ship when available
                  </span>
                ) : (
                  <span className="text-red-600">
                    Out of Stock
                  </span>
                )
              ) : (
                <span className="text-green-600">
                  In Stock
                </span>
              )}
            </div>

            {/* Quantity Selector */}
            <div className="flex items-center space-x-4">
              <span className="text-charcoal">Quantity:</span>
              <div className="flex items-center border border-gray-300 rounded-full overflow-hidden">
                <button
                  onClick={() => handleQuantityChange(quantity - 1)}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 transition-colors"
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="px-4 py-1">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(quantity + 1)}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  +
                </button>
              </div>
            </div>

            {/* Add to Cart Button */}
            <div className="flex space-x-4">
              <motion.button
                onClick={addToCart}
                className="flex-1 royal-button bg-royalGold text-royalBlue font-bold py-3 px-6 rounded-full hover:bg-yellow-500 transition-colors flex items-center justify-center"
                whileTap={{ scale: 0.95 }}
                disabled={isAddingToCart || (product.inventory?.trackInventory && product.inventory.quantity === 0 && !product.inventory.allowBackorder)}
              >
                {isAddingToCart ? (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-royalBlue mr-2"></div>
                    Adding...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <ShoppingCart className="w-5 h-5 mr-2" />
                    Add to Cart
                  </span>
                )}
              </motion.button>

              <motion.button
                className="p-3 rounded-full border border-gray-300 hover:border-royalGold text-charcoal hover:text-royalGold transition-colors"
                whileTap={{ scale: 0.95 }}
              >
                <Heart className="w-5 h-5" />
              </motion.button>

              <motion.button
                className="p-3 rounded-full border border-gray-300 hover:border-royalGold text-charcoal hover:text-royalGold transition-colors"
                whileTap={{ scale: 0.95 }}
              >
                <Share2 className="w-5 h-5" />
              </motion.button>
            </div>

            {/* Tags */}
            {product.tags && product.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 pt-4">
                {product.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-gray-100 text-charcoal text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    <Footer />
    </>
  );
}
