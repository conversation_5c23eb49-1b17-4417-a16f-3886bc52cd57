import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";

/**
 * Middleware for protecting routes
 *
 * This middleware runs before any request to routes that match the config.
 * It checks if the user is authenticated and has the appropriate role.
 */
export async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Check if the path starts with /admin (except /admin/login and /admin/studio)
  if (path.startsWith("/admin") &&
      !path.startsWith("/admin/login") &&
      !path.startsWith("/admin/studio")) {

    // Get the session token
    const session = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    console.log('Middleware checking path:', path);
    console.log('Session in middleware:', session);

    // Redirect to login if not authenticated
    if (!session) {
      console.log('No session found, redirecting to login');
      const url = new URL("/admin/login", req.url);
      url.searchParams.set("callbackUrl", encodeURI(path));
      return NextResponse.redirect(url);
    }

    // Check for admin or super_admin role
    const userRole = (session as any).role;
    if (userRole !== "admin" && userRole !== "super_admin") {
      console.log('User does not have admin privileges, redirecting to unauthorized');
      console.log('User role:', userRole);
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }

    console.log('Authentication successful, proceeding to admin area');
  }

  return NextResponse.next();
}

/**
 * Configure which routes the middleware runs on
 */
export const config = {
  matcher: ["/admin/:path*"],
};
