/**
 * Admin role types for the application
 */

export enum AdminRole {
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export interface AdminUser {
  id: string;
  username: string;
  role: AdminRole;
  name?: string;
  email?: string;
  createdAt: Date;
  lastLogin?: Date;
}

/**
 * Permission definitions for different admin roles
 */
export const ROLE_PERMISSIONS = {
  [AdminRole.ADMIN]: {
    canManageContent: true,
    canManageEvents: true,
    canManageGallery: true,
    canManageNews: true,
    canManageRsvp: true,
    canManageSettings: false,
    canManageUsers: false,
    canManageStore: false,
    canManageBankingInfo: false,
  },
  [AdminRole.SUPER_ADMIN]: {
    canManageContent: true,
    canManageEvents: true,
    canManageGallery: true,
    canManageNews: true,
    canManageRsvp: true,
    canManageSettings: true,
    canManageUsers: true,
    canManageStore: true,
    canManageBankingInfo: true,
  }
};

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  user: AdminUser | null | undefined,
  permission: keyof typeof ROLE_PERMISSIONS[AdminRole]
): boolean {
  if (!user) return false;
  
  const rolePermissions = ROLE_PERMISSIONS[user.role];
  if (!rolePermissions) return false;
  
  return rolePermissions[permission] === true;
}

/**
 * Mock admin users for development
 */
export const MOCK_ADMIN_USERS: AdminUser[] = [
  {
    id: '1',
    username: 'admin',
    role: AdminRole.ADMIN,
    name: 'Regular Admin',
    email: '<EMAIL>',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date(),
  },
  {
    id: '2',
    username: 'superadmin',
    role: AdminRole.SUPER_ADMIN,
    name: 'Super Admin',
    email: '<EMAIL>',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date(),
  }
];

/**
 * Get a mock admin user by username (for development)
 */
export function getMockAdminUser(username: string): AdminUser | undefined {
  return MOCK_ADMIN_USERS.find(user => user.username === username);
}
