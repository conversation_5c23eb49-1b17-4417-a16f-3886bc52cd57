/**
 * Sanity Studio Dashboard Configuration
 * 
 * This file configures the dashboard widgets that appear in the Sanity Studio.
 */

export default {
  widgets: [
    // Display a welcome message
    {
      name: 'document-list',
      options: {
        title: 'Welcome to Kingdom Adukrom',
        subtitle: 'Recent content updates',
        showCreateButton: true,
        limit: 5,
        types: ['page', 'news', 'event', 'gallery', 'product'],
        order: '_updatedAt desc'
      }
    },
    
    // Display recent news articles
    {
      name: 'document-list',
      options: {
        title: 'Latest News',
        subtitle: 'Recently published news articles',
        showCreateButton: true,
        limit: 5,
        types: ['news'],
        order: 'publishedAt desc',
        createButtonText: 'Create News Article'
      }
    },
    
    // Display upcoming events
    {
      name: 'document-list',
      options: {
        title: 'Upcoming Events',
        subtitle: 'Events with future dates',
        showCreateButton: true,
        limit: 5,
        types: ['event'],
        order: 'date asc',
        filter: 'date > now()',
        createButtonText: 'Create Event'
      }
    },
    
    // Display recent gallery items
    {
      name: 'document-list',
      options: {
        title: 'Gallery Updates',
        subtitle: 'Recently added gallery items',
        showCreateButton: true,
        limit: 5,
        types: ['gallery'],
        order: '_createdAt desc',
        createButtonText: 'Add Gallery Item'
      }
    },
    
    // Display recent products
    {
      name: 'document-list',
      options: {
        title: 'Store Products',
        subtitle: 'Recently added or updated products',
        showCreateButton: true,
        limit: 5,
        types: ['product'],
        order: '_updatedAt desc',
        createButtonText: 'Add Product'
      }
    },
    
    // Project info widget
    {
      name: 'project-info',
      options: {
        data: [
          {
            title: 'GitHub Repository',
            value: 'https://github.com/joelgriiyo/kingdomadukrom',
            category: 'Code'
          },
          {
            title: 'Frontend',
            value: 'https://kingdomadukrom.com',
            category: 'apps'
          },
          {
            title: 'Admin Dashboard',
            value: '/admin',
            category: 'apps'
          }
        ]
      }
    },
    
    // Project users widget
    {
      name: 'project-users'
    }
  ]
}
