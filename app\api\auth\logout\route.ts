import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * API route to handle logout
 * This provides a direct way to clear auth cookies
 */
export async function GET() {
  try {
    // Get the cookie store
    const cookieStore = cookies();

    // Clear all auth-related cookies
    cookieStore.getAll().forEach(cookie => {
      if (cookie.name.includes('next-auth') || cookie.name.includes('__Secure-next-auth')) {
        cookieStore.delete(cookie.name);
      }
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Error during API logout:', error);

    // Return error response
    return NextResponse.json(
      { success: false, message: 'Failed to log out' },
      { status: 500 }
    );
  }
}
