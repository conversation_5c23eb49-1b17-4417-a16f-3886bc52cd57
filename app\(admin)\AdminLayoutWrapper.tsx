'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { useSanityUser } from '@/lib/hooks/useSanityUser';


import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Home,
  Settings,
  FileText,
  Image as ImageIcon,
  Link2,
  LogOut,
  Menu,
  X,
  User,
  ChevronDown,
  Calendar,
  Mail,
  ShoppingBag,
  CreditCard,
  Users,
  Shield,
  Tag,
  Package
} from 'lucide-react';
import { AdminRole } from '@/lib/types/admin';
import { isSuperAdmin } from '@/lib/auth-utils';

export default function AdminLayoutWrapper({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const { user: sanityUser, loading: sanityLoading } = useSanityUser();

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);



  // Check if user is super admin
  const userIsSuperAdmin = isSuperAdmin(session);



  // Simple effect to clean up URL parameters
  useEffect(() => {
    // Clean up URL parameters if they exist
    if (window.location.search) {
      const url = new URL(window.location.href);
      if (url.searchParams.has('t') || url.searchParams.has('refresh')) {
        url.searchParams.delete('t');
        url.searchParams.delete('refresh');
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [pathname]);

  useEffect(() => {
    // Update last login time if user is logged in with email
    if (session?.user?.id && session.user.email?.includes('@')) {
      // Update the last login time
      fetch('/api/auth/update-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
        }),
      }).catch(error => {
        console.error('Error updating last login time:', error);
      });
    }
  }, [session]);

  // Add secret keyboard shortcut for super admins
  useEffect(() => {
    if (!userIsSuperAdmin) return;

    // Secret keyboard shortcut: Alt+S+A
    let keys = { alt: false, s: false, a: false };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey) keys.alt = true;
      if (e.key === 's') keys.s = true;
      if (e.key === 'a') keys.a = true;

      // Check if all keys are pressed
      if (keys.alt && keys.s && keys.a) {
        router.push('/admin/super');
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (!e.altKey) keys.alt = false;
      if (e.key === 's') keys.s = false;
      if (e.key === 'a') keys.a = false;
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [userIsSuperAdmin, router]);

  // Show a minimal loading indicator that doesn't flash or disrupt the UI
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue/40"></div>
          <p className="mt-2 text-sm text-gray-500">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // If on login or logout page, just show the children without layout
  if (pathname.includes('/admin/login') || pathname.includes('/admin/logout')) {
    return (
      <div className="min-h-screen bg-background">
        {children}
      </div>
    );
  }

  // If not authenticated and not on login page, redirect to login silently
  if (status === 'unauthenticated' && !pathname.includes('/admin/login')) {
    // Use replace instead of push to avoid adding to history stack
    // This helps prevent redirect loops
    if (typeof window !== 'undefined') {
      console.log('Redirecting unauthenticated user to login page');
      window.location.href = '/admin/login';
      return null;
    }

    // Return a minimal loading state without the yellow background
    return (
      <div className="flex h-screen w-full items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue/40"></div>
          <p className="mt-2 text-sm text-gray-500">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Define navigation items based on user role
  const commonNavItems = [
    { name: 'Dashboard', href: '/admin', icon: <Home className="h-5 w-5" /> },
    { name: 'News', href: '/admin/news', icon: <FileText className="h-5 w-5" /> },
    { name: 'Categories', href: '/admin/categories', icon: <Tag className="h-5 w-5" /> },
    { name: 'Gallery', href: '/admin/gallery', icon: <ImageIcon className="h-5 w-5" /> },
    { name: 'Events', href: '/admin/events', icon: <Calendar className="h-5 w-5" /> },
    { name: 'RSVP', href: '/admin/rsvp', icon: <Mail className="h-5 w-5" /> },
    { name: 'Partners', href: '/admin/partners', icon: <Link2 className="h-5 w-5" /> },
  ];

  // Super admin only items (hidden from regular admins)
  const superAdminItems = userIsSuperAdmin ? [
    { name: 'Store', href: '/admin/store', icon: <ShoppingBag className="h-5 w-5" /> },
    { name: 'Orders', href: '/admin/store/orders', icon: <Package className="h-5 w-5" /> },
    { name: 'Banking', href: '/admin/banking', icon: <CreditCard className="h-5 w-5" /> },
    { name: 'Users', href: '/admin/users', icon: <Users className="h-5 w-5" /> },
  ] : [];

  // Settings is available to all but with different permissions
  const settingsItem = { name: 'Settings', href: '/admin/settings', icon: <Settings className="h-5 w-5" /> };

  // We don't need to combine nav items since we're using them separately in the UI

  const isActive = (path: string) => {
    // Exact match for dashboard
    if (path === '/admin' && pathname === '/admin') {
      return true;
    }

    // Special case for store and its sub-routes
    if (path === '/admin/store' && pathname.startsWith('/admin/store/')) {
      // Don't highlight parent "Store" when on a specific sub-route like "Orders"
      return pathname === '/admin/store';
    }

    // For all other routes
    return pathname.startsWith(path) && path !== '/admin';
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Admin Header */}
      <header className="sticky top-0 z-50 w-full border-b border-royal-gold/30 bg-royalBlue/95 backdrop-blur supports-[backdrop-filter]:bg-royalBlue/80 text-white">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-white hover:bg-royalBlue/50 hover:text-royalGold"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
              <span className="sr-only">Toggle menu</span>
            </Button>
            <Link href="/admin" className="flex items-center gap-2">
              <Image
                src="/images/ai-logo1a.png"
                alt="Kingdom Admin"
                width={40}
                height={40}
                className="rounded-md border-2 border-royalGold/50"
              />
              <div className="hidden sm:flex flex-col">
                <span className="font-bold text-xl text-white">Kingdom Admin</span>
                <span className="text-xs text-white/70 welcome-message">
                  {sanityUser?.name ? `Welcome, ${sanityUser.name}` : (session?.user?.name ? `Welcome, ${session.user.name}` : 'Admin Dashboard')}
                </span>
              </div>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-1 text-white hover:bg-royalBlue/50 hover:text-royalGold">
                  <User className="h-5 w-5" />
                  <span className="hidden sm:inline-block">
                    <span className="user-name-display">{sanityUser?.name || session?.user?.name || 'Admin'}</span>
                    {userIsSuperAdmin && (
                      <span className="ml-1 text-xs bg-royalGold/80 text-white px-1 py-0.5 rounded">
                        Super
                      </span>
                    )}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-royalGold/30">
                <DropdownMenuLabel className="text-royalBlue font-semibold">
                  <span className="user-name-display">{sanityUser?.name || session?.user?.name || 'Admin'}</span>
                  {userIsSuperAdmin && (
                    <span className="ml-1 text-xs bg-royalGold/80 text-white px-1 py-0.5 rounded">
                      Super Admin
                    </span>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin/profile" className="hover:text-royalBlue">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/admin/settings" className="hover:text-royalBlue">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                {/* Super Admin Only: Manage Users */}
                {userIsSuperAdmin && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin/users" className="hover:text-royalBlue">
                      <Users className="mr-2 h-4 w-4" />
                      Manage Users
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => signOut()}
                  className="text-destructive cursor-pointer"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenuItem>
                {userIsSuperAdmin && (
                  <DropdownMenuItem
                    onClick={() => {
                      // Direct logout via API route
                      fetch('/api/auth/logout')
                        .then(() => window.location.href = '/admin/login')
                        .catch(err => console.error('Logout error:', err));
                    }}
                    className="text-destructive cursor-pointer"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Force Logout
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar for desktop */}
        <aside className="hidden md:flex w-64 flex-col border-r border-royalGold/20 bg-royalBlue/10">
          <nav className="flex-1 space-y-1 px-2 py-4">
            {/* Common nav items */}
            {commonNavItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive(item.href)
                    ? 'bg-royalBlue text-white'
                    : 'text-royalBlue hover:bg-royalBlue/20 hover:text-royalBlue'
                }`}
              >
                {item.icon}
                <span className="ml-3">{item.name}</span>
              </Link>
            ))}

            {/* Super admin items */}
            {userIsSuperAdmin && superAdminItems.length > 0 && (
              <>
                <div className="my-2 border-t border-royalGold/20 pt-2">
                  <p className="px-3 py-1 text-xs font-semibold text-royalBlue/70 uppercase tracking-wider">
                    Super Admin
                  </p>
                </div>
                {superAdminItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-royalGold text-white'
                        : 'text-royalBlue hover:bg-royalGold/20 hover:text-royalBlue'
                    }`}
                  >
                    {item.icon}
                    <span className="ml-3">{item.name}</span>
                  </Link>
                ))}
              </>
            )}

            {/* Account Settings */}
            <div className="my-2 border-t border-royalGold/20 pt-2">
              <p className="px-3 py-1 text-xs font-semibold text-royalBlue/70 uppercase tracking-wider">
                Account
              </p>
            </div>
            <Link
              href="/admin/profile"
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/admin/profile')
                  ? 'bg-royalBlue text-white'
                  : 'text-royalBlue hover:bg-royalBlue/20 hover:text-royalBlue'
              }`}
            >
              <User className="h-5 w-5" />
              <span className="ml-3">My Profile</span>
            </Link>
            <Link
              href={settingsItem.href}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive(settingsItem.href)
                  ? 'bg-royalBlue text-white'
                  : 'text-royalBlue hover:bg-royalBlue/20 hover:text-royalBlue'
              }`}
            >
              {settingsItem.icon}
              <span className="ml-3">{settingsItem.name}</span>
            </Link>
          </nav>
          <div className="border-t border-royalGold/20 p-4">
            <Button
              variant="outline"
              className="w-full justify-start border-royalBlue/30 text-royalBlue hover:bg-royalBlue/10 hover:text-royalBlue"
              onClick={() => signOut()}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </aside>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-40 md:hidden">
            <div className="fixed inset-0 bg-black/50" onClick={() => setIsMobileMenuOpen(false)} />
            <div className="fixed inset-y-0 left-0 w-64 bg-white p-4 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <Link href="/admin" className="flex items-center gap-2">
                  <Image
                    src="/images/ai-logo1a.png"
                    alt="Kingdom Admin"
                    width={40}
                    height={40}
                    className="rounded-md border-2 border-royalGold/50"
                  />
                  <div className="flex flex-col">
                    <span className="font-bold text-xl text-royalBlue">Kingdom</span>
                    <span className="text-xs text-royalBlue/70 welcome-message">
                      {sanityUser?.name ? `Welcome, ${sanityUser.name}` : (session?.user?.name ? `Welcome, ${session.user.name}` : 'Admin Dashboard')}
                    </span>
                  </div>
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-royalBlue hover:bg-royalBlue/10"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
              <nav className="space-y-1">
                {/* Common nav items */}
                {commonNavItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-royalBlue text-white'
                        : 'text-royalBlue hover:bg-royalBlue/10'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.icon}
                    <span className="ml-3">{item.name}</span>
                  </Link>
                ))}

                {/* Super admin items */}
                {userIsSuperAdmin && (
                  <>
                    <div className="my-2 border-t border-gray-200 pt-2">
                      <p className="px-3 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        Super Admin
                      </p>
                    </div>
                    {superAdminItems.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          isActive(item.href)
                            ? 'bg-royalBlue text-white'
                            : 'text-royalBlue hover:bg-royalBlue/10'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.icon}
                        <span className="ml-3">{item.name}</span>
                      </Link>
                    ))}
                  </>
                )}

                {/* Profile & Settings */}
                <div className="my-2 border-t border-gray-200 pt-2">
                  <p className="px-3 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Account
                  </p>
                </div>
                <Link
                  href="/admin/profile"
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive('/admin/profile')
                      ? 'bg-royalBlue text-white'
                      : 'text-royalBlue hover:bg-royalBlue/10'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <User className="h-5 w-5" />
                  <span className="ml-3">My Profile</span>
                </Link>
                <Link
                  href={settingsItem.href}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive(settingsItem.href)
                      ? 'bg-royalBlue text-white'
                      : 'text-royalBlue hover:bg-royalBlue/10'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {settingsItem.icon}
                  <span className="ml-3">{settingsItem.name}</span>
                </Link>
              </nav>
              <div className="absolute bottom-0 left-0 right-0 border-t border-royalGold/20 p-4">
                <Button
                  variant="outline"
                  className="w-full justify-start border-royalBlue/30 text-royalBlue hover:bg-royalBlue/10"
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    signOut();
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Main content */}
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
