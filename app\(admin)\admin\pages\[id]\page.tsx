'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getWriteClient } from '@/lib/sanity.client';
import { Save, ArrowLeft, Plus, Trash, MoveUp, MoveDown, Image as ImageIcon, Type, Layout, FileText, Edit } from 'lucide-react';
import SectionEditorModal from '../../components/SectionEditorModal';

interface PageParams {
  params: {
    id: string;
  };
}

export default function PageEditor({ params }: PageParams) {
  const { id } = params;
  const router = useRouter();
  const [page, setPage] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [editingSection, setEditingSection] = useState<number | null>(null);
  const [isAddingSection, setIsAddingSection] = useState(false);
  const [newSectionType, setNewSectionType] = useState('textSection');
  const [newSectionHeading, setNewSectionHeading] = useState('');

  // Fetch page data
  useEffect(() => {
    const fetchPage = async () => {
      try {
        setIsLoading(true);
        const client = getWriteClient();
        const pageData = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });

        if (!pageData) {
          toast.error('Page not found');
          router.push('/admin/pages');
          return;
        }

        setPage(pageData);

        // Check if there's a section parameter in the URL
        const searchParams = new URLSearchParams(window.location.search);
        const sectionParam = searchParams.get('section');

        if (sectionParam && !isNaN(parseInt(sectionParam))) {
          const sectionIndex = parseInt(sectionParam);
          if (pageData.pageBuilder && sectionIndex >= 0 && sectionIndex < pageData.pageBuilder.length) {
            // Set the editing section
            setEditingSection(sectionIndex);
          }
        }
      } catch (error) {
        console.error('Error fetching page:', error);
        toast.error('Failed to load page');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchPage();
    }
  }, [id, router]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPage((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle slug change
  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setPage((prev: any) => ({
      ...prev,
      slug: {
        ...prev.slug,
        current: value,
      },
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setPage((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setPage((prev: any) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const client = getWriteClient();

      // Update the page
      await client.patch(id)
        .set({
          title: page.title,
          slug: page.slug,
          description: page.description,
          navMenu: page.navMenu,
          navOrder: page.navOrder,
          accessLevel: page.accessLevel,
          updatedAt: new Date().toISOString(),
        })
        .commit();

      toast.success('Page saved successfully');
    } catch (error) {
      console.error('Error saving page:', error);
      toast.error('Failed to save page');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle section reordering
  const handleMoveSection = async (index: number, direction: 'up' | 'down') => {
    if (!page.pageBuilder) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Check if the new index is valid
    if (newIndex < 0 || newIndex >= page.pageBuilder.length) return;

    // Create a copy of the page builder array
    const newPageBuilder = [...page.pageBuilder];

    // Swap the sections
    [newPageBuilder[index], newPageBuilder[newIndex]] = [newPageBuilder[newIndex], newPageBuilder[index]];

    // Update the page
    setPage((prev: any) => ({
      ...prev,
      pageBuilder: newPageBuilder,
    }));

    try {
      const client = getWriteClient();

      // Update the page in Sanity
      await client.patch(id)
        .set({ pageBuilder: newPageBuilder })
        .commit();

      toast.success('Section order updated');
    } catch (error) {
      console.error('Error updating section order:', error);
      toast.error('Failed to update section order');
    }
  };

  // Handle section deletion
  const handleDeleteSection = async (index: number) => {
    if (!page.pageBuilder) return;

    if (!confirm('Are you sure you want to delete this section? This action cannot be undone.')) {
      return;
    }

    // Create a copy of the page builder array
    const newPageBuilder = [...page.pageBuilder];

    // Remove the section
    newPageBuilder.splice(index, 1);

    // Update the page
    setPage((prev: any) => ({
      ...prev,
      pageBuilder: newPageBuilder,
    }));

    try {
      const client = getWriteClient();

      // Update the page in Sanity
      await client.patch(id)
        .set({ pageBuilder: newPageBuilder })
        .commit();

      toast.success('Section deleted');
    } catch (error) {
      console.error('Error deleting section:', error);
      toast.error('Failed to delete section');
    }
  };

  // Handle section save
  const handleSectionSave = async (sectionData: any, index: number | null) => {
    try {
      const client = getWriteClient();

      // Get the current page
      const currentPage = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });

      if (!currentPage) {
        toast.error('Page not found');
        return;
      }

      // Create a copy of the page builder array
      const pageBuilder = [...(currentPage.pageBuilder || [])];

      if (index !== null && index >= 0 && index < pageBuilder.length) {
        // Update existing section
        pageBuilder[index] = sectionData;
      } else {
        // Add new section
        pageBuilder.push(sectionData);
      }

      // Update the page
      await client.patch(id)
        .set({ pageBuilder })
        .commit();

      // Refresh the page data
      const updatedPage = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });
      setPage(updatedPage);

      toast.success('Section saved successfully');

      // Reset editing state
      setEditingSection(null);
      setIsAddingSection(false);
    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section');
    }
  };

  // Handle section cancel
  const handleSectionCancel = () => {
    setEditingSection(null);
    setIsAddingSection(false);
  };

  // Get section type display name
  const getSectionTypeName = (type: string) => {
    switch (type) {
      case 'hero':
        return 'Hero Section';
      case 'textSection':
        return 'Text Section';
      case 'imageGallery':
        return 'Image Gallery';
      case 'featuredContent':
        return 'Featured Content';
      case 'contactForm':
        return 'Contact Form';
      default:
        return type;
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-royalBlue mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading page...</p>
        </div>
      </div>
    );
  }

  if (!page) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-semibold">Page not found</p>
          <Button
            className="mt-4"
            onClick={() => router.push('/admin/pages')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Pages
          </Button>
        </div>
      </div>
    );
  }

  // We'll use the modal approach instead of replacing the entire page

  // Render inline section editor
  const renderInlineSectionEditor = () => {
    if (editingSection !== null && page.pageBuilder && editingSection >= 0 && editingSection < page.pageBuilder.length) {
      // Editing existing section
      const section = page.pageBuilder[editingSection];
      return (
        <Card className="mb-6" id="section-editor">
          <CardHeader>
            <CardTitle>Edit {getSectionTypeName(section._type)}</CardTitle>
            <CardDescription>Update the content of this section</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="heading">Heading</Label>
                <Input
                  id="heading"
                  value={section.heading || ''}
                  onChange={(e) => {
                    const updatedPageBuilder = [...page.pageBuilder];
                    updatedPageBuilder[editingSection] = {
                      ...section,
                      heading: e.target.value
                    };
                    setPage({...page, pageBuilder: updatedPageBuilder});
                  }}
                  placeholder="Enter heading"
                />
              </div>

              {section._type === 'hero' && (
                <div>
                  <Label htmlFor="tagline">Tagline</Label>
                  <Input
                    id="tagline"
                    value={section.tagline || ''}
                    onChange={(e) => {
                      const updatedPageBuilder = [...page.pageBuilder];
                      updatedPageBuilder[editingSection] = {
                        ...section,
                        tagline: e.target.value
                      };
                      setPage({...page, pageBuilder: updatedPageBuilder});
                    }}
                    placeholder="Enter tagline"
                  />
                </div>
              )}

              {(section._type === 'textSection' || section._type === 'imageGallery' || section._type === 'featuredContent') && (
                <div>
                  <Label htmlFor="text">Content</Label>
                  <Textarea
                    id="text"
                    value={typeof section.text === 'string' ? section.text : JSON.stringify(section.text || '')}
                    onChange={(e) => {
                      const updatedPageBuilder = [...page.pageBuilder];
                      updatedPageBuilder[editingSection] = {
                        ...section,
                        text: e.target.value
                      };
                      setPage({...page, pageBuilder: updatedPageBuilder});
                    }}
                    placeholder="Enter content"
                    rows={6}
                  />
                </div>
              )}

              {(section._type === 'textSection' || section._type === 'featuredContent') && (
                <div>
                  <Label htmlFor="backgroundStyle">Background Style</Label>
                  <Select
                    value={section.backgroundStyle || 'none'}
                    onValueChange={(value) => {
                      const updatedPageBuilder = [...page.pageBuilder];
                      updatedPageBuilder[editingSection] = {
                        ...section,
                        backgroundStyle: value
                      };
                      setPage({...page, pageBuilder: updatedPageBuilder});
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select background style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="royalBlue">Royal Blue</SelectItem>
                      <SelectItem value="royalGold">Royal Gold</SelectItem>
                      <SelectItem value="ivory">Ivory</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handleSectionCancel}>
              Cancel
            </Button>
            <Button onClick={() => handleSectionSave(section, editingSection)}>
              Save Section
            </Button>
          </CardFooter>
        </Card>
      );
    } else if (isAddingSection) {
      // Adding new section
      const createNewSection = () => {
        // Create a new section based on the selected type
        let newSection: any = {
          _type: newSectionType,
          heading: newSectionHeading,
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring'
          }
        };

        // Add type-specific properties
        switch (newSectionType) {
          case 'hero':
            newSection.tagline = '';
            newSection.ctas = [];
            break;
          case 'textSection':
            newSection.text = '';
            newSection.backgroundStyle = 'none';
            break;
          case 'imageGallery':
            newSection.text = '';
            newSection.images = [];
            break;
          case 'featuredContent':
            newSection.text = '';
            newSection.items = [];
            newSection.layout = 'cards';
            newSection.backgroundStyle = 'none';
            break;
          case 'contactForm':
            newSection.text = '';
            newSection.submitButtonText = 'Submit';
            newSection.successMessage = 'Thank you for your message. We will get back to you soon.';
            newSection.backgroundStyle = 'none';
            break;
        }

        return newSection;
      };

      return (
        <Card className="mb-6" id="section-editor">
          <CardHeader>
            <CardTitle>Add New Section</CardTitle>
            <CardDescription>Create a new section for your page</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="sectionType">Section Type</Label>
                <Select
                  value={newSectionType}
                  onValueChange={setNewSectionType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select section type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hero">Hero Section</SelectItem>
                    <SelectItem value="textSection">Text Section</SelectItem>
                    <SelectItem value="imageGallery">Image Gallery</SelectItem>
                    <SelectItem value="featuredContent">Featured Content</SelectItem>
                    <SelectItem value="contactForm">Contact Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="heading">Heading</Label>
                <Input
                  id="heading"
                  value={newSectionHeading}
                  onChange={(e) => setNewSectionHeading(e.target.value)}
                  placeholder="Enter heading"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handleSectionCancel}>
              Cancel
            </Button>
            <Button onClick={() => handleSectionSave(createNewSection(), null)}>
              Add Section
            </Button>
          </CardFooter>
        </Card>
      );
    }

    return null;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push('/admin/pages')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Pages
        </Button>

        <Button
          onClick={handleSubmit}
          disabled={isSaving}
        >
          {isSaving ? (
            <>Saving...</>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Page
            </>
          )}
        </Button>
      </div>

      {/* Render the inline section editor */}
      {renderInlineSectionEditor()}

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Edit Page: {page.title}</CardTitle>
            <CardDescription>
              Update page content and settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="seo">SEO</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">Page Title</Label>
                    <Input
                      id="title"
                      name="title"
                      value={page.title || ''}
                      onChange={handleInputChange}
                      placeholder="Enter page title"
                    />
                  </div>

                  <div>
                    <Label htmlFor="slug">Page Slug</Label>
                    <div className="flex items-center">
                      <span className="text-gray-500 mr-1">/</span>
                      <Input
                        id="slug"
                        name="slug"
                        value={page.slug?.current || ''}
                        onChange={handleSlugChange}
                        placeholder="page-slug"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      This will be the URL of your page: {page.slug?.current ? `/${page.slug.current}` : '/page-slug'}
                    </p>
                  </div>
                </div>

                <Separator className="my-6" />

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">Page Sections</h3>
                    <Button
                      type="button"
                      onClick={() => {
                        setIsAddingSection(true);
                        // Scroll to the editor
                        setTimeout(() => {
                          const editorElement = document.getElementById('section-editor');
                          if (editorElement) {
                            editorElement.scrollIntoView({ behavior: 'smooth' });
                          }
                        }, 100);
                      }}
                      variant="outline"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Section
                    </Button>
                  </div>

                  {(!page.pageBuilder || page.pageBuilder.length === 0) ? (
                    <div className="text-center p-8 border border-dashed rounded-md">
                      <Layout className="h-12 w-12 mx-auto text-gray-400" />
                      <h3 className="mt-2 text-lg font-medium">No sections yet</h3>
                      <p className="mt-1 text-gray-500">
                        Add sections to build your page
                      </p>
                      <Button
                        type="button"
                        onClick={() => {
                          setIsAddingSection(true);
                          // Scroll to the editor
                          setTimeout(() => {
                            const editorElement = document.getElementById('section-editor');
                            if (editorElement) {
                              editorElement.scrollIntoView({ behavior: 'smooth' });
                            }
                          }, 100);
                        }}
                        variant="outline"
                        className="mt-4"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add First Section
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {page.pageBuilder.map((section: any, index: number) => (
                        <Card key={index}>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-royalBlue/10 flex items-center justify-center mr-3">
                                  {section._type === 'hero' && <Layout className="h-4 w-4 text-royalBlue" />}
                                  {section._type === 'textSection' && <Type className="h-4 w-4 text-royalBlue" />}
                                  {section._type === 'imageGallery' && <ImageIcon className="h-4 w-4 text-royalBlue" />}
                                  {section._type === 'contactForm' && <FileText className="h-4 w-4 text-royalBlue" />}
                                </div>
                                <div>
                                  <p className="font-medium">{section.heading || 'Untitled Section'}</p>
                                  <p className="text-xs text-gray-500">{getSectionTypeName(section._type)}</p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Set the editing section
                                    setEditingSection(index);
                                    // Scroll to the editor
                                    setTimeout(() => {
                                      const editorElement = document.getElementById('section-editor');
                                      if (editorElement) {
                                        editorElement.scrollIntoView({ behavior: 'smooth' });
                                      }
                                    }, 100);
                                  }}
                                  className="mr-2"
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  Edit Content
                                </Button>

                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMoveSection(index, 'up')}
                                  disabled={index === 0}
                                >
                                  <MoveUp className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMoveSection(index, 'down')}
                                  disabled={index === page.pageBuilder.length - 1}
                                >
                                  <MoveDown className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteSection(index)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="seo" className="space-y-4">
                <div>
                  <Label htmlFor="description">Meta Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={page.description || ''}
                    onChange={handleInputChange}
                    placeholder="Enter meta description"
                    rows={4}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This description appears in search engine results.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="navMenu"
                    checked={page.navMenu || false}
                    onCheckedChange={(checked) => handleSwitchChange('navMenu', checked)}
                  />
                  <Label htmlFor="navMenu">Show in Navigation Menu</Label>
                </div>

                {page.navMenu && (
                  <div>
                    <Label htmlFor="navOrder">Navigation Order</Label>
                    <Input
                      id="navOrder"
                      name="navOrder"
                      type="number"
                      value={page.navOrder || 0}
                      onChange={handleInputChange}
                      min={0}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Lower numbers appear first in the navigation menu.
                    </p>
                  </div>
                )}

                <div>
                  <Label htmlFor="accessLevel">Access Level</Label>
                  <Select
                    value={page.accessLevel || 'both'}
                    onValueChange={(value) => handleSelectChange('accessLevel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select access level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="both">Public</SelectItem>
                      <SelectItem value="admin">Admin Only</SelectItem>
                      <SelectItem value="super_admin">Super Admin Only</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500 mt-1">
                    Controls who can view this page.
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/pages')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSaving}
            >
              {isSaving ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Page
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
