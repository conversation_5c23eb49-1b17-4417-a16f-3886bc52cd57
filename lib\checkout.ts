/**
 * Checkout utility functions for the store
 * 
 * This file contains functions for handling the checkout process,
 * including creating orders and processing payments.
 */

import { Cart, CartItem, clearCart } from './cart';

// Define customer type
export interface Customer {
  name: string;
  email: string;
  phone?: string;
}

// Define address type
export interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Define checkout data type
export interface CheckoutData {
  customer: Customer;
  shippingAddress: Address;
  billingAddress?: Address;
  sameAsBilling: boolean;
  paymentMethod: string;
  notes?: string;
}

// Define order type
export interface Order {
  orderNumber: string;
  customer: Customer;
  items: CartItem[];
  shippingAddress: Address;
  billingAddress: Address;
  payment: {
    method: string;
    status: string;
    transactionId?: string;
  };
  totals: {
    subtotal: number;
    tax: number;
    shipping: number;
    discount: number;
    total: number;
  };
  status: string;
  notes?: string;
}

/**
 * Process checkout and create order
 * @param cart Cart with items and totals
 * @param checkoutData Customer and shipping information
 * @returns Created order or error
 */
export async function processCheckout(
  cart: Cart,
  checkoutData: CheckoutData
): Promise<{ success: boolean; order?: Order; error?: string }> {
  try {
    // Validate cart
    if (cart.items.length === 0) {
      return { success: false, error: 'Cart is empty' };
    }

    // Validate checkout data
    if (!checkoutData.customer.name || !checkoutData.customer.email) {
      return { success: false, error: 'Customer information is incomplete' };
    }

    if (!checkoutData.shippingAddress.line1 || !checkoutData.shippingAddress.city) {
      return { success: false, error: 'Shipping address is incomplete' };
    }

    // Prepare billing address
    const billingAddress = checkoutData.sameAsBilling
      ? checkoutData.shippingAddress
      : checkoutData.billingAddress || checkoutData.shippingAddress;

    // Prepare order data
    const orderData = {
      customer: checkoutData.customer,
      items: cart.items.map(item => ({
        product: { _type: 'reference', _ref: item.id },
        quantity: item.quantity,
        price: item.price,
        variantId: item.variantId,
        variantOptions: item.variantOptions
      })),
      shippingAddress: checkoutData.shippingAddress,
      billingAddress: {
        sameAsShipping: checkoutData.sameAsBilling,
        ...billingAddress
      },
      payment: {
        method: checkoutData.paymentMethod,
        status: 'pending'
      },
      totals: {
        subtotal: cart.subtotal,
        tax: cart.tax,
        shipping: cart.shipping,
        discount: 0, // No discount functionality yet
        total: cart.total
      },
      notes: checkoutData.notes,
      updateInventory: true // Update product inventory
    };

    // Call API to create order
    const response = await fetch('/api/store/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    });

    const data = await response.json();

    if (!response.ok) {
      return { 
        success: false, 
        error: data.message || 'Failed to create order' 
      };
    }

    // Clear cart after successful order
    clearCart();

    return { 
      success: true, 
      order: data.order 
    };
  } catch (error) {
    console.error('Error processing checkout:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error during checkout' 
    };
  }
}

/**
 * Process payment (currently a simulation)
 * @param amount Amount to charge
 * @param paymentMethod Payment method
 * @returns Payment result
 */
export async function processPayment(
  amount: number,
  paymentMethod: string
): Promise<{ success: boolean; transactionId?: string; error?: string }> {
  // This is a simulation - in a real app, you would integrate with Stripe, PayPal, etc.
  return new Promise((resolve) => {
    setTimeout(() => {
      // Simulate successful payment
      const transactionId = `tr_${Date.now()}_${Math.floor(Math.random() * 1000000)}`;
      resolve({ 
        success: true, 
        transactionId 
      });
      
      // Uncomment to simulate payment failure (for testing)
      // resolve({ success: false, error: 'Payment processing failed' });
    }, 1500);
  });
}
