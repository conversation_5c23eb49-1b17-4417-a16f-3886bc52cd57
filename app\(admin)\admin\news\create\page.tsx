'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Save, ArrowLeft, Image as ImageIcon, Calendar, Tag, Upload, Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SeoForm from '@/components/admin/SeoForm';

// Define the Category type
interface Category {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  description?: string;
  color?: string;
  icon?: string;
  order?: number;
}

export default function CreateNewsPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [newsData, setNewsData] = useState({
    title: '',
    excerpt: '',
    content: '',
    category: '',
    date: new Date().toISOString().split('T')[0],
    featuredImage: null as File | null,
    status: 'draft',
    addToGallery: false,
    seo: {
      metaTitle: '',
      metaDescription: '',
      seoKeywords: [],
      nofollowAttributes: false,
      openGraph: {
        title: '',
        description: '',
        image: null
      },
      twitter: {
        title: '',
        description: '',
        cardType: 'summary_large_image'
      }
    }
  });

  // For new category dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newCategory, setNewCategory] = useState({
    title: '',
    description: '',
    color: '#002366',
  });

  // Fetch categories from Sanity
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true);
        const response = await fetch('/api/categories', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const data = await response.json();
        setCategories(data.categories || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
        toast.error('Failed to load categories');
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      setNewsData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setNewsData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle SEO data changes
  const handleSeoChange = (seoData: any) => {
    setNewsData(prev => ({
      ...prev,
      seo: seoData
    }));
  };

  // Handle new category form input changes
  const handleCategoryChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewCategory((prev) => ({ ...prev, [name]: value }));
  };

  // Create a new category
  const handleCreateCategory = async () => {
    try {
      if (!newCategory.title) {
        toast.error('Category title is required');
        return;
      }

      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCategory),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }

      const result = await response.json();

      // Add the new category to the state
      setCategories([...categories, result.category]);

      // Set the new category as selected
      setNewsData(prev => ({ ...prev, category: result.category.title }));

      // Reset form and close dialog
      setNewCategory({
        title: '',
        description: '',
        color: '#002366',
      });
      setIsDialogOpen(false);

      toast.success('Category created successfully');
    } catch (err) {
      console.error('Error creating category:', err);
      toast.error('Failed to create category');
    }
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setNewsData((prev) => ({ ...prev, featuredImage: file }));

      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target) {
          setImagePreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // No need for a Sanity client as we'll use the server-side API route

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent, saveAsDraft = true) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Create a FormData object to send all the data including the image
      const formData = new FormData();
      formData.append('title', newsData.title);
      formData.append('excerpt', newsData.excerpt);
      formData.append('content', newsData.content);
      formData.append('category', newsData.category);
      formData.append('date', newsData.date);
      formData.append('status', saveAsDraft ? 'draft' : 'published');
      formData.append('addToGallery', newsData.addToGallery.toString());

      // Add SEO data
      formData.append('seo', JSON.stringify(newsData.seo));

      // Add the image if it exists
      if (newsData.featuredImage) {
        formData.append('featuredImage', newsData.featuredImage);
      }

      console.log('Submitting news article to API route...');

      // Send the data to the server-side API route
      const response = await fetch('/api/news', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create news article');
      }

      const result = await response.json();
      console.log('News article created successfully:', result);

      const status = saveAsDraft ? 'draft' : 'published';
      const message = saveAsDraft
        ? 'News article saved as draft'
        : 'News article published successfully';

      toast.success(message);

      // Use replace instead of push for more reliable navigation
      router.replace('/admin/news');

      // Add a small delay to ensure the navigation happens
      setTimeout(() => {
        window.location.href = '/admin/news';
      }, 500);
    } catch (error) {
      console.error('Failed to create news article:', error);
      toast.error('Failed to create news article: ' + (error instanceof Error ? error.message : 'Please try again.'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Create News Article</h1>
        </div>
      </div>

      <Separator />

      <form onSubmit={(e) => handleSubmit(e, true)} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Create News Article</CardTitle>
            <CardDescription>
              Enter the details for your news article.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="seo">SEO & Social Sharing</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                placeholder="Enter article title"
                value={newsData.title}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt">Excerpt</Label>
              <Textarea
                id="excerpt"
                name="excerpt"
                placeholder="Brief summary of the article"
                value={newsData.excerpt}
                onChange={handleChange}
                rows={2}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                name="content"
                placeholder="Write your article content here..."
                value={newsData.content}
                onChange={handleChange}
                rows={10}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <div className="flex gap-2">
                  <div className="flex flex-1">
                    <Tag className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                    <select
                      id="category"
                      name="category"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newsData.category}
                      onChange={handleChange}
                      required
                    >
                      <option value="" disabled>Select a category</option>
                      {loadingCategories ? (
                        <option value="" disabled>Loading categories...</option>
                      ) : categories.length > 0 ? (
                        categories.map((category) => (
                          <option key={category._id} value={category.title}>
                            {category.title}
                          </option>
                        ))
                      ) : (
                        <option value="" disabled>No categories found</option>
                      )}
                    </select>
                  </div>

                  <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                      <Button type="button" variant="outline" size="icon" className="h-10 w-10">
                        <Plus className="h-4 w-4" />
                        <span className="sr-only">Add Category</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Category</DialogTitle>
                        <DialogDescription>
                          Add a new category for organizing content.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="new-title" className="text-right">
                            Title
                          </Label>
                          <Input
                            id="new-title"
                            name="title"
                            value={newCategory.title}
                            onChange={handleCategoryChange}
                            className="col-span-3"
                            required
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="new-description" className="text-right">
                            Description
                          </Label>
                          <Textarea
                            id="new-description"
                            name="description"
                            value={newCategory.description}
                            onChange={handleCategoryChange}
                            className="col-span-3"
                            rows={3}
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="new-color" className="text-right">
                            Color
                          </Label>
                          <div className="col-span-3 flex items-center gap-2">
                            <Input
                              id="new-color"
                              name="color"
                              type="color"
                              value={newCategory.color}
                              onChange={handleCategoryChange}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              name="color"
                              value={newCategory.color}
                              onChange={handleCategoryChange}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateCategory}>
                          Create Category
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Publication Date</Label>
                <div className="flex">
                  <Calendar className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                  <Input
                    id="date"
                    name="date"
                    type="date"
                    value={newsData.date}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="featuredImage">Featured Image</Label>
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="flex items-center justify-center w-full">
                    <label
                      htmlFor="featuredImage"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/40 hover:bg-muted/60"
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-3 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-muted-foreground">
                          PNG, JPG or WEBP (MAX. 2MB)
                        </p>
                      </div>
                      <Input
                        id="featuredImage"
                        name="featuredImage"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleFileChange}
                      />
                    </label>
                  </div>
                </div>

                {newsData.featuredImage && (
                  <div className="relative w-24 h-24 rounded-md overflow-hidden border">
                    {imagePreview ? (
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="object-cover w-full h-full"
                        onError={() => {
                          // Fallback to icon if image fails to load
                          console.error('Image preview failed to load');
                        }}
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <ImageIcon className="h-12 w-12 text-muted-foreground" />
                      </div>
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-background/80 py-1 px-2 text-xs truncate">
                      {newsData.featuredImage.name}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="addToGallery"
                name="addToGallery"
                checked={newsData.addToGallery}
                onChange={handleChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="addToGallery">Add Image to Gallery</Label>
              <span className="text-xs text-muted-foreground ml-2">(Image will be added to the gallery when saved)</span>
            </div>
              </TabsContent>

              <TabsContent value="seo" className="pt-4">
                <SeoForm
                  title={newsData.title}
                  description={newsData.excerpt}
                  slug={newsData.title ? newsData.title.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-') : 'news-article'}
                  image={imagePreview}
                  contentType="article"
                  seoData={newsData.seo}
                  onSeoChange={handleSeoChange}
                  onImageUpload={async (file) => {
                    try {
                      // Create FormData for image upload
                      const imageFormData = new FormData();
                      imageFormData.append('image', file);

                      // Upload the image
                      const uploadResponse = await fetch('/api/upload', {
                        method: 'POST',
                        body: imageFormData,
                      });

                      if (!uploadResponse.ok) {
                        throw new Error('Failed to upload image');
                      }

                      const uploadResult = await uploadResponse.json();

                      if (uploadResult.success && uploadResult.asset) {
                        console.log('Image uploaded successfully:', uploadResult.asset);

                        // Return the image asset reference
                        return {
                          _type: 'image',
                          asset: {
                            _type: 'reference',
                            _ref: uploadResult.asset._id
                          }
                        };
                      } else {
                        throw new Error('Failed to get image asset');
                      }
                    } catch (error) {
                      console.error('Error uploading SEO image:', error);
                      throw error;
                    }
                  }}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/news')}
            >
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button
                type="submit"
                variant="outline"
                disabled={isSubmitting}
              >
                Save as Draft
              </Button>
              <Button
                type="button"
                disabled={isSubmitting}
                onClick={(e) => handleSubmit(e, false)}
              >
                {isSubmitting ? 'Publishing...' : 'Publish'}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
