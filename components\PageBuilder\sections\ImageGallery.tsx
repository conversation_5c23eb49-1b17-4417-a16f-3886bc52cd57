'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ImageGalleryProps } from '../types';
import { urlFor } from '@/lib/sanity.client';
import { getGalleryBySlug } from '@/lib/sanity';

export default function ImageGallery({
  heading,
  description,
  images = [],
  displayStyle = 'grid',
  backgroundStyle = 'none',
  animation = { duration: 0.6, delay: 0, stagger: 0.2, type: 'spring' }
}: ImageGalleryProps) {
  const [activeImage, setActiveImage] = useState<number | null>(null);
  const [galleryData, setGalleryData] = useState<{
    heading?: string;
    description?: string;
    images: Array<{
      image: any;
      caption?: string;
      alt?: string;
    }>;
    displayStyle: 'grid' | 'carousel' | 'masonry';
    backgroundStyle: 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory';
    animation?: {
      duration?: number;
      delay?: number;
      stagger?: number;
      type?: string;
    };
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch gallery data from Sanity if not provided via props
  useEffect(() => {
    const fetchGallery = async () => {
      try {
        // If we already have images from props, don't fetch
        if (images.length > 0) {
          setGalleryData({
            heading,
            description,
            images,
            displayStyle,
            backgroundStyle,
            animation
          });
          setIsLoading(false);
          return;
        }

        // Otherwise fetch from Sanity
        const data = await getGalleryBySlug('gallery');
        if (data) {
          // Create a properly typed gallery data object
          const galleryData = {
            heading: data?.title || heading,
            description: data?.description || description,
            images: data?.images || [],
            displayStyle: ((data?.displayStyle || displayStyle) as 'grid' | 'carousel' | 'masonry'),
            backgroundStyle: ((data?.backgroundStyle || backgroundStyle) as 'none' | 'light' | 'dark' | 'royalBlue' | 'royalGold' | 'ivory'),
            animation: data?.animation || animation
          };
          setGalleryData(galleryData);
        } else {
          // Fallback to props
          setGalleryData({
            heading,
            description,
            images,
            displayStyle,
            backgroundStyle,
            animation
          });
        }
      } catch (error) {
        console.error('Error fetching gallery:', error);
        // Fallback to props
        setGalleryData({
          heading,
          description,
          images,
          displayStyle,
          backgroundStyle,
          animation
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchGallery();
  }, [heading, description, images, displayStyle, backgroundStyle, animation]);

  // Helper function to safely get image URL
  const getImageUrl = (image: any, width = 600): string => {
    if (!image) return '/placeholder.jpg';

    try {
      // Handle both ImageUrlBuilder and direct image references
      const builder = urlFor(image);

      // Get the URL directly
      const imageUrl = (builder as any).url();
      return imageUrl || '/placeholder.jpg';
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '/placeholder.jpg';
    }
  };

  // Background style classes
  const bgClasses = {
    none: 'bg-white',
    light: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    royalBlue: 'bg-[#001a4d] text-white',
    royalGold: 'bg-amber-700 text-white',
    ivory: 'bg-[#f9f7e8]',
  };

  // Determine grid layout based on display style
  const gridLayout = {
    grid: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    carousel: 'flex flex-nowrap overflow-x-auto snap-x snap-mandatory',
    masonry: 'columns-1 sm:columns-2 lg:columns-3 gap-4',
  };

  // Handle lightbox open/close
  const openLightbox = (index: number) => setActiveImage(index);
  const closeLightbox = () => setActiveImage(null);

  // If loading or no data, show loading state
  if (isLoading) {
    return <div className="py-16 text-center">Loading gallery...</div>;
  }

  // If no gallery data, show error
  if (!galleryData) {
    return <div className="py-16 text-center">Gallery not found</div>;
  }

  // Use data from Sanity or props
  const {
    heading: galleryHeading,
    description: galleryDescription,
    images: galleryImages,
    displayStyle: galleryDisplayStyle,
    backgroundStyle: galleryBackgroundStyle,
    animation: galleryAnimation
  } = galleryData;

  return (
    <div className={`py-16 ${bgClasses[galleryBackgroundStyle as keyof typeof bgClasses]}`}>
      <div className="container mx-auto px-4">
        {/* Heading */}
        {galleryHeading && (
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: galleryAnimation?.duration || 0.6,
              type: galleryAnimation?.type || 'spring'
            }}
            viewport={{ once: true }}
          >
            {galleryHeading}
          </motion.h2>
        )}

        {/* Description */}
        {galleryDescription && (
          <motion.p
            className="text-lg text-center max-w-3xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: galleryAnimation?.duration || 0.6,
              delay: galleryAnimation?.delay || 0.2,
              type: galleryAnimation?.type || 'spring'
            }}
            viewport={{ once: true }}
          >
            {galleryDescription}
          </motion.p>
        )}

        {/* Gallery */}
        <div className={`grid gap-6 ${gridLayout[galleryDisplayStyle as keyof typeof gridLayout]}`}>
          {galleryImages.map((item: { image: any; caption?: string; alt?: string }, index: number) => {
            // Get image URL and caption
            const imageUrl = getImageUrl(item.image);
            const caption = item.caption || '';
            const alt = item.alt || caption || 'Gallery image';

            return (
              <motion.div
                key={index}
                className={`relative overflow-hidden rounded-lg shadow-md ${galleryDisplayStyle === 'carousel' ? 'min-w-[280px] sm:min-w-[350px] snap-center' : ''}`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: galleryAnimation?.duration || 0.5,
                  delay: (galleryAnimation?.stagger || 0.1) * index,
                  type: galleryAnimation?.type || 'spring'
                }}
                viewport={{ once: true }}
                onClick={() => openLightbox(index)}
              >
                <div className="relative aspect-[4/3] cursor-pointer group">
                  <img
                    src={imageUrl}
                    alt={alt}
                    className="object-contain transition-transform duration-500 group-hover:scale-105 w-full h-full absolute inset-0 bg-gray-100"
                  />
                  {caption && (
                    <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                      <p className="text-sm font-medium">{caption}</p>
                    </div>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Lightbox */}
        {activeImage !== null && (
          <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
            <button
              className="absolute top-4 right-4 text-white text-4xl hover:text-gray-300"
              onClick={closeLightbox}
            >
              &times;
            </button>

            <div className="relative max-w-4xl max-h-[80vh] w-full">
              <img
                src={getImageUrl(galleryImages[activeImage].image)}
                alt={galleryImages[activeImage].alt || galleryImages[activeImage].caption || 'Gallery image'}
                className="object-contain max-h-[80vh] mx-auto w-full"
              />

              {galleryImages[activeImage].caption && (
                <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-3">
                  <p className="text-center">{galleryImages[activeImage].caption}</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
