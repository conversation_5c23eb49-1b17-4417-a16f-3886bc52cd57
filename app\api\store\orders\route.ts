import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// Helper function to generate order number
function generateOrderNumber() {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `KA-${timestamp}-${random}`;
}

// GET /api/store/orders - Get all orders (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to view orders' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const limit = url.searchParams.get('limit');
    const customerId = url.searchParams.get('customerId');

    // Build the query
    let query = '*[_type == "order"]';
    const params: Record<string, any> = {};

    // Add filters
    if (status) {
      query += ' && status == $status';
      params.status = status;
    }

    if (customerId) {
      query += ' && customer.email == $customerId';
      params.customerId = customerId;
    }

    // Add sorting
    query += ' | order(createdAt desc)';

    // Add limit
    if (limit) {
      query += `[0...${parseInt(limit)}]`;
    }

    // Add projection
    query += ' { _id, orderNumber, customer, items, status, totals, createdAt, updatedAt, shippingInfo }';

    // Fetch orders from Sanity
    const orders = await client.fetch(query, params, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    return NextResponse.json({
      success: true,
      orders,
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch orders',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/store/orders - Create a new order
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.customer || !body.items || !body.totals) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields: customer, items, and totals are required' },
        { status: 400 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Create the order document
    const orderDoc = {
      _type: 'order',
      orderNumber,
      customer: body.customer,
      items: body.items,
      shippingAddress: body.shippingAddress,
      billingAddress: body.billingAddress,
      payment: body.payment || {
        method: 'credit_card',
        status: 'pending',
      },
      totals: body.totals,
      status: 'pending',
      notes: body.notes || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Create the document in Sanity
    const createdOrder = await client.create(orderDoc);

    // Update product inventory if needed
    if (body.updateInventory) {
      for (const item of body.items) {
        if (item.product && item.product._ref) {
          // Get the product
          const product = await client.fetch(`*[_type == "product" && _id == $id][0]`, {
            id: item.product._ref,
          });

          // Update inventory if tracking is enabled
          if (product && product.inventory && product.inventory.trackInventory) {
            const newQuantity = Math.max(0, (product.inventory.quantity || 0) - item.quantity);

            await client
              .patch(product._id)
              .set({
                'inventory.quantity': newQuantity,
              })
              .commit();
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Order created successfully',
      order: {
        _id: createdOrder._id,
        orderNumber,
      },
    });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create order',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
