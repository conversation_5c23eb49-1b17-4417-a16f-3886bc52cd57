import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@sanity/client';

interface EventData {
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  image?: any;
  isCountdownTarget?: boolean;
  isHighlighted?: boolean;
  showRsvp?: boolean;
  eventType?: string;
  order?: number;
}

// Create a Sanity client for server-side operations
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Server-side token, not exposed to client
  apiVersion: '2025-05-09',
  useCdn: false,
});

// GET handler for /api/events
export async function GET(req: NextRequest) {
  try {
    // Query all events from Sanity
    const events = await client.fetch(`
      *[_type == "event"] | order(date asc) {
        _id,
        title,
        slug,
        date,
        endDate,
        location,
        description,
        image,
        isCountdownTarget,
        isHighlighted,
        showRsvp,
        eventType,
        order
      }
    `);

    return NextResponse.json({ events });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST handler for /api/events
export async function POST(req: NextRequest) {
  try {
    // Validate token exists
    if (!process.env.SANITY_API_TOKEN) {
      console.error('SANITY_API_TOKEN is missing');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Parse the request body
    const eventData: EventData = await req.json();

    // Validate required fields
    if (!eventData.title || !eventData.date || !eventData.location || !eventData.description) {
      return NextResponse.json(
        { error: 'Missing required fields: title, date, location, and description are required' },
        { status: 400 }
      );
    }

    // Ensure slug exists
    if (!eventData.slug || !eventData.slug.current) {
      // Generate slug from title
      const slug = eventData.title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');

      eventData.slug = { current: slug };
    }

    console.log('Creating new event:', eventData.title);

    // Create the document in Sanity
    const result = await client.create({
      _type: 'event',
      ...eventData
    });

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to create event' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Event created successfully',
      event: result
    });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
