import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Globe, Award } from 'lucide-react';
import { urlFor } from '@/lib/sanity.client';
import { getStrategicPartners } from '@/lib/sanity';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export const metadata: Metadata = {
  title: 'Strategic Partners | Kingdom of Adukrom',
  description: 'Meet the strategic partners working with the Kingdom of Adukrom to build a brighter future for Ghana.',
};

interface Partner {
  _id: string;
  name: string;
  slug: { current: string };
  description: string;
  website?: string;
  logo?: any;
  partnershipType: string;
  active: boolean;
  featured?: boolean;
  order?: number;
}

export default async function PartnersPage() {
  // Fetch all active partners
  const allPartners = await getStrategicPartners();
  const partners = allPartners.filter((partner: Partner) => partner.active);

  // Sort partners by order property
  const sortedPartners = [...partners].sort((a: Partner, b: Partner) =>
    (a.order || 999) - (b.order || 999)
  );

  // Separate featured partners
  const featuredPartners = sortedPartners.filter((partner: Partner) => partner.featured);

  // Partner type labels for better display
  const partnerTypeLabels: Record<string, string> = {
    'corporate': 'Strategic Financial Partners',
    'heritage': 'Heritage & Cultural Partners',
    'educational': 'Educational Development Partners',
    'technology': 'Innovation & Technology Partners',
    'investment': 'Strategic Investment Partners',
    'governance': 'Traditional Governance Partners',
    'ngo': 'Development Partners',
    'government': 'Government Partners',
    'media': 'Media Partners',
    'other': 'Strategic Partners'
  };

  // Group partners by type
  const partnersByType: Record<string, Partner[]> = {};

  sortedPartners.forEach((partner: Partner) => {
    const type = partner.partnershipType;
    if (!partnersByType[type]) {
      partnersByType[type] = [];
    }
    partnersByType[type].push(partner);
  });

  // Sort partner types for display
  const partnerTypes = Object.keys(partnersByType).sort((a, b) => {
    // Custom sort order
    const order = {
      'corporate': 1,
      'government': 2,
      'ngo': 3,
      'educational': 4,
      'heritage': 5,
      'technology': 6,
      'investment': 7,
      'governance': 8,
      'media': 9,
      'other': 10
    };
    return (order[a as keyof typeof order] || 99) - (order[b as keyof typeof order] || 99);
  });

  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="royal-gradient py-24 relative overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 bg-[url('/Website Images/pattern-bg.png')] bg-repeat"></div>
          </div>

          {/* Floating particles */}
          <div className="absolute inset-0">
            {Array.from({ length: 15 }).map((_, i) => (
              <div
                key={i}
                className="absolute rounded-full bg-white opacity-20"
                style={{
                  width: Math.random() * 10 + 5,
                  height: Math.random() * 10 + 5,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animation: `float ${Math.random() * 10 + 10}s infinite ease-in-out`,
                  animationDelay: `${Math.random() * 5}s`,
                }}
              />
            ))}
          </div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">Strategic Partners</h1>
            <div className="w-24 h-1 bg-royalGold mx-auto mb-8"></div>
            <p className="text-xl text-white max-w-3xl mx-auto">
              Meet the organizations and institutions working with the Kingdom of Adukrom to build a brighter future for Ghana.
            </p>
          </div>

          {/* Decorative bottom curve */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0 100%)' }}></div>
        </section>

        {/* Featured Partners Section */}
        {featuredPartners.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4">
              <div className="flex items-center justify-center mb-10">
                <Award className="text-royalGold w-8 h-8 mr-3" />
                <h2 className="text-3xl font-bold text-royalBlue">Featured Partners</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                {featuredPartners.map((partner: Partner) => (
                  <div key={partner._id} className="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl border border-gray-100">
                    <div className="relative h-48 bg-gradient-to-r from-royalBlue/5 to-royalGold/5 flex items-center justify-center p-6">
                      {partner.logo && urlFor(partner.logo).url() ? (
                        <img
                          src={urlFor(partner.logo).url() || ''}
                          alt={partner.name}
                          className="max-h-full max-w-full object-contain"
                        />
                      ) : (
                        <div className="w-24 h-24 rounded-full bg-royalBlue/10 flex items-center justify-center">
                          <span className="text-3xl font-bold text-royalBlue">{partner.name.charAt(0)}</span>
                        </div>
                      )}
                      <div className="absolute top-3 right-3">
                        <span className="bg-royalGold/10 text-royalGold text-xs font-medium px-2.5 py-1 rounded-full border border-royalGold/20">
                          Featured
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-royalBlue mb-2">{partner.name}</h3>
                      <p className="text-gray-600 mb-4">{partner.description}</p>
                      {partner.website && (
                        <a
                          href={partner.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-royalBlue hover:text-royalGold transition-colors"
                        >
                          <Globe className="mr-2 h-4 w-4" />
                          Visit Website
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Partners By Category Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            {partners.length === 0 ? (
              <div className="text-center py-16 bg-white rounded-xl shadow-md">
                <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-xl text-gray-600 mb-8">
                  We are currently developing partnerships. Check back soon for updates.
                </p>
                <Link
                  href="/"
                  className="inline-flex items-center px-6 py-3 bg-royalBlue text-white rounded-full hover:bg-royalBlue/90 transition-colors shadow-md"
                >
                  <ArrowRight className="mr-2 h-5 w-5" />
                  Return to Home
                </Link>
              </div>
            ) : (
              <>
                <div className="mb-16 text-center">
                  <h2 className="text-3xl font-bold text-royalBlue mb-4">Our Partners By Category</h2>
                  <p className="text-gray-600 max-w-3xl mx-auto">
                    We collaborate with a diverse range of organizations across various sectors to achieve our mission.
                  </p>
                </div>

                {partnerTypes.map((type) => (
                  <div key={type} className="mb-20">
                    <div className="flex items-center justify-center mb-10">
                      <div className="w-10 h-1 bg-royalGold mr-4"></div>
                      <h2 className="text-2xl md:text-3xl font-bold text-royalBlue capitalize">
                        {partnerTypeLabels[type] || `${type} Partners`}
                      </h2>
                      <div className="w-10 h-1 bg-royalGold ml-4"></div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                      {partnersByType[type].map((partner: Partner) => (
                        <div
                          key={partner._id}
                          className="bg-white rounded-xl shadow-md overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl border border-gray-100 flex flex-col"
                        >
                          <div className="relative h-48 bg-gradient-to-r from-royalBlue/5 to-royalGold/5 flex items-center justify-center p-6 border-b border-gray-100">
                            {partner.logo && urlFor(partner.logo).url() ? (
                              <img
                                src={urlFor(partner.logo).url() || ''}
                                alt={partner.name}
                                className="max-h-full max-w-full object-contain"
                              />
                            ) : (
                              <div className="w-24 h-24 rounded-full bg-royalBlue/10 flex items-center justify-center">
                                <span className="text-3xl font-bold text-royalBlue">{partner.name.charAt(0)}</span>
                              </div>
                            )}
                            {partner.featured && (
                              <div className="absolute top-3 right-3">
                                <span className="bg-royalGold/10 text-royalGold text-xs font-medium px-2.5 py-1 rounded-full border border-royalGold/20">
                                  Featured
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="p-6 flex-grow">
                            <h3 className="text-xl font-bold text-royalBlue mb-2">{partner.name}</h3>
                            <p className="text-gray-600 mb-4">{partner.description}</p>
                          </div>
                          {partner.website && (
                            <div className="px-6 pb-6">
                              <a
                                href={partner.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-royalBlue hover:text-royalGold transition-colors"
                              >
                                <Globe className="mr-2 h-4 w-4" />
                                Visit Website
                              </a>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                <div className="text-center mt-16">
                  <Link
                    href="/"
                    className="inline-flex items-center px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                  >
                    Return to Home
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </div>
              </>
            )}
          </div>
        </section>

        {/* Partnership Inquiry Section */}
        <section className="py-24 bg-gradient-to-b from-royalBlue/5 to-royalBlue/10 relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-full h-full bg-[url('/Website Images/pattern-bg.png')] bg-repeat opacity-20"></div>
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-royalGold/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-royalBlue/10 rounded-full blur-3xl"></div>
          </div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-10 max-w-4xl mx-auto border border-gray-100">
              <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-6">Become a Strategic Partner</h2>
              <div className="w-20 h-1 bg-royalGold mx-auto mb-8"></div>
              <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-10">
                Interested in partnering with the Kingdom of Adukrom? We welcome collaborations with organizations that share our vision for development, cultural preservation, and community empowerment.
              </p>
              <div className="flex flex-col md:flex-row items-center justify-center gap-6">
                <Link
                  href="/#contact"
                  className="inline-flex items-center px-8 py-4 bg-royalBlue text-white rounded-full hover:bg-royalBlue/90 transition-all duration-300 shadow-lg transform hover:scale-105"
                >
                  Contact Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-white text-royalBlue border border-royalBlue rounded-full hover:bg-royalBlue/5 transition-all duration-300"
                >
                  Learn More About Us
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
