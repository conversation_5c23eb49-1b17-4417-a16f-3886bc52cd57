# Kingdom of Adukrom Official Website

This is the official website for the Royal Family of Africa, built with [Next.js](https://nextjs.org) and [Sanity.io](https://sanity.io).

## Repository

The official repository for this project is:
https://github.com/joelgriiyo/kingdomadukrom.git

## Latest Updates (May 10, 2025) - Logo Fix and Heroku Deployment Ready

- Fixed Node.js compatibility issues for Heroku deployment
- Added proper support for tailwindcss in production builds
- Resolved Sanity client compatibility issues
- Improved build process for Heroku deployment
- Fixed logo display throughout the site with consistent branding
- Added reusable ImageLogo component for better maintainability
- Ensured <PERSON><PERSON> and <PERSON><PERSON> appear on all pages including secret page

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Admin Interface

The admin interface is available at [http://localhost:3000/admin](http://localhost:3000/admin).

### Events Management

The events management page allows you to create, edit, and delete events.

#### Uploading Events to Sanity

To upload events from the user-facing page to Sanity, follow these steps:

1. Make sure you have the required dependencies:
   ```bash
   npm install @sanity/client dotenv
   ```

2. Run the upload script:
   ```bash
   node scripts/upload-events-to-sanity.js
   ```

3. The script will:
   - Read events from `data/events.json`
   - Upload them to your Sanity dataset
   - If an event with the same slug already exists, it will be updated

4. After uploading, refresh the admin events page to see the updated events.

#### Adding Custom Events

You can add custom events by:

1. Editing the `data/events.json` file
2. Running the upload script again
3. Or using the "Add New Event" button in the admin interface

## Sanity Studio

The Sanity Studio is available at [http://localhost:3000/studio](http://localhost:3000/studio).

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deployment Options

### Deploy on Heroku

This project is configured for deployment on Heroku. To deploy:

1. Make sure you have the [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) installed
2. Login to Heroku: `heroku login`
3. Create a new Heroku app: `heroku create kingdomadukrom`
4. Add Heroku remote: `git remote add heroku https://git.heroku.com/kingdomadukrom.git`
5. Push to Heroku: `git push heroku main`

Alternatively, you can use the deployment script:
```bash
npm run deploy
```

The project includes special build scripts for Heroku deployment that handle:
- Node.js version compatibility (requires Node.js 20+)
- Installing required dependencies like tailwindcss
- Setting up Sanity.io integration

### Deploy on Vercel

Alternatively, you can deploy on Vercel:

1. Push your code to GitHub: `git push github main`
2. Import the project in Vercel from https://github.com/joelgriiyo/kingdomadukrom.git
3. Configure the environment variables for Sanity.io

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
