import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the contact page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Contact Us',
    description: 'Get in touch with the Kingdom of Adukrom. Contact information and inquiry form.',
    url: '/contact',
    keywords: ['Contact Adukrom', 'Royal Contact', 'Kingdom Inquiries', 'Get in Touch'],
  });
}
