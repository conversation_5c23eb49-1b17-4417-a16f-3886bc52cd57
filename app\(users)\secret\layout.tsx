import type { Metadata } from 'next';
import '../../global.css';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export const metadata: Metadata = {
  title: 'Royal Coronation Livestream | Adukrom Kingdom',
  description: 'Exclusive access to the coronation ceremony of King <PERSON>',
};

export default function SecretLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Header />
      <main className="min-h-screen">
        {children}
      </main>
      <Footer />
    </>
  );
}

