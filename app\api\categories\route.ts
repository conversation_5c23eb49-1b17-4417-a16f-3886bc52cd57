import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET endpoint to fetch all categories
export async function GET() {
  try {
    console.log('Fetching categories from Sanity');

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (clientError) {
      console.error('Error getting Sanity client:', clientError);

      // Use a read-only client as fallback
      const { readClient } = await import('@/lib/sanity.client');
      client = readClient;
      console.log('Using read-only client as fallback');
    }

    // Query to fetch all categories
    const query = `*[_type == "category"] | order(order asc) {
      _id,
      title,
      slug,
      description,
      color,
      icon,
      order
    }`;

    // Fetch categories from Sanity
    const categories = await client.fetch(query, {}, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    console.log(`Fetched ${categories.length} categories`);

    return NextResponse.json({
      success: true,
      categories: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch categories',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST endpoint to create a new category
export async function POST(req: NextRequest) {
  try {
    console.log('Category creation API route called');

    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to create categories' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (clientError) {
      console.error('Error getting Sanity client:', clientError);

      // Use a read-only client as fallback
      const { readClient } = await import('@/lib/sanity.client');
      client = readClient;
      console.log('Using read-only client as fallback');
    }

    // Get the JSON data from the request
    const data = await req.json();

    // Extract fields
    const { title, description, color, icon, order } = data;

    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { success: false, message: 'Category title is required' },
        { status: 400 }
      );
    }

    // Generate a slug from the title
    const slug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    try {
      console.log('Creating category document in Sanity...');

      // Create the category document
      const categoryDoc = {
        _type: 'category',
        title,
        slug: { _type: 'slug', current: slug },
        description,
        color: color || '#002366',
        icon,
        order: order || 0
      };

      // Create the document in Sanity
      const createdCategory = await client.create(categoryDoc);
      console.log('Category document created:', createdCategory._id);

      return NextResponse.json({
        success: true,
        message: 'Category created successfully',
        category: createdCategory
      });
    } catch (sanityError) {
      console.error('Sanity operation error:', sanityError);
      return NextResponse.json(
        {
          success: false,
          message: 'Sanity operation failed',
          error: sanityError instanceof Error ? sanityError.message : 'Unknown Sanity error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in category creation handler:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create category',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
