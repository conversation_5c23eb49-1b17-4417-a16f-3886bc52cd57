
// This is a temporary development configuration
// The original config is preserved and will be restored when the server stops
const originalConfig = require('./next.config.js');

/** @type {import('next').NextConfig} */
const fastDevConfig = {
  ...originalConfig,
  
  // Disable static optimization during development
  optimizeFonts: false,
  
  // Disable image optimization during development
  images: {
    ...originalConfig.images,
    disableStaticImages: true,
  },
  
  // Experimental features for faster development
  experimental: {
    ...originalConfig.experimental,
    // Optimize compilation speed
    turbotrace: false,
    // Disable React server components during development
    serverActions: false,
  },
  
  // Webpack optimizations for development
  webpack: (config, options) => {
    // Call the original webpack config if it exists
    if (typeof originalConfig.webpack === 'function') {
      config = originalConfig.webpack(config, options);
    }
    
    // Only if we're in development mode
    if (options.dev) {
      // Disable source maps in development for faster builds
      config.devtool = 'eval';
      
      // Reduce the number of chunks
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }
    
    return config;
  },
};

module.exports = fastDevConfig;
