'use client';
import Link from 'next/link';
import { motion } from 'framer-motion';
import ScrollReveal from '@/components/ScrollReveal';
import FloatingParticles from '@/components/FloatingParticles';


export default function Coronation() {
  const events = [
    {
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '7:30 AM - 9:00 PM',
      location: 'Royal Palace Grounds',
      description:
        'The official coronation ceremony where <PERSON> will be crowned according to ancient royal traditions, with dignitaries from across Ghana and international representatives in attendance.',
      image: '/Website Images/Royal Coronation Ceremony.png'
    },
    {
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '9:30 AM - 10:00 PM',
      location: 'Safari Valley Resort, Ghana',
      description:
        'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests from business, politics, and cultural spheres.',
      image: '/Website Images/outdoor_living_space_ghana.png'
    },
    {
      title: 'Global Economic Forum',
      date: 'August 31, 2025',
      time: '9:30 AM - 10:00 PM',
      location: 'Safari Valley Resort, Ghana',
      description:
        'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Adukrom Kingdom.',
      image: '/Website Images/Global Economic Forum.webp'
    },
  ];

  return (
    <section id="coronation" className="py-20 royal-gradient relative overflow-hidden">
      <FloatingParticles
        count={30}
        colors={['#D4AF37', '#FFFFFF', '#AD8A56']}
        minSize={3}
        maxSize={10}
      />

      <div className="container mx-auto px-4 relative z-10">
        <ScrollReveal animation="fadeInDown">
          <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-16 section-title">
            Royal Coronation 2025
          </h2>
        </ScrollReveal>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {events.map((event, index) => (
            <ScrollReveal
              key={index}
              animation="fadeInUp"
              delay={0.2 * index}
            >
              <motion.div
                className="event-card bg-ivory rounded-lg overflow-hidden shadow-lg"
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px rgba(0, 0, 0, 0.2)",
                  transition: { duration: 0.3 }
                }}
              >
                <motion.div
                  className="relative h-48 overflow-hidden"
                  initial={{ opacity: 0.8 }}
                  whileHover={{ opacity: 1 }}
                >
                  <motion.img
                    src={event.image}
                    alt={event.title}
                    className="w-full h-full object-cover"
                    initial={{ scale: 1 }}
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.5 }}
                  />
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-t from-royalBlue/80 to-transparent"
                    initial={{ opacity: 0.7 }}
                    whileHover={{ opacity: 0.9 }}
                  />
                  <motion.div className="absolute bottom-0 left-0 right-0 p-4 text-center">
                    <h3 className="text-white text-xl font-bold">{event.title}</h3>
                    <p className="text-royalGold font-medium">{event.date}</p>
                  </motion.div>
                </motion.div>
                <div className="p-6">
                  <motion.div
                    className="flex items-center mb-4"
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 + (0.1 * index) }}
                  >
                    <motion.svg
                      className="w-5 h-5 text-royalGold mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                        clipRule="evenodd"
                      />
                    </motion.svg>
                    <span className="text-charcoal">{event.time}</span>
                  </motion.div>
                  <motion.div
                    className="flex items-center mb-4"
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 + (0.1 * index) }}
                  >
                    <motion.svg
                      className="w-5 h-5 text-royalGold mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </motion.svg>
                    <span className="text-charcoal">{event.location}</span>
                  </motion.div>
                  <motion.p
                    className="text-charcoal mb-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 + (0.1 * index) }}
                  >
                    {event.description}
                  </motion.p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="#rsvp"
                      className="royal-button block text-center bg-royalBlue text-white py-2 px-4 rounded-full hover:bg-blue-900 transition-colors"
                    >
                      RSVP for This Event
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            </ScrollReveal>
          ))}
        </div>

        <ScrollReveal animation="fadeInUp" delay={0.8}>
          <div className="mt-16 text-center">
            <motion.p
              className="text-white text-lg mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              For accommodation and travel information, please contact our Royal Hospitality Team.
            </motion.p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="#contact"
                className="royal-button inline-block bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
              >
                Contact Hospitality Team
              </Link>
            </motion.div>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
}
