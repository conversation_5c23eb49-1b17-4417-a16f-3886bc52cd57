'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ChevronLeft, Trash2, CreditCard, ShoppingBag } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import {
  CartItem,
  Cart as CartType,
  loadCart,
  saveCart,
  updateCartItemQuantity,
  removeFromCart,
  clearCart as clearCartUtil,
  calculateCartTotals
} from '@/lib/cart';

export default function CartPage() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cartTotals, setCartTotals] = useState<CartType>({
    items: [],
    subtotal: 0,
    tax: 0,
    shipping: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // Load cart from localStorage
  useEffect(() => {
    setIsLoading(true);
    const loadedCart = loadCart();
    setCart(loadedCart);
    setCartTotals(calculateCartTotals(loadedCart));
    setIsLoading(false);
  }, []);

  // Update cart totals whenever cart changes
  useEffect(() => {
    setCartTotals(calculateCartTotals(cart));
  }, [cart]);

  // Destructure cart totals for easier access
  const { subtotal, tax, shipping, total } = cartTotals;

  // Update quantity
  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    const updatedCart = updateCartItemQuantity(cart, id, newQuantity);
    setCart(updatedCart);
  };

  // Remove item
  const removeItem = (id: string) => {
    const updatedCart = removeFromCart(cart, id);
    setCart(updatedCart);
  };

  // Clear cart
  const clearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      setCart(clearCartUtil());
    }
  };

  // Process checkout
  const handleCheckout = async () => {
    setIsProcessing(true);

    try {
      // Redirect to checkout page
      window.location.href = '/store/checkout';
    } catch (error) {
      console.error('Error during checkout:', error);
      alert('There was an error processing your checkout. Please try again.');
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-ivory">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
        <p className="ml-3 text-royalBlue">Loading cart...</p>
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-ivory">
        <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            href="/store"
            className="flex items-center text-royalBlue hover:text-royalGold transition-colors"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Continue Shopping
          </Link>
        </div>

        <h1 className="text-3xl font-bold text-royalBlue mb-8 flex items-center">
          <ShoppingBag className="mr-2 h-6 w-6" />
          Your Shopping Cart
        </h1>

        {cart.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-md">
            <div className="w-24 h-24 mx-auto mb-4 text-gray-300">
              <ShoppingBag className="w-full h-full" />
            </div>
            <h2 className="text-xl font-semibold text-royalBlue mb-2">Your cart is empty</h2>
            <p className="text-charcoal/80 mb-6">Looks like you haven't added any products to your cart yet.</p>
            <Link
              href="/store"
              className="royal-button bg-royalGold text-royalBlue font-bold py-2 px-6 rounded-full hover:bg-yellow-500 transition-colors"
            >
              Browse Products
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                  <h2 className="text-lg font-semibold text-royalBlue">
                    {cart.length} {cart.length === 1 ? 'Item' : 'Items'}
                  </h2>
                  <button
                    onClick={clearCart}
                    className="text-red-500 hover:text-red-700 text-sm flex items-center"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Clear Cart
                  </button>
                </div>

                <div className="divide-y divide-gray-200">
                  {cart.map((item) => (
                    <div key={item.id} className="p-4 flex items-center">
                      <div className="relative w-20 h-20 bg-gray-100 rounded overflow-hidden">
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-4 flex-grow">
                        <h3 className="font-medium text-royalBlue">{item.name}</h3>
                        <p className="text-sm text-charcoal/80">${item.price.toFixed(2)}</p>
                      </div>
                      <div className="flex items-center">
                        <div className="flex items-center border border-gray-300 rounded-full overflow-hidden mr-4">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="px-2 py-1 bg-gray-100 hover:bg-gray-200 transition-colors"
                          >
                            -
                          </button>
                          <span className="px-3 py-1">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="px-2 py-1 bg-gray-100 hover:bg-gray-200 transition-colors"
                          >
                            +
                          </button>
                        </div>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-royalBlue mb-4">Order Summary</h2>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-charcoal">
                    <span>Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-charcoal">
                    <span>Tax (7.5%)</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-charcoal">
                    <span>Shipping</span>
                    <span>${shipping.toFixed(2)}</span>
                  </div>
                  <div className="border-t border-gray-200 pt-3 mt-3">
                    <div className="flex justify-between font-semibold text-royalBlue">
                      <span>Total</span>
                      <span>${total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <motion.button
                  onClick={handleCheckout}
                  className="w-full royal-button bg-royalGold text-royalBlue font-bold py-3 px-6 rounded-full hover:bg-yellow-500 transition-colors flex items-center justify-center"
                  whileTap={{ scale: 0.95 }}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-royalBlue mr-2"></div>
                      Processing...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <CreditCard className="w-5 h-5 mr-2" />
                      Proceed to Checkout
                    </span>
                  )}
                </motion.button>

                <div className="mt-4 text-xs text-center text-charcoal/70">
                  <p>This is a demo store. No actual payment will be processed.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    <Footer />
    </>
  );
}
