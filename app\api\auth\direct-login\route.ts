import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AdminRole } from '@/lib/types/admin';

/**
 * API route for direct login (for testing purposes)
 * This should NOT be used in production
 */
export async function POST(req: NextRequest) {
  try {
    // Check if we're in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { success: false, message: 'This endpoint is only available in development mode' },
        { status: 403 }
      );
    }
    
    // Get the request body
    const body = await req.json();
    const { role } = body;
    
    // Validate role
    if (!role || (role !== AdminRole.ADMIN && role !== AdminRole.SUPER_ADMIN)) {
      return NextResponse.json(
        { success: false, message: 'Invalid role' },
        { status: 400 }
      );
    }
    
    // Get the current session
    const session = await getServerSession(authOptions);
    
    // Return the session info
    return NextResponse.json({
      success: true,
      message: 'Direct login API called',
      session,
      role,
    });
  } catch (error) {
    console.error('Error in direct login API:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred' },
      { status: 500 }
    );
  }
}
