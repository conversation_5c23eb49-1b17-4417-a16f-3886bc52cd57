# News Articles Update Scripts

These scripts are designed to update the content of existing news articles in the Sanity dataset for the Adukrom Kingdom website. The scripts add complete content to articles that currently only have titles.

## Prerequisites

Before running these scripts, make sure you have:

1. Node.js installed on your system
2. Access to the Sanity project with appropriate permissions
3. A `.env` file in the root directory with the following variables:
   ```
   NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt
   NEXT_PUBLIC_SANITY_DATASET=production
   SANITY_API_TOKEN=your_sanity_api_token
   ```

## Getting a Sanity API Token

If you don't have a Sanity API token yet, follow these steps:

1. Go to [https://www.sanity.io/manage](https://www.sanity.io/manage)
2. Select your project
3. Navigate to API > Tokens
4. Click "Add API token"
5. Give it a name (e.g., "News Articles Update")
6. Select "Editor" as the permission level
7. Copy the token and add it to your `.env` file

## Running the Scripts

You can run all the scripts at once using the main script:

```bash
node scripts/update-all-news-articles.js
```

This will update all news articles in sequence.

Alternatively, you can run each script individually:

```bash
node scripts/update-news-articles.js
node scripts/update-news-articles-part2.js
node scripts/update-news-articles-part3.js
```

## What the Scripts Do

These scripts update the following news articles with complete content:

1. The Power of a Resource-Based Economy: A Vision for Africa's Future
2. Ellison Outreach Foundation, Inc. Sponsors Three Schools in Ghana's Eastern Region
3. Nana Otutu Ababio V: The Visionary Leader of the Akuapem Kingdom
4. Nananom of Adukrom Honor Hon. Asiedu Offei and Nia Senior High School During 5th Akwasidae Festival
5. The Ellison Outreach Foundation Launches Grow Together Project in Adukrom to Combat Food Insecurity
6. Adukrom Celebrates Coronation of King Allen Ellison as Mpuntuhene
7. Adukrom Hosts Inaugural Global Economic Forum: Bridging Tradition and Innovation
8. Adukrom Launches Community Development Fund with $10 Million Initial Investment

Each article is updated with rich, formatted content including headings, paragraphs, and proper formatting.

## Troubleshooting

If you encounter any issues:

1. Check that your Sanity API token has the correct permissions
2. Verify that the article slugs in the scripts match the actual slugs in your Sanity dataset
3. Make sure your `.env` file is properly configured
4. Check the console output for specific error messages

For any persistent issues, you may need to check the Sanity Management Console to verify the structure of your news articles schema.
