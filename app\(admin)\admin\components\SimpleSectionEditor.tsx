'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Save, ArrowLeft } from 'lucide-react';
import { getWriteClient } from '@/lib/sanity.client';

interface SimpleSectionEditorProps {
  pageId: string;
  sectionIndex: number;
  onBack: () => void;
}

export default function SimpleSectionEditor({ pageId, sectionIndex, onBack }: SimpleSectionEditorProps) {
  const [section, setSection] = useState<any>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch the section data when the component mounts
  useEffect(() => {
    const fetchSection = async () => {
      try {
        setIsLoading(true);
        const client = getWriteClient();
        const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id: pageId });

        if (!page || !page.pageBuilder || !page.pageBuilder[sectionIndex]) {
          toast.error('Section not found');
          onBack();
          return;
        }

        setSection(page.pageBuilder[sectionIndex]);
      } catch (error) {
        console.error('Error fetching section:', error);
        toast.error('Failed to load section');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSection();
  }, [pageId, sectionIndex, onBack]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSection((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setSection((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const client = getWriteClient();

      // Get the current page
      const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id: pageId });

      if (!page) {
        toast.error('Page not found');
        setIsSaving(false);
        return;
      }

      // Get the current page builder array
      const pageBuilder = page.pageBuilder || [];

      // Update the section
      if (sectionIndex >= 0 && sectionIndex < pageBuilder.length) {
        pageBuilder[sectionIndex] = section;
      } else {
        toast.error('Invalid section index');
        setIsSaving(false);
        return;
      }

      // Update the page
      await client.patch(pageId)
        .set({ pageBuilder })
        .commit();

      toast.success('Section saved successfully');
      onBack();
    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-royalBlue mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading section...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={onBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Page Editor
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Edit Section</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="heading">Heading</Label>
                <Input
                  id="heading"
                  name="heading"
                  value={section.heading || ''}
                  onChange={handleInputChange}
                  placeholder="Enter heading"
                />
              </div>

              {section._type === 'hero' && (
                <div>
                  <Label htmlFor="tagline">Tagline</Label>
                  <Input
                    id="tagline"
                    name="tagline"
                    value={section.tagline || ''}
                    onChange={handleInputChange}
                    placeholder="Enter tagline"
                  />
                </div>
              )}

              {(section._type === 'textSection' || section._type === 'imageGallery' || section._type === 'featuredContent' || section._type === 'contactForm') && (
                <div>
                  <Label htmlFor="text">Content</Label>
                  <Textarea
                    id="text"
                    name="text"
                    value={typeof section.text === 'string' ? section.text : JSON.stringify(section.text || '')}
                    onChange={handleInputChange}
                    placeholder="Enter content"
                    rows={6}
                  />
                </div>
              )}

              {(section._type === 'textSection' || section._type === 'featuredContent' || section._type === 'contactForm') && (
                <div>
                  <Label htmlFor="backgroundStyle">Background Style</Label>
                  <Select
                    value={section.backgroundStyle || 'none'}
                    onValueChange={(value) => handleSelectChange('backgroundStyle', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select background style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="royalBlue">Royal Blue</SelectItem>
                      <SelectItem value="royalGold">Royal Gold</SelectItem>
                      <SelectItem value="ivory">Ivory</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Section'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
