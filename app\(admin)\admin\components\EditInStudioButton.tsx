'use client';

import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

interface EditInStudioButtonProps {
  documentId: string;
  className?: string;
}

export default function EditInStudioButton({ documentId, className = '' }: EditInStudioButtonProps) {
  const handleClick = () => {
    // Open the Sanity Studio with the document ID
    window.open(`/studio/desk/page;${documentId}`, '_blank');
  };

  return (
    <Button 
      onClick={handleClick}
      variant="default"
      size="sm"
      className={`bg-royalBlue hover:bg-royalBlue/90 text-white ${className}`}
    >
      <ExternalLink className="mr-2 h-4 w-4" />
      Edit in Sanity Studio
    </Button>
  );
}
