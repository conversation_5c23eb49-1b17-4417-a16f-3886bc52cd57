/**
 * Email sending utility (STUB VERSION)
 *
 * This is a temporary stub version that doesn't require SendGrid.
 * The actual implementation will be restored when SendGrid is added back.
 */

import { logError } from './errorHandling';

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html?: string;
  from?: string;
}

/**
 * Send an email (stub version)
 * @param options - Email options
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  const { to, subject, text, html, from = process.env.EMAIL_FROM || '<EMAIL>' } = options;

  // Just log the email
  console.log('==== EMAIL SENT (STUB) ====');
  console.log(`From: ${from}`);
  console.log(`To: ${to}`);
  console.log(`Subject: ${subject}`);
  console.log(`Text: ${text}`);
  if (html) {
    console.log(`HTML: ${html}`);
  }
  console.log('==== END EMAIL ====');
  return;
}

/**
 * Send an RSVP confirmation email
 * @param email - Recipient's email address
 * @param firstName - Recipient's first name
 * @param lastName - Recipient's last name
 * @param events - Events the recipient is attending
 */
export async function sendRsvpConfirmationEmail(
  email: string,
  firstName: string,
  lastName: string,
  events: string[]
): Promise<void> {
  // Format event names for display
  const eventNames = formatEventNames(events);

  // Create the email subject and content
  const subject = 'Your RSVP Confirmation - Kingdom of Adukrom';

  const text = `
Dear ${firstName} ${lastName},

Thank you for your RSVP to the following event(s):
${eventNames}

We are delighted that you will be joining us and look forward to welcoming you.

Important Information:
- Please arrive at least 30 minutes before the event start time
- Bring a valid ID for security purposes
- Dress code: Formal attire

If you have any questions or need to update your RSVP, please contact <NAME_EMAIL>.

Warm regards,
The Royal Family of Africa
  `;

  const html = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #002366; color: #FFD700; padding: 20px; text-align: center; }
    .content { padding: 20px; }
    .footer { background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 12px; color: #666; }
    .event-list { margin: 20px 0; padding-left: 20px; }
    .important { background-color: #f9f9f9; border-left: 4px solid #002366; padding: 15px; margin: 20px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>RSVP Confirmation</h1>
    </div>
    <div class="content">
      <p>Dear ${firstName} ${lastName},</p>

      <p>Thank you for your RSVP to the following event(s):</p>
      <ul class="event-list">
        ${events.map(event => `<li>${formatEventName(event)}</li>`).join('')}
      </ul>

      <p>We are delighted that you will be joining us and look forward to welcoming you.</p>

      <div class="important">
        <h3>Important Information:</h3>
        <ul>
          <li>Please arrive at least 30 minutes before the event start time</li>
          <li>Bring a valid ID for security purposes</li>
          <li>Dress code: Formal attire</li>
        </ul>
      </div>

      <p>If you have any questions or need to update your RSVP, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>Warm regards,<br>The Royal Family of Africa</p>
    </div>
    <div class="footer">
      <p>This email was sent to ${email}. Please do not reply to this email.</p>
      <p>&copy; ${new Date().getFullYear()} The Royal Family of Africa. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
  `;

  // Send the email
  await sendEmail({
    to: email,
    subject,
    text,
    html,
  });
}

/**
 * Format a single event name
 * @param event - Event code
 */
function formatEventName(event: string): string {
  const eventMap: Record<string, string> = {
    'coronation': 'Royal Coronation Ceremony (August 29, 2025)',
    'gala': 'Royal Gala Dinner (August 30, 2025)',
    'forum': 'Global Economic Forum (August 31, 2025)',
  };

  return eventMap[event] || event;
}

/**
 * Format event names for display in messages
 * @param events - Array of event codes
 */
function formatEventNames(events: string[]): string {
  const formattedEvents = events.map(formatEventName);

  if (formattedEvents.length === 1) {
    return formattedEvents[0];
  } else if (formattedEvents.length === 2) {
    return `${formattedEvents[0]} and ${formattedEvents[1]}`;
  } else {
    const lastEvent = formattedEvents.pop();
    return `${formattedEvents.join(', ')}, and ${lastEvent}`;
  }
}
