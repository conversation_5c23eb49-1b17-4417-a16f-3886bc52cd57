'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Tag,
  User,
  FileText,
  Trash,
  Loader2,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

// Define the RSVP submission type
interface RsvpSubmission {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  events: string[];
  attendanceType: string;
  reminderPreference?: string[];
  notes?: string;
  submittedAt: string;
}

export default function RsvpDetailPage({ params }: { params: { id: string } }) {
  const [rsvp, setRsvp] = useState<RsvpSubmission | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  // Create Sanity client with write permissions
  const createSanityClient = async () => {
    try {
      const { createClient } = await import('@sanity/client');

      // Get token from environment variable
      const token = process.env.NEXT_PUBLIC_SANITY_API_TOKEN;

      if (!token) {
        console.error('Sanity API token is missing');
        toast.error('API token is missing. Please check your environment variables.');
        throw new Error('Sanity API token is missing');
      }

      return createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2025-05-08',
        useCdn: false,
      });
    } catch (error) {
      console.error('Failed to create Sanity client:', error);
      toast.error('Failed to initialize content management system');
      throw error;
    }
  };

  // Fetch RSVP details
  const fetchRsvpDetails = async () => {
    try {
      setIsLoading(true);
      const client = await createSanityClient();
      
      const query = `*[_type == "rsvp" && _id == $id][0] {
        _id,
        firstName,
        lastName,
        email,
        phone,
        country,
        events,
        attendanceType,
        reminderPreference,
        notes,
        submittedAt
      }`;
      
      const data = await client.fetch(query, { id: params.id });
      
      if (!data) {
        toast.error('RSVP submission not found');
        router.push('/admin/rsvp');
        return;
      }
      
      setRsvp(data);
    } catch (error) {
      console.error('Failed to fetch RSVP details:', error);
      toast.error('Failed to load RSVP details');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete RSVP submission
  const deleteRsvpSubmission = async () => {
    try {
      setIsDeleting(true);
      
      // Create a client to connect to Sanity with write permissions
      const client = await createSanityClient();

      // Delete the document from Sanity
      const result = await client.delete(params.id);

      if (!result) {
        throw new Error('Failed to delete RSVP submission. No response from server.');
      }

      toast.success('RSVP submission deleted successfully');
      
      // Navigate back to the RSVP list
      router.push('/admin/rsvp');
    } catch (error) {
      console.error('Failed to delete RSVP submission:', error);
      toast.error('Failed to delete RSVP submission: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setIsDeleting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  useEffect(() => {
    fetchRsvpDetails();
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-royalBlue" />
          <p className="mt-2 text-sm text-gray-500">Loading RSVP details...</p>
        </div>
      </div>
    );
  }

  if (!rsvp) {
    return (
      <div className="flex h-[calc(100vh-200px)] w-full items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto text-amber-500" />
          <h2 className="mt-4 text-xl font-semibold">RSVP Not Found</h2>
          <p className="mt-2 text-gray-500">The RSVP submission you're looking for doesn't exist or has been deleted.</p>
          <Button 
            className="mt-4"
            onClick={() => router.push('/admin/rsvp')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to RSVP List
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          onClick={() => router.push('/admin/rsvp')}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to RSVP List
        </Button>
        
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" disabled={isDeleting}>
              <Trash className="mr-2 h-4 w-4" />
              {isDeleting ? 'Deleting...' : 'Delete RSVP'}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the RSVP submission
                from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={deleteRsvpSubmission} className="bg-destructive text-destructive-foreground">
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            {rsvp.firstName} {rsvp.lastName}
          </CardTitle>
          <CardDescription>
            RSVP Submission Details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center">
                <User className="mr-2 h-5 w-5 text-royalBlue" />
                Personal Information
              </h3>
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="w-24 font-medium">Name:</div>
                  <div>{rsvp.firstName} {rsvp.lastName}</div>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Email:</div>
                  <div className="flex items-center">
                    <Mail className="mr-2 h-4 w-4 text-gray-500" />
                    <a href={`mailto:${rsvp.email}`} className="text-royalBlue hover:underline">
                      {rsvp.email}
                    </a>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Phone:</div>
                  <div className="flex items-center">
                    <Phone className="mr-2 h-4 w-4 text-gray-500" />
                    <a href={`tel:${rsvp.phone}`} className="text-royalBlue hover:underline">
                      {rsvp.phone}
                    </a>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Country:</div>
                  <div className="flex items-center">
                    <MapPin className="mr-2 h-4 w-4 text-gray-500" />
                    {rsvp.country}
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center">
                <Calendar className="mr-2 h-5 w-5 text-royalBlue" />
                Event Information
              </h3>
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="w-24 font-medium">Events:</div>
                  <div className="flex flex-wrap gap-1">
                    {rsvp.events.map((event) => (
                      <Badge key={event} variant="outline">
                        {event}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Attendance:</div>
                  <Badge variant={rsvp.attendanceType === 'physical' ? 'default' : 'secondary'}>
                    {rsvp.attendanceType === 'physical' ? 'Physical Attendance' : 'Online Attendance'}
                  </Badge>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Reminders:</div>
                  <div className="flex flex-wrap gap-1">
                    {rsvp.reminderPreference && rsvp.reminderPreference.length > 0 ? (
                      rsvp.reminderPreference.map((pref) => (
                        <Badge key={pref} variant="outline">
                          {pref === 'email' ? 'Email Reminder' : 'SMS Reminder'}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-gray-500">No reminders requested</span>
                    )}
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-24 font-medium">Submitted:</div>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-gray-500" />
                    {formatDate(rsvp.submittedAt)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <FileText className="mr-2 h-5 w-5 text-royalBlue" />
              Additional Notes
            </h3>
            <div className="p-4 bg-gray-50 rounded-md">
              {rsvp.notes ? (
                <p className="whitespace-pre-wrap">{rsvp.notes}</p>
              ) : (
                <p className="text-gray-500 italic">No additional notes provided</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={() => window.location.href = `mailto:${rsvp.email}`}
            className="flex items-center"
          >
            <Mail className="mr-2 h-4 w-4" />
            Send Email
          </Button>
          <Button 
            onClick={() => router.push('/admin/rsvp')}
            className="flex items-center"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to RSVP List
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
