'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface DebugEditButtonProps {
  sectionName: string;
  adminPath?: string;
}

export default function DebugEditButton({ sectionName, adminPath }: DebugEditButtonProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Log session info for debugging
  console.log(`DebugEditButton for ${sectionName}:`, {
    session,
    status,
    isAdmin: session?.user?.role === 'admin' || session?.user?.role === 'super_admin',
    role: session?.user?.role
  });

  const handleClick = () => {
    if (adminPath) {
      router.push(adminPath);
    } else {
      toast.info(`Edit ${sectionName} in the admin dashboard`, {
        action: {
          label: 'Go to Admin',
          onClick: () => router.push('/admin'),
        },
      });
    }
  };

  return (
    <Button
      variant="default"
      size="sm"
      onClick={handleClick}
      className="absolute top-16 right-4 z-50 bg-red-500 hover:bg-red-600 text-white font-bold shadow-lg"
    >
      <Edit className="h-4 w-4 mr-2" />
      DEBUG: Edit {sectionName} {session ? `(${session?.user?.role || 'No role'})` : '(Not logged in)'}
    </Button>
  );
}
