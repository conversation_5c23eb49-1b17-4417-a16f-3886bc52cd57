'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { ArrowLeft, Plus, Trash2, MoveUp, MoveDown, Save } from 'lucide-react';
import Link from 'next/link';
import { getWriteClient } from '@/lib/sanity.client';
import EditSectionButton from '../../../components/EditSectionButton';

// Define section types for the page builder
const sectionTypes = [
  { value: 'hero', label: 'Hero Section' },
  { value: 'textSection', label: 'Text Section' },
  { value: 'imageGallery', label: 'Image Gallery' },
  { value: 'featuredContent', label: 'Featured Content' },
  { value: 'contactForm', label: 'Contact Form' },
];

// Navigation categories
const navCategories = [
  { value: 'About', label: 'About' },
  { value: 'Media', label: 'Media' },
  { value: 'Events', label: 'Events' },
  { value: 'Other', label: 'Other' },
];

export default function EditPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = params;

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Page data
  const [page, setPage] = useState<any>({
    title: '',
    slug: { current: '' },
    navMenu: false,
    navOrder: 100,
    navCategory: '',
    pageBuilder: [],
  });

  // Fetch page data
  useEffect(() => {
    const fetchPage = async () => {
      try {
        setIsLoading(true);

        // Create a Sanity client to fetch the page
        const client = getWriteClient();

        // Fetch the page by ID
        const pageData = await client.getDocument(id);

        if (!pageData) {
          throw new Error('Page not found');
        }

        console.log('Fetched page data from Sanity:', pageData);

        // Initialize pageBuilder array if it doesn't exist
        if (!pageData.pageBuilder) {
          pageData.pageBuilder = [];
        }

        setPage(pageData);
      } catch (error) {
        console.error('Error fetching page:', error);
        toast.error('Failed to load page data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPage();
  }, [id]);

  // Handle form changes
  const handleChange = (field: string, value: any) => {
    setPage((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle slug change (convert title to slug)
  const handleSlugChange = (value: string) => {
    const slug = value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '');

    setPage((prev: any) => ({
      ...prev,
      slug: { current: slug },
    }));
  };

  // Add a new section to the page builder
  const addSection = (type: string) => {
    const newSection = {
      _key: `${type}_${Date.now()}`,
      _type: type,
    };

    // Add default fields based on section type
    switch (type) {
      case 'hero':
        Object.assign(newSection, {
          heading: 'New Hero Section',
          tagline: 'Add a tagline here',
          ctas: [],
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring',
          },
        });
        break;
      case 'textSection':
        Object.assign(newSection, {
          heading: 'New Text Section',
          text: [
            {
              _type: 'block',
              children: [
                {
                  _type: 'span',
                  text: 'Add your content here...',
                },
              ],
            },
          ],
          backgroundStyle: 'none',
          textAlign: 'left',
        });
        break;
      case 'imageGallery':
        Object.assign(newSection, {
          heading: 'New Gallery',
          description: 'Add a description here',
          images: [],
          displayStyle: 'grid',
          backgroundStyle: 'none',
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring',
          },
        });
        break;
      case 'featuredContent':
        Object.assign(newSection, {
          heading: 'Featured Content',
          description: 'Add a description here',
          items: [],
          layout: 'cards',
          backgroundStyle: 'none',
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring',
          },
        });
        break;
      case 'contactForm':
        Object.assign(newSection, {
          heading: 'Contact Us',
          description: 'Get in touch with us',
          formFields: [],
          submitButtonText: 'Send Message',
          successMessage: 'Thank you for your message. We will get back to you soon.',
          backgroundStyle: 'none',
          animation: {
            duration: 0.6,
            delay: 0,
            stagger: 0.2,
            type: 'spring',
          },
        });
        break;
    }

    setPage((prev: any) => ({
      ...prev,
      pageBuilder: [...prev.pageBuilder, newSection],
    }));

    toast.success(`Added new ${type} section`);
  };

  // Remove a section from the page builder
  const removeSection = (index: number) => {
    setPage((prev: any) => ({
      ...prev,
      pageBuilder: prev.pageBuilder.filter((_: any, i: number) => i !== index),
    }));

    toast.success('Section removed');
  };

  // Move a section up in the page builder
  const moveSectionUp = (index: number) => {
    if (index === 0) return;

    setPage((prev: any) => {
      const newPageBuilder = [...prev.pageBuilder];
      const temp = newPageBuilder[index];
      newPageBuilder[index] = newPageBuilder[index - 1];
      newPageBuilder[index - 1] = temp;
      return {
        ...prev,
        pageBuilder: newPageBuilder,
      };
    });
  };

  // Move a section down in the page builder
  const moveSectionDown = (index: number) => {
    if (index === page.pageBuilder.length - 1) return;

    setPage((prev: any) => {
      const newPageBuilder = [...prev.pageBuilder];
      const temp = newPageBuilder[index];
      newPageBuilder[index] = newPageBuilder[index + 1];
      newPageBuilder[index + 1] = temp;
      return {
        ...prev,
        pageBuilder: newPageBuilder,
      };
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);

      // Get the Sanity write client
      const client = getWriteClient();

      // Prepare the page data for saving
      const pageData = {
        ...page,
        _type: 'page',
        updatedAt: new Date().toISOString()
      };

      console.log('Saving page to Sanity:', pageData);

      // Save the page to Sanity
      const result = await client.createOrReplace(pageData);

      console.log('Page saved successfully:', result);
      toast.success('Page saved successfully');
      router.push('/admin/pages');
    } catch (error) {
      console.error('Error saving page:', error);
      toast.error('Failed to save page: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p>Loading page data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/pages">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Page</h1>
            <p className="text-muted-foreground">
              Edit page content and settings
            </p>
          </div>
        </div>
        <Button onClick={handleSubmit} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Page'}
        </Button>
      </div>

      <Separator />

      <form onSubmit={handleSubmit} className="space-y-8">
        <Tabs defaultValue="content" className="w-full">
          <TabsList>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="seo">SEO</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-6 mt-6">
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="title">Page Title</Label>
                  <Input
                    id="title"
                    value={page.title}
                    onChange={(e) => {
                      handleChange('title', e.target.value);
                      if (!page._id) {
                        // Only auto-generate slug for new pages
                        handleSlugChange(e.target.value);
                      }
                    }}
                    placeholder="Page Title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug</Label>
                  <div className="flex items-center space-x-2">
                    <span className="text-muted-foreground">/</span>
                    <Input
                      id="slug"
                      value={page.slug.current}
                      onChange={(e) => handleChange('slug', { current: e.target.value })}
                      placeholder="page-slug"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Page Builder</h3>
                <Select onValueChange={addSection}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Add section" />
                  </SelectTrigger>
                  <SelectContent>
                    {sectionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {page.pageBuilder.length === 0 ? (
                <div className="text-center py-10 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">No sections added yet. Add a section to get started.</p>
                  <div className="mt-4 flex justify-center">
                    <Select onValueChange={addSection}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Add your first section" />
                      </SelectTrigger>
                      <SelectContent>
                        {sectionTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {page.pageBuilder.map((section: any, index: number) => (
                    <Card key={section._key}>
                      <CardHeader className="pb-2 flex flex-row items-center justify-between">
                        <CardTitle className="text-lg font-medium">
                          {sectionTypes.find(type => type.value === section._type)?.label || section._type}
                          {section.heading && `: ${section.heading}`}
                        </CardTitle>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => moveSectionUp(index)}
                            disabled={index === 0}
                          >
                            <MoveUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => moveSectionDown(index)}
                            disabled={index === page.pageBuilder.length - 1}
                          >
                            <MoveDown className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => removeSection(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {/* Section editor UI would go here - simplified for this example */}
                          <div className="grid gap-4 md:grid-cols-2">
                            {section.heading !== undefined && (
                              <div className="space-y-2">
                                <Label htmlFor={`section-${index}-heading`}>Heading</Label>
                                <Input
                                  id={`section-${index}-heading`}
                                  value={section.heading}
                                  onChange={(e) => {
                                    const newPageBuilder = [...page.pageBuilder];
                                    newPageBuilder[index].heading = e.target.value;
                                    handleChange('pageBuilder', newPageBuilder);
                                  }}
                                  placeholder="Section Heading"
                                />
                              </div>
                            )}

                            {(section.tagline !== undefined || section.description !== undefined) && (
                              <div className="space-y-2">
                                <Label htmlFor={`section-${index}-description`}>
                                  {section.tagline !== undefined ? 'Tagline' : 'Description'}
                                </Label>
                                <Textarea
                                  id={`section-${index}-description`}
                                  value={section.tagline || section.description || ''}
                                  onChange={(e) => {
                                    const newPageBuilder = [...page.pageBuilder];
                                    if (section.tagline !== undefined) {
                                      newPageBuilder[index].tagline = e.target.value;
                                    } else {
                                      newPageBuilder[index].description = e.target.value;
                                    }
                                    handleChange('pageBuilder', newPageBuilder);
                                  }}
                                  placeholder={section.tagline !== undefined ? 'Section Tagline' : 'Section Description'}
                                />
                              </div>
                            )}
                          </div>

                          {/* Animation settings */}
                          {section.animation && (
                            <div className="mt-4">
                              <h4 className="text-sm font-medium mb-2">Animation Settings</h4>
                              <div className="grid gap-4 md:grid-cols-4">
                                <div className="space-y-2">
                                  <Label htmlFor={`section-${index}-animation-duration`}>Duration</Label>
                                  <Input
                                    id={`section-${index}-animation-duration`}
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    value={section.animation.duration}
                                    onChange={(e) => {
                                      const newPageBuilder = [...page.pageBuilder];
                                      newPageBuilder[index].animation.duration = parseFloat(e.target.value);
                                      handleChange('pageBuilder', newPageBuilder);
                                    }}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor={`section-${index}-animation-delay`}>Delay</Label>
                                  <Input
                                    id={`section-${index}-animation-delay`}
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    value={section.animation.delay}
                                    onChange={(e) => {
                                      const newPageBuilder = [...page.pageBuilder];
                                      newPageBuilder[index].animation.delay = parseFloat(e.target.value);
                                      handleChange('pageBuilder', newPageBuilder);
                                    }}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor={`section-${index}-animation-stagger`}>Stagger</Label>
                                  <Input
                                    id={`section-${index}-animation-stagger`}
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    value={section.animation.stagger}
                                    onChange={(e) => {
                                      const newPageBuilder = [...page.pageBuilder];
                                      newPageBuilder[index].animation.stagger = parseFloat(e.target.value);
                                      handleChange('pageBuilder', newPageBuilder);
                                    }}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor={`section-${index}-animation-type`}>Type</Label>
                                  <Select
                                    value={section.animation.type}
                                    onValueChange={(value) => {
                                      const newPageBuilder = [...page.pageBuilder];
                                      newPageBuilder[index].animation.type = value;
                                      handleChange('pageBuilder', newPageBuilder);
                                    }}
                                  >
                                    <SelectTrigger id={`section-${index}-animation-type`}>
                                      <SelectValue placeholder="Animation type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="spring">Spring</SelectItem>
                                      <SelectItem value="tween">Tween</SelectItem>
                                      <SelectItem value="inertia">Inertia</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Section specific settings would be expanded here */}
                          <div className="mt-4 text-center">
                            <EditSectionButton
                              pageId={id}
                              sectionIndex={index}
                              sectionType={section._type}
                              sectionTitle={section.heading || 'Untitled Section'}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6 mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="navMenu">Show in Navigation Menu</Label>
                  <p className="text-sm text-muted-foreground">
                    Toggle to show this page in the main navigation menu
                  </p>
                </div>
                <Switch
                  id="navMenu"
                  checked={page.navMenu}
                  onCheckedChange={(checked) => handleChange('navMenu', checked)}
                />
              </div>

              {page.navMenu && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="navOrder">Navigation Order</Label>
                    <Input
                      id="navOrder"
                      type="number"
                      min="1"
                      value={page.navOrder}
                      onChange={(e) => handleChange('navOrder', parseInt(e.target.value))}
                      placeholder="100"
                    />
                    <p className="text-sm text-muted-foreground">
                      Lower numbers appear first in the navigation
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="navCategory">Navigation Category</Label>
                    <Select
                      value={page.navCategory}
                      onValueChange={(value) => handleChange('navCategory', value)}
                    >
                      <SelectTrigger id="navCategory">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {navCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground">
                      Group this page under a dropdown in the navigation menu
                    </p>
                  </div>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="seo" className="space-y-6 mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  value={page.seoTitle || ''}
                  onChange={(e) => handleChange('seoTitle', e.target.value)}
                  placeholder="SEO Title (leave blank to use page title)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  value={page.seoDescription || ''}
                  onChange={(e) => handleChange('seoDescription', e.target.value)}
                  placeholder="Brief description for search engines"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ogImage">Social Media Image</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="ogImage"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      // Handle image upload in a real implementation
                      toast.info('Image upload would be handled here');
                    }}
                  />
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => toast.info('Image selection would open here')}
                  >
                    Select from Media
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Image displayed when sharing on social media
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? 'Saving...' : 'Save Page'}
          </Button>
        </div>
      </form>
    </div>
  );
}
