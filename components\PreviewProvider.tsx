'use client';

// Simple preview provider that just renders children
export default function PreviewProvider({
  children,
  token,
}: {
  children: React.ReactNode;
  token: string;
}) {
  return (
    <div className="preview-provider">
      {children}
      <div className="fixed bottom-0 left-0 right-0 bg-blue-500 text-white p-2 text-center text-sm">
        Preview Mode Active - <a href="/api/exit-preview" className="underline">Exit Preview</a>
      </div>
    </div>
  );
}
