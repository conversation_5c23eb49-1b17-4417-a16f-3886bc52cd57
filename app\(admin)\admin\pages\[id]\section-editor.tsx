'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { getWriteClient } from '@/lib/sanity.client';
import { Save, ArrowLeft, Plus, Trash, MoveUp, MoveDown, Image as ImageIcon, Type, Layout, FileText } from 'lucide-react';

interface SectionEditorProps {
  pageId: string;
  section: any;
  sectionIndex: number;
  onSave: () => void;
  onCancel: () => void;
}

export default function SectionEditor({ pageId, section, sectionIndex, onSave, onCancel }: SectionEditorProps) {
  const router = useRouter();
  const [sectionData, setSectionData] = useState<any>(section || {});
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('content');

  // Initialize section data if it's empty
  useEffect(() => {
    if (!section || Object.keys(section).length === 0) {
      // Set default section data based on type
      setSectionData({
        _type: 'textSection',
        heading: '',
        text: '',
      });
    } else {
      setSectionData(section);
    }
  }, [section]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSectionData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setSectionData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setSectionData((prev: any) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle section type change
  const handleSectionTypeChange = (type: string) => {
    // Create a new section data object based on the selected type
    let newSectionData: any = {
      _type: type,
    };

    // Add default fields based on section type
    switch (type) {
      case 'hero':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          tagline: sectionData.tagline || '',
          ctas: sectionData.ctas || [],
        };
        break;
      case 'textSection':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
        };
        break;
      case 'imageGallery':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          images: sectionData.images || [],
        };
        break;
      case 'featuredContent':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          items: sectionData.items || [],
        };
        break;
      case 'contactForm':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          showMap: sectionData.showMap || false,
        };
        break;
      default:
        break;
    }

    setSectionData(newSectionData);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Get the Sanity client
      const client = getWriteClient();

      // Get the current page
      const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id: pageId });

      if (!page) {
        toast.error('Page not found');
        setIsSaving(false);
        return;
      }

      // Get the current page builder array
      const pageBuilder = page.pageBuilder || [];

      // Update or add the section
      if (sectionIndex >= 0 && sectionIndex < pageBuilder.length) {
        // Update existing section
        pageBuilder[sectionIndex] = sectionData;
      } else {
        // Add new section
        pageBuilder.push(sectionData);
      }

      // Update the page
      await client.patch(pageId)
        .set({ pageBuilder })
        .commit();

      toast.success('Section saved successfully');
      onSave();
    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section');
    } finally {
      setIsSaving(false);
    }
  };

  // Render form fields based on section type
  const renderSectionFields = () => {
    switch (sectionData._type) {
      case 'hero':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={sectionData.tagline || ''}
                onChange={handleInputChange}
                placeholder="Enter tagline"
              />
            </div>
            <div>
              <Label>Background Image</Label>
              <div className="mt-2 p-4 border border-dashed rounded-md text-center">
                {sectionData.backgroundImage ? (
                  <div className="relative h-40 w-full">
                    <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                      <p className="text-sm text-gray-500">Image preview (available in Sanity Studio)</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <ImageIcon className="h-8 w-8 mx-auto text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">
                      Image upload is available in Sanity Studio
                    </p>
                  </>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="animation">Animation Settings</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="animationDuration" className="text-xs">Duration</Label>
                  <Input
                    id="animationDuration"
                    name="animation.duration"
                    type="number"
                    step="0.1"
                    min="0"
                    value={sectionData.animation?.duration || 0.6}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      setSectionData((prev: any) => ({
                        ...prev,
                        animation: {
                          ...(prev.animation || {}),
                          duration: value,
                        },
                      }));
                    }}
                  />
                </div>
                <div>
                  <Label htmlFor="animationDelay" className="text-xs">Delay</Label>
                  <Input
                    id="animationDelay"
                    name="animation.delay"
                    type="number"
                    step="0.1"
                    min="0"
                    value={sectionData.animation?.delay || 0}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      setSectionData((prev: any) => ({
                        ...prev,
                        animation: {
                          ...(prev.animation || {}),
                          delay: value,
                        },
                      }));
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      case 'textSection':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Text Content</Label>
              <Textarea
                id="text"
                name="text"
                value={typeof sectionData.text === 'string'
                  ? sectionData.text
                  : Array.isArray(sectionData.text)
                    ? JSON.stringify(sectionData.text)
                    : ''}
                onChange={(e) => {
                  try {
                    // Try to parse as JSON if it looks like JSON
                    if (e.target.value.trim().startsWith('[') && e.target.value.trim().endsWith(']')) {
                      const parsedValue = JSON.parse(e.target.value);
                      setSectionData((prev: any) => ({
                        ...prev,
                        text: parsedValue,
                      }));
                    } else {
                      // Otherwise treat as plain text
                      setSectionData((prev: any) => ({
                        ...prev,
                        text: e.target.value,
                      }));
                    }
                  } catch (error) {
                    // If JSON parsing fails, just use the raw text
                    setSectionData((prev: any) => ({
                      ...prev,
                      text: e.target.value,
                    }));
                  }
                }}
                placeholder="Enter text content"
                rows={8}
              />
              <p className="text-xs text-gray-500 mt-1">
                Note: For rich text content, you can edit this in the Sanity Studio for more formatting options.
              </p>
            </div>
            <div>
              <Label htmlFor="backgroundStyle">Background Style</Label>
              <Select
                value={sectionData.backgroundStyle || 'none'}
                onValueChange={(value) => handleSelectChange('backgroundStyle', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select background style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="royalBlue">Royal Blue</SelectItem>
                  <SelectItem value="royalGold">Royal Gold</SelectItem>
                  <SelectItem value="ivory">Ivory</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="textAlign">Text Alignment</Label>
              <Select
                value={sectionData.textAlign || 'left'}
                onValueChange={(value) => handleSelectChange('textAlign', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select text alignment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );
      case 'imageGallery':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Description</Label>
              <Textarea
                id="text"
                name="text"
                value={sectionData.text || ''}
                onChange={handleInputChange}
                placeholder="Enter gallery description"
                rows={4}
              />
            </div>
            <div>
              <Label>Gallery Images</Label>
              <div className="mt-2 p-4 border border-dashed rounded-md text-center">
                <ImageIcon className="h-8 w-8 mx-auto text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">
                  Image management will be available soon
                </p>
              </div>
            </div>
          </div>
        );
      case 'contactForm':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Description</Label>
              <Textarea
                id="text"
                name="text"
                value={sectionData.text || ''}
                onChange={handleInputChange}
                placeholder="Enter form description"
                rows={4}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="showMap"
                checked={sectionData.showMap || false}
                onCheckedChange={(checked) => handleSwitchChange('showMap', checked)}
              />
              <Label htmlFor="showMap">Show Map</Label>
            </div>
          </div>
        );
      default:
        return (
          <div className="p-4 text-center">
            <p>Please select a section type</p>
          </div>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {sectionIndex >= 0 ? 'Edit Section' : 'Add New Section'}
          </CardTitle>
          <CardDescription>
            Customize this section of your page
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="content" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-4">
              {renderSectionFields()}
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div>
                <Label htmlFor="sectionType">Section Type</Label>
                <Select
                  value={sectionData._type}
                  onValueChange={(value) => handleSectionTypeChange(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select section type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hero">Hero Section</SelectItem>
                    <SelectItem value="textSection">Text Section</SelectItem>
                    <SelectItem value="imageGallery">Image Gallery</SelectItem>
                    <SelectItem value="featuredContent">Featured Content</SelectItem>
                    <SelectItem value="contactForm">Contact Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" type="button" onClick={onCancel}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>Saving...</>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Section
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
