import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'strategicPartner',
  title: 'Strategic Partner',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Partner Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'logo',
      title: 'Partner Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alternative Text',
          type: 'string',
          description: 'Important for SEO and accessibility',
          validation: (Rule) => Rule.required(),
        },
      ],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'website',
      title: 'Website URL',
      type: 'url',
      validation: (Rule) => Rule.uri({
        scheme: ['http', 'https']
      }),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'partnershipType',
      title: 'Partnership Type',
      type: 'string',
      options: {
        list: [
          { title: 'Strategic Financial', value: 'corporate' },
          { title: 'Heritage & Cultural', value: 'heritage' },
          { title: 'Educational Development', value: 'educational' },
          { title: 'Innovation & Technology', value: 'technology' },
          { title: 'Strategic Investment', value: 'investment' },
          { title: 'Traditional Governance', value: 'governance' },
          { title: 'NGO', value: 'ngo' },
          { title: 'Government', value: 'government' },
          { title: 'Media', value: 'media' },
          { title: 'Other', value: 'other' },
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'featured',
      title: 'Featured Partner',
      type: 'boolean',
      description: 'Display this partner prominently on the homepage',
      initialValue: false,
    }),
    defineField({
      name: 'order',
      title: 'Display Order',
      type: 'number',
      description: 'Partners with lower numbers will be displayed first',
      initialValue: 0,
    }),
    defineField({
      name: 'startDate',
      title: 'Partnership Start Date',
      type: 'date',
    }),
    defineField({
      name: 'active',
      title: 'Active Partnership',
      type: 'boolean',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'partnershipType',
      media: 'logo',
    },
    prepare(selection) {
      const { title, subtitle, media } = selection;
      return {
        title,
        subtitle: subtitle ? `${subtitle.charAt(0).toUpperCase() + subtitle.slice(1)} Partner` : '',
        media,
      };
    },
  },
});
