// <PERSON>ript to clean up the project before deployment to Heroku
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute shell commands and log output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`<PERSON>rror executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Function to delete a directory
function deleteDirectory(dirPath) {
  console.log(`Deleting directory: ${dirPath}`);
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`Deleted ${dirPath}`);
    } else {
      console.log(`Directory does not exist: ${dirPath}`);
    }
  } catch (error) {
    console.error(`Error deleting directory: ${dirPath}`);
    console.error(error);
    // Try with rimraf as a fallback
    try {
      runCommand(`npx rimraf ${dirPath}`);
    } catch (rimrafError) {
      console.error(`Error using rimraf: ${rimrafError.message}`);
    }
  }
}

// Main function
async function main() {
  console.log('=== Pre-Deployment Cleanup ====');

  // Step 1: Clean the Next.js cache and build files
  console.log('\nStep 1: Cleaning Next.js cache and build files');
  deleteDirectory('.next');

  // Step 2: Remove node_modules
  console.log('\nStep 2: Removing node_modules');
  deleteDirectory('node_modules');

  // Step 3: Clean npm cache
  console.log('\nStep 3: Cleaning npm cache');
  runCommand('npm cache clean --force');

  // Step 3.5: Remove only development files
  console.log('\nStep 3.5: Removing only development files');
  // Only remove node_modules in sanity directory if it exists
  if (fs.existsSync('sanity/node_modules')) {
    console.log('Removing sanity/node_modules');
    deleteDirectory('sanity/node_modules');
  }
  // Only remove .sanity cache if it exists
  if (fs.existsSync('sanity/.sanity')) {
    console.log('Removing sanity/.sanity cache');
    deleteDirectory('sanity/.sanity');
  }

  // Step 4: Create a more aggressive .slugignore file
  console.log('\nStep 4: Creating optimized .slugignore file');
  const slugignoreContent = `
# Heroku .slugignore - Optimized for Next.js + Sanity application
# This file specifies files that should be excluded from the Heroku slug

# Development files
.git
.github
.vscode
.husky
.next/cache
node_modules/.cache
*.log

# Test files
__tests__
test
tests
cypress
jest
*.test.js
*.spec.js
*.test.tsx
*.spec.tsx

# Documentation
docs
documentation
*.md
!README.md

# Source maps
*.map

# Development configs
.eslintrc*
.prettierrc*
.babelrc*
tsconfig.json
jest.config.js
cypress.config.js
postcss.config.js
tailwind.config.js

# Large media files (these should be served from a CDN)
public/videos
public/images/large
public/images/original
public/uploads

# Development scripts
scripts
dev-scripts
.storybook

# Sanity Studio (only needed for development)
studio-node-modules
sanity-studio-backup
node_modules/@sanity/vision
node_modules/@sanity/dashboard
node_modules/@sanity/plugin-loader
node_modules/@sanity/desk-tool
node_modules/@sanity/form-builder
node_modules/@sanity/default-layout
node_modules/@sanity/default-login
node_modules/@sanity/google-maps-input
node_modules/@sanity/language-filter
node_modules/@sanity/cli

# Development dependencies
node_modules/typescript
node_modules/@types
node_modules/eslint
node_modules/prettier
node_modules/jest
node_modules/cypress
node_modules/storybook
node_modules/webpack-dev-server
node_modules/babel-loader
node_modules/style-loader
node_modules/css-loader
node_modules/postcss-loader
node_modules/sass-loader
node_modules/file-loader
node_modules/url-loader
node_modules/html-webpack-plugin
node_modules/webpack-merge
node_modules/webpack-bundle-analyzer
node_modules/webpack-manifest-plugin
node_modules/webpack-dev-middleware
node_modules/webpack-hot-middleware
node_modules/mini-css-extract-plugin
node_modules/optimize-css-assets-webpack-plugin
node_modules/terser-webpack-plugin
node_modules/copy-webpack-plugin
node_modules/clean-webpack-plugin
node_modules/case-sensitive-paths-webpack-plugin
node_modules/dotenv-webpack
node_modules/fork-ts-checker-webpack-plugin
node_modules/ts-loader
node_modules/ts-node

# Exclude all node_modules except essential ones
node_modules/*
!node_modules/next
!node_modules/react
!node_modules/react-dom
!node_modules/@sanity/client
!node_modules/@sanity/image-url
!node_modules/next-sanity
!node_modules/next-auth
!node_modules/sanity-plugin-seo
`;
  fs.writeFileSync('.slugignore', slugignoreContent);
  console.log('Created .slugignore file');

  // Step 5: Create an optimized .npmrc file
  console.log('\nStep 5: Creating optimized .npmrc file');
  const npmrcContent = `
# Optimize npm for production
production=true
fund=false
audit=false
progress=false
loglevel=error
save-exact=true
legacy-peer-deps=true
`;
  fs.writeFileSync('.npmrc', npmrcContent);
  console.log('Created .npmrc file');

  console.log('\nPre-deployment cleanup completed!');
  console.log('You can now deploy to Heroku with:');
  console.log('git push heroku store-functionality:main');
}

// Run the main function
main().catch(error => {
  console.error('Error during cleanup:', error);
  process.exit(1);
});
