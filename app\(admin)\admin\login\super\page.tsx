'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Shield, Loader2, EyeIcon, EyeOffIcon } from '@/components/icons';
import { toast } from 'sonner';

export default function SuperAdminLoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        username: formData.username,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        toast.error('Invalid credentials');
        setIsLoading(false);
        return;
      }

      // Check if user is super admin
      const response = await fetch('/api/auth/check-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ expectedRole: 'super_admin' }),
      });

      const data = await response.json();

      if (data.success && data.hasRole) {
        toast.success('Super admin login successful');
        router.push('/admin/super');
      } else {
        toast.error('You do not have super admin privileges');
        // Sign out if not a super admin
        await fetch('/api/auth/logout');
        router.push('/admin/login');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-royalBlue to-royalGold/70 p-4">
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10"></div>
      <Card className="w-full max-w-md shadow-xl border-royalGold/30 backdrop-blur-sm bg-white/95">
        <CardHeader className="space-y-2 text-center">
          <div className="flex justify-center mb-4">
            <Shield className="h-16 w-16 text-royalGold" />
          </div>
          <CardTitle className="text-2xl font-bold text-royalBlue">Super Admin Access</CardTitle>
          <CardDescription className="text-royalBlue/70">
            Restricted area - Super administrator credentials required
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-royalBlue">Username</Label>
              <Input
                id="username"
                name="username"
                placeholder="superadmin"
                value={formData.username}
                onChange={handleChange}
                className="border-royalBlue/30 focus:border-royalGold focus:ring-royalGold/30"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-royalBlue">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  value={formData.password}
                  onChange={handleChange}
                  className="border-royalBlue/30 focus:border-royalGold focus:ring-royalGold/30"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-royalBlue/70 hover:text-royalBlue hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? 'Hide password' : 'Show password'}
                  </span>
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              className="w-full bg-royalGold hover:bg-royalGold/90 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Authenticating...
                </div>
              ) : (
                <div className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Access Super Admin
                </div>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
