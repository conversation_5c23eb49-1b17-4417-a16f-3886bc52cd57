import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'rsvp',
  title: 'RSVP Submissions',
  type: 'document',
  fields: [
    defineField({
      name: 'firstName',
      title: 'First Name',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'lastName',
      title: 'Last Name',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'email',
      title: 'Email',
      type: 'string',
      validation: Rule => Rule.required().email()
    }),
    defineField({
      name: 'phone',
      title: 'Phone',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'country',
      title: 'Country',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'events',
      title: 'Events',
      type: 'array',
      of: [{ type: 'string' }],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'attendanceType',
      title: 'Attendance Type',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'reminderPreference',
      title: 'Reminder Preference',
      type: 'array',
      of: [{ type: 'string' }]
    }),
    defineField({
      name: 'notes',
      title: 'Additional Notes',
      type: 'text'
    }),
    defineField({
      name: 'submittedAt',
      title: 'Submitted At',
      type: 'datetime',
      validation: Rule => Rule.required()
    })
  ],
  preview: {
    select: {
      title: 'firstName',
      subtitle: 'lastName',
      email: 'email'
    },
    prepare({ title, subtitle, email }) {
      return {
        title: `${title} ${subtitle}`,
        subtitle: email
      };
    }
  }
});