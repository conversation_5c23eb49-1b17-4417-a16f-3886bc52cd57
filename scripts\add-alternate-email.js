// This script adds an alternate email for a user
// Run with: node scripts/add-alternate-email.js

const { createClient } = require('@sanity/client');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Ensure the data directory exists
const ensureDataDir = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Create the users file if it doesn't exist
const ensureUsersFile = () => {
  ensureDataDir();
  if (!fs.existsSync(usersFilePath)) {
    fs.writeFileSync(usersFilePath, JSON.stringify([], null, 2));
  }
};

// Get all local users
const getLocalUsers = () => {
  ensureUsersFile();
  try {
    const usersData = fs.readFileSync(usersFilePath, 'utf8');
    return JSON.parse(usersData);
  } catch (error) {
    console.error('Error reading local users file:', error);
    return [];
  }
};

// Save local users
const saveLocalUsers = (users) => {
  ensureUsersFile();
  fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
};

// Add an alternate email for a user
const addAlternateEmail = async (originalEmail, alternateEmail, password) => {
  try {
    console.log(`Adding alternate email ${alternateEmail} for user with email ${originalEmail}`);
    
    // Find the user in Sanity
    const sanityUser = await client.fetch(
      `*[_type == "adminUser" && email == $email][0]`,
      { email: originalEmail }
    );
    
    if (!sanityUser) {
      console.log(`User with email ${originalEmail} not found in Sanity`);
      return false;
    }
    
    console.log(`Found user in Sanity: ${sanityUser.name} (${sanityUser._id})`);
    
    // Create a new user in Sanity with the alternate email
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const newSanityUser = {
      _type: 'adminUser',
      _id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      username: alternateEmail.split('@')[0],
      name: sanityUser.name,
      email: alternateEmail,
      role: sanityUser.role,
      hashedPassword,
      isActive: true,
      isProtected: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    const createdUser = await client.createOrReplace(newSanityUser);
    console.log(`Created new user in Sanity with ID: ${createdUser._id}`);
    
    // Add the user to the local users file
    const localUsers = getLocalUsers();
    
    // Check if the user already exists locally
    const existingUserIndex = localUsers.findIndex(user => user.email === alternateEmail);
    
    if (existingUserIndex !== -1) {
      console.log(`User with email ${alternateEmail} already exists locally, updating`);
      
      localUsers[existingUserIndex] = {
        ...localUsers[existingUserIndex],
        name: sanityUser.name,
        password: hashedPassword,
        role: sanityUser.role,
        isTemporary: false,
        updatedAt: new Date().toISOString()
      };
    } else {
      console.log(`Adding new local user with email ${alternateEmail}`);
      
      localUsers.push({
        id: createdUser._id,
        name: sanityUser.name,
        email: alternateEmail,
        password: hashedPassword,
        role: sanityUser.role,
        isTemporary: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLogin: null
      });
    }
    
    saveLocalUsers(localUsers);
    console.log(`Local users file updated`);
    
    return true;
  } catch (error) {
    console.error('Error adding alternate email:', error);
    return false;
  }
};

// Main function
async function main() {
  console.log('Starting alternate email addition...');
  
  // Add alternate email for the user
  await addAlternateEmail(
    '<EMAIL>',  // Original email
    '<EMAIL>',   // Alternate email with typo
    'Admin123!'                 // Password
  );
  
  console.log('\nAlternate email addition complete!');
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
