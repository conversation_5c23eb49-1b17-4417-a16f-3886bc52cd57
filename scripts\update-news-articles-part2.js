// This script updates existing news articles in the Sanity dataset with complete content
require('dotenv').config();
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Array of news articles to update with complete content
const newsArticles = [
  {
    title: "<PERSON>: The Visionary Leader of the Akuapem Kingdom",
    slug: "nana-otutu-ababio-v-the-visionary-leader-of-the-akuapem-kingdom",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "In the heart of Adukrom, one leader stands as a beacon of hope and progress: <PERSON>. His tireless efforts and visionary initiatives are reshaping the community, making him a true hero to his people."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "<PERSON>, the revered <PERSON>fa<PERSON>e (Chief of the Right) and Adukrom Hene (Chief of Adukrom), combines deep-rooted traditional wisdom with modern insights, thanks to his academic background from the University of Education Winneba. His leadership transcends mere titles; it is marked by a profound commitment to improving the lives of his community."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "At the forefront of Nana Ababio V's initiatives is a bold plan to address food insecurity, a pressing issue in many regions. Recognizing the critical role of agriculture in sustaining his community, he has championed efforts to boost agricultural production and sustainability. By introducing advanced farming techniques and supporting local farmers, Nana Ababio V is ensuring that Adukrom has a reliable and robust food supply, reducing dependence on external sources and fostering local self-sufficiency."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "But his vision extends far beyond agriculture. Nana Ababio V is dedicated to enhancing public infrastructure, particularly in the areas of water supply and sanitation. Under his leadership, Adukrom is witnessing significant improvements in its water and sewer systems. His initiatives aim to provide clean, safe water to every household, a fundamental aspect of public health and quality of life. His commitment to upgrading sanitation facilities is equally commendable, addressing a critical need and fostering a healthier environment for all residents."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "One of Nana Ababio V's most ambitious projects is the establishment of a local banking system. This groundbreaking initiative is set to revolutionize the financial landscape of Adukrom, offering accessible and inclusive financial services to the community. By creating a sustainable banking infrastructure, Nana Ababio V is not only empowering residents but also stimulating local economic growth. This visionary plan will support local businesses, provide financial stability, and drive economic development, further solidifying his role as a transformative leader."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Nana Otutu Ababio V's leadership embodies a blend of tradition and innovation. His tireless efforts to improve food security, enhance public works, and establish a local banking system reflect a deep commitment to the welfare and advancement of Adukrom. Through his visionary leadership, he is building a legacy of progress and prosperity, truly deserving of the title of hero in the eyes of his people."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "As Adukrom continues to thrive under his guidance, Nana Otutu Ababio V stands as a shining example of how dedicated leadership and visionary thinking can bring about transformative change. His work not only addresses immediate needs but also lays a solid foundation for a prosperous future, making him a hero whose impact will be felt for generations to come."
          }
        ]
      }
    ]
  },
  {
    title: "Nananom of Adukrom Honor Hon. Asiedu Offei and Nia Senior High School During 5th Akwasidae Festival",
    slug: "nananom-of-adukrom-honor-hon-asiedu-offei-and-nia-senior-high-school",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Adukrom, Eastern Region, Ghana — October 2024 — The 5th Akwasidae Festival, celebrated with traditional pomp and grandeur, saw the Nananom of Adukrom, led by Nana Otutuababiov, King of Adukrom, recognizing distinguished contributions to the community. Two significant awards were presented during the event, marking the day as a celebration of both leadership and academic excellence."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Honorable Asiedu Offei, the Presiding Member (PM) of Akwapim North, was awarded for his meritorious contribution to the well-being of the Adukrom community. The Nananom, in presenting this prestigious honor, commended Hon. Offei for his tireless dedication to local development initiatives that have improved the quality of life for the people of Adukrom. His leadership, advocacy for community projects, and service to his constituents have been instrumental in bringing positive change to the area."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "In a separate recognition, Nia Senior High School was awarded a citation for their exceptional performance after qualifying at the regional level to contest in the National Science and Maths Quiz. This achievement highlights the school's commitment to academic excellence, particularly in STEM (Science, Technology, Engineering, and Mathematics) education. The Nananom, in presenting the citation, praised the students and faculty for their hard work and dedication, which has brought pride to the Adukrom community."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Speaking at the ceremony, Nana Otutuababiov expressed his joy in recognizing individuals and institutions that uplift the community. \"The success of Adukrom lies in the collective efforts of its people. Hon. Asiedu Offei's leadership and Nia Senior High School's academic accomplishments reflect the spirit of progress we strive for. It is an honor to celebrate them during this sacred festival,\" Nana Otutuababiov remarked."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The 5th Akwasidae Festival, a celebration of the Ashanti cultural calendar, brought together community leaders, elders, and citizens to honor tradition and acknowledge key contributors to the socio-economic growth of Adukrom."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "This year's festival was particularly significant as it not only celebrated cultural heritage but also underscored the importance of leadership and education in building a prosperous future for Adukrom. Both Hon. Asiedu Offei and Nia Senior High School have set remarkable examples for others to follow."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h3',
        children: [
          {
            _type: 'span',
            text: "About Akwasidae:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Akwasidae is a traditional festival celebrated by the Ashanti people and other Akan groups in Ghana. It is held every six weeks and marks the veneration of ancestors, while also serving as an occasion to celebrate the community's achievements and honor significant contributors."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h3',
        children: [
          {
            _type: 'span',
            text: "About Nananom of Adukrom:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The Nananom of Adukrom is the traditional council of chiefs, led by the King, Nana Otutuababiov. They play a key role in the cultural, social, and developmental affairs of the Adukrom community, fostering growth and preserving traditions."
          }
        ]
      }
    ]
  },
  {
    title: "The Ellison Outreach Foundation Launches Grow Together Project in Adukrom to Combat Food Insecurity",
    slug: "ellison-outreach-foundation-launches-grow-together-project",
    body: [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "August 31, 2024 — The Ellison Outreach Foundation, Inc., a non-partisan NGO, is proud to announce the launch of its \"Grow Together Project\" in collaboration with the Office of Nana Otutu Ababio V, the King of the Okere Kingdom. This program, headquartered at the Anunkode Royal Palace in Adukrom-Akuapem, Eastern Region of Ghana and was made possible by a generous endowment from the John J. and Katherine Jenkins Estate, which is under the John and Katherine Jenkins Humanitarian Fund and is part of the foundation's broader mission to empower local farmers and combat food insecurity by providing them with high-quality seeds and the necessary resources to grow crops."
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "Project Goals:"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Feed 100,000 people for free: The project plans to distribute seeds that will be cultivated with the foundation's investment, ultimately providing food for 100,000 individuals in the community."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Promote sustainable agriculture: By supporting local farmers, the project encourages sustainable agricultural practices that contribute to long-term economic growth."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "\"We are thrilled to launch the Grow Together Project, which aligns with our commitment to promoting food security and economic empowerment. This initiative will have a profound impact on the local community by ensuring that everyone has access to nutritious food while supporting sustainable farming practices,\" said Prince Allen Ellison, Chairman of the Ellison Outreach Foundation."
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "Nana Otutu Ababio V stated \"The Grow Together Project is a monumental step forward for our kingdom. This initiative not only addresses the immediate need for food security but also lays the groundwork for sustainable agricultural development. I commend the Ellison Outreach Foundation for their visionary approach in partnering with us to create a brighter, more prosperous future for our people. Together, we are planting the seeds of hope and growth for generations to come.\""
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: "About the Ellison Outreach Foundation"
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: "The Ellison Outreach Foundation, Inc. is an NGO registered 501(c)3 not-for-profit organization dedicated to uplifting communities through education, economic empowerment, and sustainable development. With a focus on underserved populations, the foundation implements programs that foster resilience, prosperity, and positive change worldwide."
          }
        ]
      }
    ]
  }
];

// Function to update a news article
async function updateNewsArticle(article) {
  try {
    console.log(`Updating article: ${article.title}`);

    // Find the article by slug
    const query = `*[_type == "news" && slug.current == $slug][0]._id`;
    const articleId = await client.fetch(query, { slug: article.slug });

    if (!articleId) {
      console.error(`Article with slug "${article.slug}" not found.`);
      return null;
    }

    // Update the article
    const updatedArticle = await client
      .patch(articleId)
      .set({ body: article.body })
      .commit();

    console.log(`Updated article with ID: ${updatedArticle._id}`);
    return updatedArticle;
  } catch (error) {
    console.error(`Error updating article "${article.title}":`, error);
    return null;
  }
}

// Main function to update all articles
async function updateAllArticles() {
  console.log('Starting to update news articles...');

  for (const article of newsArticles) {
    await updateNewsArticle(article);
  }

  console.log('Finished updating news articles.');
}

// Run the main function
updateAllArticles().catch(console.error);
