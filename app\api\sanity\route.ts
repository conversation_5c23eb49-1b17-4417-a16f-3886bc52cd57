import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AdminRole } from '@/lib/types/admin';
import { logError } from '@/lib/errorHandling';

/**
 * Secure API route for Sanity operations
 * This route is protected and only accessible to authenticated admin users
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Check if user has admin role
    const userRole = session.user.role as AdminRole;
    if (userRole !== AdminRole.ADMIN && userRole !== AdminRole.SUPER_ADMIN) {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    const { operation, document, params } = body;
    
    if (!operation) {
      return NextResponse.json(
        { success: false, message: 'Missing operation parameter' },
        { status: 400 }
      );
    }
    
    // Get the Sanity write client
    const client = getWriteClient();
    
    // Perform the requested operation
    let result;
    
    switch (operation) {
      case 'create':
        if (!document || !document._type) {
          return NextResponse.json(
            { success: false, message: 'Invalid document' },
            { status: 400 }
          );
        }
        result = await client.create(document);
        break;
        
      case 'update':
        if (!document || !document._id) {
          return NextResponse.json(
            { success: false, message: 'Invalid document' },
            { status: 400 }
          );
        }
        result = await client.createOrReplace(document);
        break;
        
      case 'patch':
        if (!params?.id || !params?.patch) {
          return NextResponse.json(
            { success: false, message: 'Missing id or patch data' },
            { status: 400 }
          );
        }
        result = await client.patch(params.id).set(params.patch).commit();
        break;
        
      case 'delete':
        if (!params?.id) {
          return NextResponse.json(
            { success: false, message: 'Missing document id' },
            { status: 400 }
          );
        }
        result = await client.delete(params.id);
        break;
        
      default:
        return NextResponse.json(
          { success: false, message: `Unsupported operation: ${operation}` },
          { status: 400 }
        );
    }
    
    return NextResponse.json({ success: true, result });
  } catch (error) {
    logError(error, 'api/sanity');
    
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'An unknown error occurred' 
      },
      { status: 500 }
    );
  }
}
