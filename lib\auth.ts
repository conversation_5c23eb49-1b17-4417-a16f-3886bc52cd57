import { NextAuthOptions } from "next-auth";
import { DefaultSession } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { env } from "./env";
import { logError } from "./errorHandling";
import { AdminRole, getMockAdminUser } from "./types/admin";
import { getWriteClient } from "./sanity.client";
import bcrypt from "bcryptjs";

/**
 * Authentication configuration for NextAuth.js
 *
 * This sets up a credentials-based authentication system for the admin area.
 * In a production environment, you might want to use a more robust authentication
 * provider like OAuth or a database of users.
 */
export const authOptions: NextAuthOptions = {
  providers: [
    // Credentials provider for username/password login
    CredentialsProvider({
      name: "Admin Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          // Check if credentials are provided
          if (!credentials?.username || !credentials?.password) {
            console.error("No credentials provided");
            return null;
          }

          console.log('Authorizing with credentials:', {
            providedUsername: credentials.username,
            passwordProvided: !!credentials.password
          });

          // First check if the user exists in Sanity
          try {
            // Get the Sanity client
            const client = getWriteClient();

            // Try to find the user by username or email
            const user = await client.fetch(`
              *[_type == "adminUser" && (username == $username || email == $username) && isActive == true][0] {
                _id,
                username,
                name,
                email,
                role,
                hashedPassword,
                createdAt,
                lastLogin,
                permissions
              }
            `, {
              username: credentials.username
            });

            // If user found, verify password
            if (user && user.hashedPassword) {
              const isPasswordValid = await bcrypt.compare(credentials.password, user.hashedPassword);

              if (isPasswordValid) {
                console.log('Sanity user credentials match:', user.username);

                // Update last login time in Sanity
                await client
                  .patch(user._id)
                  .set({ lastLogin: new Date().toISOString() })
                  .commit();

                // Also update last login in local storage if it exists
                try {
                  const { updateLastLogin } = await import('./users');
                  updateLastLogin(user._id);
                } catch (localError) {
                  console.warn('Could not update last login in local storage:', localError);
                  // Continue anyway since Sanity is the source of truth
                }

                return {
                  id: user._id,
                  name: user.name,
                  email: user.email,
                  role: user.role,
                  username: user.username,
                  permissions: user.permissions,
                  lastLogin: new Date().toISOString()
                };
              } else {
                console.log('Sanity user found but password does not match');
                // If we found the user in Sanity but password doesn't match,
                // don't fall back to hardcoded passwords for security reasons
                return null;
              }
            }
          } catch (error) {
            console.error('Error checking Sanity user:', error);
          }

          // Disable API verification to avoid client fetch errors
          // We'll use direct credential checks instead
          /*
          if (credentials.username.includes('@')) {
            try {
              // Call our API route to verify the user
              const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/verify-user`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: credentials.username,
                  password: credentials.password,
                }),
              });

              const data = await response.json();

              if (data.success && data.user) {
                console.log('User credentials match from API:', data.user.email);

                return {
                  id: data.user.id,
                  name: data.user.name,
                  email: data.user.email,
                  role: data.user.role,
                  createdAt: data.user.createdAt,
                  updatedAt: data.user.updatedAt,
                  lastLogin: new Date().toISOString()
                };
              }
            } catch (error) {
              console.error('Error verifying user via API:', error);
            }
          }
          */

          // Check for regular admin
          const adminUsername = process.env.ADMIN_USERNAME;
          const adminPassword = process.env.ADMIN_PASSWORD;

          // Check for super admin
          const superAdminUsername = process.env.SUPER_ADMIN_USERNAME || 'superadmin';
          const superAdminPassword = process.env.SUPER_ADMIN_PASSWORD || 'kingdom2024super';

          // Check if credentials match regular admin
          if (
            adminUsername &&
            adminPassword &&
            credentials.username.toLowerCase() === adminUsername.toLowerCase() &&
            credentials.password === adminPassword
          ) {
            console.log('Regular admin credentials match');
            return {
              id: "1",
              name: "Admin",
              email: "<EMAIL>",
              role: AdminRole.ADMIN
            };
          }

          // Check if credentials match super admin
          if (
            superAdminUsername &&
            superAdminPassword &&
            credentials.username.toLowerCase() === superAdminUsername.toLowerCase() &&
            credentials.password === superAdminPassword
          ) {
            console.log('Super admin credentials match');
            return {
              id: "2",
              name: "Super Admin",
              email: "<EMAIL>",
              role: AdminRole.SUPER_ADMIN
            };
          }

          // Remove hardcoded fallbacks for production security
          // Only use environment variables and Sanity CMS for authentication

          console.log('Credentials do not match any admin user');
          return null;
        } catch (error) {
          console.error('Error in authorize function:', error);
          logError(error, "auth.authorize");
          return null;
        }
      }
    }),
  ],

  // Custom pages
  pages: {
    signIn: "/admin/login",
    signOut: "/admin/login",
    error: "/admin/login",
  },

  // Callbacks to customize the JWT and session
  callbacks: {
    // Add custom claims to the JWT
    async jwt({ token, user, trigger, session }) {
      if (user) {
        console.log('Adding user data to token:', user);
        token.role = user.role;
        token.userId = user.id;
      }

      // Handle session update
      if (trigger === 'update') {
        console.log('JWT callback - update trigger with session data:', session);

        // If we have a name in the session update, use it to update the token
        if (session?.name) {
          console.log('Updating token name to:', session.name);
          token.name = session.name;
        }
      }

      return token;
    },

    // Add custom properties to the session
    async session({ session, token, trigger }) {
      console.log('Creating session from token:', token);
      if (session.user) {
        (session.user as any).role = token.role;
        (session.user as any).id = token.userId;

        // Handle session update
        if (trigger === 'update') {
          console.log('Session callback - update trigger with token:', token);
          // Make sure the session has the updated name from the token
          session.user.name = token.name;
        }

        // Disable automatic session refresh to avoid client fetch errors
        // If we need fresh data, we'll handle it manually through explicit API calls
        /*
        if (session.user.email?.includes('@')) {
          try {
            // Call our API to get fresh user data
            const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/update-session`, {
              method: 'POST',
              headers: {
                'Cookie': `next-auth.session-token=${token.jti}`,
              },
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success && data.user) {
                // Update session with fresh user data
                session.user.name = data.user.name;
                session.user.role = data.user.role;
                (session.user as any).createdAt = data.user.createdAt;
                (session.user as any).updatedAt = data.user.updatedAt;
                (session.user as any).lastLogin = data.user.lastLogin;

                console.log('Session updated with fresh user data:', {
                  name: session.user.name,
                  email: session.user.email,
                  role: session.user.role
                });
              }
            }
          } catch (error) {
            console.error('Error refreshing user data in session callback:', error);
          }
        }
        */
      }
      return session;
    },
  },

  // Session configuration
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },

  // Debug disabled in all environments
  debug: false, // Disable debug to prevent debug elements from appearing

  // Secret for JWT encryption
  secret: process.env.NEXTAUTH_SECRET,

  // Events for debugging
  events: {
    async signOut(message) {
      console.log('User signed out:', message);
    },
    async session(message) {
      console.log('Session updated:', message);
    },
  },
};

/**
 * Helper function to check if a user is authenticated
 */
export function isAuthenticated(session: any): boolean {
  return !!session?.user;
}

/**
 * Helper function to check if a user has admin role
 */
export function isAdmin(session: any): boolean {
  return isAuthenticated(session) && (
    session.user.role === AdminRole.ADMIN ||
    session.user.role === AdminRole.SUPER_ADMIN
  );
}

/**
 * Helper function to check if a user has super admin role
 */
export function isSuperAdmin(session: any): boolean {
  return isAuthenticated(session) && session.user.role === AdminRole.SUPER_ADMIN;
}

/**
 * Helper function to check if a user has permission to access a feature
 */
export function hasPermission(
  session: any,
  permission: keyof (typeof import('./types/admin').ROLE_PERMISSIONS)[AdminRole]
): boolean {
  if (!isAuthenticated(session)) return false;

  const role = session.user.role as AdminRole;

  // Use the imported ROLE_PERMISSIONS directly
  const rolePermissions = {
    [AdminRole.ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: false,
      canManageUsers: false,
      canManageStore: false,
      canManageBankingInfo: false,
    },
    [AdminRole.SUPER_ADMIN]: {
      canManageContent: true,
      canManageEvents: true,
      canManageGallery: true,
      canManageNews: true,
      canManageRsvp: true,
      canManageSettings: true,
      canManageUsers: true,
      canManageStore: true,
      canManageBankingInfo: true,
    }
  };

  return rolePermissions[role]?.[permission] === true;
}

/**
 * Types for NextAuth
 */
declare module "next-auth" {
  interface User {
    role?: string;
  }

  interface Session {
    user: {
      id?: string;
      role?: string;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    userId?: string;
    role?: string;
  }
}
