import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@sanity/client';
import { getWriteClient } from '@/lib/sanity.client';

// Create a function to get the Sanity client for server-side operations
const getClient = () => {
  // Validate token exists
  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN is missing');
    throw new Error('SANITY_API_TOKEN is required for write operations');
  }

  return createClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
    token: process.env.SANITY_API_TOKEN, // Server-side token, not exposed to client
    apiVersion: '2023-05-03',
    useCdn: false,
  });
};

// GET endpoint to fetch all news articles
export async function GET() {
  try {
    console.log('Fetching news articles from Sanity');

    // Get the Sanity client
    let client;
    try {
      client = getClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        { error: 'Server configuration error', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // Query to fetch all news articles
    const query = `*[_type == "news"] | order(publishedAt desc) {
      _id,
      title,
      excerpt,
      body,
      slug,
      publishedAt,
      status,
      categoryName,
      mainImage
    }`;

    // Fetch news articles from Sanity
    const news = await client.fetch(query, {}, {
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });

    console.log(`Fetched ${news.length} news articles`);

    return NextResponse.json({
      success: true,
      news: news
    });
  } catch (error) {
    console.error('Error fetching news articles:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch news articles',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    console.log('News article creation API route called');

    // Get the Sanity client
    let client;
    try {
      client = getClient();
    } catch (error) {
      console.error('Error getting Sanity client:', error);
      return NextResponse.json(
        { error: 'Server configuration error', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }

    // Get the form data from the request
    const formData = await req.formData();

    // Extract text fields
    const title = formData.get('title') as string;
    const excerpt = formData.get('excerpt') as string;
    const content = formData.get('content') as string;
    const category = formData.get('category') as string;
    const date = formData.get('date') as string;
    const status = formData.get('status') as string || 'draft';
    const addToGallery = formData.get('addToGallery') === 'true';

    // Extract image file
    const featuredImage = formData.get('featuredImage') as File | null;

    // Validate required fields
    if (!title || !excerpt || !content || !date) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate a slug from the title
    const slug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    try {
      console.log('Creating news document in Sanity...');

      // Create the news document
      let newsDoc: any = {
        _type: 'news',
        title: title,
        slug: { _type: 'slug', current: slug },
        excerpt: excerpt,
        // Convert content string to Portable Text format
        body: [
          {
            _type: 'block',
            style: 'normal',
            _key: `block-${Date.now()}`,
            markDefs: [],
            children: [
              {
                _type: 'span',
                marks: [],
                text: content,
                _key: `span-${Date.now()}`,
              },
            ],
          },
        ],
        publishedAt: new Date(date).toISOString(),
        status: status,
      };

      // If category is selected, add it as a string field instead of a reference
      // This avoids the need for category documents to exist in Sanity
      if (category) {
        newsDoc.categoryName = category;
      }

      // Create the document in Sanity
      const createdNews = await client.create(newsDoc);
      console.log('News document created:', createdNews._id);

      // If there's a featured image, upload it
      if (featuredImage) {
        console.log('Uploading featured image:', featuredImage.name);

        // Create an asset from the file
        const asset = await client.assets.upload('image', featuredImage, {
          filename: featuredImage.name,
        });

        console.log('Image uploaded successfully, asset ID:', asset._id);

        // Patch the news document with the image
        const patchResult = await client
          .patch(createdNews._id)
          .set({
            mainImage: {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: asset._id,
              },
              alt: title,
            },
          })
          .commit();

        console.log('News document patched with image:', patchResult._id);

        // If addToGallery is true, add the image to the gallery
        if (addToGallery) {
          try {
            console.log('Adding image to gallery...');

            // Prepare the gallery item data
            const galleryData = {
              title: title,
              description: excerpt,
              mainImage: {
                _type: 'image',
                asset: {
                  _type: 'reference',
                  _ref: asset._id
                },
                alt: title
              },
              fromArticle: {
                _type: 'reference',
                _ref: createdNews._id
              }
            };

            // Add category if provided
            if (category) {
              galleryData.category = {
                _type: 'string',
                value: category
              };
            }

            // Call the gallery API to create a gallery item
            const galleryResponse = await fetch(new URL('/api/gallery/from-article', req.url).toString(), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(galleryData),
            });

            if (galleryResponse.ok) {
              console.log('Image added to gallery successfully');
            } else {
              console.error('Failed to add image to gallery');
            }
          } catch (galleryError) {
            console.error('Error adding image to gallery:', galleryError);
            // Continue with the response even if gallery addition fails
          }
        }
      }

      return NextResponse.json({
        success: true,
        message: status === 'draft' ? 'News article saved as draft' : 'News article published successfully',
        article: createdNews
      });
    } catch (sanityError) {
      console.error('Sanity operation error:', sanityError);
      return NextResponse.json(
        {
          error: 'Sanity operation failed',
          details: sanityError instanceof Error ? sanityError.message : 'Unknown Sanity error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in news article creation handler:', error);
    return NextResponse.json(
      {
        error: 'Failed to create news article',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
