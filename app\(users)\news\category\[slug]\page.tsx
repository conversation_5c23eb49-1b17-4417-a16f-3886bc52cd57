import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { getCategories } from '@/lib/sanity';
import { urlFor } from '@/lib/sanity';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { formatDate } from '@/lib/utils';

// Custom function to get news by category
async function getNewsByCategory(categorySlug: string) {
  const sanityClient = (await import('@/lib/sanity')).sanityClient;

  return sanityClient.fetch(`
    *[_type == "news" && category->slug.current == $categorySlug] | order(publishedAt desc) {
      _id,
      title,
      slug,
      excerpt,
      mainImage,
      publishedAt,
      category->{
        title,
        slug
      }
    }
  `, { categorySlug });
}

// Generate static params for all categories
export async function generateStaticParams() {
  const categories = await getCategories();

  return categories.map((category: any) => ({
    slug: category.slug.current,
  }));
}

export default async function NewsCategoryPage({ params }: { params: { slug: string } }) {
  // Fetch all categories and news articles for this category
  const [categories, newsArticles] = await Promise.all([
    getCategories(),
    getNewsByCategory(params.slug)
  ]);

  // Find the current category
  const currentCategory = categories.find((cat: any) => cat.slug.current === params.slug);

  // If category not found, return 404
  if (!currentCategory) {
    notFound();
  }

  return (
    <>
      <Header />
      <main>
        <section className="py-20 bg-ivory">
          <div className="container mx-auto px-4">
            <div className="flex justify-center mb-4">
              <Link href="/news" className="flex items-center gap-2 text-royalBlue hover:text-royalGold transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to All News
              </Link>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-royalBlue text-center mb-4">
              {currentCategory.title}
            </h1>

            {currentCategory.description && (
              <p className="text-lg text-center max-w-3xl mx-auto mb-12">
                {currentCategory.description}
              </p>
            )}

            {/* Categories filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <Link
                href="/news"
                className="px-6 py-2 rounded-full text-sm font-medium bg-white text-royalBlue border border-royalBlue hover:bg-royalBlue hover:text-white transition-all"
              >
                All News
              </Link>
              {categories.map((category: any) => (
                <Link
                  key={category._id}
                  href={`/news/category/${category.slug.current}`}
                  className={`px-6 py-2 rounded-full text-sm font-medium ${
                    category.slug.current === params.slug
                      ? 'bg-royalGold text-royalBlue'
                      : 'bg-white text-royalBlue border border-royalBlue hover:bg-royalBlue hover:text-white'
                  } transition-all`}
                >
                  {category.title}
                </Link>
              ))}
            </div>

            {/* News grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {newsArticles.length > 0 ? (
                newsArticles.map((article: any) => (
                  <Link
                    key={article._id}
                    href={`/news/${article.slug.current}`}
                    className="group"
                  >
                    <div className="bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-2">
                      {article.mainImage && (
                        <div className="relative h-56 overflow-hidden">
                          <img
                            src={urlFor(article.mainImage).url()}
                            alt={article.title}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                          />
                        </div>
                      )}
                      <div className="p-6">
                        <div className="text-sm text-gray-500 mb-2">
                          {article.publishedAt && formatDate(article.publishedAt)}
                        </div>
                        <h3 className="text-xl font-bold text-royalBlue mb-2 group-hover:text-royalGold transition-colors">
                          {article.title}
                        </h3>
                        <p className="text-gray-700 mb-4 line-clamp-3">
                          {article.excerpt}
                        </p>
                        <div className="flex justify-end">
                          <span className="inline-flex items-center text-royalBlue font-medium group-hover:text-royalGold transition-colors">
                            Read more
                            <svg className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="col-span-3 text-center py-12">
                  <p className="text-xl text-gray-500">No news articles found in this category. Check back soon for updates!</p>
                </div>
              )}
            </div>

            <div className="mt-12 text-center">
              <Link href="/news" className="royal-button bg-royalBlue text-white py-3 px-8 rounded-full hover:bg-blue-900 transition-colors inline-block">
                View All News
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
