'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format, isAfter, parseISO } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Calendar, Clock, MapPin, Save, ArrowLeft, Upload, Loader2, AlertCircle } from 'lucide-react';
import { LocationPicker } from '@/components/ui/location-picker';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Image from 'next/image';
import SeoForm from '@/components/admin/SeoForm';

export default function NewEventPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Form validation state
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [touched, setTouched] = useState<{[key: string]: boolean}>({});

  const [eventData, setEventData] = useState({
    title: '',
    slug: '',
    date: format(new Date(), "yyyy-MM-dd'T'HH:mm"),
    endDate: '',
    location: '',
    description: '',
    imageAlt: '',
    isCountdownTarget: false,
    isHighlighted: false,
    showRsvp: true,
    eventType: 'ceremony',
    order: 0,
    seo: {
      metaTitle: '',
      metaDescription: '',
      seoKeywords: [],
      nofollowAttributes: false,
      openGraph: {
        title: '',
        description: '',
        image: null
      },
      twitter: {
        title: '',
        description: '',
        cardType: 'summary_large_image'
      }
    }
  });

  // Validate form fields
  useEffect(() => {
    const newErrors: {[key: string]: string} = {};

    // Title validation
    if (!eventData.title && touched.title) {
      newErrors.title = 'Title is required';
    } else if (eventData.title.length > 100 && touched.title) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    // Slug validation
    if (!eventData.slug && touched.slug) {
      newErrors.slug = 'Slug is required';
    } else if (!/^[a-z0-9-]+$/.test(eventData.slug) && touched.slug) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    // Date validation
    if (!eventData.date && touched.date) {
      newErrors.date = 'Start date is required';
    }

    // End date validation (if provided)
    if (eventData.endDate && eventData.date && touched.endDate) {
      const startDate = new Date(eventData.date);
      const endDate = new Date(eventData.endDate);

      if (isAfter(startDate, endDate)) {
        newErrors.endDate = 'End date must be after start date';
      }
    }

    // Location validation
    if (!eventData.location && touched.location) {
      newErrors.location = 'Location is required';
    } else if (eventData.location.length > 100 && touched.location) {
      newErrors.location = 'Location must be less than 100 characters';
    }

    // Description validation
    if (!eventData.description && touched.description) {
      newErrors.description = 'Description is required';
    } else if (eventData.description.length < 10 && touched.description) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    // Image alt text validation (if image is provided)
    if (imageFile && !eventData.imageAlt && touched.imageAlt) {
      newErrors.imageAlt = 'Alt text is required when an image is uploaded';
    }

    setErrors(newErrors);
  }, [eventData, touched, imageFile]);

  // Handle field blur for validation
  const handleBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  // Handle SEO data changes
  const handleSeoChange = (seoData: any) => {
    setEventData(prev => ({
      ...prev,
      seo: seoData
    }));
  };

  // Create Sanity client with write permissions
  const createSanityClient = async () => {
    try {
      const { createClient } = await import('@sanity/client');

      // Get token from environment variable
      const token = process.env.NEXT_PUBLIC_SANITY_API_TOKEN;

      if (!token) {
        console.error('Sanity API token is missing');
        toast.error('API token is missing. Please check your environment variables.');
        throw new Error('Sanity API token is missing');
      }

      return createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2025-05-08',
        useCdn: false,
      });
    } catch (error) {
      console.error('Failed to create Sanity client:', error);
      toast.error('Failed to initialize content management system');
      throw error;
    }
  };

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImageFile(file);

    // Create a preview URL
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched for validation
    const allFields = ['title', 'slug', 'date', 'location', 'description', 'imageAlt'];
    const touchedFields = allFields.reduce((acc, field) => ({ ...acc, [field]: true }), {});
    setTouched(touchedFields);

    // Check for validation errors
    const hasErrors = Object.keys(errors).length > 0;
    const requiredFields = ['title', 'slug', 'date', 'location', 'description'];
    const missingRequired = requiredFields.some(field => !eventData[field as keyof typeof eventData]);

    if (hasErrors || missingRequired) {
      toast.error('Please fix the form errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      const client = await createSanityClient();

      // If this is set as countdown target, unset all other events
      if (eventData.isCountdownTarget) {
        await client
          .patch({ query: `*[_type == "event" && isCountdownTarget == true]` })
          .set({ isCountdownTarget: false })
          .commit();
      }

      // Create the event document
      let eventDoc: any = {
        _type: 'event',
        title: eventData.title,
        slug: { _type: 'slug', current: eventData.slug || generateSlug(eventData.title) },
        date: new Date(eventData.date).toISOString(),
        location: eventData.location,
        description: eventData.description,
        isCountdownTarget: eventData.isCountdownTarget,
        isHighlighted: eventData.isHighlighted,
        showRsvp: eventData.showRsvp,
        eventType: eventData.eventType,
        order: Number(eventData.order),
        seo: eventData.seo
      };

      // Add optional fields if they exist
      if (eventData.endDate) {
        eventDoc.endDate = new Date(eventData.endDate).toISOString();
      }

      // Create the document in Sanity
      const createdEvent = await client.create(eventDoc);

      // If there's an image, upload it
      if (imageFile) {
        // Create an asset from the file
        const asset = await client.assets.upload('image', imageFile, {
          filename: imageFile.name,
        });

        // Patch the event document with the image
        await client
          .patch(createdEvent._id)
          .set({
            image: {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: asset._id,
              },
              alt: eventData.imageAlt || eventData.title,
            },
          })
          .commit();
      }

      toast.success('Event created successfully');
      // Use replace instead of push for more reliable navigation
      router.replace('/admin/events');
      // Add a small delay to ensure the navigation happens
      setTimeout(() => {
        window.location.href = '/admin/events';
      }, 500);
    } catch (error) {
      console.error('Failed to create event:', error);
      toast.error('Failed to create event');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Create New Event</h1>
          <p className="text-muted-foreground">
            Add a new event to the Kingdom website.
          </p>
        </div>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>

      <Separator />

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Create New Event</CardTitle>
            <CardDescription>
              Enter the details for your event.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details">Event Details</TabsTrigger>
                <TabsTrigger value="seo">SEO & Social Sharing</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Event Title</Label>
                  <Input
                    id="title"
                    value={eventData.title}
                    onChange={(e) => {
                      setEventData({
                        ...eventData,
                        title: e.target.value,
                        slug: generateSlug(e.target.value)
                      });
                    }}
                    onBlur={() => handleBlur('title')}
                    className={errors.title ? 'border-red-500' : ''}
                    required
                  />
                  {errors.title && (
                    <p className="text-xs text-red-500 flex items-center mt-1">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.title}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug</Label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground text-sm">
                      /events/
                    </span>
                    <Input
                      id="slug"
                      value={eventData.slug}
                      onChange={(e) => setEventData({ ...eventData, slug: e.target.value })}
                      onBlur={() => handleBlur('slug')}
                      className={`rounded-l-none ${errors.slug ? 'border-red-500' : ''}`}
                      required
                    />
                  </div>
                  {errors.slug && (
                    <p className="text-xs text-red-500 flex items-center mt-1">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.slug}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    The URL-friendly name that will be used in the address bar.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Start Date & Time</Label>
                    <div className="flex">
                      <Calendar className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="date"
                        type="datetime-local"
                        value={eventData.date}
                        onChange={(e) => setEventData({ ...eventData, date: e.target.value })}
                        onBlur={() => handleBlur('date')}
                        className={errors.date ? 'border-red-500' : ''}
                        required
                      />
                    </div>
                    {errors.date && (
                      <p className="text-xs text-red-500 flex items-center mt-1">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.date}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endDate">End Date & Time (Optional)</Label>
                    <div className="flex">
                      <Clock className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                      <Input
                        id="endDate"
                        type="datetime-local"
                        value={eventData.endDate}
                        onChange={(e) => setEventData({ ...eventData, endDate: e.target.value })}
                        onBlur={() => handleBlur('endDate')}
                        className={errors.endDate ? 'border-red-500' : ''}
                      />
                    </div>
                    {errors.endDate && (
                      <p className="text-xs text-red-500 flex items-center mt-1">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.endDate}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <LocationPicker
                    value={eventData.location}
                    onChange={(value) => setEventData({ ...eventData, location: value })}
                    onBlur={() => handleBlur('location')}
                    error={errors.location}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={eventData.description}
                    onChange={(e) => setEventData({ ...eventData, description: e.target.value })}
                    onBlur={() => handleBlur('description')}
                    className={errors.description ? 'border-red-500' : ''}
                    rows={4}
                    required
                  />
                  {errors.description && (
                    <p className="text-xs text-red-500 flex items-center mt-1">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {errors.description}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="eventType">Event Type</Label>
                  <select
                    id="eventType"
                    value={eventData.eventType}
                    onChange={(e) => setEventData({ ...eventData, eventType: e.target.value })}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="ceremony">Ceremony</option>
                    <option value="dinner">Dinner</option>
                    <option value="conference">Conference</option>
                    <option value="meeting">Meeting</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                {/* Event Image Section */}
                <div className="mt-8 space-y-4">
                  <h3 className="text-lg font-medium">Event Image</h3>
                  <div className="space-y-2">
                    <Label htmlFor="image">Event Image</Label>
                    <div className="flex items-center gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('image')?.click()}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Image
                      </Button>
                      <Input
                        id="image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                      />
                      <div className="mt-4 w-full">
                        {imagePreview ? (
                          <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-lg border border-border">
                            <Image
                              src={imagePreview}
                              alt="Preview"
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-lg border border-border bg-gray-100 flex items-center justify-center">
                            <p className="text-gray-400">No image selected</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="imageAlt">Image Alt Text</Label>
                    <Input
                      id="imageAlt"
                      value={eventData.imageAlt}
                      onChange={(e) => setEventData({ ...eventData, imageAlt: e.target.value })}
                      onBlur={() => handleBlur('imageAlt')}
                      className={errors.imageAlt ? 'border-red-500' : ''}
                      placeholder="Brief description of the image for accessibility"
                    />
                    {errors.imageAlt && (
                      <p className="text-xs text-red-500 flex items-center mt-1">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.imageAlt}
                      </p>
                    )}
                  </div>
                </div>

                {/* Display Options Section */}
                <div className="mt-8 space-y-4">
                  <h3 className="text-lg font-medium">Display Options</h3>
                  <p className="text-sm text-muted-foreground">Configure how the event is displayed on the website.</p>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isCountdownTarget"
                      checked={eventData.isCountdownTarget}
                      onCheckedChange={(checked) => setEventData({ ...eventData, isCountdownTarget: checked })}
                    />
                    <Label htmlFor="isCountdownTarget">Use as Countdown Target</Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-7">
                    If enabled, this event will be used for the main countdown timer on the website.
                    Only one event can be the countdown target.
                  </p>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isHighlighted"
                      checked={eventData.isHighlighted}
                      onCheckedChange={(checked) => setEventData({ ...eventData, isHighlighted: checked })}
                    />
                    <Label htmlFor="isHighlighted">Highlight Event</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showRsvp"
                      checked={eventData.showRsvp}
                      onCheckedChange={(checked) => setEventData({ ...eventData, showRsvp: checked })}
                    />
                    <Label htmlFor="showRsvp">Show RSVP Button</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="order">Display Order</Label>
                    <Input
                      id="order"
                      type="number"
                      value={eventData.order.toString()}
                      onChange={(e) => setEventData({ ...eventData, order: parseInt(e.target.value) || 0 })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Events with lower numbers will be displayed first.
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="seo" className="pt-4">
                <SeoForm
                  title={eventData.title}
                  description={eventData.description}
                  slug={eventData.slug || generateSlug(eventData.title)}
                  image={imagePreview}
                  contentType="event"
                  seoData={eventData.seo}
                  onSeoChange={handleSeoChange}
                  onImageUpload={async (file) => {
                    try {
                      // Create FormData for image upload
                      const formData = new FormData();
                      formData.append('image', file);
                      formData.append('alt', eventData.title || 'Event image');

                      // Upload the image through our server-side API route
                      const imageUploadResponse = await fetch(`/api/upload`, {
                        method: 'POST',
                        body: formData,
                      });

                      if (!imageUploadResponse.ok) {
                        const errorData = await imageUploadResponse.json();
                        throw new Error(errorData.error || 'Failed to upload image');
                      }

                      const imageResult = await imageUploadResponse.json();
                      console.log('SEO Image uploaded successfully:', imageResult);

                      if (!imageResult.asset) {
                        throw new Error('No image asset returned from server');
                      }

                      // Return the image asset reference
                      return {
                        _type: 'image',
                        asset: {
                          _type: 'reference',
                          _ref: imageResult.asset._id
                        }
                      };
                    } catch (error) {
                      console.error('Error uploading SEO image:', error);
                      throw error;
                    }
                  }}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Create Event
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
