import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the cart page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Your Cart',
    description: 'View and manage items in your shopping cart.',
    url: '/store/cart',
    keywords: ['Shopping Cart', 'Royal Merchandise', 'Checkout'],
    noIndex: true, // We don't want search engines to index cart pages
  });
}
