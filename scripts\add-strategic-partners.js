/**
 * <PERSON><PERSON><PERSON> to add strategic partners to Sanity
 *
 * This script adds the strategic partners shown in the "Strategic Partners of the Crown" section
 * to the Sanity CMS.
 *
 * Run with: node scripts/add-strategic-partners.js
 */

// Import the Sanity client
const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Configure the client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Need to provide this when running the script
  apiVersion: '2023-05-03', // Use a consistent API version
  useCdn: false
});

// Strategic partners data
const partners = [
  {
    name: 'Remit Global',
    slug: { current: 'remit-global' },
    description: 'Strategic financial partner supporting the Kingdom\'s economic initiatives.',
    partnershipType: 'corporate',
    featured: true,
    active: true,
    order: 1
  },
  {
    name: 'Royal Lion',
    slug: { current: 'royal-lion' },
    description: 'Heritage and cultural partner preserving the Kingdom\'s traditions.',
    partnershipType: 'heritage',
    featured: true,
    active: true,
    order: 2
  },
  {
    name: 'TEF',
    slug: { current: 'tef' },
    description: 'Educational development partner advancing knowledge and skills in the community.',
    partnershipType: 'educational',
    featured: true,
    active: true,
    order: 3
  },
  {
    name: 'Lightace Global',
    slug: { current: 'lightace-global' },
    description: 'Innovation and technology partner bringing digital solutions to the Kingdom.',
    partnershipType: 'technology',
    featured: true,
    active: true,
    order: 4
  },
  {
    name: 'Akuapem Nifaman Council',
    slug: { current: 'akuapem-nifaman-council' },
    description: 'Traditional governance partner supporting the Kingdom\'s leadership structure.',
    partnershipType: 'governance',
    featured: true,
    active: true,
    order: 5
  }
];

// Function to upload an image to Sanity
async function uploadImage(imagePath, altText) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.error(`Image not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const imageAsset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath),
      contentType: `image/${path.extname(imagePath).substring(1)}`
    });

    return {
      _type: 'image',
      asset: {
        _type: 'reference',
        _ref: imageAsset._id
      },
      alt: altText
    };
  } catch (error) {
    console.error(`Error uploading image ${imagePath}:`, error);
    return null;
  }
}

// Function to create a strategic partner
async function createPartner(partner) {
  try {
    // Check if partner already exists
    const existingPartner = await client.fetch(
      `*[_type == "strategicPartner" && slug.current == $slug][0]`,
      { slug: partner.slug.current }
    );

    if (existingPartner) {
      console.log(`Partner ${partner.name} already exists, updating...`);

      // Update existing partner
      const updatedPartner = await client
        .patch(existingPartner._id)
        .set({
          name: partner.name,
          description: partner.description,
          partnershipType: partner.partnershipType,
          featured: partner.featured,
          active: partner.active,
          order: partner.order
        })
        .commit();

      console.log(`Updated partner: ${updatedPartner.name}`);
      return updatedPartner;
    }

    // Create new partner
    console.log(`Creating new partner: ${partner.name}`);
    const newPartner = await client.create({
      _type: 'strategicPartner',
      ...partner
    });

    console.log(`Created partner: ${newPartner.name}`);
    return newPartner;
  } catch (error) {
    console.error(`Error creating/updating partner ${partner.name}:`, error);
    return null;
  }
}

// Main function to add all partners
async function addStrategicPartners() {
  console.log('Starting to add strategic partners to Sanity...');

  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  for (const partner of partners) {
    await createPartner(partner);
  }

  console.log('Finished adding strategic partners to Sanity');
}

// Run the script
addStrategicPartners().catch(console.error);
