# Next.js Troubleshooting Guide

This guide provides solutions for common Next.js errors and issues you might encounter while developing the Kingdom of Adukrom website.

## Table of Contents

1. [Build Errors](#build-errors)
2. [Runtime Errors](#runtime-errors)
3. [Hydration Errors](#hydration-errors)
4. [Sanity Integration Issues](#sanity-integration-issues)
5. [Performance Issues](#performance-issues)

## Build Errors

### Error: ENOENT: no such file or directory, open '.next/server/pages-manifest.json'

This error occurs when the Next.js build files are missing or corrupted.

**Solution:**

1. Clean the Next.js cache:
   ```bash
   npm run clean
   ```

2. If that doesn't work, try a full clean and rebuild:
   ```bash
   npm run rebuild:full
   ```

### TypeScript Errors During Build

If you encounter TypeScript errors during build that prevent deployment:

**Solution:**

1. Fix the TypeScript errors if possible
2. If you need to deploy despite the errors, the `next.config.js` has `typescript.ignoreBuildErrors` set to `true`

## Runtime Errors

### Error: Request error while attempting to reach Sanity API

This error occurs when the application can't connect to the Sanity API.

**Solution:**

1. Check that your `.env.local` file has the correct Sanity API tokens:
   ```
   NEXT_PUBLIC_SANITY_API_TOKEN="your-token-here"
   SANITY_API_TOKEN="your-token-here"
   ```

2. Verify that your Sanity project ID and dataset are correct:
   ```
   NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt
   NEXT_PUBLIC_SANITY_DATASET=production
   ```

3. Check if the Sanity API is down or if there are network issues

### Error: ChunkLoadError

This error occurs when the browser fails to load a JavaScript chunk.

**Solution:**

1. Clear your browser cache
2. Clean and rebuild the application:
   ```bash
   npm run rebuild
   ```

## Hydration Errors

### Error: Hydration failed because the initial UI does not match what was rendered on the server

This error occurs when the HTML rendered on the server doesn't match what React tries to render on the client.

**Solution:**

1. Run the hydration checker to identify issues:
   ```bash
   npm run check:hydration
   ```

2. Apply automatic fixes:
   ```bash
   npm run fix:hydration
   ```

3. For components with dynamic content, add `suppressHydrationWarning`:
   ```jsx
   <div suppressHydrationWarning>
     {dynamicContent}
   </div>
   ```

4. Move random values (Math.random, Date.now) to useEffect:
   ```jsx
   const [randomValue, setRandomValue] = useState(0);
   
   useEffect(() => {
     setRandomValue(Math.random());
   }, []);
   ```

### Error: Text content does not match server-rendered HTML

This error occurs when text content differs between server and client rendering.

**Solution:**

1. Ensure consistent rendering between server and client
2. Use `suppressHydrationWarning` on elements with dynamic text
3. For date formatting, use the `FormattedDate` component which handles hydration correctly

## Sanity Integration Issues

### Error: Failed to fetch data from Sanity

This error occurs when the application can't fetch data from Sanity.

**Solution:**

1. Check that your Sanity tokens are correct in `.env.local`
2. Verify that your CORS settings in Sanity allow requests from your domain
3. Check if the Sanity API is down or if there are network issues

### Error: Cannot find module '@sanity/client'

This error occurs when the Sanity client package is missing or incompatible.

**Solution:**

1. Install the Sanity client package:
   ```bash
   npm install @sanity/client
   ```

2. Make sure you're using a compatible Node.js version (v20+)

## Performance Issues

### Slow Page Loads

If pages are loading slowly:

**Solution:**

1. Use the Network tab in browser DevTools to identify slow resources
2. Optimize images using Next.js Image component with proper sizes
3. Implement code splitting with dynamic imports
4. Use Incremental Static Regeneration (ISR) for frequently accessed pages

### Memory Leaks

If you notice memory usage increasing over time:

**Solution:**

1. Check for missing cleanup in useEffect hooks
2. Ensure event listeners are properly removed
3. Use React DevTools to profile component renders

## Quick Commands

Here are some useful commands for troubleshooting:

- **Clean Next.js cache**: `npm run clean`
- **Full clean and rebuild**: `npm run rebuild:full`
- **Check for hydration issues**: `npm run check:hydration`
- **Fix hydration issues**: `npm run fix:hydration`
- **Run with automatic error fixing**: `npm run dev:safe`

## Getting Help

If you're still experiencing issues:

1. Check the Next.js documentation: https://nextjs.org/docs
2. Check the Sanity documentation: https://www.sanity.io/docs
3. Search for similar issues on GitHub or Stack Overflow
4. Reach out to the development team for assistance
