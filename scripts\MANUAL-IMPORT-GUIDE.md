# Manual Blog Import Guide

This guide provides step-by-step instructions for manually importing blog content from an external website into your Sanity database.

## Overview

Since the website might not be accessible programmatically, this guide explains how to:

1. Manually download blog articles and images
2. Organize them in the correct directory structure
3. Run the helper script to import them into Sanity

## Step 1: Set Up Directories

Create the following directories in your project:

```
manual-import/
├── html/
└── images/
```

You can do this manually or run:

```bash
mkdir -p manual-import/html manual-import/images
```

## Step 2: Download Blog Articles

For each article on the website:

1. Open the article in your browser
2. Save the complete webpage (not just the HTML)
   - In Chrome: Right-click > "Save as..." > Select "Webpage, Complete"
   - In Firefox: Right-click > "Save Page As..." > Select "Web Page, complete"
   - In Safari: File > "Save As..." > Select "Web Page, Complete"

3. Save the HTML file in the `manual-import/html` directory
4. Rename the file to something descriptive (e.g., `article-title.html`)

## Step 3: Download Images

For each article:

1. Create a list of all images in the article
2. Download each image
   - Right-click on the image > "Save Image As..."
   - Save the image in the `manual-import/images` directory
   - Keep the original filename if possible

Alternatively, if you saved the webpage as "complete", you can:

1. Look in the automatically created folder alongside your saved HTML file
2. Copy all images from this folder to the `manual-import/images` directory

## Step 4: Install Dependencies

Install the required dependencies:

```bash
npm install cheerio dotenv
```

## Step 5: Set Up Environment Variables

Make sure your `.env.local` file contains:

```
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your_write_token
```

## Step 6: Run the Import Script

Run the manual import helper script:

```bash
node scripts/manual-import-helper.js
```

The script will:
1. Process all HTML files in the `manual-import/html` directory
2. Upload all images from the `manual-import/images` directory to Sanity
3. Create or update articles in your Sanity database

## Step 7: Verify the Import

After the import is complete:

1. Open Sanity Studio
2. Check the "News" section
3. Verify that all articles and images were imported correctly
4. Make any necessary adjustments to the content

## Troubleshooting

### HTML Structure Issues

If the script has trouble extracting content from your HTML files, you might need to adjust the selectors in the script:

```javascript
// In extractArticleData function
const title = $('.post-title, .entry-title').first().text().trim() || path.basename(filename, '.html');
const date = $('.post-date, .entry-date').first().text().trim();
const content = $('.post-content, .entry-content').html() || $('body').html();
```

### Image Reference Issues

If images aren't showing up in the imported articles:

1. Check that the image filenames match the references in the HTML
2. Make sure all images were successfully uploaded to Sanity
3. Try manually adding the images to the articles in Sanity Studio

### Alternative: Direct Sanity Studio Import

If the script doesn't work for you, you can always:

1. Create articles directly in Sanity Studio
2. Upload images through the Sanity Studio interface
3. Copy and paste content from the original articles

## Tips for Large Imports

If you have many articles to import:

1. Process them in batches (e.g., 10 articles at a time)
2. Keep track of which articles you've already imported
3. Consider creating a spreadsheet to track progress

## After Import

Once all content is imported:

1. Review and edit the articles as needed
2. Add categories or tags if necessary
3. Publish the articles if they're not already published
4. Test the news section on your website to ensure everything displays correctly
