/**
 * <PERSON><PERSON><PERSON> to create default pages in Sanity
 * 
 * This script creates the main pages from the website in Sanity
 * so they can be edited from the admin dashboard.
 * 
 * Run with: node scripts/create-default-pages.js
 */

const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Define the default pages to create
const defaultPages = [
  {
    title: 'Home',
    slug: {
      _type: 'slug',
      current: '/'
    },
    description: 'The official homepage of the Kingdom of Adukrom, led by King <PERSON>.',
    navMenu: true,
    navOrder: 0,
    accessLevel: 'super_admin',
    pageBuilder: [
      {
        _type: 'hero',
        heading: 'Welcome to the Royal Court of Adukrom Kingdom',
        tagline: 'Preserving Heritage, Building Legacy',
        ctas: [
          {
            title: 'Learn More',
            link: '#about',
            isPrimary: true
          },
          {
            title: 'Contact Us',
            link: '#contact',
            isPrimary: false
          }
        ]
      }
    ]
  },
  {
    title: 'About',
    slug: {
      _type: 'slug',
      current: 'about'
    },
    description: 'Learn about the Kingdom of Adukrom and King Allen Ellison.',
    navMenu: true,
    navOrder: 10,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'textSection',
        heading: 'About the Kingdom',
        text: 'The Kingdom of Adukrom is a traditional monarchy located in Ghana, West Africa. Led by King Allen Ellison, the kingdom is dedicated to preserving cultural heritage while embracing modern development.'
      }
    ]
  },
  {
    title: 'Coronation',
    slug: {
      _type: 'slug',
      current: 'coronation'
    },
    description: 'Information about the royal coronation ceremony of King Allen Ellison.',
    navMenu: true,
    navOrder: 20,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'textSection',
        heading: 'Royal Coronation Ceremony',
        text: 'The coronation of King Allen Ellison will be a historic event celebrating the rich cultural traditions of the Adukrom Kingdom.'
      }
    ]
  },
  {
    title: 'RSVP',
    slug: {
      _type: 'slug',
      current: 'rsvp'
    },
    description: 'RSVP for royal events and ceremonies.',
    navMenu: true,
    navOrder: 30,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'contactForm',
        heading: 'RSVP for Royal Events',
        text: 'Please fill out this form to attend our upcoming royal events.'
      }
    ]
  },
  {
    title: 'Initiatives',
    slug: {
      _type: 'slug',
      current: 'initiatives'
    },
    description: 'Explore the various initiatives and projects of the Adukrom Kingdom.',
    navMenu: true,
    navOrder: 40,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'textSection',
        heading: 'Royal Initiatives',
        text: 'The Kingdom of Adukrom is committed to various initiatives focused on education, healthcare, and economic development.'
      }
    ]
  },
  {
    title: 'Events',
    slug: {
      _type: 'slug',
      current: 'events'
    },
    description: 'Calendar of upcoming royal events and ceremonies.',
    navMenu: true,
    navOrder: 45,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'textSection',
        heading: 'Royal Events Calendar',
        text: 'Stay updated with all the upcoming events and ceremonies of the Adukrom Kingdom.'
      }
    ]
  },
  {
    title: 'Gallery',
    slug: {
      _type: 'slug',
      current: 'gallery'
    },
    description: 'Photo gallery showcasing the Kingdom of Adukrom and royal activities.',
    navMenu: true,
    navOrder: 50,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'imageGallery',
        heading: 'Royal Gallery',
        text: 'A collection of images from the Kingdom of Adukrom and royal ceremonies.'
      }
    ]
  },
  {
    title: 'Contact',
    slug: {
      _type: 'slug',
      current: 'contact'
    },
    description: 'Contact information for the Royal Court of Adukrom Kingdom.',
    navMenu: true,
    navOrder: 60,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'contactForm',
        heading: 'Contact the Royal Court',
        text: 'Reach out to the Royal Court of Adukrom Kingdom with your inquiries.'
      }
    ]
  },
  {
    title: 'News',
    slug: {
      _type: 'slug',
      current: 'news'
    },
    description: 'Latest news and announcements from the Adukrom Kingdom.',
    navMenu: true,
    navOrder: 70,
    accessLevel: 'both',
    pageBuilder: [
      {
        _type: 'textSection',
        heading: 'Royal News',
        text: 'Stay informed with the latest news and announcements from the Adukrom Kingdom.'
      }
    ]
  }
];

// Function to create a page if it doesn't exist
async function createPageIfNotExists(page) {
  try {
    // Check if the page already exists
    const existingPage = await client.fetch(
      `*[_type == "page" && slug.current == $slug][0]`,
      { slug: page.slug.current }
    );

    if (existingPage) {
      console.log(`Page "${page.title}" already exists with ID: ${existingPage._id}`);
      return existingPage._id;
    }

    // Create the page
    const result = await client.create({
      _type: 'page',
      ...page
    });

    console.log(`Created page "${page.title}" with ID: ${result._id}`);
    return result._id;
  } catch (error) {
    console.error(`Error creating page "${page.title}":`, error);
    return null;
  }
}

// Main function to create all default pages
async function createDefaultPages() {
  console.log('Creating default pages in Sanity...');
  
  for (const page of defaultPages) {
    await createPageIfNotExists(page);
  }
  
  console.log('Finished creating default pages.');
}

// Run the script
createDefaultPages().catch(console.error);
