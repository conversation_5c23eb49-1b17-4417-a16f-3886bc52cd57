import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getNewsBySlug, getNews } from '@/lib/sanity';
import { urlFor } from '@/lib/sanity';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import PortableText from '@/components/PortableText';
import { formatDate } from '@/lib/utils';
import { generateDynamicNewsMetadata } from '@/lib/metadata-generator';

// Define the NewsArticle type
interface NewsArticle {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  excerpt?: string;
  body?: any;
  publishedAt: string;
  mainImage?: {
    asset: {
      _ref: string;
    };
    alt?: string;
    caption?: string;
  };
  gallery?: {
    image: {
      asset: {
        _ref: string;
      };
    };
    alt: string;
    caption?: string;
  }[];
  categoryName?: string;
  category?: {
    _id: string;
    title: string;
    slug: {
      current: string;
    };
  };
  author?: {
    _id: string;
    name: string;
    image?: {
      asset: {
        _ref: string;
      };
    };
  };
}

// Generate static params for all news articles
export async function generateStaticParams() {
  const news = await getNews();

  return news.map((article: any) => ({
    slug: article.slug.current,
  }));
}

// Generate metadata for this page
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const article = await getNewsBySlug(params.slug);

  if (!article) {
    return {
      title: 'Article Not Found',
      description: 'The requested article could not be found.',
    };
  }

  return generateDynamicNewsMetadata(article);
}

export default async function NewsArticlePage({ params }: { params: { slug: string } }) {
  // Fetch the news article by slug
  const article: NewsArticle = await getNewsBySlug(params.slug);

  // Log article data for debugging
  console.log('Article data:', JSON.stringify({
    id: article?._id,
    title: article?.title,
    hasBody: !!article?.body,
    bodyType: article?.body ? typeof article.body : 'undefined',
    isBodyArray: article?.body ? Array.isArray(article.body) : false,
    bodyLength: article?.body && Array.isArray(article.body) ? article.body.length : 0
  }));

  // If article not found, return 404
  if (!article) {
    notFound();
  }

  // Convert body to portable text format if it's a string
  if (article.body && typeof article.body === 'string') {
    console.log('Converting string body to portable text format');
    article.body = [
      {
        _type: 'block',
        _key: 'converted-body',
        style: 'normal',
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: 'converted-span',
            text: article.body,
            marks: []
          }
        ]
      }
    ];
  }

  // Ensure body is an array if it's null or undefined
  if (!article.body) {
    console.log('Creating empty body array');
    article.body = [];
  }

  return (
    <>
      <Header />
      <main>
        <article className="py-20 bg-ivory">
          <div className="container mx-auto px-4">
            <div className="flex justify-center mb-4">
              <Link href="/news" className="flex items-center gap-2 text-royalBlue hover:text-royalGold transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to News
              </Link>
            </div>

            {/* Article header */}
            <div className="max-w-4xl mx-auto mb-12">
              {article.category && (
                <Link
                  href={`/news/category/${article.category.slug.current}`}
                  className="inline-block px-4 py-1 bg-royalGold text-white text-sm font-medium rounded-full mb-4"
                >
                  {article.category.title}
                </Link>
              )}

              {/* If no category but we have categoryName, show that instead */}
              {!article.category && article.categoryName && (
                <span className="inline-block px-4 py-1 bg-royalGold text-white text-sm font-medium rounded-full mb-4">
                  {article.categoryName}
                </span>
              )}

              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-royalBlue mb-6">
                {article.title}
              </h1>

              <div className="flex items-center text-gray-600 mb-8">
                <time dateTime={article.publishedAt} suppressHydrationWarning>
                  {formatDate(article.publishedAt)}
                </time>
                {article.author && (
                  <>
                    <span className="mx-2">•</span>
                    <div className="flex items-center">
                      {article.author.image && (
                        <div className="relative w-8 h-8 rounded-full overflow-hidden mr-2">
                          <Image
                            src={urlFor(article.author.image).url()}
                            alt={article.author.name || "Author"}
                            fill
                            className="object-cover"
                            unoptimized={true}
                          />
                        </div>
                      )}
                      <span>{article.author.name}</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Featured image */}
            {article.mainImage && (
              <div className="max-w-5xl mx-auto mb-12 rounded-lg overflow-hidden shadow-xl">
                <Image
                  src={urlFor(article.mainImage).url()}
                  alt={article.title || "Article image"}
                  width={1200}
                  height={675}
                  className="w-full h-auto"
                  priority
                  unoptimized={true}
                />
                {article.mainImage.caption && (
                  <div className="bg-white p-3 text-sm text-center text-gray-600 italic">
                    {article.mainImage.caption}
                  </div>
                )}
              </div>
            )}

            {/* Article content */}
            <div className="max-w-4xl mx-auto prose prose-lg prose-headings:text-royalBlue prose-a:text-royalBlue hover:prose-a:text-royalGold prose-img:rounded-lg prose-img:shadow-md">
              {article.body && Array.isArray(article.body) && article.body.length > 0 ? (
                <PortableText value={article.body} />
              ) : (
                <div className="space-y-6">
                  <p className="text-gray-800 text-lg leading-relaxed">
                    {article.excerpt || 'No content available for this article.'}
                  </p>

                  {/* Create a simple portable text structure if body exists but isn't in the right format */}
                  {article.body && typeof article.body === 'string' && (
                    <div className="mt-4">
                      {article.body.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="text-gray-800 leading-relaxed mb-4">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  )}

                  {/* Show default content if no body content is available */}
                  {(!article.body || (Array.isArray(article.body) && article.body.length === 0)) && (
                    <>
                      <h2 className="text-2xl font-bold text-royalBlue mt-8">About This Article</h2>

                      <p className="text-gray-800 leading-relaxed">
                        The Kingdom of Adukrom is proud to present this important announcement as part of our ongoing commitment to transparency and community engagement.
                      </p>

                      <p className="text-gray-800 leading-relaxed">
                        Under the leadership of King Allen Ellison, the Kingdom of Adukrom continues to strengthen its cultural heritage while embracing modern governance principles.
                      </p>

                      <div className="my-8 p-4 bg-gray-50 border-l-4 border-royalBlue rounded">
                        <p className="italic text-gray-700">
                          "We are committed to preserving our rich cultural heritage while building a prosperous future for all citizens of Adukrom Kingdom."
                        </p>
                        <p className="text-right text-sm text-gray-600 mt-2">— King Allen Ellison</p>
                      </div>

                      <p className="text-gray-800 leading-relaxed">
                        For more information about upcoming royal events and initiatives, please visit our <a href="/events" className="text-royalBlue hover:text-royalGold transition-colors">events page</a> or contact the Royal Court Communications Office.
                      </p>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Image Gallery */}
            {article.gallery && article.gallery.length > 0 && (
              <div className="max-w-5xl mx-auto mt-16">
                <h2 className="text-3xl font-bold text-royalBlue mb-8 text-center">
                  Image Gallery
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {article.gallery.map((galleryItem, index) => (
                    <div key={index} className="group cursor-pointer">
                      <div className="relative overflow-hidden rounded-lg shadow-lg transition-transform duration-300 group-hover:scale-105">
                        <Image
                          src={urlFor(galleryItem.image).url()}
                          alt={galleryItem.alt}
                          width={400}
                          height={300}
                          className="w-full h-64 object-cover"
                          unoptimized={true}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                      </div>
                      {galleryItem.caption && (
                        <p className="mt-3 text-sm text-gray-600 text-center italic">
                          {galleryItem.caption}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </article>

        {/* Related articles section could go here */}

      </main>
      <Footer />
    </>
  );
}
