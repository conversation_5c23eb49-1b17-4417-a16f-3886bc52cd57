'use client';

import { motion } from 'framer-motion';
import { HeroProps } from '../types';
import { urlFor } from '@/lib/sanity.client';
import Link from 'next/link';

export default function Hero({ heading, tagline, backgroundImage, ctas }: HeroProps) {
  // Helper function to safely get image URL
  const getImageUrl = (image: any): string => {
    if (!image) return '/placeholder.jpg';
    
    try {
      const imageUrl = urlFor(image).url();
      return imageUrl;
    } catch (error) {
      console.error('Error generating image URL:', error);
      return '/placeholder.jpg';
    }
  };

  return (
    <div className="relative min-h-[70vh] flex items-center justify-center overflow-hidden">
      {/* Background image with overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ 
          backgroundImage: `url(${backgroundImage ? getImageUrl(backgroundImage) : '/Website Images/ghana-landscape-building-01.png'})`,
        }}
      >
        <div className="absolute inset-0 bg-black/50"></div>
      </div>
      
      {/* Content */}
      <div className="container mx-auto px-4 relative z-10 text-center py-20">
        <motion.h1 
          className="text-4xl md:text-6xl font-bold text-white mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {heading || 'Welcome to Kingdom of Adukrom'}
        </motion.h1>
        
        {tagline && (
          <motion.p 
            className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {tagline}
          </motion.p>
        )}
        
        {ctas && ctas.length > 0 && (
          <motion.div 
            className="flex flex-wrap gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {ctas.map((cta, index) => (
              <Link 
                key={index} 
                href={cta.link || '#'}
                className={`px-8 py-3 rounded-full font-medium transition-colors ${
                  cta.isPrimary 
                    ? 'bg-royalGold text-white hover:bg-amber-600' 
                    : 'bg-white text-royalBlue hover:bg-gray-100'
                }`}
              >
                {cta.title}
              </Link>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
}
