/**
 * <PERSON><PERSON><PERSON> to add gallery items to Sanity
 *
 * This script adds the gallery items shown in the Gallery section to the Sanity CMS.
 *
 * Run with: node scripts/add-gallery-items.js
 */

// Import the Sanity client
const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Configure the client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Need to provide this when running the script
  apiVersion: '2023-05-03', // Use a consistent API version
  useCdn: false
});

// Gallery items data
const galleryItems = [
  {
    title: 'Royal Coronation Ceremony',
    slug: { current: 'royal-coronation-ceremony' },
    description: 'The official coronation ceremony of the King',
    imagePath: 'public/Website Images/gallery/coronation1.jpg',
    imageAlt: 'Royal Coronation Ceremony',
    categoryTitle: 'Ceremonies',
    featured: true,
    order: 1
  },
  {
    title: 'Traditional Dance Performance',
    slug: { current: 'traditional-dance-performance' },
    description: 'Cultural dance performances during royal events',
    imagePath: 'public/Website Images/gallery/traditional-dance.jpg',
    imageAlt: 'Traditional Dance Performance',
    categoryTitle: 'Culture',
    featured: true,
    order: 2
  },
  {
    title: 'Royal Palace',
    slug: { current: 'royal-palace' },
    description: 'The majestic royal palace of Adukrom',
    imagePath: 'public/Website Images/gallery/palace.jpg',
    imageAlt: 'Royal Palace',
    categoryTitle: 'Architecture',
    featured: true,
    order: 3
  },
  {
    title: 'Community Gathering',
    slug: { current: 'community-gathering' },
    description: 'Community members gathering for a royal event',
    imagePath: 'public/Website Images/gallery/community.jpg',
    imageAlt: 'Community Gathering',
    categoryTitle: 'Events',
    featured: true,
    order: 4
  },
  {
    title: 'Royal Artifacts',
    slug: { current: 'royal-artifacts' },
    description: 'Historical artifacts from the kingdom',
    imagePath: 'public/Website Images/gallery/artifacts.jpg',
    imageAlt: 'Royal Artifacts',
    categoryTitle: 'Heritage',
    featured: true,
    order: 5
  },
  {
    title: 'Traditional Ceremony',
    slug: { current: 'traditional-ceremony' },
    description: 'Traditional ceremonies of the kingdom',
    imagePath: 'public/Website Images/gallery/ceremony.jpg',
    imageAlt: 'Traditional Ceremony',
    categoryTitle: 'Ceremonies',
    featured: true,
    order: 6
  }
];

// Function to get or create a category
async function getOrCreateCategory(title) {
  try {
    // Check if category exists
    const existingCategory = await client.fetch(
      `*[_type == "category" && title == $title][0]`,
      { title }
    );

    if (existingCategory) {
      return existingCategory;
    }

    // Create slug from title
    const slug = title.toLowerCase().replace(/\s+/g, '-');

    // Create new category
    console.log(`Creating new category: ${title}`);
    const newCategory = await client.create({
      _type: 'category',
      title,
      slug: { current: slug },
      description: `${title} category for gallery items`
    });

    console.log(`Created category: ${newCategory.title}`);
    return newCategory;
  } catch (error) {
    console.error(`Error creating category ${title}:`, error);
    return null;
  }
}

// Function to upload an image to Sanity
async function uploadImage(imagePath, altText) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.error(`Image not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const imageAsset = await client.assets.upload('image', imageBuffer, {
      filename: path.basename(imagePath),
      contentType: `image/${path.extname(imagePath).substring(1)}`
    });

    return {
      _type: 'image',
      asset: {
        _type: 'reference',
        _ref: imageAsset._id
      },
      alt: altText
    };
  } catch (error) {
    console.error(`Error uploading image ${imagePath}:`, error);
    return null;
  }
}

// Function to create a gallery item
async function createGalleryItem(item) {
  try {
    // Check if gallery item already exists
    const existingItem = await client.fetch(
      `*[_type == "gallery" && slug.current == $slug][0]`,
      { slug: item.slug.current }
    );

    // Get or create category
    const category = await getOrCreateCategory(item.categoryTitle);
    if (!category) {
      console.error(`Failed to get/create category for ${item.title}`);
      return null;
    }

    // Upload image
    const image = await uploadImage(item.imagePath, item.imageAlt);
    if (!image) {
      console.error(`Failed to upload image for ${item.title}`);
      return null;
    }

    if (existingItem) {
      console.log(`Gallery item ${item.title} already exists, updating...`);

      // Update existing item
      const updatedItem = await client
        .patch(existingItem._id)
        .set({
          title: item.title,
          description: item.description,
          image,
          images: [{ image, alt: item.imageAlt }],
          category: {
            _type: 'reference',
            _ref: category._id
          },
          featured: item.featured,
          order: item.order
        })
        .commit();

      console.log(`Updated gallery item: ${updatedItem.title}`);
      return updatedItem;
    }

    // Create new gallery item
    console.log(`Creating new gallery item: ${item.title}`);
    const newItem = await client.create({
      _type: 'gallery',
      title: item.title,
      slug: item.slug,
      description: item.description,
      image,
      images: [{ image, alt: item.imageAlt }],
      category: {
        _type: 'reference',
        _ref: category._id
      },
      featured: item.featured,
      order: item.order,
      publishedAt: new Date().toISOString()
    });

    console.log(`Created gallery item: ${newItem.title}`);
    return newItem;
  } catch (error) {
    console.error(`Error creating/updating gallery item ${item.title}:`, error);
    return null;
  }
}

// Main function to add all gallery items
async function addGalleryItems() {
  console.log('Starting to add gallery items to Sanity...');

  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  for (const item of galleryItems) {
    await createGalleryItem(item);
  }

  console.log('Finished adding gallery items to Sanity');
}

// Run the script
addGalleryItems().catch(console.error);
