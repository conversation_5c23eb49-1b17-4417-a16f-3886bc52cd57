'use client';
import { useState } from 'react';

export default function Newsletter() {
    const [email, setEmail] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [message, setMessage] = useState('');

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      if (!email) {
        setMessage('Please enter your email address');
        return;
      }

      setIsSubmitting(true);
      setMessage('');

      try {
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'newsletter',
            email,
          }),
        });

        const data = await response.json();

        if (data.success) {
          setMessage('Thank you for subscribing!');
          setEmail('');
        } else {
          setMessage('Failed to subscribe. Please try again later.');
        }
      } catch (error) {
        console.error('Error subscribing:', error);
        setMessage('An error occurred. Please try again later.');
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <section className="py-12 bg-ivory relative">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-royalBlue rounded-lg shadow-xl p-8 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-40 h-40">
              <svg viewBox="0 0 100 100" className="text-royalGold/20" fill="currentColor">
                <path d="M0 0 L100 0 L100 100 Z" />
              </svg>
            </div>

            <div className="relative z-10">
              <h3 className="text-2xl font-bold text-white mb-2">Stay Connected with the Royal Court</h3>
              <p className="text-royalGold mb-6">
                Subscribe to receive updates on royal events, initiatives, and announcements.
              </p>

              <form id="newsletter-form" className="flex flex-col md:flex-row gap-4" onSubmit={handleSubmit}>
                <input
                  type="email"
                  placeholder="Your email address"
                  className="form-input flex-grow px-4 py-3 rounded-full focus:outline-none"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <button
                  type="submit"
                  className="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Subscribing...' : 'Subscribe'}
                </button>
              </form>
              {message && (
                <div className={`mt-4 text-center ${message.includes('Thank you') ? 'text-green-500' : 'text-red-500'}`}>
                  {message}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    );
  }