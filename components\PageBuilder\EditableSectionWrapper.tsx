'use client';

import { useState, useEffect, useRef } from 'react';
import { Edit, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface EditableSectionWrapperProps {
  children: React.ReactNode;
  section: any;
  index: number;
}

export default function EditableSectionWrapper({ children, section, index }: EditableSectionWrapperProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedSection, setEditedSection] = useState<any>(section);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Listen for global edit mode changes
  useEffect(() => {
    const enableEditMode = () => setIsEditMode(true);
    const disableEditMode = () => {
      setIsEditMode(false);
      setIsEditing(false);
    };

    document.addEventListener('enableEditMode', enableEditMode);
    document.addEventListener('disableEditMode', disableEditMode);

    return () => {
      document.removeEventListener('enableEditMode', enableEditMode);
      document.removeEventListener('disableEditMode', disableEditMode);
    };
  }, []);

  // Store changes in a data attribute for the AdminControls to collect
  useEffect(() => {
    if (wrapperRef.current && JSON.stringify(section) !== JSON.stringify(editedSection)) {
      wrapperRef.current.setAttribute('data-section-changes', JSON.stringify(editedSection));
      wrapperRef.current.setAttribute('data-section-index', index.toString());
    } else if (wrapperRef.current) {
      wrapperRef.current.removeAttribute('data-section-changes');
    }
  }, [editedSection, section, index]);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedSection(section);
  };

  const handleSaveEdit = () => {
    setIsEditing(false);
    // Changes are stored in the data attribute and will be collected by AdminControls
  };

  const renderEditor = () => {
    switch (section._type) {
      case 'hero':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                value={editedSection.heading || ''}
                onChange={(e) => setEditedSection({ ...editedSection, heading: e.target.value })}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                value={editedSection.tagline || ''}
                onChange={(e) => setEditedSection({ ...editedSection, tagline: e.target.value })}
                placeholder="Enter tagline"
              />
            </div>
          </div>
        );
      case 'textSection':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                value={editedSection.heading || ''}
                onChange={(e) => setEditedSection({ ...editedSection, heading: e.target.value })}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Content</Label>
              <Textarea
                id="text"
                value={typeof editedSection.text === 'string' ? editedSection.text : JSON.stringify(editedSection.text || '')}
                onChange={(e) => setEditedSection({ ...editedSection, text: e.target.value })}
                placeholder="Enter content"
                rows={6}
              />
            </div>
            <div>
              <Label htmlFor="backgroundStyle">Background Style</Label>
              <Select
                value={editedSection.backgroundStyle || 'none'}
                onValueChange={(value) => setEditedSection({ ...editedSection, backgroundStyle: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select background style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="royalBlue">Royal Blue</SelectItem>
                  <SelectItem value="royalGold">Royal Gold</SelectItem>
                  <SelectItem value="ivory">Ivory</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );
      case 'imageGallery':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                value={editedSection.heading || ''}
                onChange={(e) => setEditedSection({ ...editedSection, heading: e.target.value })}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Description</Label>
              <Textarea
                id="text"
                value={editedSection.text || ''}
                onChange={(e) => setEditedSection({ ...editedSection, text: e.target.value })}
                placeholder="Enter description"
                rows={4}
              />
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                value={editedSection.heading || ''}
                onChange={(e) => setEditedSection({ ...editedSection, heading: e.target.value })}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Content</Label>
              <Textarea
                id="text"
                value={editedSection.text || ''}
                onChange={(e) => setEditedSection({ ...editedSection, text: e.target.value })}
                placeholder="Enter content"
                rows={6}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div ref={wrapperRef} className="relative group">
      {isEditMode && !isEditing && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleEditClick}
          className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity bg-white"
        >
          <Edit className="h-4 w-4 mr-1" />
          Edit Section
        </Button>
      )}

      {isEditing ? (
        <Card className="my-4 border-2 border-blue-500">
          <CardHeader>
            <CardTitle>Edit {section._type} Section</CardTitle>
          </CardHeader>
          <CardContent>
            {renderEditor()}
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancelEdit}>
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button variant="default" size="sm" onClick={handleSaveEdit}>
              <Check className="h-4 w-4 mr-1" />
              Apply Changes
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className={isEditMode ? "outline outline-2 outline-dashed outline-blue-300 outline-offset-2" : ""}>
          {children}
        </div>
      )}
    </div>
  );
}
