import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate metadata for the main events page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Royal Events',
    description: 'Discover upcoming royal events and ceremonies from the Kingdom of Adukrom.',
    url: '/events',
    keywords: ['Adukrom Events', 'Royal Ceremonies', 'Kingdom Events', 'African Royal Events'],
  });
}
