'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import CountdownTimer from '@/components/CountdownTimer';

export default function SecretPage() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showLivestream, setShowLivestream] = useState(false);

  const [accessCode, setAccessCode] = useState(['', '', '', '', '', '']);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Handle access code input
  const handleCodeInput = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    if (value.length <= 1) {
      const newCode = [...accessCode];
      newCode[index] = value;
      setAccessCode(newCode);

      // Auto-focus next input
      if (value.length === 1 && index < 5) {
        const nextInput = document.querySelector(`input[data-index="${index + 2}"]`) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Handle access form submission
  const handleAccessSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const code = accessCode.join('');
    // For demo purposes, any 6-digit code works
    if (code.length === 6) {
      setShowLivestream(true);
      window.scrollTo(0, 0);
    }
  };



  // Handle FAQ toggle
  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  return (
    <div className="min-h-screen flex flex-col bg-ivory">

      {/* Main Content */}
      <main>
        {/* Access Code Section - shown if livestream is not active */}
        {!showLivestream && (
          <section id="access-code" className="py-16 royal-gradient">
            <div className="container mx-auto px-4">
              <div className="max-w-4xl mx-auto text-center">
                <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">Royal Coronation Livestream 2025</h1>
                <p className="text-xl text-royalGold mb-8">Enter your NFT access code to watch the coronation ceremony of King Allen Ellison</p>

                <div className="mb-12">
                  <CountdownTimer
                    size="md"
                    showTitle={true}
                    showLabels={true}
                    textColor="text-white"
                    numberColor="text-royalGold"
                  />
                </div>

                <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 shadow-lg">
                  <h2 className="text-2xl font-bold text-white mb-6">Enter Your NFT Access Code</h2>

                  <form id="access-form" className="space-y-8" onSubmit={handleAccessSubmit}>
                    <div>
                      <label className="block text-royalGold font-medium mb-4">Your 6-Digit Access Code</label>
                      <div className="flex justify-center space-x-2">
                        {[1, 2, 3, 4, 5, 6].map((num, index) => (
                          <input
                            key={num}
                            type="text"
                            maxLength={1}
                            className="code-input"
                            data-index={num}
                            value={accessCode[index]}
                            onChange={(e) => handleCodeInput(e, index)}
                            required
                          />
                        ))}
                      </div>
                      <p className="text-white/70 text-sm mt-4">The code was sent to your email after your NFT purchase</p>
                    </div>

                    <div>
                      <button
                        type="submit"
                        className="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                      >
                        Access Livestream
                      </button>
                    </div>
                  </form>

                  <div className="mt-8 text-white/80 text-sm">
                    <p>Having trouble? <button onClick={() => setShowHelpModal(true)} className="text-royalGold hover:underline">Get Help</button></p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* Livestream Section - shown if access code is entered */}
        {showLivestream && (
          <section id="livestream-section" className="py-16 bg-ivory">
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-royalBlue">Royal Coronation Ceremony</h2>
                  <div className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-red-500 rounded-full live-indicator mr-2"></span>
                    <span className="text-red-500 font-bold">LIVE</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Video */}
                  <div className="lg:col-span-2">
                    <div className="video-container bg-royalBlue">
                      {/* Placeholder for video player */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <svg className="w-20 h-20 text-royalGold mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z"></path>
                          </svg>
                          <p className="text-white text-lg">Livestream will begin shortly</p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-2xl font-bold text-royalBlue mb-4">The Coronation of Prince Allen</h3>
                      <p className="text-charcoal mb-4">Witness the historic coronation ceremony of Prince Allen as he ascends to the throne of Adukrom Kingdom. This sacred ceremony follows ancient traditions dating back centuries, with royal elders, dignitaries, and international guests in attendance.</p>

                      <div className="flex items-center space-x-4 mt-6">
                        <button className="flex items-center space-x-2 bg-royalBlue text-white py-2 px-4 rounded-full hover:bg-blue-900 transition-colors">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-2 14.5v-9l6 4.5-6 4.5z"></path>
                          </svg>
                          <span>HD Quality</span>
                        </button>

                        <button className="flex items-center space-x-2 bg-white border border-royalBlue text-royalBlue py-2 px-4 rounded-full hover:bg-royalBlue/5 transition-colors">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                          </svg>
                          <span>Share</span>
                        </button>

                        <button className="flex items-center space-x-2 bg-white border border-royalBlue text-royalBlue py-2 px-4 rounded-full hover:bg-royalBlue/5 transition-colors">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"></path>
                          </svg>
                          <span>Favorite</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Live Chat */}
                  <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-royalGold/30">
                    <div className="bg-royalBlue text-white p-4">
                      <h3 className="font-bold">Live Chat</h3>
                    </div>

                    <div className="h-96 overflow-y-auto p-4" id="chat-messages">
                      <div className="chat-message p-3 border-b border-gray-100">
                        <div className="flex items-center mb-1">
                          <span className="font-bold text-royalBlue">Royal Moderator</span>
                          <span className="ml-2 text-xs text-gray-500">10:05 AM</span>
                        </div>
                        <p className="text-charcoal">Welcome to the Royal Coronation livestream! Please be respectful in the chat.</p>
                      </div>

                      <div className="chat-message p-3 border-b border-gray-100">
                        <div className="flex items-center mb-1">
                          <span className="font-bold text-royalBlue">GhanaProud</span>
                          <span className="ml-2 text-xs text-gray-500">10:07 AM</span>
                        </div>
                        <p className="text-charcoal">So excited to witness this historic moment! Greetings from Accra!</p>
                      </div>

                      <div className="chat-message p-3 border-b border-gray-100">
                        <div className="flex items-center mb-1">
                          <span className="font-bold text-royalBlue">GlobalCitizen</span>
                          <span className="ml-2 text-xs text-gray-500">10:08 AM</span>
                        </div>
                        <p className="text-charcoal">The royal regalia looks amazing! Can someone explain the significance of the golden stool?</p>
                      </div>

                      <div className="chat-message p-3 border-b border-gray-100">
                        <div className="flex items-center mb-1">
                          <span className="font-bold text-royalBlue">HistoryBuff</span>
                          <span className="ml-2 text-xs text-gray-500">10:10 AM</span>
                        </div>
                        <p className="text-charcoal">@GlobalCitizen The golden stool represents the soul of the Adukrom people and symbolizes authority and unity.</p>
                      </div>

                      <div className="chat-message p-3 border-b border-gray-100">
                        <div className="flex items-center mb-1">
                          <span className="font-bold text-royalBlue">TraditionKeeper</span>
                          <span className="ml-2 text-xs text-gray-500">10:12 AM</span>
                        </div>
                        <p className="text-charcoal">The elders are now preparing for the sacred oath ceremony. This is a pivotal moment in the coronation.</p>
                      </div>
                    </div>

                    <div className="p-4 border-t border-gray-200">
                      <form className="flex">
                        <input type="text" placeholder="Type your message..." className="form-input flex-grow px-4 py-2 rounded-l-md focus:outline-none border border-gray-300" />
                        <button type="submit" className="bg-royalGold text-royalBlue px-4 py-2 rounded-r-md hover:bg-yellow-500 transition-colors">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
                          </svg>
                        </button>
                      </form>
                    </div>
                  </div>
                </div>

                {/* Event Schedule */}
                <div className="mt-12">
                  <h3 className="text-2xl font-bold text-royalBlue mb-6">Coronation Event Schedule</h3>

                  <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-royalGold/30">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-royalBlue text-white">
                          <tr>
                            <th className="py-3 px-6 text-left">Time</th>
                            <th className="py-3 px-6 text-left">Ceremony</th>
                            <th className="py-3 px-6 text-left">Description</th>
                            <th className="py-3 px-6 text-left">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className="border-b border-gray-200">
                            <td className="py-4 px-6">9:00 AM</td>
                            <td className="py-4 px-6 font-medium">Arrival of Dignitaries</td>
                            <td className="py-4 px-6">Welcome of royal guests and international representatives</td>
                            <td className="py-4 px-6"><span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Completed</span></td>
                          </tr>
                          <tr className="border-b border-gray-200 bg-royalGold/5">
                            <td className="py-4 px-6">10:00 AM</td>
                            <td className="py-4 px-6 font-medium">Traditional Procession</td>
                            <td className="py-4 px-6">Royal elders and chiefs escort the king-elect to the ceremonial grounds</td>
                            <td className="py-4 px-6"><span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">In Progress</span></td>
                          </tr>
                          <tr className="border-b border-gray-200">
                            <td className="py-4 px-6">11:30 AM</td>
                            <td className="py-4 px-6 font-medium">Sacred Oath Ceremony</td>
                            <td className="py-4 px-6">King Allen Ellison takes the sacred oath to serve the kingdom</td>
                            <td className="py-4 px-6"><span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Upcoming</span></td>
                          </tr>
                          <tr className="border-b border-gray-200 bg-royalGold/5">
                            <td className="py-4 px-6">12:30 PM</td>
                            <td className="py-4 px-6 font-medium">Crowning Ceremony</td>
                            <td className="py-4 px-6">Official placement of royal crown and presentation of royal regalia</td>
                            <td className="py-4 px-6"><span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Upcoming</span></td>
                          </tr>
                          <tr>
                            <td className="py-4 px-6">1:30 PM</td>
                            <td className="py-4 px-6 font-medium">First Royal Address</td>
                            <td className="py-4 px-6">King Allen Ellison delivers his first official address to the kingdom</td>
                            <td className="py-4 px-6"><span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Upcoming</span></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* NFT Information Section */}
        <section className="py-16 bg-ivory">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-16 section-title">Royal NFT Access</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-royalBlue mb-4">Exclusive Digital Collectible</h3>
                  <p className="text-charcoal mb-6">Your Royal Coronation NFT is more than just a ticket - it's a piece of history. Each NFT is uniquely numbered and provides:</p>

                  <ul className="space-y-3 mb-8">
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-royalGold mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <span>Exclusive access to the live coronation ceremony</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-royalGold mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <span>Permanent record of your participation in this historic event</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-royalGold mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <span>Access to exclusive behind-the-scenes content</span>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-royalGold mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <span>Invitation to future royal virtual events</span>
                    </li>
                  </ul>

                  <div className="mt-8">
                    <a href="#" className="royal-button inline-block bg-royalBlue text-white py-3 px-8 rounded-full hover:bg-blue-900 transition-colors">Purchase NFT Access</a>
                  </div>
                </div>

                <div>
                  <div className="nft-card rounded-lg overflow-hidden shadow-xl p-6 shimmer">
                    <div className="relative">
                      <svg className="w-full h-64" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="300" height="300" fill="#002366" />
                        <circle cx="150" cy="100" r="50" fill="#D4AF37" />
                        <path d="M125 80 L150 50 L175 80" stroke="#002366" strokeWidth="3" fill="none" />
                        <path d="M125 80 L175 80" stroke="#002366" strokeWidth="3" fill="none" />
                        <rect x="75" y="150" width="150" height="100" fill="#D4AF37" />
                        <rect x="100" y="175" width="100" height="50" fill="#002366" />
                        <text x="150" y="205" fontFamily="Arial" fontSize="14" fill="#D4AF37" textAnchor="middle">ROYAL CORONATION</text>
                        <text x="150" y="225" fontFamily="Arial" fontSize="10" fill="#D4AF37" textAnchor="middle">KING ALLEN ELLISON</text>
                        <text x="150" y="280" fontFamily="Arial" fontSize="16" fill="#002366" textAnchor="middle" fontWeight="bold">ADUKROM KINGDOM</text>
                      </svg>

                      <div className="absolute top-4 right-4 bg-royalGold text-royalBlue text-xs font-bold px-2 py-1 rounded">
                        #0721
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-xl font-bold text-royalBlue">Royal Coronation Access NFT</h4>
                      <p className="text-charcoal text-sm mt-2">Minted on August 1, 2025</p>

                      <div className="flex justify-between items-center mt-4">
                        <div>
                          <p className="text-xs text-gray-500">Current Value</p>
                          <p className="text-royalBlue font-bold">0.25 ETH</p>
                        </div>
                        <div className="bg-royalBlue/10 rounded-full px-3 py-1 text-xs text-royalBlue font-medium">
                          Limited Edition
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-16">
                <h3 className="text-2xl font-bold text-royalBlue mb-6">Frequently Asked Questions</h3>

                <div className="space-y-4">
                  {[
                    {
                      question: "How do I get my access code after purchasing the NFT?",
                      answer: "After completing your NFT purchase, the access code will be sent to the email address associated with your wallet. You'll also be able to view it in your NFT metadata on the blockchain."
                    },
                    {
                      question: "Can I share my access code with others?",
                      answer: "Each access code is tied to a specific NFT and can only be used on one device at a time. Sharing your code may result in access being revoked. The NFT represents your exclusive right to view the ceremony."
                    },
                    {
                      question: "Will the livestream be available for replay after the event?",
                      answer: "Yes, NFT holders will have exclusive access to the full ceremony recording for 30 days following the event. After that period, selected highlights will be made available to the general public."
                    },
                    {
                      question: "What happens if I lose my access code?",
                      answer: "If you lose your access code, you can recover it by verifying ownership of your NFT through our support portal. You'll need to connect your wallet to prove ownership."
                    }
                  ].map((faq, index) => (
                    <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden border border-royalGold/30">
                      <button
                        className="w-full text-left p-4 focus:outline-none flex justify-between items-center"
                        onClick={() => toggleFaq(index)}
                      >
                        <span className="font-medium text-royalBlue">{faq.question}</span>
                        <svg
                          className={`w-5 h-5 text-royalGold transform transition-transform ${activeFaq === index ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </button>
                      <div className={`px-4 pb-4 ${activeFaq === index ? 'block' : 'hidden'}`}>
                        <p className="text-charcoal">{faq.answer}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Help Modal */}
      {showHelpModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-xl p-8">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-royalBlue">Need Help?</h3>
              <button onClick={() => setShowHelpModal(false)} className="text-gray-500 hover:text-gray-700">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-royalBlue mb-2">Can't find your access code?</h4>
                <p className="text-charcoal text-sm">Check the email associated with your NFT purchase. The code was sent immediately after your transaction was confirmed.</p>
              </div>

              <div>
                <h4 className="font-medium text-royalBlue mb-2">Code not working?</h4>
                <p className="text-charcoal text-sm">Make sure you're entering the code exactly as it appears in your email, including any dashes or special characters.</p>
              </div>

              <div>
                <h4 className="font-medium text-royalBlue mb-2">Technical issues?</h4>
                <p className="text-charcoal text-sm">Try refreshing your browser or using a different browser. Our platform works best with Chrome, Firefox, and Safari.</p>
              </div>

              <div className="pt-4">
                <h4 className="font-medium text-royalBlue mb-2">Contact Support</h4>
                <form className="space-y-4">
                  <div>
                    <label htmlFor="support-email" className="block text-charcoal text-sm mb-1">Your Email</label>
                    <input type="email" id="support-email" className="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none" />
                  </div>
                  <div>
                    <label htmlFor="support-issue" className="block text-charcoal text-sm mb-1">Issue</label>
                    <select id="support-issue" className="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                      <option value="">Select an issue</option>
                      <option value="missing-code">Missing access code</option>
                      <option value="invalid-code">Invalid access code</option>
                      <option value="streaming-issues">Streaming issues</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="support-message" className="block text-charcoal text-sm mb-1">Message</label>
                    <textarea id="support-message" rows={3} className="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none"></textarea>
                  </div>
                  <button type="submit" className="w-full bg-royalGold text-royalBlue font-bold py-2 px-4 rounded-md hover:bg-yellow-500 transition-colors">Submit</button>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
