/**
 * Enhanced Development Server with Automatic Error Fixing
 * 
 * This script:
 * 1. Runs the hydration error fixer
 * 2. Starts the Next.js development server
 * 3. Monitors for errors in the console output
 * 4. Applies fixes automatically when possible
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Configuration
const PORT = process.env.PORT || 3000;
const ROOT_DIR = path.resolve(__dirname, '..');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Error patterns to watch for
const ERROR_PATTERNS = [
  {
    pattern: /A tree hydrated but some attributes of the server rendered HTML didn't match/,
    type: 'hydration',
    fix: () => {
      console.log(`${COLORS.yellow}Hydration mismatch detected. Running hydration fixer...${COLORS.reset}`);
      execSync('node scripts/fix-hydration.js', { stdio: 'inherit' });
    }
  },
  {
    pattern: /Module not found: Can't resolve '([^']+)'/,
    type: 'missing-module',
    fix: (match) => {
      const module = match[1];
      console.log(`${COLORS.yellow}Missing module detected: ${module}. Installing...${COLORS.reset}`);
      try {
        execSync(`npm install ${module}`, { stdio: 'inherit' });
        return true;
      } catch (error) {
        console.error(`${COLORS.red}Failed to install ${module}. Try installing it manually.${COLORS.reset}`);
        return false;
      }
    }
  },
  {
    pattern: /ChunkLoadError/,
    type: 'chunk-load',
    fix: () => {
      console.log(`${COLORS.yellow}ChunkLoadError detected. Cleaning Next.js cache...${COLORS.reset}`);
      try {
        execSync('npx next clean', { stdio: 'inherit' });
        return true;
      } catch (error) {
        console.error(`${COLORS.red}Failed to clean Next.js cache. Try running 'npx next clean' manually.${COLORS.reset}`);
        return false;
      }
    }
  }
];

/**
 * Run the hydration error fixer
 */
function runHydrationFixer() {
  console.log(`${COLORS.cyan}Running hydration error fixer...${COLORS.reset}`);
  try {
    execSync('node scripts/fix-hydration.js', { stdio: 'inherit' });
    console.log(`${COLORS.green}Hydration error fixer completed.${COLORS.reset}`);
  } catch (error) {
    console.error(`${COLORS.red}Error running hydration fixer:${COLORS.reset}`, error.message);
  }
}

/**
 * Start the Next.js development server
 */
function startDevServer() {
  console.log(`${COLORS.cyan}Starting Next.js development server...${COLORS.reset}`);
  
  // Use spawn to keep the process running and capture output
  const nextProcess = spawn('npx', ['next', 'dev', '--port', PORT], {
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true,
  });
  
  // Create readline interfaces to read stdout and stderr line by line
  const stdoutReader = readline.createInterface({ input: nextProcess.stdout });
  const stderrReader = readline.createInterface({ input: nextProcess.stderr });
  
  // Process stdout
  stdoutReader.on('line', (line) => {
    console.log(line);
    checkForErrors(line);
  });
  
  // Process stderr
  stderrReader.on('line', (line) => {
    console.error(`${COLORS.red}${line}${COLORS.reset}`);
    checkForErrors(line);
  });
  
  // Handle process exit
  nextProcess.on('close', (code) => {
    console.log(`${COLORS.yellow}Next.js development server exited with code ${code}${COLORS.reset}`);
    process.exit(code);
  });
  
  // Handle process errors
  nextProcess.on('error', (error) => {
    console.error(`${COLORS.red}Failed to start Next.js development server:${COLORS.reset}`, error.message);
    process.exit(1);
  });
  
  return nextProcess;
}

/**
 * Check a line of output for known error patterns
 */
function checkForErrors(line) {
  for (const errorPattern of ERROR_PATTERNS) {
    const match = line.match(errorPattern.pattern);
    if (match) {
      console.log(`${COLORS.yellow}Detected ${errorPattern.type} error. Attempting to fix...${COLORS.reset}`);
      
      // Apply the fix
      const fixResult = errorPattern.fix(match);
      
      if (fixResult) {
        console.log(`${COLORS.green}Fix applied for ${errorPattern.type} error.${COLORS.reset}`);
      }
      
      break;
    }
  }
}

/**
 * Main function
 */
function main() {
  console.log(`${COLORS.cyan}=== Enhanced Development Server ====${COLORS.reset}`);
  console.log(`${COLORS.cyan}This script automatically fixes common errors while running the development server.${COLORS.reset}`);
  
  // Run the hydration error fixer first
  runHydrationFixer();
  
  // Start the Next.js development server
  const nextProcess = startDevServer();
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log(`${COLORS.yellow}Stopping development server...${COLORS.reset}`);
    nextProcess.kill('SIGINT');
  });
}

// Run the main function
main();
