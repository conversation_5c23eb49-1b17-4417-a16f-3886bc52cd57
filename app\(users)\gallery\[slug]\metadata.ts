import { Metadata } from 'next';
import { generateDynamicGalleryMetadata } from '@/lib/metadata-generator';
import { getGalleryBySlug } from '@/lib/sanity';

// Generate dynamic metadata for gallery pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Fetch the gallery data
    const gallery = await getGalleryBySlug(params.slug);

    if (!gallery) {
      return {
        title: 'Gallery Not Found',
        description: 'The requested gallery could not be found.',
      };
    }

    // Generate metadata for the gallery
    return generateDynamicGalleryMetadata(gallery);
  } catch (error) {
    console.error('Error generating gallery metadata:', error);

    // Fallback metadata
    return {
      title: 'Gallery',
      description: 'Explore the royal gallery of the Kingdom of Adukrom.',
    };
  }
}
