
# Heroku .slugignore - Optimized for Next.js + Sanity application
# This file specifies files that should be excluded from the Heroku slug

# Development files
.git
.github
.vscode
.husky
.next/cache
node_modules/.cache
*.log

# Test files
__tests__
test
tests
cypress
jest
*.test.js
*.spec.js
*.test.tsx
*.spec.tsx

# Documentation
docs
documentation
*.md
!README.md

# Source maps
*.map

# Development configs
.eslintrc*
.prettierrc*
.babelrc*
jest.config.js
cypress.config.js

# Development scripts
scripts
dev-scripts
.storybook

# Sanity Studio (only needed for development)
studio-node-modules
sanity-studio-backup
node_modules/@sanity/vision
node_modules/@sanity/dashboard
node_modules/@sanity/plugin-loader
node_modules/@sanity/desk-tool
node_modules/@sanity/form-builder
node_modules/@sanity/default-layout
node_modules/@sanity/default-login
node_modules/@sanity/google-maps-input
node_modules/@sanity/language-filter
node_modules/@sanity/cli

# Development dependencies
node_modules/typescript
node_modules/@types
node_modules/eslint
node_modules/prettier
node_modules/jest
node_modules/cypress
node_modules/storybook
node_modules/webpack-dev-server
node_modules/babel-loader
node_modules/style-loader
node_modules/css-loader
node_modules/postcss-loader
node_modules/sass-loader
node_modules/file-loader
node_modules/url-loader
node_modules/html-webpack-plugin
node_modules/webpack-merge
node_modules/webpack-bundle-analyzer
node_modules/webpack-manifest-plugin
node_modules/webpack-dev-middleware
node_modules/webpack-hot-middleware
node_modules/mini-css-extract-plugin
node_modules/optimize-css-assets-webpack-plugin
node_modules/terser-webpack-plugin
node_modules/copy-webpack-plugin
node_modules/clean-webpack-plugin
node_modules/case-sensitive-paths-webpack-plugin
node_modules/dotenv-webpack
node_modules/fork-ts-checker-webpack-plugin
node_modules/ts-loader
node_modules/ts-node

# Exclude all node_modules except essential ones
node_modules/*
!node_modules/next
!node_modules/react
!node_modules/react-dom
!node_modules/@sanity/client
!node_modules/@sanity/image-url
!node_modules/next-sanity
!node_modules/next-auth
!node_modules/sanity-plugin-seo
!node_modules/styled-components

# Exclude removed packages
node_modules/@upstash
node_modules/canvas
node_modules/@sentry
node_modules/@opentelemetry
