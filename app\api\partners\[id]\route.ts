import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';
import { getWriteClient } from '@/lib/sanity.client';
import { logError } from '@/lib/errorHandling';

// Define the params type for better type safety
type Params = {
  id: string;
};

// GET /api/partners/[id] - Get a specific strategic partner by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Params }
) {
  // Extract the ID from params - this is the correct way in Next.js 15
  const { id } = params;

  try {

    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
    } catch (clientError) {
      console.error('Error getting Sanity client:', clientError);

      // Use a read-only client as fallback
      const { readClient } = await import('@/lib/sanity.client');
      client = readClient;
      console.log('Using read-only client as fallback');
    }

    // Query to fetch the strategic partner by ID
    const query = `*[_type == "strategicPartner" && _id == $id][0] {
      _id,
      name,
      slug,
      description,
      website,
      logo,
      partnershipType,
      featured,
      order,
      startDate,
      active
    }`;

    // Fetch the strategic partner from Sanity
    const partner = await client.fetch(query, { id }, {
      cache: 'no-store' // Disable caching to always get fresh data
    });

    if (!partner) {
      return NextResponse.json(
        { success: false, message: 'Strategic partner not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      partner,
    });
  } catch (error) {
    console.error(`Error fetching strategic partner with ID ${id}:`, error);
    logError(error, `GET /api/partners/${id}`);

    return NextResponse.json(
      { success: false, message: 'Failed to fetch strategic partner', error: String(error) },
      { status: 500 }
    );
  }
}

// PATCH /api/partners/[id] - Update a strategic partner
export async function PATCH(
  request: NextRequest,
  { params }: { params: Params }
) {
  // Extract the ID from params - this is the correct way in Next.js 15
  const { id } = params;

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to update strategic partners' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Get the Sanity client
    const client = getWriteClient();

    // Check if the partner exists
    const existingPartner = await client.fetch(
      `*[_type == "strategicPartner" && _id == $id][0]`,
      { id }
    );

    if (!existingPartner) {
      return NextResponse.json(
        { success: false, message: 'Strategic partner not found' },
        { status: 404 }
      );
    }

    // Log the data being sent to Sanity
    console.log('Updating strategic partner with data:', {
      id: id,
      name: body.name,
      logo: body.logo ? 'Logo provided' : 'No logo',
    });

    // Prepare the update object
    const updateObj: Record<string, any> = {
      _type: 'strategicPartner',
    };

    // Add fields to update only if they are provided
    if (body.name !== undefined) updateObj.name = body.name;
    if (body.slug !== undefined) updateObj.slug = body.slug;
    if (body.description !== undefined) updateObj.description = body.description;
    if (body.website !== undefined) updateObj.website = body.website;
    if (body.logo !== undefined) updateObj.logo = body.logo;
    if (body.partnershipType !== undefined) updateObj.partnershipType = body.partnershipType;
    if (body.featured !== undefined) updateObj.featured = body.featured;
    if (body.order !== undefined) updateObj.order = body.order;
    if (body.startDate !== undefined) updateObj.startDate = body.startDate;
    if (body.active !== undefined) updateObj.active = body.active;

    // Update the strategic partner
    const updatedPartner = await client
      .patch(id)
      .set(updateObj)
      .commit();

    console.log('Strategic partner updated successfully:', {
      _id: updatedPartner._id,
      name: updatedPartner.name,
    });

    return NextResponse.json({
      success: true,
      message: 'Strategic partner updated successfully',
      partner: updatedPartner,
    });
  } catch (error) {
    console.error(`Error updating strategic partner with ID ${id}:`, error);
    logError(error, `PATCH /api/partners/${id}`);

    return NextResponse.json(
      { success: false, message: 'Failed to update strategic partner', error: String(error) },
      { status: 500 }
    );
  }
}

// DELETE /api/partners/[id] - Delete a strategic partner
export async function DELETE(
  request: NextRequest,
  { params }: { params: Params }
) {
  // Extract the ID from params - this is the correct way in Next.js 15
  const { id } = params;

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to delete strategic partners' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Check if the partner exists
    const existingPartner = await client.fetch(
      `*[_type == "strategicPartner" && _id == $id][0]`,
      { id }
    );

    if (!existingPartner) {
      return NextResponse.json(
        { success: false, message: 'Strategic partner not found' },
        { status: 404 }
      );
    }

    // Delete the strategic partner
    await client.delete(id);

    return NextResponse.json({
      success: true,
      message: 'Strategic partner deleted successfully',
    });
  } catch (error) {
    console.error(`Error deleting strategic partner with ID ${id}:`, error);
    logError(error, `DELETE /api/partners/${id}`);

    return NextResponse.json(
      { success: false, message: 'Failed to delete strategic partner', error: String(error) },
      { status: 500 }
    );
  }
}
