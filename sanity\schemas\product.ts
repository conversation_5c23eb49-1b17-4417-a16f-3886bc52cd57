import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3,
    }),
    defineField({
      name: 'longDescription',
      title: 'Long Description',
      type: 'array',
      of: [{ type: 'block' }],
    }),
    defineField({
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              title: 'Alternative Text',
              type: 'string',
              description: 'Important for accessibility and SEO',
            },
            {
              name: 'caption',
              title: 'Caption',
              type: 'string',
            },
          ],
        },
      ],
      validation: (Rule) => Rule.required().min(1),
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'reference',
      to: [{ type: 'image' }],
      description: 'This will be used as the featured image',
    }),
    defineField({
      name: 'price',
      title: 'Price',
      type: 'number',
      validation: (Rule) => Rule.required().min(0),
    }),
    defineField({
      name: 'compareAtPrice',
      title: 'Compare At Price',
      type: 'number',
      description: 'Original price, if this product is on sale',
    }),
    defineField({
      name: 'currency',
      title: 'Currency',
      type: 'string',
      initialValue: 'USD',
      options: {
        list: [
          { title: 'USD', value: 'USD' },
          { title: 'EUR', value: 'EUR' },
          { title: 'GBP', value: 'GBP' },
          { title: 'CAD', value: 'CAD' },
        ],
      },
    }),
    defineField({
      name: 'sku',
      title: 'SKU',
      type: 'string',
      description: 'Stock Keeping Unit',
    }),
    defineField({
      name: 'inventory',
      title: 'Inventory',
      type: 'object',
      fields: [
        {
          name: 'quantity',
          title: 'Quantity',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'trackInventory',
          title: 'Track Inventory',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'allowBackorder',
          title: 'Allow Backorder',
          type: 'boolean',
          initialValue: false,
        },
      ],
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [{ type: 'storeCategory' }],
      description: 'Select a store category for this product',
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags',
      },
    }),
    defineField({
      name: 'featured',
      title: 'Featured',
      type: 'boolean',
      initialValue: false,
      description: 'Show this product on the homepage',
    }),
    defineField({
      name: 'isDigital',
      title: 'Is Digital Product',
      type: 'boolean',
      initialValue: false,
      description: 'Is this a digital product (e.g., download, NFT)?',
    }),
    defineField({
      name: 'digitalFile',
      title: 'Digital File',
      type: 'file',
      hidden: ({ parent }) => !parent?.isDigital,
    }),
    defineField({
      name: 'weight',
      title: 'Weight (in kg)',
      type: 'number',
      hidden: ({ parent }) => parent?.isDigital,
    }),
    defineField({
      name: 'dimensions',
      title: 'Dimensions',
      type: 'object',
      hidden: ({ parent }) => parent?.isDigital,
      fields: [
        { name: 'length', title: 'Length (cm)', type: 'number' },
        { name: 'width', title: 'Width (cm)', type: 'number' },
        { name: 'height', title: 'Height (cm)', type: 'number' },
      ],
    }),
    defineField({
      name: 'variants',
      title: 'Variants',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'sku', title: 'SKU', type: 'string' },
            { name: 'price', title: 'Price', type: 'number' },
            { name: 'inventory', title: 'Inventory', type: 'number' },
            {
              name: 'options',
              title: 'Options',
              type: 'object',
              fields: [
                { name: 'color', title: 'Color', type: 'string' },
                { name: 'size', title: 'Size', type: 'string' },
                { name: 'material', title: 'Material', type: 'string' },
                { name: 'style', title: 'Style', type: 'string' },
              ],
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoMetaFields',
      description: 'Search engine optimization settings',
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'price',
      media: 'images.0',
    },
    prepare({ title, subtitle, media }) {
      return {
        title,
        subtitle: subtitle ? `$${subtitle}` : 'No price set',
        media,
      };
    },
  },
});
