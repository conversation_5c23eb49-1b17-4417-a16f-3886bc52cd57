import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';
import { createClient } from '@sanity/client';

// POST /api/gallery/cleanup - Remove duplicate gallery items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has admin or super_admin role
    if (!session?.user || (!isAdmin(session) && !isSuperAdmin(session))) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be an admin to clean up gallery items' },
        { status: 401 }
      );
    }
    
    // Get the Sanity client
    let client;
    try {
      client = getWriteClient();
      console.log('Successfully created Sanity write client');
    } catch (error) {
      console.error('Error creating write client:', error);
      
      // Fallback to direct client creation
      const token = process.env.SANITY_API_TOKEN;
      if (!token) {
        return NextResponse.json(
          { success: false, message: 'Server configuration error: Missing Sanity API token' },
          { status: 500 }
        );
      }
      
      console.log('Using fallback Sanity client');
      client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2023-05-03',
        useCdn: false,
      });
    }
    
    // Get all gallery items
    const galleryItems = await client.fetch(`
      *[_type == "gallery"] | order(_createdAt asc) {
        _id,
        title,
        _createdAt,
        image,
        images
      }
    `);
    
    console.log(`Found ${galleryItems.length} gallery items`);
    
    // Group items by title to find duplicates
    const itemsByTitle: Record<string, any[]> = {};
    galleryItems.forEach((item: any) => {
      const title = item.title || 'Untitled';
      if (!itemsByTitle[title]) {
        itemsByTitle[title] = [];
      }
      itemsByTitle[title].push(item);
    });
    
    // Find titles with duplicates
    const duplicateTitles = Object.keys(itemsByTitle).filter(
      title => itemsByTitle[title].length > 1
    );
    
    console.log(`Found ${duplicateTitles.length} titles with duplicates`);
    
    // Process duplicates
    const deletedItems = [];
    const updatedItems = [];
    
    for (const title of duplicateTitles) {
      const items = itemsByTitle[title];
      
      // Sort by creation date (oldest first)
      items.sort((a: any, b: any) => {
        return new Date(a._createdAt).getTime() - new Date(b._createdAt).getTime();
      });
      
      // Keep the oldest item and update it if needed
      const itemToKeep = items[0];
      const itemsToDelete = items.slice(1);
      
      // Check if the item to keep has the images array
      let needsUpdate = false;
      if (!itemToKeep.images || !Array.isArray(itemToKeep.images) || itemToKeep.images.length === 0) {
        needsUpdate = true;
        
        // Create images array from the image field if it exists
        if (itemToKeep.image) {
          await client.patch(itemToKeep._id)
            .set({
              images: [
                {
                  _type: 'object',
                  image: itemToKeep.image,
                  alt: itemToKeep.title || 'Image',
                  caption: ''
                }
              ]
            })
            .commit();
          
          updatedItems.push(itemToKeep._id);
        }
      }
      
      // Delete duplicate items
      for (const itemToDelete of itemsToDelete) {
        try {
          await client.delete(itemToDelete._id);
          deletedItems.push(itemToDelete._id);
        } catch (deleteError) {
          console.error(`Error deleting item ${itemToDelete._id}:`, deleteError);
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Cleanup completed: ${deletedItems.length} duplicates removed, ${updatedItems.length} items updated`,
      deletedCount: deletedItems.length,
      updatedCount: updatedItems.length,
      deletedItems,
      updatedItems
    });
  } catch (error) {
    console.error('Error cleaning up gallery items:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to clean up gallery items', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
