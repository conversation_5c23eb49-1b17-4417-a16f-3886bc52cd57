'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useSanityUser } from '@/lib/hooks/useSanityUser';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { User, Save, Loader2 } from 'lucide-react';

export default function DirectClient() {
  const { data: session } = useSession();
  const { user: sanityUser, refreshUser } = useSanityUser();
  const [isSaving, setIsSaving] = useState(false);
  const [name, setName] = useState('');

  // Update name when Sanity user or session changes
  useEffect(() => {
    // Prefer Sanity user data if available
    if (sanityUser?.name) {
      setName(sanityUser.name);
    } else if (session?.user?.name) {
      setName(session.user.name);
    }
  }, [sanityUser, session]);

  // Handle profile update with a direct approach
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Validate form
      if (!name) {
        toast.error('Name is required');
        setIsSaving(false);
        return;
      }

      // Log session data for debugging
      console.log('Session data:', {
        id: session?.user?.id,
        email: session?.user?.email,
        name: session?.user?.name
      });

      console.log('Updating profile with name:', name);

      // Update profile using direct-update API
      const response = await fetch('/api/users/direct-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name }),
      });

      const data = await response.json();

      if (data.success) {
        console.log('Profile updated successfully:', data);

        // Force a hard reload of the page
        toast.success('Profile updated successfully! Refreshing...', {
          duration: 2000
        });

        // Wait a moment before reloading
        setTimeout(() => {
          // Force a complete page reload from the server
          window.location.href = window.location.href;
        }, 1500);

        // Set loading to false
        setIsSaving(false);
      } else {
        console.error('Failed to update profile:', data);
        toast.error(data.message || 'Failed to update profile');
        setIsSaving(false);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <User className="mr-2 h-6 w-6 text-royalBlue" />
          My Profile
        </h1>
        <p className="text-muted-foreground">
          Update your profile information
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>
            Update your account profile information
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleProfileUpdate}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Your full name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={session?.user?.email || ''}
                disabled
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">
                Email address cannot be changed. Contact a super admin if you need to update your email.
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
