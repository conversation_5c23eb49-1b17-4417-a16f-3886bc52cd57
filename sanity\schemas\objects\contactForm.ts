import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'contactForm',
  title: 'Contact Form',
  type: 'object',
  fields: [
    defineField({
      name: 'heading',
      title: 'Heading',
      type: 'string',
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 2,
    }),
    defineField({
      name: 'formFields',
      title: 'Form Fields',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Field Name',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'label',
              title: 'Field Label',
              type: 'string',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'type',
              title: 'Field Type',
              type: 'string',
              options: {
                list: [
                  { title: 'Text', value: 'text' },
                  { title: 'Email', value: 'email' },
                  { title: 'Phone', value: 'tel' },
                  { title: 'Textarea', value: 'textarea' },
                  { title: 'Select', value: 'select' },
                  { title: 'Checkbox', value: 'checkbox' },
                  { title: 'Radio', value: 'radio' },
                ],
              },
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'options',
              title: 'Options',
              type: 'array',
              of: [{ type: 'string' }],
              hidden: ({ parent }) => !['select', 'radio'].includes(parent?.type),
            },
            {
              name: 'required',
              title: 'Required',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'placeholder',
              title: 'Placeholder',
              type: 'string',
              hidden: ({ parent }) => parent?.type === 'checkbox',
            },
          ],
          preview: {
            select: {
              title: 'label',
              subtitle: 'type',
            },
          },
        },
      ],
    }),
    defineField({
      name: 'animation',
      title: 'Animation Settings',
      type: 'object',
      fields: [
        { name: 'duration', type: 'number', title: 'Duration', initialValue: 0.6 },
        { name: 'delay', type: 'number', title: 'Delay', initialValue: 0 },
        { name: 'stagger', type: 'number', title: 'Stagger Children', initialValue: 0 },
        { name: 'type', type: 'string', title: 'Type', initialValue: 'spring' }
      ]
    }),
    defineField({
      name: 'submitButtonText',
      title: 'Submit Button Text',
      type: 'string',
      initialValue: 'Submit',
    }),
    defineField({
      name: 'successMessage',
      title: 'Success Message',
      type: 'text',
      rows: 2,
      initialValue: 'Thank you for your message. We will get back to you soon.',
    }),
    defineField({
      name: 'backgroundStyle',
      title: 'Background Style',
      type: 'string',
      options: {
        list: [
          { title: 'None', value: 'none' },
          { title: 'Light', value: 'light' },
          { title: 'Dark', value: 'dark' },
          { title: 'Royal Blue', value: 'royalBlue' },
          { title: 'Royal Gold', value: 'royalGold' },
          { title: 'Ivory', value: 'ivory' },
        ],
      },
      initialValue: 'none',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      fields: 'formFields',
    },
    prepare({ title, fields }) {
      return {
        title: `Form: ${title || 'Untitled'}`,
        subtitle: `${fields?.length || 0} field${fields?.length === 1 ? '' : 's'}`,
      };
    },
  },
});
