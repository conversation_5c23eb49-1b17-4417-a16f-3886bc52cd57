'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  Globe,
  Facebook,
  Twitter,
  Info,
  Check,
  X
} from 'lucide-react';
import Image from 'next/image';
import { urlFor } from '@/lib/sanity.client';

interface SeoPreviewProps {
  title: string;
  description: string;
  slug: string;
  image?: any; // Sanity image reference
  baseUrl?: string;
  contentType?: string;
  seoData: {
    metaTitle?: string;
    metaDescription?: string;
    seoKeywords?: string[];
    nofollowAttributes?: boolean;
    openGraph?: {
      title?: string;
      description?: string;
      image?: any;
    };
    twitter?: {
      title?: string;
      description?: string;
      cardType?: string;
    };
    instagram?: {
      caption?: string;
      hashtags?: string[];
    };
  };
  onSeoChange: (seoData: any) => void;
}

export default function SeoPreview({
  title,
  description,
  slug,
  image,
  baseUrl = 'https://kingdomadukrom.com',
  contentType = 'article',
  seoData,
  onSeoChange
}: SeoPreviewProps) {
  const [activeTab, setActiveTab] = useState('search');

  // Calculate character counts
  const metaTitleLength = (seoData.metaTitle || title || '').length;
  const metaDescriptionLength = (seoData.metaDescription || description || '').length;

  // Determine if title and description are optimal length
  const isTitleOptimal = metaTitleLength >= 30 && metaTitleLength <= 60;
  const isDescriptionOptimal = metaDescriptionLength >= 70 && metaDescriptionLength <= 160;

  // Get the URL for the page
  const pageUrl = `${baseUrl}/${slug}`;

  // Get the image URL
  let imageUrl;
  try {
    if (seoData.openGraph?.image) {
      if (typeof seoData.openGraph.image === 'string') {
        imageUrl = seoData.openGraph.image;
      } else {
        try {
          const imageBuilder = urlFor(seoData.openGraph.image);
          if (imageBuilder && typeof imageBuilder.width === 'function') {
            imageUrl = imageBuilder.width(1200).height(630).url();
          } else {
            console.warn('Invalid Sanity image reference in openGraph.image');
            imageUrl = '/images/placeholder-og.jpg';
          }
        } catch (err) {
          console.error('Error processing openGraph image:', err);
          imageUrl = '/images/placeholder-og.jpg';
        }
      }
    } else if (image) {
      if (typeof image === 'string') {
        imageUrl = image;
      } else {
        try {
          const imageBuilder = urlFor(image);
          if (imageBuilder && typeof imageBuilder.width === 'function') {
            imageUrl = imageBuilder.width(1200).height(630).url();
          } else {
            console.warn('Invalid Sanity image reference in image prop');
            imageUrl = '/images/placeholder-og.jpg';
          }
        } catch (err) {
          console.error('Error processing image prop:', err);
          imageUrl = '/images/placeholder-og.jpg';
        }
      }
    } else {
      // Use a placeholder image instead of a domain that might not be configured
      imageUrl = '/images/placeholder-og.jpg';
    }
  } catch (error) {
    console.error('Error generating image URL:', error);
    imageUrl = '/images/placeholder-og.jpg';
  }

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    const updatedSeoData = { ...seoData };

    // Handle nested fields
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      updatedSeoData[parent] = {
        ...updatedSeoData[parent],
        [child]: value
      };
    } else {
      updatedSeoData[field] = value;
    }

    onSeoChange(updatedSeoData);
  };

  return (
    <Card className="w-full h-full border-primary/10">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center text-lg border-b pb-2">
          <Globe className="mr-2 h-5 w-5" />
          SEO Preview
        </CardTitle>
        <CardDescription className="text-sm pt-1">
          Preview how your content will appear in search results and social media
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <Tabs defaultValue="search" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="search">Search Results</TabsTrigger>
            <TabsTrigger value="facebook">Facebook</TabsTrigger>
            <TabsTrigger value="twitter">X (Twitter)</TabsTrigger>
            <TabsTrigger value="instagram">Instagram</TabsTrigger>
          </TabsList>

          {/* Search Results Preview */}
          <TabsContent value="search" className="space-y-4">
            <div className="mt-4 border rounded-md p-4 bg-white">
              <div className="text-blue-600 text-xl font-medium truncate">
                {seoData.metaTitle || title || 'Page Title'}
              </div>
              <div className="text-green-700 text-sm truncate">
                {pageUrl}
              </div>
              <div className="text-gray-600 text-sm mt-1 line-clamp-2">
                {seoData.metaDescription || description || 'Add a description to see how this page might appear in search results.'}
              </div>
            </div>

            <div className="space-y-4 mt-6">
              <div>
                <div className="flex justify-between">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <span className={`text-xs ${isTitleOptimal ? 'text-green-600' : 'text-amber-600'}`}>
                    {metaTitleLength}/60
                  </span>
                </div>
                <Input
                  id="metaTitle"
                  value={seoData.metaTitle || ''}
                  onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                  placeholder={title}
                  className="mt-1"
                />
                {!isTitleOptimal && (
                  <p className="text-xs text-amber-600 mt-1 flex items-center">
                    <Info className="h-3 w-3 mr-1" />
                    Optimal length is between 30-60 characters
                  </p>
                )}
              </div>

              <div>
                <div className="flex justify-between">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <span className={`text-xs ${isDescriptionOptimal ? 'text-green-600' : 'text-amber-600'}`}>
                    {metaDescriptionLength}/160
                  </span>
                </div>
                <Textarea
                  id="metaDescription"
                  value={seoData.metaDescription || ''}
                  onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                  placeholder={description}
                  className="mt-1"
                  rows={3}
                />
                {!isDescriptionOptimal && (
                  <p className="text-xs text-amber-600 mt-1 flex items-center">
                    <Info className="h-3 w-3 mr-1" />
                    Optimal length is between 70-160 characters
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="nofollowAttributes"
                  checked={seoData.nofollowAttributes || false}
                  onCheckedChange={(checked) => handleInputChange('nofollowAttributes', checked)}
                />
                <Label htmlFor="nofollowAttributes">No-index (hide from search engines)</Label>
              </div>
            </div>
          </TabsContent>

          {/* Facebook Preview */}
          <TabsContent value="facebook" className="space-y-4">
            <div className="mt-4 border rounded-md overflow-hidden bg-white">
              <div className="relative h-[200px] w-full bg-gray-100">
                {imageUrl ? (
                  <div className="relative h-full w-full">
                    <Image
                      src={imageUrl}
                      alt="Open Graph preview"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full w-full bg-gray-200">
                    <span className="text-gray-400">No image available</span>
                  </div>
                )}
              </div>
              <div className="p-3">
                <div className="text-gray-500 text-xs uppercase">kingdomadukrom.com</div>
                <div className="text-gray-900 font-bold">
                  {seoData.openGraph?.title || seoData.metaTitle || title || 'Page Title'}
                </div>
                <div className="text-gray-600 text-sm mt-1 line-clamp-3">
                  {seoData.openGraph?.description || seoData.metaDescription || description || 'Add a description to see how this page might appear when shared on Facebook.'}
                </div>
              </div>
            </div>

            <div className="space-y-4 mt-6">
              <div>
                <Label htmlFor="ogTitle">Facebook Title</Label>
                <Input
                  id="ogTitle"
                  value={seoData.openGraph?.title || ''}
                  onChange={(e) => handleInputChange('openGraph.title', e.target.value)}
                  placeholder={seoData.metaTitle || title}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="ogDescription">Facebook Description</Label>
                <Textarea
                  id="ogDescription"
                  value={seoData.openGraph?.description || ''}
                  onChange={(e) => handleInputChange('openGraph.description', e.target.value)}
                  placeholder={seoData.metaDescription || description}
                  className="mt-1"
                  rows={3}
                />
              </div>
            </div>
          </TabsContent>

          {/* X (formerly Twitter) Preview */}
          <TabsContent value="twitter" className="space-y-4">
            <div className="mt-4 border rounded-md overflow-hidden bg-white">
              <div className="relative h-[200px] w-full bg-gray-100">
                {imageUrl ? (
                  <div className="relative h-full w-full">
                    <Image
                      src={imageUrl}
                      alt="X (formerly Twitter) card preview"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full w-full bg-gray-200">
                    <span className="text-gray-400">No image available</span>
                  </div>
                )}
              </div>
              <div className="p-3">
                <div className="text-gray-900 font-bold">
                  {seoData.twitter?.title || seoData.openGraph?.title || seoData.metaTitle || title || 'Page Title'}
                </div>
                <div className="text-gray-600 text-sm mt-1 line-clamp-3">
                  {seoData.twitter?.description || seoData.openGraph?.description || seoData.metaDescription || description || 'Add a description to see how this page might appear when shared on X (formerly Twitter).'}
                </div>
                <div className="text-gray-500 text-xs mt-2">kingdomadukrom.com</div>
              </div>
            </div>

            <div className="space-y-4 mt-6">
              <div>
                <Label htmlFor="twitterTitle">X Title</Label>
                <Input
                  id="twitterTitle"
                  value={seoData.twitter?.title || ''}
                  onChange={(e) => handleInputChange('twitter.title', e.target.value)}
                  placeholder={seoData.openGraph?.title || seoData.metaTitle || title}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="twitterDescription">X Description</Label>
                <Textarea
                  id="twitterDescription"
                  value={seoData.twitter?.description || ''}
                  onChange={(e) => handleInputChange('twitter.description', e.target.value)}
                  placeholder={seoData.openGraph?.description || seoData.metaDescription || description}
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="twitterCardType">X Card Type</Label>
                <select
                  id="twitterCardType"
                  value={seoData.twitter?.cardType || 'summary_large_image'}
                  onChange={(e) => handleInputChange('twitter.cardType', e.target.value)}
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                >
                  <option value="summary">Summary</option>
                  <option value="summary_large_image">Summary with Large Image</option>
                </select>
              </div>
            </div>
          </TabsContent>

          {/* Instagram Preview */}
          <TabsContent value="instagram" className="space-y-4">
            <div className="mt-4 border rounded-md overflow-hidden bg-white">
              <div className="relative h-[300px] w-full bg-gray-100">
                {imageUrl ? (
                  <div className="relative h-full w-full">
                    <Image
                      src={imageUrl}
                      alt="Instagram preview"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full w-full bg-gray-200">
                    <span className="text-gray-400">No image available</span>
                  </div>
                )}
              </div>
              <div className="p-3">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-200 mr-2"></div>
                  <div className="text-sm font-semibold">kingdomadukrom</div>
                </div>
                <div className="text-sm mt-2 line-clamp-2">
                  {seoData.openGraph?.title || seoData.metaTitle || title || 'Page Title'}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="instagramTitle">Instagram Caption</Label>
                <Textarea
                  id="instagramCaption"
                  value={seoData.instagram?.caption || ''}
                  onChange={(e) => handleInputChange('instagram.caption', e.target.value)}
                  placeholder={`Check out ${seoData.openGraph?.title || seoData.metaTitle || title || 'our page'}!`}
                  className="mt-1"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  This is a preview of how your content might appear when shared on Instagram.
                  Instagram doesn't use meta tags like Facebook and X, but this helps you prepare captions.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-3 pb-3">
        <div className="flex items-center text-xs text-gray-500">
          <Info className="h-3 w-3 mr-1" />
          SEO settings help improve visibility in search results
        </div>
        <Button variant="outline" size="sm" className="h-8 text-xs" onClick={() => setActiveTab(activeTab === 'search' ? 'facebook' : activeTab === 'facebook' ? 'twitter' : 'search')}>
          Next Preview
        </Button>
      </CardFooter>
    </Card>
  );
}
