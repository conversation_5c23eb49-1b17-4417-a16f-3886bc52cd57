/**
 * Fast Development Server Script
 * 
 * This script starts a faster development server by:
 * 1. Using Next.js with optimized development settings
 * 2. Disabling unnecessary features during development
 * 3. Limiting the number of pages compiled initially
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const TEMP_CONFIG_PATH = path.join(ROOT_DIR, '.next-dev-config.js');
const ORIGINAL_CONFIG_PATH = path.join(ROOT_DIR, 'next.config.js');

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Create a temporary optimized Next.js config for development
 */
function createOptimizedDevConfig() {
  console.log(`${COLORS.yellow}Creating optimized development configuration...${COLORS.reset}`);
  
  // Backup the original config
  if (fs.existsSync(ORIGINAL_CONFIG_PATH)) {
    const originalConfig = fs.readFileSync(ORIGINAL_CONFIG_PATH, 'utf8');
    
    // Create a modified config for faster development
    const optimizedConfig = `
// This is a temporary development configuration
// The original config is preserved and will be restored when the server stops
const originalConfig = require('./next.config.js');

/** @type {import('next').NextConfig} */
const fastDevConfig = {
  ...originalConfig,
  
  // Disable static optimization during development
  optimizeFonts: false,
  
  // Disable image optimization during development
  images: {
    ...originalConfig.images,
    disableStaticImages: true,
  },
  
  // Experimental features for faster development
  experimental: {
    ...originalConfig.experimental,
    // Optimize compilation speed
    turbotrace: false,
    // Disable React server components during development
    serverActions: false,
  },
  
  // Webpack optimizations for development
  webpack: (config, options) => {
    // Call the original webpack config if it exists
    if (typeof originalConfig.webpack === 'function') {
      config = originalConfig.webpack(config, options);
    }
    
    // Only if we're in development mode
    if (options.dev) {
      // Disable source maps in development for faster builds
      config.devtool = 'eval';
      
      // Reduce the number of chunks
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }
    
    return config;
  },
};

module.exports = fastDevConfig;
`;
    
    // Write the optimized config to a temporary file
    fs.writeFileSync(TEMP_CONFIG_PATH, optimizedConfig);
    console.log(`${COLORS.green}✓ Created optimized development configuration${COLORS.reset}`);
  } else {
    console.error(`${COLORS.red}Error: next.config.js not found${COLORS.reset}`);
    process.exit(1);
  }
}

/**
 * Restore the original Next.js config
 */
function restoreOriginalConfig() {
  console.log(`${COLORS.yellow}Restoring original configuration...${COLORS.reset}`);
  
  if (fs.existsSync(TEMP_CONFIG_PATH)) {
    fs.unlinkSync(TEMP_CONFIG_PATH);
    console.log(`${COLORS.green}✓ Restored original configuration${COLORS.reset}`);
  }
}

/**
 * Start the development server with optimized settings
 */
function startDevServer() {
  console.log(`${COLORS.cyan}Starting fast development server...${COLORS.reset}`);
  
  // Set environment variables for faster development
  const env = {
    ...process.env,
    // Disable telemetry for faster startup
    NEXT_TELEMETRY_DISABLED: '1',
    // Use the temporary config
    NODE_ENV: 'development',
    // Limit the number of workers for faster startup
    NEXT_WORKER_THREADS: '2',
  };
  
  // Start the Next.js development server with the temporary config
  const nextDev = spawn('next', ['dev', '--port', '3000'], {
    env,
    stdio: 'inherit',
    shell: true,
  });
  
  // Handle process exit
  process.on('SIGINT', () => {
    console.log(`\n${COLORS.yellow}Shutting down development server...${COLORS.reset}`);
    nextDev.kill('SIGINT');
    restoreOriginalConfig();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log(`\n${COLORS.yellow}Shutting down development server...${COLORS.reset}`);
    nextDev.kill('SIGTERM');
    restoreOriginalConfig();
    process.exit(0);
  });
  
  // Handle server exit
  nextDev.on('close', (code) => {
    console.log(`\n${COLORS.yellow}Development server exited with code ${code}${COLORS.reset}`);
    restoreOriginalConfig();
    process.exit(code);
  });
}

/**
 * Main function
 */
function main() {
  console.log(`${COLORS.cyan}=== Fast Development Server ====${COLORS.reset}`);
  
  // Create optimized development configuration
  createOptimizedDevConfig();
  
  // Start the development server
  startDevServer();
}

// Run the main function
main();
