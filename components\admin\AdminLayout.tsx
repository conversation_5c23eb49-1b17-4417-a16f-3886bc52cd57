'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AlertTriangle, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isAuthorized, setIsAuthorized] = React.useState(false);

  React.useEffect(() => {
    if (status === 'loading') {
      return; // Still loading, don't do anything yet
    }

    // Check if the user is a super admin
    if (session?.user?.role === 'super_admin') {
      setIsAuthorized(true);
    }

    // Set loading to false
    setIsLoading(false);
  }, [session, status]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-royalBlue border-t-transparent mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 max-w-3xl mx-auto my-8">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
          <h3 className="font-medium">Super Admin Access Required</h3>
        </div>
        <p className="mt-2 text-sm">
          This section is restricted to super administrators only.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin')}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  // Render children if authorized
  return <>{children}</>;
}
