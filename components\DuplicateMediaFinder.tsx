'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Search, Trash2, X, AlertTriangle, FileImage, FileVideo } from 'lucide-react';
import Image from 'next/image';
import { urlFor, fileUrlFor, isVideoFile } from '@/lib/sanity.client';
import axios from 'axios';

interface DuplicateMediaFinderProps {
  onDuplicatesRemoved?: () => void;
}

export default function DuplicateMediaFinder({ onDuplicatesRemoved }: DuplicateMediaFinderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [duplicates, setDuplicates] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({});
  const [searchResults, setSearchResults] = useState<any[]>([]);

  // Find duplicates in the gallery
  const findDuplicates = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get('/api/gallery/duplicates');
      if (response.data.success) {
        setDuplicates(response.data.duplicates || []);
        setSearchResults(response.data.duplicates || []);
        
        // Initialize selected items
        const initialSelected: Record<string, boolean> = {};
        response.data.duplicates.forEach((group: any) => {
          // Keep the first item in each group, select others for deletion
          if (group.items && group.items.length > 0) {
            group.items.forEach((item: any, index: number) => {
              initialSelected[item._id] = index > 0; // Select all except the first one
            });
          }
        });
        setSelectedItems(initialSelected);
        
        toast.success(`Found ${response.data.duplicates.length} duplicate groups`);
      } else {
        toast.error(response.data.message || 'Failed to find duplicates');
      }
    } catch (error) {
      console.error('Error finding duplicates:', error);
      toast.error('Failed to find duplicates');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete selected duplicates
  const deleteSelectedDuplicates = async () => {
    const itemsToDelete = Object.entries(selectedItems)
      .filter(([_, isSelected]) => isSelected)
      .map(([id]) => id);

    if (itemsToDelete.length === 0) {
      toast.info('No items selected for deletion');
      return;
    }

    setIsDeleting(true);
    try {
      const response = await axios.post('/api/gallery/delete-batch', {
        ids: itemsToDelete
      });

      if (response.data.success) {
        toast.success(`Successfully deleted ${response.data.deleted} items`);
        
        // Remove deleted items from the list
        const updatedDuplicates = duplicates.map(group => ({
          ...group,
          items: group.items.filter((item: any) => !itemsToDelete.includes(item._id))
        })).filter(group => group.items.length > 1); // Only keep groups with at least 2 items
        
        setDuplicates(updatedDuplicates);
        setSearchResults(updatedDuplicates);
        
        // Reset selected items
        const newSelectedItems: Record<string, boolean> = {};
        updatedDuplicates.forEach((group: any) => {
          if (group.items && group.items.length > 0) {
            group.items.forEach((item: any, index: number) => {
              if (!itemsToDelete.includes(item._id)) {
                newSelectedItems[item._id] = index > 0;
              }
            });
          }
        });
        setSelectedItems(newSelectedItems);
        
        // Call the callback if provided
        if (onDuplicatesRemoved) {
          onDuplicatesRemoved();
        }
        
        // Close the dialog if no more duplicates
        if (updatedDuplicates.length === 0) {
          setIsOpen(false);
        }
      } else {
        toast.error(response.data.message || 'Failed to delete items');
      }
    } catch (error) {
      console.error('Error deleting items:', error);
      toast.error('Failed to delete items');
    } finally {
      setIsDeleting(false);
    }
  };

  // Toggle selection of an item
  const toggleItemSelection = (id: string) => {
    setSelectedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Select all items in a group except the first one
  const selectGroup = (groupId: string, select: boolean) => {
    const group = duplicates.find(g => g.id === groupId);
    if (!group) return;
    
    const newSelectedItems = { ...selectedItems };
    group.items.forEach((item: any, index: number) => {
      if (index > 0) { // Skip the first item
        newSelectedItems[item._id] = select;
      }
    });
    
    setSelectedItems(newSelectedItems);
  };

  // Count selected items
  const selectedCount = Object.values(selectedItems).filter(Boolean).length;

  return (
    <>
      <Button 
        variant="outline" 
        onClick={() => {
          setIsOpen(true);
          findDuplicates();
        }}
      >
        <Search className="mr-2 h-4 w-4" />
        Find Duplicate Media
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Duplicate Media Finder</DialogTitle>
            <DialogDescription>
              Find and manage duplicate media files in the gallery
            </DialogDescription>
          </DialogHeader>

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              <p className="mt-4 text-muted-foreground">Searching for duplicates...</p>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <p className="text-sm text-muted-foreground">
                  Found {searchResults.length} groups of duplicates. Select items to delete.
                </p>
                <p className="text-sm font-medium">
                  {selectedCount} items selected
                </p>
              </div>

              {searchResults.map((group) => (
                <Card key={group.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg">
                        {group.items[0]?.mediaType === 'video' ? 'Video' : 'Image'} Duplicates
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => selectGroup(group.id, true)}
                        >
                          Select All
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => selectGroup(group.id, false)}
                        >
                          Deselect All
                        </Button>
                      </div>
                    </div>
                    <CardDescription>
                      {group.items.length} duplicate items found
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {group.items.map((item: any, index: number) => (
                        <div 
                          key={item._id} 
                          className={`relative border rounded-md overflow-hidden ${
                            selectedItems[item._id] ? 'border-destructive' : index === 0 ? 'border-green-500' : 'border-gray-200'
                          }`}
                        >
                          <div className="absolute top-2 right-2 z-10">
                            <Checkbox 
                              checked={!!selectedItems[item._id]} 
                              onCheckedChange={() => toggleItemSelection(item._id)}
                              disabled={index === 0} // Can't select the first item
                              className={index === 0 ? 'opacity-50' : ''}
                            />
                          </div>
                          
                          {index === 0 && (
                            <div className="absolute top-2 left-2 z-10 bg-green-500 text-white text-xs px-2 py-1 rounded">
                              Keep
                            </div>
                          )}
                          
                          <div className="aspect-square relative">
                            {item.mediaType === 'video' || isVideoFile(item.video) ? (
                              <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                                <FileVideo className="h-8 w-8 text-white/70" />
                              </div>
                            ) : (
                              <Image
                                src={item.image ? urlFor(item.image).url() : '/images/placeholder.jpg'}
                                alt={item.title || 'Gallery item'}
                                fill
                                className="object-cover"
                              />
                            )}
                          </div>
                          
                          <div className="p-2 bg-background">
                            <p className="text-xs font-medium truncate">{item.title}</p>
                            <p className="text-xs text-muted-foreground">ID: {item._id.substring(0, 8)}...</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <Separator />
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium">No duplicates found</p>
              <p className="text-sm text-muted-foreground mt-1">
                All media files in the gallery appear to be unique
              </p>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={deleteSelectedDuplicates} 
              disabled={isDeleting || selectedCount === 0}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-background mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Selected ({selectedCount})
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
