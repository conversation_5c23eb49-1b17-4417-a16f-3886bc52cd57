@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global CSS Variables */
:root {
  /* Royal color palette */
  --royal-blue: #002366;
  --royal-gold: #D4AF37;
  --ivory: #F5F5DC;
  --forest-green: #014421;
  --charcoal: #111111;
  --bronze: #AD8A56;
  --white: #FFFFFF;
  --black: #000000;

  /* Spacing and sizing */
  --header-height: 80px;
  --footer-height: 300px;
  --container-width: 1200px;
  --radius: 0.5rem;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: "Montserrat", sans-serif;
  background-color: var(--ivory);
  color: var(--charcoal);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Reset styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: "<PERSON>aramond", "Georgia", serif;
  color: var(--royal-blue);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
}

a {
  color: var(--royal-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--royal-gold);
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.bg-royal-blue {
  background-color: var(--royal-blue);
  color: var(--white);
}

.bg-royal-gold {
  background-color: var(--royal-gold);
  color: var(--charcoal);
}

.bg-ivory {
  background-color: var(--ivory);
}

.text-royal-blue {
  color: var(--royal-blue);
}

.text-royal-gold {
  color: var(--royal-gold);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fadeIn {
  animation: fadeIn 1s ease forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft 1s ease forwards;
}

.animate-fadeInRight {
  animation: fadeInRight 1s ease forwards;
}

.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
