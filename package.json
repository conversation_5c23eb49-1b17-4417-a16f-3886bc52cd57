{"name": "kingdom2", "version": "0.1.1", "private": true, "engines": {"node": "20.x", "npm": "10.x"}, "scripts": {"dev": "cross-env NODE_ENV=development next dev -p 8000", "dev:fast": "node scripts/fast-dev.js -p 8000", "dev:safe": "node scripts/dev-with-fixes.js -p 8000", "dev:full": "node dev-full.js -p 8000", "dev:express": "node server.js -p 8000", "dev:simple": "node dev.js -p 8000", "dev:local": "node local-server.js", "dev:static": "node scripts/simple-server.js", "build": "next build", "start": "next start -p $PORT", "lint": "next lint", "fix:hydration": "node scripts/fix-hydration.js", "check:hydration": "node scripts/check-hydration.js", "clean": "node scripts/clean-rebuild.js --skip-build", "clean:full": "node scripts/clean-rebuild.js --full --skip-build", "clean:cache": "node scripts/clean-cache.js", "rebuild": "node scripts/clean-rebuild.js", "rebuild:full": "node scripts/clean-rebuild.js --full", "optimize": "node scripts/optimize-all.js", "optimize:codebase": "node scripts/optimize-codebase.js", "optimize:build": "node scripts/optimize-build.js", "optimize:assets": "node scripts/optimize-assets.js", "analyze": "node scripts/analyze-bundle.js", "heroku-postbuild": "NEXT_TELEMETRY_DISABLED=1 NEXT_MINIMAL_BUILD=true node heroku-build.js", "deploy": "node deploy-heroku.js", "push": "node push-to-github.js", "add-news": "node scripts/add-news-articles.js", "cleanup-news": "node scripts/delete-test-article.js", "import-news": "node scripts/import-news-to-sanity.js", "clean-for-heroku": "node clean-for-heroku.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@sanity/client": "^6.15.7", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.88.2", "@sanity/visual-editing": "^1.8.0", "@tanstack/react-query": "^5.75.7", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "form-data": "^4.0.2", "framer-motion": "^12.10.5", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.509.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-sanity": "^9.11.0", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.3", "react-resizable-panels": "^3.0.1", "recharts": "^2.15.3", "rimraf": "6.0.1", "sanity": "^3.88.2", "sanity-plugin-seo": "^1.3.0", "slugify": "^1.6.6", "sonner": "^2.0.3", "styled-components": "^6.1.18", "tailwind-merge": "^3.2.0", "typescript": "^5.0.4", "vaul": "^1.1.2", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "4.1.7", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "18.3.22", "@types/react-dom": "^18", "autoprefixer": "10.4.21", "cross-env": "7.0.3", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-config-next": "15.3.2", "postcss": "8.5.3", "tailwindcss": "4.1.7"}}