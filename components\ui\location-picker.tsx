'use client'

import * as React from 'react'
import { useState, useEffect } from 'react'
import { Check, ChevronsUpDown, MapPin, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AlertCircle } from 'lucide-react'

interface LocationPickerProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  error?: string
  required?: boolean
}

export function LocationPicker({
  value,
  onChange,
  onBlur,
  error,
  required = false,
}: LocationPickerProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<{
    id: string;
    name: string;
    fullName: string;
  }[]>([])

  // Debounce search term
  useEffect(() => {
    if (!searchTerm || searchTerm.length < 3) {
      setSuggestions([])
      return
    }

    const timer = setTimeout(() => {
      fetchLocationSuggestions(searchTerm)
    }, 500)

    return () => clearTimeout(timer)
  }, [searchTerm])

  const fetchLocationSuggestions = async (query: string) => {
    if (query.length < 3) return

    setIsLoading(true)
    try {
      // This is a mock implementation - in a real app, you would call an API
      // For example: const response = await fetch(`/api/locations?search=${query}`)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Mock data for common cities
      const mockCities = [
        { id: '1', name: 'Accra', fullName: 'Accra, Ghana' },
        { id: '2', name: 'Kumasi', fullName: 'Kumasi, Ghana' },
        { id: '3', name: 'Tamale', fullName: 'Tamale, Ghana' },
        { id: '4', name: 'Cape Coast', fullName: 'Cape Coast, Ghana' },
        { id: '5', name: 'Sekondi-Takoradi', fullName: 'Sekondi-Takoradi, Ghana' },
        { id: '6', name: 'Sunyani', fullName: 'Sunyani, Ghana' },
        { id: '7', name: 'Koforidua', fullName: 'Koforidua, Ghana' },
        { id: '8', name: 'Ho', fullName: 'Ho, Ghana' },
        { id: '9', name: 'Wa', fullName: 'Wa, Ghana' },
        { id: '10', name: 'Bolgatanga', fullName: 'Bolgatanga, Ghana' },
        { id: '11', name: 'Adukrom', fullName: 'Adukrom, Eastern Region, Ghana' },
        { id: '12', name: 'New York', fullName: 'New York, NY, USA' },
        { id: '13', name: 'Los Angeles', fullName: 'Los Angeles, CA, USA' },
        { id: '14', name: 'Chicago', fullName: 'Chicago, IL, USA' },
        { id: '15', name: 'London', fullName: 'London, UK' },
        { id: '16', name: 'Paris', fullName: 'Paris, France' },
        { id: '17', name: 'Tokyo', fullName: 'Tokyo, Japan' },
      ]
      
      const filteredCities = mockCities.filter(city => 
        city.name.toLowerCase().includes(query.toLowerCase()) ||
        city.fullName.toLowerCase().includes(query.toLowerCase())
      )
      
      setSuggestions(filteredCities)
    } catch (error) {
      console.error('Error fetching location suggestions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              !value && "text-muted-foreground",
              error && "border-red-500"
            )}
            onClick={() => setSearchTerm('')}
          >
            {value ? (
              <div className="flex items-center">
                <MapPin className="mr-2 h-4 w-4 shrink-0" />
                <span>{value}</span>
              </div>
            ) : (
              <span>Select location...</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Search for cities..." 
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandList>
              <CommandEmpty>
                {isLoading ? (
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Searching...
                  </div>
                ) : searchTerm.length < 3 ? (
                  "Type at least 3 characters to search"
                ) : (
                  "No locations found"
                )}
              </CommandEmpty>
              <CommandGroup>
                {suggestions.map((location) => (
                  <CommandItem
                    key={location.id}
                    value={location.fullName}
                    onSelect={() => {
                      onChange(location.fullName)
                      setOpen(false)
                      if (onBlur) onBlur()
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === location.fullName ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                    {location.fullName}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
            <div className="border-t p-2">
              <Input
                placeholder="Or type a custom location"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onBlur={onBlur}
                className="w-full"
              />
            </div>
          </Command>
        </PopoverContent>
      </Popover>
      {error && (
        <p className="text-xs text-red-500 flex items-center mt-1">
          <AlertCircle className="h-3 w-3 mr-1" />
          {error}
        </p>
      )}
    </div>
  )
}
