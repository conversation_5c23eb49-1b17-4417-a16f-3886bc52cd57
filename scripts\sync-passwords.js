// This script syncs passwords from local storage to Sanity
// Run with: node scripts/sync-passwords.js

const { createClient } = require('@sanity/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Path to the JSON file that stores user data
const usersFilePath = path.join(process.cwd(), 'data', 'users.json');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Get all local users
const getLocalUsers = () => {
  try {
    if (fs.existsSync(usersFilePath)) {
      const usersData = fs.readFileSync(usersFilePath, 'utf8');
      return JSON.parse(usersData);
    }
    return [];
  } catch (error) {
    console.error('Error reading local users file:', error);
    return [];
  }
};

// Fetch all Sanity users
const fetchSanityUsers = async () => {
  try {
    console.log('Fetching users from Sanity...');
    
    const users = await client.fetch(
      `*[_type == "adminUser"] {
        _id,
        username,
        name,
        email,
        role,
        isActive,
        isProtected
      }`
    );
    
    console.log(`Found ${users.length} users in Sanity`);
    return users;
  } catch (error) {
    console.error('Error fetching Sanity users:', error);
    return [];
  }
};

// Sync passwords from local to Sanity
const syncPasswords = async () => {
  try {
    const localUsers = getLocalUsers();
    const sanityUsers = await fetchSanityUsers();
    
    console.log(`Found ${localUsers.length} local users`);
    
    // Create a map of Sanity users by email for quick lookup
    const sanityUsersByEmail = {};
    sanityUsers.forEach(user => {
      if (user.email) {
        sanityUsersByEmail[user.email] = user;
      }
    });
    
    // Track changes
    let updated = 0;
    let skipped = 0;
    
    // Process each local user
    for (const localUser of localUsers) {
      if (!localUser.email || !localUser.password) {
        console.log(`Skipping local user with missing email or password: ${localUser.id}`);
        skipped++;
        continue;
      }
      
      const sanityUser = sanityUsersByEmail[localUser.email];
      
      if (sanityUser) {
        console.log(`Syncing password for ${localUser.email} (${sanityUser._id})`);
        
        try {
          // Update password in Sanity
          await client
            .patch(sanityUser._id)
            .set({
              hashedPassword: localUser.password,
              updatedAt: new Date().toISOString()
            })
            .commit();
            
          console.log(`✅ Password synced for ${localUser.email}`);
          updated++;
        } catch (error) {
          console.error(`Error updating password for ${localUser.email}:`, error);
          skipped++;
        }
      } else {
        console.log(`No matching Sanity user found for ${localUser.email}`);
        skipped++;
      }
    }
    
    return { updated, skipped };
  } catch (error) {
    console.error('Error syncing passwords:', error);
    throw error;
  }
};

// Main function
async function main() {
  console.log('Starting password sync...');
  
  const { updated, skipped } = await syncPasswords();
  
  console.log(`\nPassword sync complete: ${updated} updated, ${skipped} skipped`);
}

// Run the main function
main()
  .then(() => {
    console.log('Done!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
