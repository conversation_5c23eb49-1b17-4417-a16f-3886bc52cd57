// <PERSON>ript to optimize the application for Heroku deployment
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to execute shell commands and log output
function runCommand(command) {
  console.log(`Running: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return output;
  } catch (error) {
    console.error(`<PERSON>rror executing command: ${command}`);
    console.error(error);
    process.exit(1);
  }
}

// Main optimization process
console.log('Starting optimization for Heroku deployment...');

// 1. Remove unnecessary files and directories
console.log('Removing unnecessary files and directories...');

// Create a .slugignore file to exclude files from the slug
const slugignoreContent = `
# Exclude development files
.git
.github
.vscode
.next/cache
node_modules/.cache
*.log
*.md
*.txt
!robots.txt
!requirements.txt

# Exclude test files
__tests__
test
tests
*.test.js
*.spec.js

# Exclude documentation
docs
documentation
*.md

# Exclude source maps
*.map

# Exclude large media files that can be served from CDN
public/videos
public/images/large

# Exclude Sanity Studio files that aren't needed for production
studio-node-modules
sanity-studio-backup

# Exclude development scripts
scripts
dev-scripts
`;

fs.writeFileSync('.slugignore', slugignoreContent);
console.log('.slugignore file created successfully');

// 2. Update next.config.js to optimize the build
console.log('Updating Next.js configuration for production...');
const nextConfigPath = path.join(__dirname, 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');

  // Add optimization settings
  if (!nextConfig.includes('swcMinify: true')) {
    nextConfig = nextConfig.replace(
      'const nextConfig = {',
      'const nextConfig = {\n  swcMinify: true,'
    );
    fs.writeFileSync(nextConfigPath, nextConfig);
    console.log('Added swcMinify optimization to next.config.js');
  }
}

// 3. Create a custom .npmrc file to reduce npm package size
console.log('Creating optimized .npmrc file...');
const npmrcContent = `
# Optimize npm for production
production=true
fund=false
audit=false
progress=false
loglevel=error
save-exact=true
`;

fs.writeFileSync('.npmrc', npmrcContent);
console.log('.npmrc file created successfully');

// 4. Run the build with optimizations
console.log('Building the application with optimizations...');
runCommand('npm run build');

// 5. Clean up node_modules
console.log('Cleaning up node_modules...');
runCommand('npm prune --production');

console.log('Optimization process completed!');
console.log('The application should now be ready for Heroku deployment.');
console.log('Try pushing to Heroku again with: git push heroku store-functionality:main');
