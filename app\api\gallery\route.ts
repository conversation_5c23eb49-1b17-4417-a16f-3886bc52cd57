import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getGallery } from '@/lib/sanity';
import { isAdmin, isSuperAdmin } from '@/lib/auth-utils';

// GET /api/gallery - Get all gallery items
export async function GET(request: NextRequest) {
  try {
    // Check authentication for admin routes
    const session = await getServerSession(authOptions);
    
    // Get gallery items from Sanity
    const galleryItems = await getGallery();
    
    return NextResponse.json({
      success: true,
      items: galleryItems
    });
  } catch (error) {
    console.error('Error fetching gallery items:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch gallery items', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
