'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { getSiteSettings, urlFor } from '@/lib/sanity';
import CountdownTimer from '@/components/CountdownTimer';
import { Calendar, Clock, MapPin, Users, ArrowRight } from 'lucide-react';
import { useEventStore } from '@/lib/stores/eventStore';
import { useEventsQuery, useCountdownEventQuery } from '@/lib/hooks/useEvents';
import Header from '@/components/Header';



interface Event {
  _id: string;
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  image?: {
    asset: {
      _ref: string;
      _type: string;
    };
    alt?: string;
  };
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
}

// Hardcoded events to ensure something always displays
const DEFAULT_EVENTS: Event[] = [
  {
    _id: 'coronation-event',
    title: 'Royal Coronation Ceremony',
    slug: { current: 'royal-coronation-ceremony' },
    date: '2025-08-29T07:30:00.000Z',
    endDate: '2025-08-29T21:00:00.000Z',
    location: 'Royal Palace Grounds',
    description: 'The official coronation ceremony of King Allen Ellison, marking a new era for the Adukrom Kingdom.',
    isCountdownTarget: true,
    isHighlighted: true,
    showRsvp: true,
    eventType: 'ceremony',
    order: 1
  },
  {
    _id: 'royal-gala',
    title: 'Royal Gala Dinner',
    slug: { current: 'royal-gala-dinner' },
    date: '2025-08-30T19:00:00.000Z',
    location: 'Grand Ballroom, Accra',
    description: 'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests.',
    isCountdownTarget: false,
    isHighlighted: false,
    showRsvp: true,
    eventType: 'dinner',
    order: 2
  },
  {
    _id: 'economic-forum',
    title: 'Global Economic Forum',
    slug: { current: 'global-economic-forum' },
    date: '2025-08-31T10:00:00.000Z',
    location: 'Adukrom Convention Center',
    description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Adukrom Kingdom.',
    isCountdownTarget: false,
    isHighlighted: false,
    showRsvp: true,
    eventType: 'conference',
    order: 3
  }
];

export default function EventsPage() {
  const [events, setEvents] = useState<Event[]>(DEFAULT_EVENTS);
  const [isLoading, setIsLoading] = useState(true);
  const [highlightedEvent, setHighlightedEvent] = useState<Event | null>(DEFAULT_EVENTS[0]);

  // Use the events store and query hooks
  const { events: storeEvents, isLoading: storeLoading, error: storeError, countdownEvent: storeCountdownEvent } = useEventStore();
  const { data: queryEvents, isLoading: queryLoading, error: queryError } = useEventsQuery();
  const { data: countdownEvent } = useCountdownEventQuery();

  // Type assertion to fix TypeScript errors
  const typedQueryEvents = queryEvents as Event[] | undefined;

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Get site settings to update the coronation date
        const settings = await getSiteSettings();
        console.log('Fetched site settings:', settings);

        // If we have events from the query, use those
        if (typedQueryEvents && typedQueryEvents.length > 0) {
          console.log('Using events from query');
          setEvents(typedQueryEvents);

          // Set the highlighted event (either the countdown target or the first event)
          // Make sure countdownEvent is a valid Event object before using it
          const validCountdownEvent = countdownEvent && typeof countdownEvent === 'object' && countdownEvent !== null && '_id' in countdownEvent ? countdownEvent as Event : null;
          const highlightedEvent = validCountdownEvent || typedQueryEvents.find(event => event.isCountdownTarget) || typedQueryEvents[0];
          setHighlightedEvent(highlightedEvent);
        }
        // Otherwise, if we have events from the store, use those
        else if (storeEvents && storeEvents.length > 0) {
          console.log('Using events from store');
          setEvents(storeEvents);

          // Set the highlighted event (either the countdown target or the first event)
          // Make sure storeCountdownEvent is a valid Event object before using it
          const validStoreCountdownEvent = storeCountdownEvent && typeof storeCountdownEvent === 'object' && storeCountdownEvent !== null && '_id' in storeCountdownEvent ? storeCountdownEvent as Event : null;
          const highlightedEvent = validStoreCountdownEvent || storeEvents.find(event => event.isCountdownTarget) || storeEvents[0];
          setHighlightedEvent(highlightedEvent);
        }
        // If no events from store or query but we have coronation date in settings
        else if (settings && settings.events && settings.events.coronationDate) {
          console.log('No store/query events, using default events with settings date');

          // Update the coronation event with the date from settings
          const updatedEvents = [...DEFAULT_EVENTS];
          updatedEvents[0] = {
            ...updatedEvents[0],
            date: settings.events.coronationDate,
            location: settings.events.coronationLocation || updatedEvents[0].location
          };

          setEvents(updatedEvents);
          setHighlightedEvent(updatedEvents[0]);
        }
        // Fallback to default events
        else {
          console.log('Using default events');
          setEvents(DEFAULT_EVENTS);
          setHighlightedEvent(DEFAULT_EVENTS[0]);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
        // Keep using the default events
        setEvents(DEFAULT_EVENTS);
        setHighlightedEvent(DEFAULT_EVENTS[0]);
      }
    };

    fetchSettings();
  }, [typedQueryEvents, storeEvents, countdownEvent, storeCountdownEvent]);

  // Set loading state based on query and store loading states
  useEffect(() => {
    setIsLoading(queryLoading || storeLoading);
  }, [queryLoading, storeLoading]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Parse the date string - JavaScript automatically converts UTC to local time
      const date = new Date(dateString);

      // Format the date in the user's local time zone
      return format(date, 'MMMM d, yyyy h:mm a');
    } catch (error) {
      console.error('Error formatting date:', error, dateString);
      return 'Invalid date';
    }
  };

  // Get image URL from event data (handles both direct imageUrl and Sanity image references)
  const getImageUrl = (event: Event): string | null => {
    // First try to use the direct imageUrl if available
    if (event.imageUrl) {
      return event.imageUrl;
    }
    // Then try to use the Sanity image reference
    else if (event.image) {
      try {
        // Use the urlFor helper from Sanity client
        const imageUrl = urlFor(event.image).url();
        if (imageUrl) {
          return imageUrl;
        }
      } catch (error) {
        console.error('Error getting image URL:', error);
      }
    }
    // If no image is found
    return null;
  };

  // Get image alt text
  const getImageAlt = (event: Event): string => {
    return event.imageAlt || event.image?.alt || event.title;
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-ivory">
      <Header isGalleryPage={true} />
      {/* Hero Section */}
      <section className="royal-gradient py-20 relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              className="text-4xl md:text-5xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Royal Events
            </motion.h1>
            <motion.p
              className="text-xl text-royalGold mb-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Join us for these special occasions in the Adukrom Kingdom
            </motion.p>

            {highlightedEvent && (
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-xl p-8 shadow-lg"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <h2 className="text-2xl font-bold text-white mb-4">Featured Event</h2>
                <div className="flex flex-col md:flex-row gap-6 items-center">
                  {getImageUrl(highlightedEvent) ? (
                    <div className="relative w-full md:w-1/3 aspect-video rounded-lg overflow-hidden">
                      <Image
                        src={getImageUrl(highlightedEvent)!}
                        alt={getImageAlt(highlightedEvent)}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="relative w-full md:w-1/3 aspect-video rounded-lg overflow-hidden bg-royalBlue/10 flex flex-col items-center justify-center">
                      {highlightedEvent.eventType === 'ceremony' ? (
                        <>
                          <div className="text-royalGold">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14" />
                            </svg>
                          </div>
                          <p className="mt-2 text-xs text-white/70">Royal Ceremony</p>
                        </>
                      ) : (
                        <Calendar className="h-12 w-12 text-white/30" />
                      )}
                    </div>
                  )}
                  <div className="text-left md:w-2/3">
                    <h3 className="text-xl font-bold text-royalGold">{highlightedEvent.title}</h3>
                    <div className="flex items-center mt-2 text-white/80">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{formatDate(highlightedEvent.date)}</span>
                    </div>
                    <div className="flex items-center mt-1 text-white/80">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{highlightedEvent.location}</span>
                    </div>
                    <p className="mt-4 text-white/90 line-clamp-3">{highlightedEvent.description}</p>

                    <div className="mt-6">
                      <CountdownTimer
                        key={`event-countdown-${highlightedEvent._id}`}
                        size="sm"
                        showTitle={true}
                        showLabels={true}
                        textColor="text-white/80"
                        numberColor="text-royalGold"
                        className="mb-4"
                      />

                      {highlightedEvent.showRsvp && (
                        <Link
                          href="/#rsvp"
                          className="inline-block bg-royalGold text-royalBlue font-bold py-2 px-6 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                        >
                          RSVP Now
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* Events List Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-royalBlue text-center mb-12 section-title">Upcoming Events</h2>

          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalBlue"></div>
            </div>
          ) : events.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {events.map((event) => (
                <motion.div
                  key={event._id}
                  className="bg-white rounded-lg shadow-lg overflow-hidden border border-royalGold/20 hover:shadow-xl transition-shadow"
                  variants={itemVariants}
                >
                  {getImageUrl(event) ? (
                    <div className="relative h-48 w-full">
                      <Image
                        src={getImageUrl(event)!}
                        alt={getImageAlt(event)}
                        fill
                        className="object-cover"
                      />
                      {event.isCountdownTarget && (
                        <div className="absolute top-2 right-2 bg-royalGold text-royalBlue text-xs font-bold px-2 py-1 rounded">
                          Countdown Event
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="h-48 w-full bg-royalBlue/10 flex flex-col items-center justify-center">
                      {event.eventType === 'ceremony' ? (
                        <>
                          <div className="text-royalGold">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14" />
                            </svg>
                          </div>
                          <p className="mt-2 text-sm text-royalBlue/70">Royal Ceremony</p>
                        </>
                      ) : (
                        <>
                          <Calendar className="h-12 w-12 text-royalBlue/30" />
                          <p className="mt-2 text-sm text-royalBlue/70">{event.eventType || 'Event'}</p>
                        </>
                      )}
                    </div>
                  )}

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-royalBlue mb-2">{event.title}</h3>

                    <div className="flex items-center mt-2 text-charcoal/80">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{formatDate(event.date)}</span>
                    </div>

                    {event.endDate && (
                      <div className="flex items-center mt-1 text-charcoal/80">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>Until {formatDate(event.endDate)}</span>
                      </div>
                    )}

                    <div className="flex items-center mt-1 text-charcoal/80">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{event.location}</span>
                    </div>

                    <p className="mt-4 text-charcoal line-clamp-3">{event.description}</p>

                    <div className="mt-6 flex justify-between items-center">
                      <div className="flex items-center text-sm text-charcoal/70">
                        <Users className="h-4 w-4 mr-1" />
                        <span>Public Event</span>
                      </div>

                      {event.showRsvp && (
                        <Link
                          href="/#rsvp"
                          className="flex items-center text-royalBlue font-medium hover:text-royalGold transition-colors"
                        >
                          RSVP <ArrowRight className="ml-1 h-4 w-4" />
                        </Link>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="h-16 w-16 mx-auto text-royalBlue/30 mb-4" />
              <h3 className="text-xl font-bold text-royalBlue mb-2">No Events Scheduled</h3>
              <p className="text-charcoal">Check back soon for upcoming royal events.</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
