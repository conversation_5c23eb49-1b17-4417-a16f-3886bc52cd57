# Deploying to Heroku

This guide provides step-by-step instructions for deploying your Next.js application with Sanity integration to Heroku.

## Prerequisites

1. [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) installed
2. Git repository initialized
3. Sanity project set up and configured

## Step 1: Prepare Your Application

### Update package.json

Ensure your `package.json` has the correct scripts:

```json
"scripts": {
  "dev": "next dev",
  "build": "next build",
  "start": "next start -p $PORT",
  "heroku-postbuild": "npm run build"
}
```

### Create a Procfile

Create a file named `Procfile` (no extension) in the root of your project:

```
web: npm start
```

### Add .npmrc file

Create a `.npmrc` file in the root of your project to optimize memory usage:

```
node-options=--max-old-space-size=2048
```

## Step 2: Set Up Heroku

### Create a Heroku App

```bash
heroku login
heroku create your-app-name
```

### Set Environment Variables

```bash
heroku config:set NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
heroku config:set NEXT_PUBLIC_SANITY_DATASET=production
heroku config:set SANITY_API_TOKEN=your_write_token
heroku config:set NEXTAUTH_URL=https://your-app-name.herokuapp.com
heroku config:set NEXTAUTH_SECRET=your_nextauth_secret
```

## Step 3: Configure Sanity

### Add CORS Origin

1. Go to [manage.sanity.io](https://manage.sanity.io/)
2. Select your project
3. Go to Settings > API > CORS Origins
4. Add your Heroku domain (e.g., `https://your-app-name.herokuapp.com`)
5. Check "Allow credentials"

### Seed Your Database

Run the seeding script locally before deploying:

```bash
node scripts/seed-store.js
```

## Step 4: Deploy to Heroku

```bash
git add .
git commit -m "Prepare for Heroku deployment"
git push heroku main
```

## Step 5: Open Your App

```bash
heroku open
```

## Troubleshooting

### Application Error

If you see an "Application Error" page:

```bash
heroku logs --tail
```

### Memory Issues

If you encounter memory issues during build:

```bash
heroku config:set NODE_OPTIONS="--max_old_space_size=2048"
```

### Database Connection Issues

If your app can't connect to Sanity:

1. Verify environment variables are set correctly
2. Check CORS settings in Sanity
3. Ensure your Sanity token has the necessary permissions

### Image Processing Issues

If you encounter issues with image processing:

```bash
heroku config:set NEXT_SHARP_PATH=/tmp
```

## Maintenance

### Updating Your App

```bash
git add .
git commit -m "Update application"
git push heroku main
```

### Checking Logs

```bash
heroku logs --tail
```

### Restarting the App

```bash
heroku restart
```

## Additional Resources

- [Heroku DevCenter](https://devcenter.heroku.com/)
- [Next.js on Heroku](https://elements.heroku.com/buildpacks/mars/heroku-nextjs)
- [Sanity Documentation](https://www.sanity.io/docs)
