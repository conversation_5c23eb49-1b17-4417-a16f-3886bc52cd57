'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Save, ArrowLeft, X, Upload, ImageIcon, Tag, Loader2, AlertCircle, RefreshCw, PlusCircle, FileVideo } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import VideoPlayer from '@/components/VideoPlayer';
import AdminVideoPlayer from '@/components/AdminVideoPlayer';

interface UploadedMedia {
  file: File;
  preview: string;
  title: string;
  description: string;
  category: string;
  mediaType: 'image' | 'video';
}

interface Category {
  _id: string;
  title: string;
  slug?: {
    current: string;
  };
  description?: string;
  color?: string;
  icon?: string;
  order?: number;
}

export default function GalleryUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedMedia, setUploadedMedia] = useState<UploadedMedia[]>([]);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState<number | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadErrors, setUploadErrors] = useState<{index: number, message: string, fileName: string}[]>([]);
  const [activeTab, setActiveTab] = useState<'image' | 'video'>('image');

  // New category state
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [newCategory, setNewCategory] = useState({
    title: '',
    description: '',
    color: '#002366', // Default royal blue
    icon: '',
    order: 0
  });
  const [isCreatingCategory, setIsCreatingCategory] = useState(false);

  // Fetch categories from API
  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();

      if (data.success && data.categories) {
        console.log('Categories loaded:', data.categories.length);
        setCategories(data.categories);

        // If no categories are available, create a default one
        if (data.categories.length === 0) {
          console.log('No categories found, creating a default category');
          try {
            const createResponse = await fetch('/api/categories', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                title: 'General',
                description: 'General gallery images',
                color: '#002366',
                order: 0
              }),
            });

            const createData = await createResponse.json();

            if (createData.success && createData.category) {
              console.log('Default category created:', createData.category);
              setCategories([createData.category]);
            }
          } catch (createError) {
            console.error('Failed to create default category:', createError);
          }
        }
      } else {
        console.error('Failed to fetch categories:', data.error || 'Unknown error');
        toast.error('Failed to load categories');
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to load categories');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch of categories
  useEffect(() => {
    fetchCategories();
  }, []);

  // File input references
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>, mediaType: 'image' | 'video') => {
    if (!event.target.files || event.target.files.length === 0) return;

    const files = Array.from(event.target.files);
    const newMedia = files.map(file => ({
      file,
      preview: mediaType === 'image'
        ? URL.createObjectURL(file)
        : URL.createObjectURL(file), // For videos, we'll show a thumbnail or first frame
      title: '',
      description: '',
      category: '',
      mediaType,
    }));

    setUploadedMedia(prev => [...prev, ...newMedia]);

    if (selectedMediaIndex === null && newMedia.length > 0) {
      setSelectedMediaIndex(uploadedMedia.length);
    }

    // Reset the file input
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadedMedia.length, selectedMediaIndex]);

  // Remove a media item
  const removeMedia = (index: number) => {
    setUploadedMedia(prev => {
      const newMedia = [...prev];
      URL.revokeObjectURL(newMedia[index].preview);
      newMedia.splice(index, 1);
      return newMedia;
    });

    if (selectedMediaIndex === index) {
      setSelectedMediaIndex(uploadedMedia.length > 1 ? 0 : null);
    } else if (selectedMediaIndex !== null && selectedMediaIndex > index) {
      setSelectedMediaIndex(selectedMediaIndex - 1);
    }
  };

  // Update media metadata
  const updateMediaMetadata = (index: number, field: keyof UploadedMedia, value: string) => {
    setUploadedMedia(prev => {
      const newMedia = [...prev];
      newMedia[index] = { ...newMedia[index], [field]: value };
      return newMedia;
    });
  };

  // Handle creating a new category
  const handleCreateCategory = async () => {
    if (!newCategory.title) {
      toast.error('Category title is required');
      return;
    }

    setIsCreatingCategory(true);

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCategory),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Failed to create category');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to create category');
      }

      // Add the new category to the list
      setCategories(prev => [...prev, data.category]);

      // If a media item is selected, update its category to the new one
      if (selectedMediaIndex !== null) {
        updateMediaMetadata(selectedMediaIndex, 'category', data.category._id);
      }

      toast.success('Category created successfully');

      // Reset the new category form
      setNewCategory({
        title: '',
        description: '',
        color: '#002366',
        icon: '',
        order: 0
      });

      // Close the dialog
      setIsAddingCategory(false);
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category: ' + (error instanceof Error ? error.message : 'Please try again.'));
    } finally {
      setIsCreatingCategory(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (uploadedMedia.length === 0) {
      toast.error('Please upload at least one image or video');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create a FormData object to send to our server-side API
      const formData = new FormData();

      // Add all media files to the FormData
      uploadedMedia.forEach((media, index) => {
        formData.append('files', media.file);
      });

      // Add metadata as JSON strings
      formData.append('titles', JSON.stringify(uploadedMedia.map(media => media.title)));
      formData.append('descriptions', JSON.stringify(uploadedMedia.map(media => media.description)));
      formData.append('categories', JSON.stringify(uploadedMedia.map(media => media.category)));
      formData.append('mediaTypes', JSON.stringify(uploadedMedia.map(media => media.mediaType)));

      console.log('Uploading media:', uploadedMedia.length);
      console.log('Media types:', uploadedMedia.map(media => media.mediaType));
      console.log('Categories being sent:', uploadedMedia.map(media => media.category));

      // Send the request to our server-side API
      const response = await fetch('/api/gallery/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      console.log('Upload response:', data);

      if (!response.ok) {
        throw new Error(data.message || data.error || `Server error: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.message || data.error || 'Failed to upload images');
      }

      // Extract any errors from the response
      const failedUploads = data.results.filter((r: any) => r.error);

      // Check for partial success
      if (data.status === 'partial') {
        // Show a warning toast for partial success
        toast.warning(data.message, {
          description: 'Some images failed to upload. See details below.',
          duration: 5000
        });

        // Log the failed uploads
        console.warn('Failed uploads:', failedUploads);

        // Store the errors to display them
        setUploadErrors(failedUploads.map((r: any) => ({
          index: r.index,
          message: r.message,
          fileName: r.fileName || `Image ${r.index + 1}`
        })));

        // Don't navigate away if there are errors, so the user can see them
        setIsSubmitting(false);

        // Remove the successfully uploaded media from the list
        const successfulIndices = data.results
          .filter((r: any) => !r.error)
          .map((r: any) => r.index);

        setUploadedMedia(prev =>
          prev.filter((_, index) => !successfulIndices.includes(index))
        );

        return; // Don't navigate away
      } else if (data.status === 'complete') {
        // Show a success toast for complete success
        toast.success(data.message);

        // Navigate to the gallery page
        router.replace('/admin/gallery');

        // Add a small delay to ensure the navigation happens
        setTimeout(() => {
          window.location.href = '/admin/gallery';
        }, 500);
      }

      // If there are any successful uploads, show the success count
      if (data.stats && data.stats.success > 0) {
        console.log(`Successfully uploaded ${data.stats.success} out of ${data.stats.total} images`);
      }
    } catch (error) {
      console.error('Failed to upload images:', error);
      toast.error('Failed to upload images: ' + (error instanceof Error ? error.message : 'Please try again.'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Upload Gallery Media</h1>
        </div>
      </div>

      <Separator />

      {/* Display upload errors if any */}
      {uploadErrors.length > 0 && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Upload Errors</AlertTitle>
          <AlertDescription>
            <p className="mb-2">The following images failed to upload:</p>
            <ul className="list-disc pl-5 space-y-1">
              {uploadErrors.map((error, idx) => (
                <li key={idx}>
                  <strong>{error.fileName}</strong>: {error.message}
                </li>
              ))}
            </ul>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => setUploadErrors([])}
            >
              <X className="mr-2 h-4 w-4" />
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Upload Media</CardTitle>
                <CardDescription>
                  Upload images or videos to the gallery
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="image" value={activeTab} onValueChange={(value) => setActiveTab(value as 'image' | 'video')}>
                  <TabsList className="grid w-full grid-cols-2 mb-4">
                    <TabsTrigger value="image">Images</TabsTrigger>
                    <TabsTrigger value="video">Videos</TabsTrigger>
                  </TabsList>

                  <TabsContent value="image">
                    <div
                      className="border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors hover:border-primary/50"
                      onClick={() => imageInputRef.current?.click()}
                    >
                      <input
                        type="file"
                        ref={imageInputRef}
                        onChange={(e) => handleFileChange(e, 'image')}
                        accept="image/jpeg,image/png,image/webp"
                        multiple
                        className="hidden"
                      />
                      <div className="flex flex-col items-center justify-center text-center">
                        <ImageIcon className="h-10 w-10 text-muted-foreground mb-4" />
                        <p className="text-sm font-medium">
                          Click to select images
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          or drag files to this area
                        </p>
                        <p className="text-xs text-muted-foreground mt-4">
                          Supports: JPG, PNG, WEBP (max 5MB)
                        </p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="video">
                    <div
                      className="border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors hover:border-primary/50"
                      onClick={() => videoInputRef.current?.click()}
                    >
                      <input
                        type="file"
                        ref={videoInputRef}
                        onChange={(e) => handleFileChange(e, 'video')}
                        accept="video/mp4"
                        multiple
                        className="hidden"
                      />
                      <div className="flex flex-col items-center justify-center text-center">
                        <FileVideo className="h-10 w-10 text-muted-foreground mb-4" />
                        <p className="text-sm font-medium">
                          Click to select videos
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          or drag files to this area
                        </p>
                        <p className="text-xs text-muted-foreground mt-4">
                          Supports: MP4 (max 50MB)
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter>
                <div className="w-full">
                  <p className="text-sm font-medium mb-2">Uploaded Media ({uploadedMedia.length})</p>
                  <div className="grid grid-cols-3 gap-2">
                    {uploadedMedia.map((media, index) => (
                      <div
                        key={index}
                        className={`relative aspect-square rounded-md overflow-hidden border cursor-pointer ${
                          selectedMediaIndex === index ? 'ring-2 ring-primary' : ''
                        }`}
                        onClick={() => setSelectedMediaIndex(index)}
                      >
                        {media.mediaType === 'image' ? (
                          <Image
                            src={media.preview}
                            alt={media.title || `Media ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="relative w-full h-full bg-black overflow-hidden">
                            {/* Direct video element with controls */}
                            <video
                              className="w-full h-full object-contain"
                              src={media.preview}
                              controls
                              preload="auto"
                              onError={(e) => {
                                console.error('Video error:', e);
                              }}
                            />

                            {/* Video badge */}
                            <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
                              Video
                            </div>
                          </div>
                        )}
                        <button
                          type="button"
                          className="absolute top-1 right-1 bg-background/80 rounded-full p-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeMedia(index);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>

          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Media Details</CardTitle>
                <CardDescription>
                  {selectedMediaIndex !== null
                    ? `Editing details for ${uploadedMedia[selectedMediaIndex].mediaType} ${selectedMediaIndex + 1}`
                    : 'Select an image or video to edit its details'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedMediaIndex !== null ? (
                  <div className="space-y-4">
                    <div className="aspect-video relative rounded-lg overflow-hidden border">
                      {uploadedMedia[selectedMediaIndex].mediaType === 'image' ? (
                        <Image
                          src={uploadedMedia[selectedMediaIndex].preview}
                          alt={uploadedMedia[selectedMediaIndex].title || `Media ${selectedMediaIndex + 1}`}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="relative w-full h-full bg-black overflow-hidden">
                          {/* Direct video element with controls */}
                          <video
                            className="w-full h-full object-contain"
                            src={uploadedMedia[selectedMediaIndex].preview}
                            controls
                            preload="auto"
                            onError={(e) => {
                              console.error('Video error:', e);
                            }}
                          />

                          {/* Video badge */}
                          <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
                            Video
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        placeholder="Enter media title"
                        value={uploadedMedia[selectedMediaIndex].title}
                        onChange={(e) => updateMediaMetadata(selectedMediaIndex, 'title', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Enter media description"
                        rows={3}
                        value={uploadedMedia[selectedMediaIndex].description}
                        onChange={(e) => updateMediaMetadata(selectedMediaIndex, 'description', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <div className="flex gap-2">
                        <div className="flex-1 flex">
                          <Tag className="mr-2 h-4 w-4 mt-3 text-muted-foreground" />
                          <select
                            id="category"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            value={uploadedMedia[selectedMediaIndex].category}
                            onChange={(e) => updateMediaMetadata(selectedMediaIndex, 'category', e.target.value)}
                            disabled={isLoading}
                          >
                            <option value="" disabled>
                              {isLoading ? 'Loading categories...' : 'Select a category'}
                            </option>
                            {categories.map((category) => (
                              <option key={category._id} value={category._id}>
                                {category.title}
                              </option>
                            ))}
                          </select>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          className="h-10 w-10"
                          onClick={fetchCategories}
                          title="Refresh Categories"
                          disabled={isLoading}
                        >
                          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          className="h-10 w-10"
                          onClick={() => setIsAddingCategory(true)}
                          title="Add New Category"
                        >
                          <PlusCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No Image Selected</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Upload images or select an existing one to edit its details
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/admin/gallery')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || uploadedMedia.length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Media
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </form>

      {/* Add New Category Dialog */}
      <Dialog open={isAddingCategory} onOpenChange={setIsAddingCategory}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Category</DialogTitle>
            <DialogDescription>
              Create a new category for gallery images. Categories help organize and filter images.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="categoryTitle">Category Title *</Label>
              <Input
                id="categoryTitle"
                placeholder="e.g., Culture, Architecture, Nature"
                value={newCategory.title}
                onChange={(e) => setNewCategory({ ...newCategory, title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="categoryDescription">Description</Label>
              <Textarea
                id="categoryDescription"
                placeholder="Brief description of this category"
                rows={3}
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryColor">Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="categoryColor"
                    type="color"
                    className="w-12 h-10 p-1"
                    value={newCategory.color}
                    onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}
                  />
                  <Input
                    type="text"
                    value={newCategory.color}
                    onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}
                    className="flex-1"
                    placeholder="#002366"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryOrder">Display Order</Label>
                <Input
                  id="categoryOrder"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={newCategory.order.toString()}
                  onChange={(e) => setNewCategory({ ...newCategory, order: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingCategory(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateCategory}
              disabled={isCreatingCategory || !newCategory.title}
            >
              {isCreatingCategory ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Create Category
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
