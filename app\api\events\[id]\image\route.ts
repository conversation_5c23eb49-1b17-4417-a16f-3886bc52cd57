import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@sanity/client';

// Create a Sanity client for server-side operations
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN, // Server-side token, not exposed to client
  apiVersion: '2023-05-03',
  useCdn: false,
});

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Image upload API route called for event ID:', params.id);
    
    if (!params.id) {
      console.error('No event ID provided');
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Validate token exists
    if (!process.env.SANITY_API_TOKEN) {
      console.error('SANITY_API_TOKEN is missing');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }
    
    // Get the form data from the request
    const formData = await req.formData();
    const imageFile = formData.get('image') as File;
    const imageAlt = formData.get('alt') as string || '';
    
    if (!imageFile) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }
    
    console.log('Received image file:', imageFile.name, 'size:', imageFile.size, 'type:', imageFile.type);
    
    // Validate image file type
    if (!imageFile.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      );
    }
    
    try {
      console.log('Uploading image to Sanity...');
      
      // Upload the image as an asset
      const asset = await client.assets.upload('image', imageFile, {
        filename: imageFile.name,
      });
      
      console.log('Image uploaded successfully, asset ID:', asset._id);
      
      // Get the actual file extension from the asset ID
      // Sanity asset IDs are in the format: image-abc123-800x600-jpg
      const assetIdParts = asset._id.split('-');
      const fileExtension = assetIdParts[assetIdParts.length - 1];
      const assetIdWithoutExt = assetIdParts.slice(0, -1).join('-');
      
      // Construct the image URL for direct access
      const imageUrl = `https://cdn.sanity.io/images/${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt'}/${process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'}/${assetIdWithoutExt.replace('image-', '')}.${fileExtension}`;
      
      console.log('Constructed image URL:', imageUrl);
      
      // Get the event document to get the title (for alt text if not provided)
      const event = await client.getDocument(params.id);
      const eventTitle = event?.title || '';
      
      // Patch the event document with the image reference and direct URL
      console.log('Patching event document with image data...');
      const patchResult = await client
        .patch(params.id)
        .set({
          // Set the structured image reference for Sanity
          image: {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: asset._id,
            },
            alt: imageAlt || eventTitle,
          },
          // Also set the direct imageUrl for backward compatibility
          imageUrl: imageUrl,
          imageAlt: imageAlt || eventTitle,
        })
        .commit();
      
      console.log('Image patch successful:', patchResult._id);
      
      return NextResponse.json({
        success: true,
        message: 'Image uploaded and event updated successfully',
        imageUrl: imageUrl,
        imageRef: asset._id,
        event: patchResult
      });
    } catch (sanityError) {
      console.error('Sanity operation error:', sanityError);
      return NextResponse.json(
        {
          error: 'Sanity operation failed',
          details: sanityError instanceof Error ? sanityError.message : 'Unknown Sanity error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in image upload handler:', error);
    return NextResponse.json(
      {
        error: 'Failed to upload image',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
