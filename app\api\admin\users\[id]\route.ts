import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getWriteClient } from '@/lib/sanity.client';
import { isSuperAdmin } from '@/lib/auth-utils';
import { updateUserTransaction, deleteUserTransaction } from '@/lib/user-transaction';

// GET /api/admin/users/[id] - Get a specific admin user
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Properly extract and await the id parameter
    const { id } = context.params;

    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to view user details' },
        { status: 401 }
      );
    }

    // Get the Sanity client
    const client = getWriteClient();

    // Fetch the admin user
    const user = await client.fetch(`
      *[_type == "adminUser" && _id == $id][0] {
        _id,
        username,
        name,
        email,
        role,
        image,
        createdAt,
        lastLogin,
        isActive,
        permissions
      }
    `, { id });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error('Error fetching admin user:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch admin user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/users/[id] - Update a specific admin user
export async function PATCH(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Properly extract and await the id parameter
    const { id } = context.params;

    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to update users' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Get the Sanity client for validation
    const client = getWriteClient();

    // Check if the user exists in Sanity
    const existingUser = await client.fetch(`
      *[_type == "adminUser" && _id == $id][0]
    `, { id });

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'User not found in Sanity' },
        { status: 404 }
      );
    }

    // Check if username or email already exists (if they're being updated)
    if (body.username || body.email) {
      const duplicateUser = await client.fetch(`
        *[_type == "adminUser" && _id != $id && (username == $username || email == $email)][0] {
          username,
          email
        }
      `, {
        id,
        username: body.username || '',
        email: body.email || ''
      });

      if (duplicateUser) {
        const field = duplicateUser.username === body.username ? 'username' : 'email';
        return NextResponse.json(
          { success: false, message: `A user with this ${field} already exists` },
          { status: 400 }
        );
      }
    }

    try {
      // Use the transaction utility to update the user in both systems
      const result = await updateUserTransaction(id, {
        name: body.name,
        email: body.email,
        username: body.username,
        password: body.password,
        role: body.role,
        isActive: body.isActive,
        permissions: body.permissions,
        isProtected: body.isProtected
      });

      return NextResponse.json({
        success: true,
        message: result.message || 'User updated successfully',
        user: result.user,
        warnings: result.localError ? [result.localError] : undefined
      });
    } catch (transactionError) {
      console.error('Transaction error updating user:', transactionError);
      return NextResponse.json(
        {
          success: false,
          message: transactionError instanceof Error ? transactionError.message : 'Failed to update user'
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating admin user:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update admin user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete a specific admin user
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Properly extract and await the id parameter
    const { id } = context.params;

    // Check authentication
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has super_admin role
    if (!session?.user || !isSuperAdmin(session)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be a super admin to delete users' },
        { status: 401 }
      );
    }

    try {
      // Use the transaction utility to delete the user from both systems
      const result = await deleteUserTransaction(id);

      return NextResponse.json({
        success: true,
        message: result.message || 'User deleted successfully',
        warnings: result.localError ? [result.localError] : undefined
      });
    } catch (transactionError) {
      console.error('Transaction error deleting user:', transactionError);
      return NextResponse.json(
        {
          success: false,
          message: transactionError instanceof Error ? transactionError.message : 'Failed to delete user'
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error deleting admin user:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete admin user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
