import { create } from 'zustand';
import { getEvents, getEventById, getCountdownTargetEvent } from '@/lib/sanity';
import { logError } from '@/lib/errorHandling';

export interface Event {
  _id: string;
  title: string;
  slug: { current: string };
  date: string;
  endDate?: string;
  location: string;
  description: string;
  imageUrl?: string;
  imageAlt?: string;
  isCountdownTarget: boolean;
  isHighlighted: boolean;
  showRsvp: boolean;
  eventType: string;
  order: number;
}

interface EventState {
  events: Event[];
  isLoading: boolean;
  error: string | null;
  countdownEvent: Event | null;
  selectedEvent: Event | null;

  // Actions
  fetchEvents: () => Promise<Event[]>;
  fetchEventById: (id: string) => Promise<Event | null>;
  fetchCountdownEvent: () => Promise<void>;
  setSelectedEvent: (event: Event | null) => void;

  // Admin actions
  deleteEvent: (id: string) => Promise<boolean>;
  setCountdownTarget: (id: string) => Promise<boolean>;
  createEvent: (event: Omit<Event, '_id'>) => Promise<string | null>;
  updateEvent: (id: string, event: Partial<Event>) => Promise<boolean>;
}

export const useEventStore = create<EventState>((set, get) => ({
  events: [],
  isLoading: false,
  error: null,
  countdownEvent: null,
  selectedEvent: null,

  fetchEvents: async (): Promise<Event[]> => {
    set({ isLoading: true, error: null });
    try {
      const events = await getEvents();
      set({ events: events || [], isLoading: false });
      return events;
    } catch (error) {
      logError(error, 'fetchEvents');
      set({ error: 'Failed to fetch events', isLoading: false });
      return [];
    }
  },

  fetchEventById: async (id: string): Promise<Event | null> => {
    set({ isLoading: true, error: null });
    try {
      const event = await getEventById(id) as Event | null;
      if (event) {
        set({ selectedEvent: event, isLoading: false });
      } else {
        set({ error: 'Event not found', isLoading: false });
      }
      return event;
    } catch (error) {
      logError(error, 'fetchEventById');
      set({ error: 'Failed to fetch event', isLoading: false });
      return null;
    }
  },

  fetchCountdownEvent: async () => {
    try {
      const event = await getCountdownTargetEvent();
      set({ countdownEvent: event as Event | null });
    } catch (error) {
      logError(error, 'fetchCountdownEvent');
      // Don't set error state here as this is a secondary operation
    }
  },

  setSelectedEvent: (event) => {
    set({ selectedEvent: event });
  },

  deleteEvent: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Attempting to delete event with ID: ${id}`);

      // Use the API endpoint to delete the event
      const response = await fetch(`/api/events/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        // Use revalidation instead of no-store
        next: { revalidate: 60 },
      });

      console.log('Delete response status:', response.status);

      const data = await response.json();
      console.log('Delete response data:', data);

      if (!response.ok) {
        throw new Error(data.error || `Failed to delete event: ${response.status}`);
      }

      // Update local state
      const { events } = get();
      set({
        events: events.filter(event => event._id !== id),
        isLoading: false
      });

      return true;
    } catch (error) {
      logError(error, 'deleteEvent');
      set({ error: 'Failed to delete event', isLoading: false });
      return false;
    }
  },

  setCountdownTarget: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Create a Sanity client
      const { createClient } = await import('@sanity/client');

      // Get token from environment variable
      const token = process.env.NEXT_PUBLIC_SANITY_API_TOKEN;

      if (!token) {
        throw new Error('Sanity API token is missing');
      }

      const client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2025-05-09',
        useCdn: false,
      });

      // First, unset all events as countdown targets
      await client
        .patch({ query: `*[_type == "event" && isCountdownTarget == true]` })
        .set({ isCountdownTarget: false })
        .commit();

      // Then, set the selected event as the countdown target
      await client
        .patch(id)
        .set({ isCountdownTarget: true })
        .commit();

      // Update local state
      const { events } = get();
      const updatedEvents = events.map(event => ({
        ...event,
        isCountdownTarget: event._id === id
      }));

      set({
        events: updatedEvents,
        countdownEvent: updatedEvents.find(event => event._id === id) || null,
        isLoading: false
      });

      return true;
    } catch (error) {
      logError(error, 'setCountdownTarget');
      set({ error: 'Failed to set countdown target', isLoading: false });
      return false;
    }
  },

  createEvent: async (eventData) => {
    set({ isLoading: true, error: null });
    try {
      // Create a Sanity client
      const { createClient } = await import('@sanity/client');

      // Get token from environment variable
      const token = process.env.NEXT_PUBLIC_SANITY_API_TOKEN;

      if (!token) {
        throw new Error('Sanity API token is missing');
      }

      const client = createClient({
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
        token: token,
        apiVersion: '2025-05-09',
        useCdn: false,
      });

      // Create the event document
      const result = await client.create({
        _type: 'event',
        ...eventData
      });

      // Refresh events list
      get().fetchEvents();

      set({ isLoading: false });
      return result._id;
    } catch (error) {
      logError(error, 'createEvent');
      set({ error: 'Failed to create event', isLoading: false });
      return null;
    }
  },

  updateEvent: async (id: string, eventData: Partial<Event>) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Attempting to update event with ID: ${id}`);

      // Use the API endpoint to update the event
      const response = await fetch(`/api/events/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
        // Use revalidation instead of no-store
        next: { revalidate: 60 },
      });

      console.log('Update response status:', response.status);

      const data = await response.json();
      console.log('Update response data:', data);

      if (!response.ok) {
        throw new Error(data.error || `Failed to update event: ${response.status}`);
      }

      // Refresh events list
      get().fetchEvents();

      set({ isLoading: false });
      return true;
    } catch (error) {
      logError(error, 'updateEvent');
      set({ error: 'Failed to update event', isLoading: false });
      return false;
    }
  }
}));
