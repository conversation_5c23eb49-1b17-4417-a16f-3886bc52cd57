'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Save, ArrowLeft } from 'lucide-react';
import { getWriteClient } from '@/lib/sanity.client';

interface NewSectionPageParams {
  params: {
    id: string;
  };
}

export default function NewSectionPage({ params }: NewSectionPageParams) {
  const { id } = params;
  const router = useRouter();
  const [sectionType, setSectionType] = useState('textSection');
  const [heading, setHeading] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  const handleBack = () => {
    router.push(`/admin/pages/${id}`);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    try {
      const client = getWriteClient();
      
      // Get the current page
      const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });
      
      if (!page) {
        toast.error('Page not found');
        setIsSaving(false);
        return;
      }
      
      // Create a new section based on the selected type
      let newSection: any = {
        _type: sectionType,
        heading: heading,
        animation: {
          duration: 0.6,
          delay: 0,
          stagger: 0.2,
          type: 'spring'
        }
      };
      
      // Add type-specific properties
      switch (sectionType) {
        case 'hero':
          newSection.tagline = '';
          newSection.ctas = [];
          break;
        case 'textSection':
          newSection.text = '';
          newSection.backgroundStyle = 'none';
          break;
        case 'imageGallery':
          newSection.text = '';
          newSection.images = [];
          break;
        case 'featuredContent':
          newSection.text = '';
          newSection.items = [];
          newSection.layout = 'cards';
          newSection.backgroundStyle = 'none';
          break;
        case 'contactForm':
          newSection.text = '';
          newSection.submitButtonText = 'Submit';
          newSection.successMessage = 'Thank you for your message. We will get back to you soon.';
          newSection.backgroundStyle = 'none';
          break;
      }
      
      // Get the current page builder array
      const pageBuilder = page.pageBuilder || [];
      
      // Add the new section
      pageBuilder.push(newSection);
      
      // Update the page
      await client.patch(id)
        .set({ pageBuilder })
        .commit();
      
      toast.success('Section added successfully');
      
      // Redirect to the section editor for the new section
      const newSectionIndex = pageBuilder.length - 1;
      router.push(`/admin/pages/sections/${id}/${newSectionIndex}`);
    } catch (error) {
      console.error('Error adding section:', error);
      toast.error('Failed to add section');
      setIsSaving(false);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Page Editor
        </Button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Add New Section</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="sectionType">Section Type</Label>
                <Select
                  value={sectionType}
                  onValueChange={setSectionType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select section type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hero">Hero Section</SelectItem>
                    <SelectItem value="textSection">Text Section</SelectItem>
                    <SelectItem value="imageGallery">Image Gallery</SelectItem>
                    <SelectItem value="featuredContent">Featured Content</SelectItem>
                    <SelectItem value="contactForm">Contact Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="heading">Heading</Label>
                <Input
                  id="heading"
                  value={heading}
                  onChange={(e) => setHeading(e.target.value)}
                  placeholder="Enter heading"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? 'Adding...' : 'Add Section'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
