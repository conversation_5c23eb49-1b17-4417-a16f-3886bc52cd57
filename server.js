// Custom server for Next.js with improved error handling and logging
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Determine if we're in development or production
const dev = process.env.NODE_ENV !== 'production';
const hostname = '0.0.0.0'; // Use 0.0.0.0 to listen on all available network interfaces
const port = process.env.PORT || 3000;

// Initialize Next.js
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Log environment information
console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`Node.js version: ${process.version}`);
console.log(`Listening on: ${hostname}:${port}`);

// Prepare the Next.js app
app.prepare()
  .then(() => {
    // Create HTTP server
    const server = createServer((req, res) => {
      try {
        // Parse the URL
        const parsedUrl = parse(req.url, true);
        const { pathname, query } = parsedUrl;

        // Log incoming requests in production for debugging
        if (!dev) {
          console.log(`Request: ${req.method} ${pathname}`);
        }

        // Let Next.js handle the request
        handle(req, res, parsedUrl);
      } catch (err) {
        console.error('Error handling request:', err);
        res.statusCode = 500;
        res.end('Internal Server Error');
      }
    });

    // Add error handling for the server
    server.on('error', (err) => {
      console.error('Server error:', err);
    });

    // Start listening
    server.listen(port, hostname, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port}`);
    });
  })
  .catch((err) => {
    console.error('Error preparing Next.js app:', err);
    process.exit(1);
  });
