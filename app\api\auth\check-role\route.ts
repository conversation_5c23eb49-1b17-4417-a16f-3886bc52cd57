import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AdminRole } from '@/lib/types/admin';

/**
 * API route to check if the current user has a specific role
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    // If no session, user is not authenticated
    if (!session?.user) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated',
        hasRole: false
      }, { status: 401 });
    }
    
    // Get the expected role from the request body
    const body = await req.json();
    const { expectedRole } = body;
    
    // Validate expected role
    if (!expectedRole || !Object.values(AdminRole).includes(expectedRole as AdminRole)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid role specified',
        hasRole: false
      }, { status: 400 });
    }
    
    // Get the user's role from the session
    const userRole = session.user.role as AdminRole;
    
    // Check if the user has the expected role
    const hasRole = userRole === expectedRole;
    
    // Return the result
    return NextResponse.json({
      success: true,
      message: hasRole ? 'User has the specified role' : 'User does not have the specified role',
      hasRole,
      userRole
    });
  } catch (error) {
    console.error('Error checking role:', error);
    return NextResponse.json({
      success: false,
      message: 'An error occurred while checking the role',
      hasRole: false
    }, { status: 500 });
  }
}
