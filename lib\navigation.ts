// Navigation configuration for the website
// This file centralizes all navigation items and their structure

export interface SubMenuItem {
  name: string;
  href: string;
  description?: string;
  isExternal?: boolean;
  isNew?: boolean;
  icon?: string;
}

export interface MenuItem {
  name: string;
  href: string;
  description?: string;
  icon?: string; // SVG path for icons
  children?: SubMenuItem[];
  isExternal?: boolean;
  isNew?: boolean; // To highlight new items
  isFeatured?: boolean; // For special styling
}

// Main navigation items
export const mainNavItems: MenuItem[] = [
  {
    name: 'Home',
    href: '/',
    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
  },
  {
    name: 'About',
    href: '/#about',
    icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
    children: [
      {
        name: 'Mission & Vision',
        href: '/#about',
        description: 'Learn about our purpose and goals',
        icon: 'M13 10V3L4 14h7v7l9-11h-7z',
      },
      {
        name: 'Leadership',
        href: '/#leadership',
        description: 'Meet the royal leadership',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
      },
      {
        name: 'History',
        href: '/#history',
        description: 'Explore our rich heritage',
        icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      },
    ],
  },
  {
    name: 'Coronation',
    href: '/#coronation',
    icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
    isFeatured: true,
  },
  {
    name: 'Initiatives',
    href: '/#initiatives',
    icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',
    children: [
      {
        name: 'Education',
        href: '/#education',
        description: 'Supporting educational programs',
        icon: 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
      },
      {
        name: 'Healthcare',
        href: '/#healthcare',
        description: 'Improving community health',
        icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z',
      },
      {
        name: 'Economic Development',
        href: '/#economic-development',
        description: 'Building prosperity for all',
        icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
      },
      {
        name: 'Cultural Preservation',
        href: '/#cultural-preservation',
        description: 'Preserving our heritage',
        icon: 'M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z',
      },
    ],
  },
  {
    name: 'Store',
    href: '/store',
    icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
    isNew: true,
    children: [
      {
        name: 'All Products',
        href: '/store',
        description: 'Browse all merchandise',
        icon: 'M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z',
      },
      {
        name: 'Apparel',
        href: '/store?category=apparel',
        description: 'T-shirts, hoodies, and more',
        icon: 'M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z',
      },
      {
        name: 'Collectibles',
        href: '/store?category=collectibles',
        description: 'Limited edition items',
        icon: 'M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z',
      },
      {
        name: 'Shopping Cart',
        href: '/store/cart',
        description: 'View your cart',
        icon: 'M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z',
      },
    ],
  },
  {
    name: 'Gallery',
    href: '/gallery',
    icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
  },
  {
    name: 'News',
    href: '/news',
    icon: 'M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z',
  },
  {
    name: 'Contact',
    href: '/#contact',
    icon: 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
  },
];

// Footer navigation items (can be different from main nav)
export const footerNavItems: MenuItem[] = [
  {
    name: 'Home',
    href: '/',
  },
  {
    name: 'About',
    href: '/#about',
  },
  {
    name: 'Initiatives',
    href: '/#initiatives',
  },
  {
    name: 'Gallery',
    href: '/gallery',
  },
  {
    name: 'News',
    href: '/news',
  },
  {
    name: 'Contact',
    href: '/#contact',
  },
  {
    name: 'Privacy Policy',
    href: '/privacy',
    isExternal: true,
  },
];

// Function to get the active section based on the current path
export function getActiveSection(pathname: string): string {
  if (pathname === '/') return 'home';
  if (pathname.startsWith('/gallery')) return 'gallery';
  if (pathname.startsWith('/news')) return 'news';
  if (pathname.startsWith('/events')) return 'events';
  if (pathname.startsWith('/contact')) return 'contact';
  if (pathname.startsWith('/about')) return 'about';

  // Add more path checks for future pages

  // Default to home if no match
  return 'home';
}

// Helper function to add new pages to navigation
export function addPageToNavigation(
  page: MenuItem,
  parent?: string
): void {
  if (!parent) {
    // Add to main navigation
    mainNavItems.push(page);
    return;
  }

  // Find parent and add as child
  const parentItem = mainNavItems.find(item => item.name.toLowerCase() === parent.toLowerCase());
  if (parentItem) {
    if (!parentItem.children) {
      parentItem.children = [];
    }
    parentItem.children.push(page);
  }
}
