import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate dynamic metadata for this page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Strategic Partners',
    description: 'Meet the strategic partners of the Kingdom of Adukrom who are helping to build a brighter future.',
    url: '/partners',
    keywords: ['Strategic Partners', 'Adukrom Kingdom', 'Crown Partners', 'Global Impact', 'Collaboration'],
  });
}

export default function PartnersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-white">
      {children}
    </div>
  );
}
