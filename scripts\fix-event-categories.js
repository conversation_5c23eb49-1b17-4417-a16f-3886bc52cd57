const { createClient } = require('@sanity/client');
require('dotenv').config({ path: '.env.local' });

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2025-05-09',
  useCdn: false,
});

// Main function to fix event categories
async function fixEventCategories() {
  console.log('Starting to fix event categories...');

  // Validate token
  if (!process.env.SANITY_API_TOKEN) {
    console.error('SANITY_API_TOKEN is missing. Please add it to your .env.local file.');
    process.exit(1);
  }

  try {
    // 1. Find all categories
    const categories = await client.fetch(`
      *[_type == "category"] {
        _id,
        title,
        slug
      }
    `);

    console.log('Found categories:', categories.map(c => `${c.title} (${c._id})`).join(', '));

    // 2. Find the "Events" and "events" categories
    const eventsCapitalCategory = categories.find(c => c.title === 'Events');
    const eventsLowerCategory = categories.find(c => c.title === 'events');

    if (!eventsCapitalCategory && !eventsLowerCategory) {
      console.log('No Events categories found, nothing to fix.');
      return;
    }

    // If only one category exists, make sure it's properly named
    if (!eventsCapitalCategory) {
      console.log('Only lowercase "events" category found. Renaming to "Events".');

      // Update the "events" category to "Events"
      await client
        .patch(eventsLowerCategory._id)
        .set({
          title: 'Events',
          slug: { current: 'events' }
        })
        .commit();

      console.log(`Renamed category "events" to "Events" with ID: ${eventsLowerCategory._id}`);

      // Use this as the target category
      const targetCategoryId = eventsLowerCategory._id;

      // Update all galleries with this category
      await updateGalleryCategories(targetCategoryId);
      return;
    }

    if (!eventsLowerCategory) {
      console.log('Only "Events" category found, no need to rename.');

      // Use this as the target category
      const targetCategoryId = eventsCapitalCategory._id;

      // Update all galleries with this category
      await updateGalleryCategories(targetCategoryId);
      return;
    }

    // Both categories exist, we need to merge them
    console.log(`Found both "events" (${eventsLowerCategory._id}) and "Events" (${eventsCapitalCategory._id}) categories.`);

    // 3. Find all galleries with the lowercase "events" category
    const eventsLowerGalleries = await client.fetch(`
      *[_type == "gallery" && references($categoryId)] {
        _id,
        title
      }
    `, { categoryId: eventsLowerCategory._id });

    console.log(`Found ${eventsLowerGalleries.length} galleries with lowercase "events" category.`);

    // 4. Find all galleries with the "Events" category
    const eventsCapitalGalleries = await client.fetch(`
      *[_type == "gallery" && references($categoryId)] {
        _id,
        title
      }
    `, { categoryId: eventsCapitalCategory._id });

    console.log(`Found ${eventsCapitalGalleries.length} galleries with "Events" category.`);

    // 5. Decide which category to keep
    // We'll keep the "Events" category
    const targetCategoryId = eventsCapitalCategory._id;

    // 6. Update the target category name
    await client
      .patch(targetCategoryId)
      .set({
        title: 'Events',
        slug: { current: 'events' }
      })
      .commit();

    console.log(`Updated category name to "Events" with ID: ${targetCategoryId}`);

    // 7. Update all galleries with the lowercase "events" category to use the target category
    for (const gallery of eventsLowerGalleries) {
      await client
        .patch(gallery._id)
        .set({
          category: {
            _type: 'reference',
            _ref: targetCategoryId
          }
        })
        .commit();

      console.log(`Updated gallery "${gallery.title}" to use the "Events" category.`);
    }

    // 8. Delete the unused category
    await client.delete(eventsLowerCategory._id);
    console.log(`Deleted unused category with ID: ${eventsLowerCategory._id}`);

    console.log('Event categories fixed successfully!');
  } catch (error) {
    console.error('Error fixing event categories:', error);
    process.exit(1);
  }
}

// Function to update all galleries to use the target category
async function updateGalleryCategories(targetCategoryId) {
  // Find all galleries
  const galleries = await client.fetch(`
    *[_type == "gallery"] {
      _id,
      title,
      "categoryId": category._ref
    }
  `);

  console.log(`Found ${galleries.length} total galleries.`);

  // Update event-related galleries to use the target category
  const eventKeywords = ['coronation', 'ceremony', 'gala', 'dinner', 'forum', 'economic'];

  for (const gallery of galleries) {
    const title = gallery.title.toLowerCase();
    const isEventRelated = eventKeywords.some(keyword => title.includes(keyword));

    if (isEventRelated && gallery.categoryId !== targetCategoryId) {
      await client
        .patch(gallery._id)
        .set({
          category: {
            _type: 'reference',
            _ref: targetCategoryId
          }
        })
        .commit();

      console.log(`Updated gallery "${gallery.title}" to use the "Events" category.`);
    }
  }

  console.log('Gallery categories updated successfully!');
}

// Run the fix
fixEventCategories();
