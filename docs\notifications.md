# Notification Services

This document provides information about the notification services implemented in the Kingdom of Adukrom website.

## Overview

The notification system allows sending emails and SMS messages to users for various purposes, such as:

- RSVP confirmations
- Event reminders
- News updates
- Administrative notifications

## Services Used

### Email Service (SendGrid)

[SendGrid](https://sendgrid.com/) is used for sending emails. It provides reliable email delivery and tracking.

#### Setup

1. Create a SendGrid account
2. Create an API key with "Mail Send" permissions
3. Add the API key to your environment variables:

```
SENDGRID_API_KEY=your_api_key
EMAIL_FROM=<EMAIL>
```

### SMS Service (Twilio)

[Twilio](https://www.twilio.com/) is used for sending SMS messages. It provides global SMS coverage.

#### Setup

1. Create a Twilio account
2. Get your Account SID and Auth Token
3. Purchase a phone number
4. Add the credentials to your environment variables:

```
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

## Implementation

### Email Module (`lib/email.ts`)

The email module provides functions for sending emails:

- `sendEmail(options)`: Generic function for sending emails
- `sendRsvpConfirmationEmail(email, firstName, lastName, events)`: Sends an RSVP confirmation email

### SMS Module (`lib/sms.ts`)

The SMS module provides functions for sending SMS messages:

- `sendSms(options)`: Generic function for sending SMS messages
- `sendRsvpConfirmationSms(phoneNumber, firstName, events)`: Sends an RSVP confirmation SMS

### Notification Module (`lib/notifications.ts`)

The notification module provides high-level functions that use both email and SMS services:

- `sendRsvpConfirmationNotifications(rsvpData)`: Sends RSVP confirmation notifications via email and/or SMS based on user preferences
- `sendEventReminderNotifications(rsvpData, eventCode, daysUntilEvent)`: Sends event reminder notifications

## Usage

### Sending RSVP Confirmations

The RSVP API automatically sends confirmation notifications when a user submits an RSVP:

```typescript
// In app/api/rsvp/route.ts
await sendRsvpConfirmationNotifications({
  firstName: body.firstName,
  lastName: body.lastName,
  email: body.email,
  phone: body.phone,
  events: body.events,
  reminderPreference: body.reminder || ['email']
});
```

### Sending Event Reminders

Event reminders can be sent using the `send-event-reminders.js` script:

```bash
node scripts/send-event-reminders.js coronation 7
```

This will send reminders to all attendees of the coronation event if the event is 7 days away.

## Testing

### Testing in Development

In development mode, emails and SMS messages are not actually sent. Instead, they are logged to the console:

```
==== EMAIL SENT ====
From: <EMAIL>
To: <EMAIL>
Subject: Your RSVP Confirmation - Kingdom of Adukrom
Text: ...
HTML: ...
==== END EMAIL ====
```

```
==== SMS SENT ====
From: +**********
To: +**********
Body: Thank you, John! Your RSVP for Royal Coronation Ceremony has been confirmed.
==== END SMS ====
```

### Testing in Production

To test in production without sending real messages, you can:

1. Create test accounts in SendGrid and Twilio
2. Use sandbox phone numbers in Twilio
3. Use test email addresses that you control

## Monitoring

### Email Monitoring

You can monitor email delivery in the SendGrid dashboard:

- Delivery rates
- Open rates
- Click rates
- Bounces and blocks

### SMS Monitoring

You can monitor SMS delivery in the Twilio dashboard:

- Delivery status
- Error rates
- Cost tracking

## Troubleshooting

### Common Email Issues

- **Emails not sending**: Check your SendGrid API key and permissions
- **Emails going to spam**: Improve your sender reputation and email content
- **High bounce rate**: Clean your email list and verify email addresses

### Common SMS Issues

- **SMS not sending**: Check your Twilio credentials and phone number
- **Invalid phone numbers**: Validate and format phone numbers correctly
- **International SMS issues**: Ensure your Twilio account has international permissions
