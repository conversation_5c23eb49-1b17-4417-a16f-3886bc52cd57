import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'hero',
  title: 'Hero Section',
  type: 'object',
  fields: [
    defineField({
      name: 'heading',
      title: 'Heading',
      type: 'string',
    }),
    defineField({
      name: 'tagline',
      title: 'Tagline',
      type: 'string',
    }),
    defineField({
      name: 'backgroundImage',
      title: 'Background Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'ctas',
      title: 'Call to Actions',
      type: 'array',
      of: [
        {
          title: 'Call to Action',
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
            },
            {
              name: 'link',
              title: 'Link',
              type: 'string',
            },
            {
              name: 'isPrimary',
              title: 'Primary Button',
              type: 'boolean',
              initialValue: false,
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'animation',
      title: 'Animation Settings',
      type: 'object',
      fields: [
        { name: 'duration', type: 'number', title: 'Duration', initialValue: 0.6 },
        { name: 'delay', type: 'number', title: 'Delay', initialValue: 0 },
        { name: 'stagger', type: 'number', title: 'Stagger Children', initialValue: 0 },
        { name: 'type', type: 'string', title: 'Type', initialValue: 'spring' }
      ]
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      subtitle: 'tagline',
      media: 'backgroundImage',
    },
    prepare({ title, subtitle, media }) {
      return {
        title: `Hero: ${title || 'Untitled'}`,
        subtitle,
        media,
      };
    },
  },
});
