import '../global.css';
import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate dynamic metadata for the home page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'The Royal Family of Africa',
    description: 'The Crown of Africa. The Rise of a New Era.',
    url: '/',
    keywords: ['Royal Family of Africa', 'King <PERSON>', 'Ghana Royalty', 'African Kingdom', 'Crown of Africa'],
  });
}

export default function UsersLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // This layout should not render html or body tags
  // as it's nested inside the root layout
  return <div suppressHydrationWarning>{children}</div>;
}
