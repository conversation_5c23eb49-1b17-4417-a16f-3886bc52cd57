/**
 * This script uploads events from a JSON file to Sanity
 * 
 * Usage:
 * 1. Create a JSON file with events data (see example below)
 * 2. Run: node scripts/upload-events-to-sanity.js
 * 
 * Example events.json:
 * [
 *   {
 *     "title": "Royal Coronation Ceremony",
 *     "slug": "royal-coronation",
 *     "date": "2024-12-15T10:00:00.000Z",
 *     "location": "Adukrom Palace, Eastern Region, Ghana",
 *     "description": "The official coronation ceremony of King <PERSON>.",
 *     "isCountdownTarget": true,
 *     "isHighlighted": true,
 *     "showRsvp": true,
 *     "eventType": "ceremony",
 *     "order": 1
 *   },
 *   ...
 * ]
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@sanity/client');
require('dotenv').config();

// Create Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  token: process.env.NEXT_PUBLIC_SANITY_API_TOKEN,
  apiVersion: '2025-05-08',
  useCdn: false,
});

// Path to the events JSON file
const eventsFilePath = path.join(__dirname, '../data/events.json');

// Hardcoded events to use if no file exists
const hardcodedEvents = [
  {
    title: 'Royal Coronation Ceremony',
    slug: 'royal-coronation',
    date: '2024-12-15T10:00:00.000Z',
    location: 'Adukrom Palace, Eastern Region, Ghana',
    description: 'The official coronation ceremony of King Allen Ellison.',
    isCountdownTarget: true,
    isHighlighted: true,
    showRsvp: true,
    eventType: 'ceremony',
    order: 1
  },
  {
    title: 'Welcome Dinner',
    slug: 'welcome-dinner',
    date: '2024-12-14T18:00:00.000Z',
    location: 'Accra, Ghana',
    description: 'A welcome dinner for all guests attending the coronation ceremony.',
    isCountdownTarget: false,
    isHighlighted: true,
    showRsvp: true,
    eventType: 'dinner',
    order: 2
  },
  {
    title: 'Cultural Festival',
    slug: 'cultural-festival',
    date: '2024-12-16T14:00:00.000Z',
    location: 'Adukrom Community Center, Ghana',
    description: 'A celebration of Ghanaian culture with music, dance, and food.',
    isCountdownTarget: false,
    isHighlighted: true,
    showRsvp: true,
    eventType: 'ceremony',
    order: 3
  }
];

// Function to upload events to Sanity
async function uploadEventsToSanity() {
  try {
    // Check if the events file exists
    let events;
    try {
      if (fs.existsSync(eventsFilePath)) {
        const fileContent = fs.readFileSync(eventsFilePath, 'utf8');
        events = JSON.parse(fileContent);
        console.log(`Loaded ${events.length} events from ${eventsFilePath}`);
      } else {
        console.log(`No events file found at ${eventsFilePath}, using hardcoded events`);
        events = hardcodedEvents;
      }
    } catch (fileError) {
      console.error('Error reading events file:', fileError);
      console.log('Using hardcoded events instead');
      events = hardcodedEvents;
    }

    // Validate Sanity token
    if (!process.env.NEXT_PUBLIC_SANITY_API_TOKEN) {
      console.error('Error: Sanity API token is missing. Please add it to your .env file.');
      process.exit(1);
    }

    console.log(`Uploading ${events.length} events to Sanity...`);

    // Upload each event to Sanity
    for (const event of events) {
      // Format the event for Sanity
      const sanityEvent = {
        _type: 'event',
        title: event.title,
        slug: { _type: 'slug', current: event.slug },
        date: event.date,
        endDate: event.endDate || undefined,
        location: event.location,
        description: event.description,
        isCountdownTarget: event.isCountdownTarget || false,
        isHighlighted: event.isHighlighted || false,
        showRsvp: event.showRsvp !== undefined ? event.showRsvp : true,
        eventType: event.eventType || 'ceremony',
        order: event.order || 0
      };

      // Check if the event already exists
      const existingEvents = await client.fetch(
        `*[_type == "event" && slug.current == $slug]`,
        { slug: event.slug }
      );

      if (existingEvents.length > 0) {
        // Update existing event
        const existingEvent = existingEvents[0];
        console.log(`Updating existing event: ${event.title}`);
        await client
          .patch(existingEvent._id)
          .set(sanityEvent)
          .commit();
      } else {
        // Create new event
        console.log(`Creating new event: ${event.title}`);
        await client.create(sanityEvent);
      }
    }

    console.log('All events uploaded successfully!');
  } catch (error) {
    console.error('Error uploading events to Sanity:', error);
    process.exit(1);
  }
}

// Run the upload function
uploadEventsToSanity();
