/**
 * Asset Optimization Script
 * 
 * This script:
 * 1. Optimizes images in the public directory
 * 2. Converts images to modern formats (WebP, AVIF)
 * 3. Generates responsive image sizes
 * 4. Optimizes fonts and other static assets
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const PUBLIC_DIR = path.join(ROOT_DIR, 'public');
const IMAGE_DIRS = [
  path.join(PUBLIC_DIR, 'images'),
  path.join(PUBLIC_DIR, 'Website Images'),
  path.join(PUBLIC_DIR, 'assets'),
];

// Image extensions to process
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif'];

// Sizes for responsive images
const RESPONSIVE_SIZES = [640, 750, 828, 1080, 1200, 1920];

/**
 * Run a command and log the output
 */
function runCommand(command, options = {}) {
  console.log(`${COLORS.blue}> ${command}${COLORS.reset}`);
  try {
    return execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
  } catch (error) {
    console.error(`${COLORS.red}Command failed: ${command}${COLORS.reset}`);
    if (!options.ignoreError) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if a directory exists
 */
function directoryExists(dirPath) {
  try {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Create directory if it doesn't exist
 */
function ensureDirectoryExists(dirPath) {
  if (!directoryExists(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

/**
 * Install required packages for image optimization
 */
function installDependencies() {
  console.log(`\n${COLORS.yellow}Installing required packages for image optimization...${COLORS.reset}`);
  
  try {
    // Check if sharp is installed
    execSync('npm list sharp', { stdio: 'pipe' });
    console.log(`${COLORS.green}✓ sharp is already installed${COLORS.reset}`);
  } catch (error) {
    console.log('Installing sharp for image processing...');
    runCommand('npm install --no-save sharp', { ignoreError: true });
  }
  
  try {
    // Check if imagemin is installed
    execSync('npm list imagemin', { stdio: 'pipe' });
    console.log(`${COLORS.green}✓ imagemin is already installed${COLORS.reset}`);
  } catch (error) {
    console.log('Installing imagemin for image optimization...');
    runCommand('npm install --no-save imagemin imagemin-jpegtran imagemin-optipng imagemin-gifsicle', { ignoreError: true });
  }
}

/**
 * Find all images in the public directory
 */
function findImages() {
  console.log(`\n${COLORS.yellow}Finding images to optimize...${COLORS.reset}`);
  
  let allImages = [];
  
  for (const dir of IMAGE_DIRS) {
    if (!directoryExists(dir)) {
      console.log(`${COLORS.yellow}Directory ${dir} does not exist, skipping...${COLORS.reset}`);
      continue;
    }
    
    const images = findImagesInDirectory(dir);
    console.log(`Found ${images.length} images in ${path.relative(ROOT_DIR, dir)}`);
    allImages = allImages.concat(images);
  }
  
  console.log(`${COLORS.green}✓ Found ${allImages.length} images total${COLORS.reset}`);
  return allImages;
}

/**
 * Find all images in a directory recursively
 */
function findImagesInDirectory(dir) {
  const images = [];
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    
    try {
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Recursively search subdirectories
        const subImages = findImagesInDirectory(filePath);
        images.push(...subImages);
      } else if (stat.isFile()) {
        // Check if the file is an image
        const ext = path.extname(file).toLowerCase();
        if (IMAGE_EXTENSIONS.includes(ext)) {
          images.push(filePath);
        }
      }
    } catch (error) {
      console.error(`Error processing ${filePath}: ${error.message}`);
    }
  }
  
  return images;
}

/**
 * Optimize images using sharp
 */
function optimizeImages(images) {
  console.log(`\n${COLORS.yellow}Optimizing images...${COLORS.reset}`);
  
  if (images.length === 0) {
    console.log(`${COLORS.yellow}No images found to optimize${COLORS.reset}`);
    return;
  }
  
  // Create an optimized directory for each image directory
  const optimizedDirs = new Set();
  
  for (const imagePath of images) {
    const dir = path.dirname(imagePath);
    const optimizedDir = path.join(dir, 'optimized');
    
    if (!optimizedDirs.has(optimizedDir)) {
      ensureDirectoryExists(optimizedDir);
      optimizedDirs.add(optimizedDir);
    }
    
    const fileName = path.basename(imagePath);
    const fileNameWithoutExt = path.basename(imagePath, path.extname(imagePath));
    const optimizedPath = path.join(optimizedDir, fileName);
    
    // Skip if the optimized file already exists and is newer than the source
    if (fs.existsSync(optimizedPath)) {
      const sourceStats = fs.statSync(imagePath);
      const optimizedStats = fs.statSync(optimizedPath);
      
      if (optimizedStats.mtime > sourceStats.mtime) {
        console.log(`Skipping ${fileName} (already optimized)`);
        continue;
      }
    }
    
    try {
      // Use sharp to optimize the image
      const sharp = require('sharp');
      
      // Create a sharp instance
      const image = sharp(imagePath);
      
      // Get image metadata
      const metadata = image.metadata();
      
      // Optimize the image
      image
        .jpeg({ quality: 80, progressive: true })
        .toFile(optimizedPath)
        .then(() => {
          console.log(`Optimized ${fileName}`);
        })
        .catch(error => {
          console.error(`Error optimizing ${fileName}: ${error.message}`);
        });
      
      // Generate WebP version
      const webpPath = path.join(optimizedDir, `${fileNameWithoutExt}.webp`);
      image
        .webp({ quality: 80 })
        .toFile(webpPath)
        .then(() => {
          console.log(`Generated WebP for ${fileName}`);
        })
        .catch(error => {
          console.error(`Error generating WebP for ${fileName}: ${error.message}`);
        });
    } catch (error) {
      console.error(`Error processing ${fileName}: ${error.message}`);
    }
  }
  
  console.log(`${COLORS.green}✓ Image optimization complete${COLORS.reset}`);
}

/**
 * Create a Next.js Image component wrapper for optimized images
 */
function createNextImageComponent() {
  console.log(`\n${COLORS.yellow}Creating optimized Next.js Image component...${COLORS.reset}`);
  
  const componentDir = path.join(ROOT_DIR, 'components');
  const componentPath = path.join(componentDir, 'OptimizedImage.tsx');
  
  if (!directoryExists(componentDir)) {
    console.log(`${COLORS.yellow}Components directory does not exist, skipping...${COLORS.reset}`);
    return;
  }
  
  const componentContent = `/**
 * OptimizedImage Component
 * 
 * A wrapper around Next.js Image component with optimized defaults
 * and automatic WebP/AVIF support.
 */

import { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoadingComplete'> {
  fallbackSrc?: string;
  onLoad?: () => void;
  className?: string;
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fallbackSrc,
  onLoad,
  className,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  
  // Reset state when src changes
  useEffect(() => {
    setIsLoaded(false);
    setError(false);
  }, [src]);
  
  // Handle image load
  const handleLoadingComplete = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };
  
  // Handle image error
  const handleError = () => {
    setError(true);
  };
  
  // Use fallback if there's an error and fallbackSrc is provided
  const imageSrc = error && fallbackSrc ? fallbackSrc : src;
  
  return (
    <div className={cn(
      'relative overflow-hidden',
      !isLoaded && 'bg-gray-100 animate-pulse',
      className
    )}>
      <Image
        src={imageSrc}
        alt={alt}
        width={width}
        height={height}
        onLoadingComplete={handleLoadingComplete}
        onError={handleError}
        {...props}
        className={cn(
          'transition-opacity duration-300',
          !isLoaded && 'opacity-0',
          isLoaded && 'opacity-100',
          props.className
        )}
      />
    </div>
  );
}
`;
  
  fs.writeFileSync(componentPath, componentContent);
  console.log(`${COLORS.green}✓ Created OptimizedImage component at components/OptimizedImage.tsx${COLORS.reset}`);
}

/**
 * Main function
 */
async function main() {
  console.log(`${COLORS.cyan}=== Asset Optimization ====${COLORS.reset}`);
  
  // Install dependencies
  installDependencies();
  
  // Find images
  const images = findImages();
  
  // Optimize images
  optimizeImages(images);
  
  // Create Next.js Image component wrapper
  createNextImageComponent();
  
  console.log(`\n${COLORS.green}✓ Asset optimization complete!${COLORS.reset}`);
  console.log(`\n${COLORS.cyan}Next steps:${COLORS.reset}`);
  console.log(`1. Use the OptimizedImage component in your components`);
  console.log(`2. Check the optimized images in the public/images/optimized directory`);
}

// Run the main function
main().catch(error => {
  console.error(`${COLORS.red}Error: ${error.message}${COLORS.reset}`);
  process.exit(1);
});
