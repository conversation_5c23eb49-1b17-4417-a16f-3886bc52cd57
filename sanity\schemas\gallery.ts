import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'gallery',
  title: 'Gallery',
  type: 'document',
  groups: [
    { name: 'content', title: 'Content' },
    { name: 'settings', title: 'Settings' },
    { name: 'animation', title: 'Animation' },
  ],
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
      group: 'content',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
      group: 'settings',
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3,
      group: 'content',
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoMetaFields',
      description: 'Search engine optimization settings',
      group: 'settings',
    }),
    defineField({
      name: 'images',
      title: 'Gallery Media',
      type: 'array',
      of: [{
        type: 'object',
        fields: [
          {
            name: 'mediaType',
            title: 'Media Type',
            type: 'string',
            options: {
              list: [
                { title: 'Image', value: 'image' },
                { title: 'Video', value: 'video' },
              ],
            },
            initialValue: 'image',
          },
          {
            name: 'image',
            title: 'Image',
            type: 'image',
            options: {
              hotspot: true,
            },
            hidden: ({ parent }) => parent?.mediaType === 'video',
          },
          {
            name: 'video',
            title: 'Video',
            type: 'file',
            options: {
              accept: 'video/mp4',
            },
            hidden: ({ parent }) => parent?.mediaType === 'image',
          },
          {
            name: 'thumbnail',
            title: 'Video Thumbnail',
            type: 'image',
            description: 'Optional thumbnail for video (will use first frame if not provided)',
            hidden: ({ parent }) => parent?.mediaType === 'image',
          },
          {
            name: 'alt',
            title: 'Alternative Text',
            type: 'string',
            description: 'Important for SEO and accessibility',
          },
          {
            name: 'caption',
            title: 'Caption',
            type: 'string',
          },
        ],
        preview: {
          select: {
            title: 'caption',
            mediaType: 'mediaType',
            image: 'image',
            video: 'video',
            thumbnail: 'thumbnail',
          },
          prepare({ title, mediaType, image, video, thumbnail }) {
            return {
              title: title || `Untitled ${mediaType === 'video' ? 'Video' : 'Image'}`,
              subtitle: mediaType === 'video' ? 'Video' : 'Image',
              media: mediaType === 'video' ? thumbnail || video : image,
            };
          },
        },
      }],
      options: {
        layout: 'grid',
      },
      group: 'content',
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [{ type: 'category' }],
      group: 'settings',
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags',
      },
      group: 'settings',
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      group: 'settings',
    }),
    defineField({
      name: 'featured',
      title: 'Featured',
      type: 'boolean',
      description: 'Mark this gallery as featured',
      initialValue: false,
      group: 'settings',
    }),
    defineField({
      name: 'displayStyle',
      title: 'Display Style',
      type: 'string',
      options: {
        list: [
          { title: 'Grid', value: 'grid' },
          { title: 'Carousel', value: 'carousel' },
          { title: 'Masonry', value: 'masonry' },
        ],
      },
      initialValue: 'grid',
      group: 'settings',
    }),
    defineField({
      name: 'backgroundStyle',
      title: 'Background Style',
      type: 'string',
      options: {
        list: [
          { title: 'None', value: 'none' },
          { title: 'Light', value: 'light' },
          { title: 'Dark', value: 'dark' },
          { title: 'Royal Blue', value: 'royalBlue' },
          { title: 'Royal Gold', value: 'royalGold' },
          { title: 'Ivory', value: 'ivory' },
        ],
      },
      initialValue: 'none',
      group: 'settings',
    }),
    defineField({
      name: 'animation',
      title: 'Animation Settings',
      type: 'object',
      fields: [
        { name: 'duration', type: 'number', title: 'Duration', initialValue: 0.6 },
        { name: 'delay', type: 'number', title: 'Delay', initialValue: 0 },
        { name: 'stagger', type: 'number', title: 'Stagger Children', initialValue: 0.2 },
        { name: 'type', type: 'string', title: 'Type', initialValue: 'spring' }
      ],
      group: 'animation',
    }),
    defineField({
      name: 'order',
      title: 'Order',
      type: 'number',
      description: 'Order in which to display this image (lower numbers appear first)',
      initialValue: 0,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      media: 'image',
      category: 'category.title',
    },
    prepare(selection) {
      const { category } = selection;
      return {
        ...selection,
        subtitle: category ? `Category: ${category}` : '',
      };
    },
  },
  orderings: [
    {
      title: 'Order Ascending',
      name: 'orderAsc',
      by: [{ field: 'order', direction: 'asc' }],
    },
    {
      title: 'Order Descending',
      name: 'orderDesc',
      by: [{ field: 'order', direction: 'desc' }],
    },
    {
      title: 'Publication Date, New',
      name: 'publishedAtDesc',
      by: [{ field: 'publishedAt', direction: 'desc' }],
    },
    {
      title: 'Publication Date, Old',
      name: 'publishedAtAsc',
      by: [{ field: 'publishedAt', direction: 'asc' }],
    },
  ],
});
