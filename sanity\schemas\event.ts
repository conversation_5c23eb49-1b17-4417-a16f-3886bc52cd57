import { defineField, defineType } from 'sanity';

export default defineType({
  name: 'event',
  title: 'Event',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Event Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'date',
      title: 'Event Date & Time',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'endDate',
      title: 'End Date & Time',
      type: 'datetime',
      description: 'Optional end date for multi-day events',
    }),
    defineField({
      name: 'location',
      title: 'Location',
      type: 'string',
      validation: (Rule) => Rule.required().min(3).max(100),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
      validation: (Rule) => Rule.required().min(10),
    }),
    defineField({
      name: 'image',
      title: 'Event Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alternative Text',
          type: 'string',
          description: 'Important for SEO and accessibility',
          validation: (Rule) => Rule.required(),
        },
        {
          name: 'caption',
          title: 'Caption',
          type: 'string',
        },
      ],
      validation: (Rule) => Rule.required().custom((value, context) => {
        // For ceremony events, image is required
        if (context.document?.eventType === 'ceremony' && !value) {
          return 'Image is required for ceremony events';
        }
        return true;
      }),
    }),
    defineField({
      name: 'isCountdownTarget',
      title: 'Use as Countdown Target',
      type: 'boolean',
      description: 'If enabled, this event will be used for the main countdown timer',
      initialValue: false,
    }),
    defineField({
      name: 'isHighlighted',
      title: 'Highlight Event',
      type: 'boolean',
      description: 'If enabled, this event will be highlighted on the website',
      initialValue: false,
    }),
    defineField({
      name: 'showRsvp',
      title: 'Show RSVP Button',
      type: 'boolean',
      description: 'If enabled, an RSVP button will be shown for this event',
      initialValue: true,
    }),
    defineField({
      name: 'eventType',
      title: 'Event Type',
      type: 'string',
      options: {
        list: [
          { title: 'Ceremony', value: 'ceremony' },
          { title: 'Dinner', value: 'dinner' },
          { title: 'Conference', value: 'conference' },
          { title: 'Meeting', value: 'meeting' },
          { title: 'Other', value: 'other' },
        ],
      },
      validation: (Rule) => Rule.required(),
      initialValue: 'other',
    }),
    defineField({
      name: 'seo',
      title: 'SEO Settings',
      type: 'seoMetaFields',
      description: 'Search engine optimization settings',
    }),
    defineField({
      name: 'order',
      title: 'Display Order',
      type: 'number',
      description: 'Events with lower numbers will be displayed first',
      initialValue: 0,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      date: 'date',
      media: 'image',
    },
    prepare(selection) {
      const { title, date, media } = selection;
      return {
        title,
        subtitle: date ? new Date(date).toLocaleDateString() : 'No date',
        media,
      };
    },
  },
});
