how about this


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Royal Court of Adukrom Kingdom | King <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        royalGold: '#D4AF37',
                        royalBlue: '#002366',
                        ivory: '#F5F5DC',
                        forestGreen: '#014421',
                        charcoal: '#111111',
                        bronze: '#AD8A56'
                    },
                    fontFamily: {
                        'serif': ['Garamond', 'Georgia', 'serif'],
                        'sans': ['Montserrat', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #F5F5DC;
        }

        .royal-gradient {
            background: linear-gradient(135deg, #002366 0%, #1C2541 100%);
        }

        .gold-gradient {
            background: linear-gradient(135deg, #D4AF37 0%, #CBA135 100%);
        }

        .hero-section {
            background-image: linear-gradient(rgba(0, 35, 102, 0.7), rgba(0, 35, 102, 0.7)), url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 1200 800'%3E%3Cdefs%3E%3Cpattern id='pattern' width='100' height='100' patternUnits='userSpaceOnUse'%3E%3Cpath d='M50 0 L100 50 L50 100 L0 50 Z' fill='none' stroke='%23D4AF37' stroke-width='1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='%23002366'/%3E%3Crect width='100%25' height='100%25' fill='url(%23pattern)' opacity='0.3'/%3E%3C/svg%3E");
            background-size: cover;
            background-position: center;
        }

        .crown-icon {
            filter: drop-shadow(0 0 5px rgba(212, 175, 55, 0.5));
        }

        .nav-link {
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #D4AF37;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .active-nav::after {
            width: 100%;
        }

        .section-title {
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            width: 60%;
            height: 3px;
            bottom: -10px;
            left: 20%;
            background-color: #D4AF37;
        }

        .event-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .royal-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .royal-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.7s ease;
        }

        .royal-button:hover::before {
            left: 100%;
        }

        .gallery-item {
            overflow: hidden;
        }

        .gallery-item img {
            transition: transform 0.5s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
        }

        .scroll-to-top {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 99;
            transition: opacity 0.3s ease;
        }

        /* Custom animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
            animation: fadeIn 1s ease forwards;
        }

        .delay-100 { animation-delay: 0.1s; }
        .delay-200 { animation-delay: 0.2s; }
        .delay-300 { animation-delay: 0.3s; }
        .delay-400 { animation-delay: 0.4s; }
        .delay-500 { animation-delay: 0.5s; }

        /* Form styling */
        .form-input {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-input:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
        }

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            overflow: auto;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.show {
            display: flex;
            opacity: 1;
        }

        .modal-content {
            animation: modalFadeIn 0.5s ease forwards;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Header & Navigation -->
    <header class="sticky top-0 z-50 royal-gradient shadow-lg">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <svg class="w-10 h-10 text-royalGold crown-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.18l7 3.12v4.7c0 4.67-3.13 8.42-7 9.88-3.87-1.46-7-5.21-7-9.88V6.3l7-3.12z"/>
                        <path d="M12 6l-2.12 2.12 2.12 2.12 2.12-2.12z"/>
                        <path d="M12 12l-2.12 2.12 2.12 2.12 2.12-2.12z"/>
                    </svg>
                    <div class="ml-3">
                        <h1 class="text-white text-lg md:text-xl font-bold">Adukrom Kingdom</h1>
                        <p class="text-royalGold text-xs md:text-sm">The Royal Court of King Allen Ellison</p>
                    </div>
                </div>

                <nav class="hidden md:flex space-x-6">
                    <a href="#home" class="nav-link text-white hover:text-royalGold transition-colors active-nav">Home</a>
                    <a href="#about" class="nav-link text-white hover:text-royalGold transition-colors">About</a>
                    <a href="#coronation" class="nav-link text-white hover:text-royalGold transition-colors">Coronation</a>
                    <a href="#initiatives" class="nav-link text-white hover:text-royalGold transition-colors">Initiatives</a>
                    <a href="#gallery" class="nav-link text-white hover:text-royalGold transition-colors">Gallery</a>
                    <a href="#contact" class="nav-link text-white hover:text-royalGold transition-colors">Contact</a>
                </nav>

                <button id="mobile-menu-button" class="md:hidden text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden hidden pt-4 pb-2 animate-fadeIn">
                <a href="#home" class="block py-2 text-white hover:text-royalGold">Home</a>
                <a href="#about" class="block py-2 text-white hover:text-royalGold">About</a>
                <a href="#coronation" class="block py-2 text-white hover:text-royalGold">Coronation</a>
                <a href="#initiatives" class="block py-2 text-white hover:text-royalGold">Initiatives</a>
                <a href="#gallery" class="block py-2 text-white hover:text-royalGold">Gallery</a>
                <a href="#contact" class="block py-2 text-white hover:text-royalGold">Contact</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-section min-h-screen flex items-center">
        <div class="container mx-auto px-4 py-20 text-center">
            <div class="animate-fadeIn opacity-0">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">Welcome to Adukrom Kingdom</h1>
                <p class="text-xl md:text-2xl text-royalGold font-semibold mb-8">The Rise of a New Era</p>
                <p class="text-lg text-white mb-12 max-w-3xl mx-auto">Join us in celebrating a new era of prosperity, heritage, and visionary leadership for our people and the global community.</p>
                <div class="flex flex-col md:flex-row justify-center gap-4">
                    <a href="#coronation" class="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Coronation Events</a>
                    <a href="#rsvp" class="royal-button bg-transparent text-white border-2 border-royalGold py-3 px-8 rounded-full hover:bg-royalGold/20 transition-colors">RSVP Now</a>
                </div>
            </div>

            <div class="mt-16 animate-fadeIn opacity-0 delay-300">
                <div class="flex justify-center space-x-4 mb-4">
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-royalGold" id="days">00</div>
                        <div class="text-sm text-white">Days</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-royalGold" id="hours">00</div>
                        <div class="text-sm text-white">Hours</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-royalGold" id="minutes">00</div>
                        <div class="text-sm text-white">Minutes</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-royalGold" id="seconds">00</div>
                        <div class="text-sm text-white">Seconds</div>
                    </div>
                </div>
                <p class="text-white">Until the Royal Coronation</p>
            </div>
        </div>
    </section>

    <!-- About the King -->
    <section id="about" class="py-20 bg-ivory">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-16 section-title">About King Allen Ellison</h2>

            <div class="flex flex-col md:flex-row gap-10 items-center">
                <div class="md:w-1/2">
                    <svg class="w-full h-auto max-w-md mx-auto" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
                        <rect width="300" height="400" fill="#002366" rx="5" />
                        <circle cx="150" cy="120" r="70" fill="#AD8A56" />
                        <path d="M150 200 L150 320 Q150 350, 180 350 L240 350 Q270 350, 270 320 L270 200 Z" fill="#AD8A56" />
                        <path d="M150 200 L150 320 Q150 350, 120 350 L60 350 Q30 350, 30 320 L30 200 Z" fill="#AD8A56" />
                        <path d="M120 100 Q150 130, 180 100 Q150 150, 120 100 Z" fill="#111111" />
                        <circle cx="130" cy="110" r="5" fill="white" />
                        <circle cx="170" cy="110" r="5" fill="white" />
                        <path d="M140 130 Q150 140, 160 130" fill="none" stroke="#111111" stroke-width="2" />
                        <path d="M100 70 Q120 50, 150 60 Q180 50, 200 70 Q220 90, 210 120 Q190 150, 150 160 Q110 150, 90 120 Q80 90, 100 70 Z" fill="none" stroke="#D4AF37" stroke-width="4" />
                        <path d="M130 60 L150 30 L170 60" fill="none" stroke="#D4AF37" stroke-width="4" />
                        <circle cx="150" cy="30" r="10" fill="#D4AF37" />
                        <circle cx="130" cy="60" r="6" fill="#D4AF37" />
                        <circle cx="170" cy="60" r="6" fill="#D4AF37" />
                        <rect x="120" y="200" width="60" height="80" fill="#014421" />
                        <rect x="135" y="200" width="30" height="80" fill="#D4AF37" />
                        <path d="M30 350 L30 380 L270 380 L270 350" fill="#111111" />
                    </svg>
                </div>

                <div class="md:w-1/2">
                    <h3 class="text-2xl font-bold text-royalBlue mb-4">Biography</h3>
                    <p class="text-charcoal mb-6">King Allen Ellison ascends to the throne of Adukrom Kingdom with a distinguished background in leadership, community development, and international relations. His Majesty brings a wealth of experience and a vision for sustainable growth that honors our rich heritage while embracing innovation.</p>

                    <h3 class="text-2xl font-bold text-royalBlue mb-4">Vision & Mission</h3>
                    <p class="text-charcoal mb-6">King Allen Ellison envisions Adukrom Kingdom as a beacon of cultural preservation, economic prosperity, and educational advancement. His mission is to unite traditional wisdom with modern development strategies to create sustainable opportunities for all citizens while strengthening our global partnerships.</p>

                    <h3 class="text-2xl font-bold text-royalBlue mb-4">Royal Heritage</h3>
                    <p class="text-charcoal mb-6">The Adukrom Kingdom traces its roots back through centuries of proud Ghanaian history. Our royal lineage has been a cornerstone of stability and cultural identity in the Eastern Region, with each monarch contributing to our rich tapestry of traditions and governance systems.</p>

                    <div class="mt-8">
                        <blockquote class="italic border-l-4 border-royalGold pl-4 py-2 text-royalBlue">
                            "Our kingdom's strength lies in honoring our ancestors while building bridges to the future. Together, we will create prosperity that respects our traditions and embraces innovation."
                            <footer class="text-right font-bold mt-2">— King Allen Ellison</footer>
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Coronation Events -->
    <section id="coronation" class="py-20 royal-gradient">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-white text-center mb-16 section-title">Royal Coronation 2023</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Event 1 -->
                <div class="event-card bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="gold-gradient p-4 text-center">
                        <h3 class="text-royalBlue text-xl font-bold">Royal Coronation Ceremony</h3>
                        <p class="text-charcoal font-medium">August 28, 2023</p>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">9:00 AM - 2:00 PM</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">Adukrom Royal Palace</span>
                        </div>
                        <p class="text-charcoal mb-6">The official coronation ceremony where King Allen Ellison will be crowned according to ancient royal traditions, with dignitaries from across Ghana and international representatives in attendance.</p>
                        <a href="#rsvp" class="royal-button block text-center bg-royalBlue text-white py-2 px-4 rounded-full hover:bg-blue-900 transition-colors">RSVP for This Event</a>
                    </div>
                </div>

                <!-- Event 2 -->
                <div class="event-card bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="gold-gradient p-4 text-center">
                        <h3 class="text-royalBlue text-xl font-bold">Royal Gala Dinner</h3>
                        <p class="text-charcoal font-medium">August 29, 2023</p>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">7:00 PM - 11:00 PM</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">Grand Ballroom, Accra</span>
                        </div>
                        <p class="text-charcoal mb-6">An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests from business, politics, and cultural spheres.</p>
                        <a href="#rsvp" class="royal-button block text-center bg-royalBlue text-white py-2 px-4 rounded-full hover:bg-blue-900 transition-colors">RSVP for This Event</a>
                    </div>
                </div>

                <!-- Event 3 -->
                <div class="event-card bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="gold-gradient p-4 text-center">
                        <h3 class="text-royalBlue text-xl font-bold">Global Economic Forum</h3>
                        <p class="text-charcoal font-medium">August 30, 2023</p>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">10:00 AM - 4:00 PM</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <svg class="w-5 h-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-charcoal">Adukrom Convention Center</span>
                        </div>
                        <p class="text-charcoal mb-6">A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Adukrom Kingdom.</p>
                        <a href="#rsvp" class="royal-button block text-center bg-royalBlue text-white py-2 px-4 rounded-full hover:bg-blue-900 transition-colors">RSVP for This Event</a>
                    </div>
                </div>
            </div>

            <div class="mt-16 text-center">
                <p class="text-white text-lg mb-6">For accommodation and travel information, please contact our Royal Hospitality Team.</p>
                <a href="#contact" class="royal-button inline-block bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Contact Hospitality Team</a>
            </div>
        </div>
    </section>

    <!-- RSVP Section -->
    <section id="rsvp" class="py-20 bg-ivory">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-16 section-title">RSVP for Royal Events</h2>

            <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-xl p-8 border border-royalGold/30">
                <form id="rsvp-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="firstName" class="block text-charcoal font-medium mb-2">First Name*</label>
                            <input type="text" id="firstName" name="firstName" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                        </div>
                        <div>
                            <label for="lastName" class="block text-charcoal font-medium mb-2">Last Name*</label>
                            <input type="text" id="lastName" name="lastName" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-charcoal font-medium mb-2">Email Address*</label>
                        <input type="email" id="email" name="email" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                    </div>

                    <div>
                        <label for="phone" class="block text-charcoal font-medium mb-2">Phone Number*</label>
                        <input type="tel" id="phone" name="phone" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                    </div>

                    <div>
                        <label for="country" class="block text-charcoal font-medium mb-2">Country*</label>
                        <select id="country" name="country" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                            <option value="">Select your country</option>
                            <option value="Ghana">Ghana</option>
                            <option value="Nigeria">Nigeria</option>
                            <option value="South Africa">South Africa</option>
                            <option value="United States">United States</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Canada">Canada</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-charcoal font-medium mb-2">Which events will you attend?*</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="event1" name="events" value="coronation" class="w-4 h-4 text-royalGold">
                                <label for="event1" class="ml-2 text-charcoal">Royal Coronation Ceremony (Aug 28)</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="event2" name="events" value="gala" class="w-4 h-4 text-royalGold">
                                <label for="event2" class="ml-2 text-charcoal">Royal Gala Dinner (Aug 29)</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="event3" name="events" value="forum" class="w-4 h-4 text-royalGold">
                                <label for="event3" class="ml-2 text-charcoal">Global Economic Forum (Aug 30)</label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-charcoal font-medium mb-2">Attendance Type*</label>
                        <div class="flex space-x-6">
                            <div class="flex items-center">
                                <input type="radio" id="physical" name="attendanceType" value="physical" class="w-4 h-4 text-royalGold" required>
                                <label for="physical" class="ml-2 text-charcoal">Physical Attendance</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="online" name="attendanceType" value="online" class="w-4 h-4 text-royalGold" required>
                                <label for="online" class="ml-2 text-charcoal">Online Attendance</label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-charcoal font-medium mb-2">Reminder Preference</label>
                        <div class="flex space-x-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="emailReminder" name="reminder" value="email" class="w-4 h-4 text-royalGold">
                                <label for="emailReminder" class="ml-2 text-charcoal">Email Reminder</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="smsReminder" name="reminder" value="sms" class="w-4 h-4 text-royalGold">
                                <label for="smsReminder" class="ml-2 text-charcoal">SMS Reminder</label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="notes" class="block text-charcoal font-medium mb-2">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Submit RSVP</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Royal Initiatives -->
    <section id="initiatives" class="py-20 royal-gradient">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-white text-center mb-16 section-title">Royal Initiatives</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Initiative 1 -->
                <div class="bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-royalBlue text-center mb-4">Education Fund</h3>
                        <p class="text-charcoal text-center mb-6">Supporting educational opportunities for youth across the Eastern Region through scholarships and school infrastructure development.</p>
                        <div class="text-center">
                            <a href="#contact" class="text-royalBlue font-medium hover:text-royalGold transition-colors">Learn More →</a>
                        </div>
                    </div>
                </div>

                <!-- Initiative 2 -->
                <div class="bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-royalBlue text-center mb-4">Healthcare Access</h3>
                        <p class="text-charcoal text-center mb-6">Improving healthcare facilities and services in rural communities through mobile clinics and medical training programs.</p>
                        <div class="text-center">
                            <a href="#contact" class="text-royalBlue font-medium hover:text-royalGold transition-colors">Learn More →</a>
                        </div>
                    </div>
                </div>

                <!-- Initiative 3 -->
                <div class="bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-royalBlue text-center mb-4">Cultural Preservation</h3>
                        <p class="text-charcoal text-center mb-6">Documenting and preserving traditional knowledge, arts, and practices through digital archives and community festivals.</p>
                        <div class="text-center">
                            <a href="#contact" class="text-royalBlue font-medium hover:text-royalGold transition-colors">Learn More →</a>
                        </div>
                    </div>
                </div>

                <!-- Initiative 4 -->
                <div class="bg-ivory rounded-lg overflow-hidden shadow-lg">
                    <div class="p-6">
                        <div class="w-16 h-16 bg-royalGold rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-royalBlue text-center mb-4">Sustainable Development</h3>
                        <p class="text-charcoal text-center mb-6">Promoting eco-friendly practices and renewable energy solutions to protect our natural resources for future generations.</p>
                        <div class="text-center">
                            <a href="#contact" class="text-royalBlue font-medium hover:text-royalGold transition-colors">Learn More →</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-16 text-center">
                <p class="text-white text-lg mb-6">Interested in partnering with us on these initiatives?</p>
                <a href="#contact" class="royal-button inline-block bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Partnership Opportunities</a>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="py-20 bg-ivory">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-16 section-title">Royal Gallery</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Gallery Item 1 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <path d="M0 100 Q200 150, 400 100 L400 300 L0 300 Z" fill="#014421" />
                        <circle cx="200" cy="100" r="50" fill="#D4AF37" />
                        <path d="M175 80 L200 50 L225 80" stroke="#002366" stroke-width="3" fill="none" />
                        <path d="M175 80 L225 80" stroke="#002366" stroke-width="3" fill="none" />
                        <path d="M150 200 Q200 150, 250 200 Q200 250, 150 200 Z" fill="#D4AF37" />
                        <rect x="180" y="200" width="40" height="100" fill="#D4AF37" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">Traditional Palace Grounds</h3>
                        <p class="text-charcoal text-sm">The historic royal palace of Adukrom Kingdom, home to generations of leadership.</p>
                    </div>
                </div>

                <!-- Gallery Item 2 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <rect x="50" y="50" width="300" height="200" fill="#F5F5DC" />
                        <rect x="75" y="75" width="250" height="150" fill="#D4AF37" />
                        <circle cx="200" cy="150" r="50" fill="#002366" />
                        <path d="M175 130 Q200 170, 225 130" stroke="#D4AF37" stroke-width="3" fill="none" />
                        <circle cx="185" cy="120" r="5" fill="#D4AF37" />
                        <circle cx="215" cy="120" r="5" fill="#D4AF37" />
                        <path d="M150 50 L200 20 L250 50" fill="#D4AF37" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">Royal Regalia Exhibition</h3>
                        <p class="text-charcoal text-sm">Sacred symbols of authority and heritage displayed during special ceremonies.</p>
                    </div>
                </div>

                <!-- Gallery Item 3 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <path d="M0 150 Q100 100, 200 150 Q300 200, 400 150 L400 300 L0 300 Z" fill="#014421" />
                        <circle cx="100" cy="200" r="30" fill="#D4AF37" />
                        <circle cx="300" cy="200" r="30" fill="#D4AF37" />
                        <rect x="50" y="50" width="300" height="100" fill="#D4AF37" />
                        <rect x="75" y="75" width="250" height="50" fill="#F5F5DC" />
                        <path d="M150 150 L200 100 L250 150 Z" fill="#D4AF37" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">Community Development Projects</h3>
                        <p class="text-charcoal text-sm">King Allen Ellison's initiatives bringing modern infrastructure to rural communities.</p>
                    </div>
                </div>

                <!-- Gallery Item 4 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <circle cx="200" cy="150" r="100" fill="#F5F5DC" />
                        <path d="M150 100 Q200 150, 250 100" stroke="#D4AF37" stroke-width="10" fill="none" />
                        <path d="M150 200 Q200 150, 250 200" stroke="#D4AF37" stroke-width="10" fill="none" />
                        <circle cx="150" cy="100" r="15" fill="#014421" />
                        <circle cx="250" cy="100" r="15" fill="#014421" />
                        <circle cx="150" cy="200" r="15" fill="#014421" />
                        <circle cx="250" cy="200" r="15" fill="#014421" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">Cultural Festival</h3>
                        <p class="text-charcoal text-sm">Annual celebration of Adukrom's rich cultural heritage through music, dance, and art.</p>
                    </div>
                </div>

                <!-- Gallery Item 5 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <rect x="100" y="50" width="200" height="200" fill="#F5F5DC" />
                        <rect x="125" y="75" width="150" height="150" fill="#D4AF37" />
                        <path d="M150 125 L250 125 M150 175 L250 175 M200 100 L200 200" stroke="#002366" stroke-width="5" />
                        <circle cx="200" cy="150" r="25" fill="#014421" />
                        <path d="M185 150 L215 150" stroke="#D4AF37" stroke-width="3" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">International Partnerships</h3>
                        <p class="text-charcoal text-sm">King Allen Ellison fostering global connections for economic development.</p>
                    </div>
                </div>

                <!-- Gallery Item 6 -->
                <div class="gallery-item rounded-lg overflow-hidden shadow-lg">
                    <svg class="w-full h-64 bg-royalBlue" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="400" height="300" fill="#002366" />
                        <path d="M0 200 Q100 150, 200 200 Q300 250, 400 200 L400 300 L0 300 Z" fill="#014421" />
                        <circle cx="200" cy="100" r="50" fill="#D4AF37" />
                        <path d="M175 100 L225 100 M200 75 L200 125" stroke="#002366" stroke-width="5" />
                        <rect x="150" y="150" width="100" height="100" fill="#F5F5DC" />
                        <path d="M150 150 L250 250 M150 250 L250 150" stroke="#D4AF37" stroke-width="3" />
                    </svg>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-royalBlue">Healthcare Initiative Launch</h3>
                        <p class="text-charcoal text-sm">Opening ceremony of the new mobile health clinic serving remote villages.</p>
                    </div>
                </div>
            </div>

            <div class="mt-12 text-center">
                <a href="#" class="royal-button inline-block bg-royalBlue text-white py-3 px-8 rounded-full hover:bg-blue-900 transition-colors">View Full Gallery</a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 royal-gradient">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-white text-center mb-16 section-title">Contact the Royal Court</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="bg-ivory rounded-lg p-8 shadow-lg">
                        <h3 class="text-2xl font-bold text-royalBlue mb-6">Get in Touch</h3>

                        <form id="contact-form" class="space-y-6">
                            <div>
                                <label for="contactName" class="block text-charcoal font-medium mb-2">Full Name*</label>
                                <input type="text" id="contactName" name="contactName" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                            </div>

                            <div>
                                <label for="contactEmail" class="block text-charcoal font-medium mb-2">Email Address*</label>
                                <input type="email" id="contactEmail" name="contactEmail" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                            </div>

                            <div>
                                <label for="contactSubject" class="block text-charcoal font-medium mb-2">Subject*</label>
                                <select id="contactSubject" name="contactSubject" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none">
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Event Information">Event Information</option>
                                    <option value="Partnership Opportunity">Partnership Opportunity</option>
                                    <option value="Media Request">Media Request</option>
                                    <option value="Support">Support</option>
                                </select>
                            </div>

                            <div>
                                <label for="contactMessage" class="block text-charcoal font-medium mb-2">Message*</label>
                                <textarea id="contactMessage" name="contactMessage" rows="4" required class="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Send Message</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div>
                    <div class="space-y-8">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-xl font-bold text-white">Email</h3>
                                <p class="text-royalGold mt-1"><EMAIL></p>
                                <p class="text-white mt-2">For official correspondence and inquiries</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-xl font-bold text-white">Phone</h3>
                                <p class="text-royalGold mt-1">+233 (0) 302 123 456</p>
                                <p class="text-white mt-2">Available Monday-Friday, 9am-5pm GMT</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-xl font-bold text-white">Address</h3>
                                <p class="text-royalGold mt-1">Royal Palace of Adukrom</p>
                                <p class="text-white mt-2">Eastern Region, Ghana</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-royalBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-xl font-bold text-white">Office Hours</h3>
                                <p class="text-royalGold mt-1">Monday-Friday: 9am-5pm</p>
                                <p class="text-white mt-2">Closed on national holidays</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-12 flex justify-center space-x-6">
                        <a href="#" class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center hover:bg-yellow-500 transition-colors">
                            <svg class="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
                            </svg>
                        </a>
                        <a href="#" class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center hover:bg-yellow-500 transition-colors">
                            <svg class="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"></path>
                            </svg>
                        </a>
                        <a href="#" class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center hover:bg-yellow-500 transition-colors">
                            <svg class="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                            </svg>
                        </a>
                        <a href="#" class="w-12 h-12 bg-royalGold rounded-full flex items-center justify-center hover:bg-yellow-500 transition-colors">
                            <svg class="w-6 h-6 text-royalBlue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-12 bg-ivory">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto bg-royalBlue rounded-lg shadow-xl p-8 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-40 h-40">
                    <svg viewBox="0 0 100 100" class="text-royalGold/20" fill="currentColor">
                        <path d="M0 0 L100 0 L100 100 Z"></path>
                    </svg>
                </div>

                <div class="relative z-10">
                    <h3 class="text-2xl font-bold text-white mb-2">Stay Connected with the Royal Court</h3>
                    <p class="text-royalGold mb-6">Subscribe to receive updates on royal events, initiatives, and announcements.</p>

                    <form id="newsletter-form" class="flex flex-col md:flex-row gap-4">
                        <input type="email" placeholder="Your email address" class="form-input flex-grow px-4 py-3 rounded-full focus:outline-none">
                        <button type="submit" class="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg">Subscribe</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="royal-gradient pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <div>
                    <div class="flex items-center mb-6">
                        <svg class="w-10 h-10 text-royalGold crown-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 2.18l7 3.12v4.7c0 4.67-3.13 8.42-7 9.88-3.87-1.46-7-5.21-7-9.88V6.3l7-3.12z"/>
                            <path d="M12 6l-2.12 2.12 2.12 2.12 2.12-2.12z"/>
                            <path d="M12 12l-2.12 2.12 2.12 2.12 2.12-2.12z"/>
                        </svg>
                        <div class="ml-3">
                            <h2 class="text-white text-lg font-bold">Adukrom Kingdom</h2>
                            <p class="text-royalGold text-sm">The Royal Court</p>
                        </div>
                    </div>
                    <p class="text-white/80 mb-6">Preserving our heritage, embracing our future, and building prosperity for all citizens under the visionary leadership of King Allen Ellison.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-royalGold hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-royalGold hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"></path>
                            </svg>
                        </a>
                        <a href="#" class="text-royalGold hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'93c3aff95265438b',t:'MTc0NjY1MjA1MS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script>