# Sanity Seeding Scripts

This directory contains scripts to help set up and manage the Sanity database with sample content.

## Seed Store Script

The `seed-store.js` script populates your Sanity database with sample store products and categories. This is useful for testing the store functionality or setting up a demo store.

## Seed News Script

The `seed-news.js` script populates your Sanity database with sample news articles and categories. This is useful for testing the news functionality or setting up demo content for the news section.

### Prerequisites

Before running the script, make sure you have:

1. Set up your Sanity project
2. Created the necessary schemas (product, category, siteSettings)
3. Added the following environment variables to your `.env.local` file:
   - `NEXT_PUBLIC_SANITY_PROJECT_ID`
   - `NEXT_PUBLIC_SANITY_DATASET`
   - `SANITY_API_TOKEN` (with write access)

### Running the Scripts

To run the store seeding script:

```bash
node scripts/seed-store.js
```

To run the news seeding script:

```bash
node scripts/seed-news.js
```

### What the Scripts Do

The store seeding script performs the following actions:

1. Creates or updates site settings with store configuration
2. Creates sample categories (Apparel, Accessories, Collectibles)
3. Creates sample products in each category
4. Checks for existing items to avoid duplicates

The news seeding script performs the following actions:

1. Creates sample news categories (Royal Announcements, Events, Culture, Community)
2. Creates sample news articles with rich text content
3. Assigns categories to news articles
4. Checks for existing items to avoid duplicates

### Customizing the Scripts

You can modify the scripts to add your own content:

1. Edit the `categories` array to add or modify categories
2. Edit the `products` or `newsArticles` array to add or modify content

### Troubleshooting

If you encounter errors:

1. Make sure your environment variables are correctly set
2. Check that your Sanity token has write permissions
3. Verify that your schemas match the data structure in the script

## Adding Images

The script uses placeholder references for images. To add actual images:

1. Upload images to your Sanity project using the Sanity Studio
2. Update the image references in the script or directly in Sanity Studio

## API Endpoints

### Store API Endpoints

The store functionality uses the following API endpoints:

- `GET /api/store/products` - Get all products
- `GET /api/store/products/[id]` - Get a specific product
- `GET /api/store/settings` - Get store settings
- `GET /api/store/orders` - Get all orders (admin only)

### News API Endpoints

The news functionality uses the following API endpoints:

- `GET /api/news` - Get all news articles
- `POST /api/news` - Create a new news article (admin only)
- `GET /api/news/search?q=query` - Search for news articles

### RSVP API Endpoints

The RSVP functionality uses the following API endpoints:

- `POST /api/rsvp` - Submit an RSVP
- `GET /api/rsvp/[id]` - Get a specific RSVP (admin only)
- `DELETE /api/rsvp/[id]` - Delete an RSVP (admin only)

These endpoints are already set up in the project and will work once you have data in your Sanity database.

## Event Reminder Script

The `send-event-reminders.js` script sends reminders to people who have RSVP'd to events. This script can be run manually or scheduled to run automatically.

### Running the Script

To run the script manually:

```bash
node scripts/send-event-reminders.js [event-code] [days-before]
```

Example:
```bash
node scripts/send-event-reminders.js coronation 7
```

This will send reminders to all attendees of the coronation event if the event is 7 days away.

### Scheduling the Script

You can schedule this script to run automatically using a cron job or a service like Heroku Scheduler:

```
# Run every day at 9:00 AM
0 9 * * * node /path/to/scripts/send-event-reminders.js coronation 7
0 9 * * * node /path/to/scripts/send-event-reminders.js coronation 1
0 9 * * * node /path/to/scripts/send-event-reminders.js gala 1
0 9 * * * node /path/to/scripts/send-event-reminders.js forum 1
```

This will send reminders 7 days before and 1 day before each event.
