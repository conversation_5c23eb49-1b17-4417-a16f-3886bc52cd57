'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { Upload, X, FileVideo, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { urlFor, fileUrlFor, isVideoFile } from '@/lib/sanity.client';
import SanityVideo from '@/components/SanityVideo';
import axios from 'axios';
import { toast } from 'sonner';

interface MediaUploaderProps {
  value: any;
  onChange: (value: any) => void;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
  label?: string;
  description?: string;
}

export default function MediaUploader({
  value,
  onChange,
  accept = "image/jpeg, image/png, image/gif, image/webp, video/mp4",
  maxSize = 50, // Default to 50MB max
  className = '',
  label = 'Upload Media',
  description = 'Upload an image or video file',
}: MediaUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Function to handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > maxSize) {
      toast.error(`File too large. Maximum size is ${maxSize}MB.`);
      return;
    }

    // Start upload
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Upload file with progress tracking
      const response = await axios.post('/api/upload', formData, {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(progress);
          }
        },
      });

      if (response.data.success) {
        toast.success(`${response.data.fileType === 'video' ? 'Video' : 'Image'} uploaded successfully`);
        onChange(response.data.asset);
      } else {
        toast.error(response.data.message || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.response?.data?.message || 'Error uploading file');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Function to remove the current media
  const handleRemove = () => {
    onChange(null);
  };

  // Determine if the current value is a video
  const isVideo = value ? isVideoFile(value) : false;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current media preview */}
      {value && (
        <div className="relative rounded-md overflow-hidden border border-gray-200">
          <div className="absolute top-2 right-2 z-10">
            <Button
              variant="destructive"
              size="icon"
              onClick={handleRemove}
              className="h-8 w-8 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {isVideo ? (
            <div className="relative aspect-video bg-gray-100">
              <SanityVideo
                value={value}
                className="w-full h-full"
                controls={true}
                autoPlay={false}
                loop={false}
              />
            </div>
          ) : (
            <div className="relative aspect-video bg-gray-100">
              <Image
                src={urlFor(value).url() || ''}
                alt="Uploaded media"
                fill
                className="object-cover"
                unoptimized={true}
              />
            </div>
          )}

          <div className="p-2 bg-gray-50 text-xs text-gray-500">
            {isVideo ? (
              <div className="flex items-center">
                <FileVideo className="h-4 w-4 mr-1" />
                <span>Video</span>
              </div>
            ) : (
              <div className="flex items-center">
                <ImageIcon className="h-4 w-4 mr-1" />
                <span>Image</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Upload controls */}
      <div className="space-y-2">
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileChange}
          className="hidden"
          id="media-upload"
        />

        {isUploading ? (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500 text-center">
              Uploading... {uploadProgress}%
            </p>
          </div>
        ) : (
          <Button
            type="button"
            variant="outline"
            className="w-full"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            <Upload className="h-4 w-4 mr-2" />
            {value ? 'Replace' : label}
          </Button>
        )}

        <p className="text-xs text-gray-500">
          {description}. Maximum size: {maxSize}MB.
        </p>
      </div>
    </div>
  );
}
