import { Suspense } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getGallery, getCategories } from '@/lib/sanity';
import GalleryClient from './GalleryClient';

// This is a server component that fetches data
export default async function GalleryPage() {
  // Fetch gallery items and categories from Sanity
  let galleryItems = [];
  let categoriesData = [];

  try {
    console.log('Fetching gallery data from Sanity...');
    [galleryItems, categoriesData] = await Promise.all([
      getGallery(),
      getCategories()
    ]);
    console.log(`Fetched ${galleryItems.length} gallery items from Sanity`);
    console.log(`Fetched ${categoriesData.length} categories from Sanity`);

    // Debug gallery items
    if (galleryItems.length > 0) {
      console.log('First gallery item:', {
        id: galleryItems[0]._id,
        title: galleryItems[0].title,
        hasImages: galleryItems[0].images && galleryItems[0].images.length > 0,
        imageCount: galleryItems[0].images ? galleryItems[0].images.length : 0,
        category: galleryItems[0].category ? galleryItems[0].category.title : 'None'
      });
    } else {
      console.log('No gallery items found');
    }

    // Debug categories
    if (categoriesData.length > 0) {
      console.log('Categories:', categoriesData.map(cat => cat.title));
    } else {
      console.log('No categories found');
    }
  } catch (error) {
    console.error('Error fetching from Sanity:', error);
    throw new Error('Failed to fetch gallery data from Sanity');
  }

  // Format categories for the filter
  // Always start with 'All' category
  // Filter out the duplicate "CULTURE" category (all caps one)
  console.log('All categories before filtering:', categoriesData.map((cat: any) =>
    `${cat.title} (${cat.slug?.current || 'no-slug'})`
  ));

  const filteredCategories = categoriesData.filter((category: any) =>
    !(category.title === "CULTURE" || category.slug?.current === "culture-oqs")
  );

  console.log('Filtered out categories:', categoriesData
    .filter((category: any) => category.title === "CULTURE" || category.slug?.current === "culture-oqs")
    .map((cat: any) => `${cat.title} (${cat.slug?.current || 'no-slug'})`));

  console.log('Remaining categories after filtering:', filteredCategories.map((cat: any) =>
    `${cat.title} (${cat.slug?.current || 'no-slug'})`
  ));

  const allCategories = [
    { id: 'all', name: 'All' },
    ...filteredCategories.map((category: any) => {
      // Make sure we have a valid slug, otherwise use the ID
      const slugId = category.slug?.current || `category-${category._id}`;
      return {
        id: slugId,
        name: category.title,
        _id: category._id // Keep the original ID for reference
      };
    })
  ];

  // Check for duplicate names and IDs and make them unique
  const uniqueCategories = [];
  const seenIds = new Set();
  const seenNames = new Set();

  for (const category of allCategories) {
    // Special case for "All" category
    if (category.id === 'all') {
      seenIds.add(category.id);
      seenNames.add(category.name.toLowerCase());
      uniqueCategories.push(category);
      continue;
    }

    let finalCategory = { ...category };
    let needsNewId = false;

    // Check for duplicate IDs
    if (seenIds.has(category.id)) {
      needsNewId = true;
    }

    // Check for duplicate names (case insensitive)
    if (seenNames.has(category.name.toLowerCase())) {
      // If this is a duplicate name, modify the display name
      const originalName = category.name;

      // If we have the original ID, use it to make a unique name
      if (category._id) {
        finalCategory.name = `${originalName} (${category._id.substring(0, 4)})`;
      } else {
        // Otherwise just append a number
        let counter = 1;
        let newName = `${originalName} (${counter})`;

        while (seenNames.has(newName.toLowerCase())) {
          counter++;
          newName = `${originalName} (${counter})`;
        }

        finalCategory.name = newName;
      }

      // Also ensure ID is unique
      needsNewId = true;
    }

    // If we need a new ID, generate one
    if (needsNewId) {
      let counter = 1;
      let newId = `${category.id}-${counter}`;

      while (seenIds.has(newId)) {
        counter++;
        newId = `${category.id}-${counter}`;
      }

      finalCategory.id = newId;
    }

    // Add to our tracking sets and final array
    seenIds.add(finalCategory.id);
    seenNames.add(finalCategory.name.toLowerCase());
    uniqueCategories.push(finalCategory);
  }

  // Log the final categories for debugging
  console.log('Final categories:', uniqueCategories.map(c => `${c.name} (${c.id})`));

  return (
    <>
      <Header isGalleryPage={true} />
      <main>
        <section className="py-20 bg-ivory">
          <div className="container mx-auto px-4">
            <div className="flex justify-center mb-4">
              <Link href="/" className="flex items-center gap-2 text-royalBlue hover:text-royalGold transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </Link>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-royalBlue text-center mb-8">
              Royal Gallery
            </h1>

            <p className="text-lg text-center max-w-3xl mx-auto mb-12">
              Explore the visual journey of Adukrom Kingdom, showcasing our rich heritage,
              development initiatives, and cultural celebrations.
            </p>

            {/* Client-side component for interactivity */}
            <Suspense fallback={<div className="text-center py-10">Loading gallery...</div>}>
              <GalleryClient galleryItems={galleryItems} categories={uniqueCategories} />
            </Suspense>

            <div className="mt-12 text-center">
              <Link href="/" className="royal-button bg-royalBlue text-white py-3 px-8 rounded-full hover:bg-blue-900 transition-colors inline-block">
                Back to Home
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
