'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ContactFormProps } from '../types';

export default function ContactForm({
  heading,
  description,
  formFields = [],
  submitButtonText = 'Submit',
  successMessage = 'Thank you for your message. We will get back to you soon.',
  backgroundStyle = 'none'
}: ContactFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Background style classes
  const bgClasses = {
    none: 'bg-white',
    light: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    royalBlue: 'bg-[#001a4d] text-white',
    royalGold: 'bg-amber-700 text-white',
    ivory: 'bg-[#f9f7e8]',
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';

    setFormData(prev => ({
      ...prev,
      [name]: isCheckbox ? (e.target as HTMLInputElement).checked : value
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    formFields.forEach(field => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label} is required`;
      }

      // Email validation
      if (field.type === 'email' && formData[field.name] && !/\S+@\S+\.\S+/.test(formData[field.name])) {
        newErrors[field.name] = 'Please enter a valid email address';
      }

      // Phone validation
      if (field.type === 'tel' && formData[field.name] && !/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(formData[field.name])) {
        newErrors[field.name] = 'Please enter a valid phone number';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Send the form data to the server to save in Sanity
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          formType: 'contact',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit form');
      }

      setIsSubmitted(true);
      setFormData({});
    } catch (error) {
      console.error('Error submitting form:', error);
      setErrors({ form: 'There was an error submitting the form. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render form field based on type
  const renderField = (field: NonNullable<ContactFormProps['formFields']>[0], index: number) => {
    if (!field) return null;

    const { name, label, type, options = [], required, placeholder } = field;

    const baseClasses = 'w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-royalBlue';
    const darkModeClasses = backgroundStyle === 'dark' || backgroundStyle === 'royalBlue' || backgroundStyle === 'royalGold'
      ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:bg-white/20'
      : 'bg-white border-gray-300 text-gray-900';

    const fieldClasses = `${baseClasses} ${darkModeClasses} ${errors[name] ? 'border-red-500' : ''}`;

    switch (type) {
      case 'textarea':
        return (
          <div className="mb-6" key={index}>
            <label className="block mb-2 font-medium">
              {label} {required && <span className="text-red-500">*</span>}
            </label>
            <textarea
              name={name}
              value={formData[name] || ''}
              onChange={handleChange}
              placeholder={placeholder}
              rows={4}
              className={fieldClasses}
              required={required}
            />
            {errors[name] && <p className="mt-1 text-red-500 text-sm">{errors[name]}</p>}
          </div>
        );

      case 'select':
        return (
          <div className="mb-6" key={index}>
            <label className="block mb-2 font-medium">
              {label} {required && <span className="text-red-500">*</span>}
            </label>
            <select
              name={name}
              value={formData[name] || ''}
              onChange={handleChange}
              className={fieldClasses}
              required={required}
            >
              <option value="">{placeholder || 'Select an option'}</option>
              {options.map((option: string, i: number) => (
                <option key={i} value={option}>{option}</option>
              ))}
            </select>
            {errors[name] && <p className="mt-1 text-red-500 text-sm">{errors[name]}</p>}
          </div>
        );

      case 'checkbox':
        return (
          <div className="mb-6" key={index}>
            <label className="flex items-center">
              <input
                type="checkbox"
                name={name}
                checked={formData[name] || false}
                onChange={handleChange}
                className="w-4 h-4 text-royalBlue"
                required={required}
              />
              <span className="ml-2">
                {label} {required && <span className="text-red-500">*</span>}
              </span>
            </label>
            {errors[name] && <p className="mt-1 text-red-500 text-sm">{errors[name]}</p>}
          </div>
        );

      case 'radio':
        return (
          <div className="mb-6" key={index}>
            <label className="block mb-2 font-medium">
              {label} {required && <span className="text-red-500">*</span>}
            </label>
            <div className="space-y-2">
              {options.map((option: string, i: number) => (
                <label key={i} className="flex items-center">
                  <input
                    type="radio"
                    name={name}
                    value={option}
                    checked={formData[name] === option}
                    onChange={handleChange}
                    className="w-4 h-4 text-royalBlue"
                    required={required}
                  />
                  <span className="ml-2">{option}</span>
                </label>
              ))}
            </div>
            {errors[name] && <p className="mt-1 text-red-500 text-sm">{errors[name]}</p>}
          </div>
        );

      default: // text, email, tel
        return (
          <div className="mb-6" key={index}>
            <label className="block mb-2 font-medium">
              {label} {required && <span className="text-red-500">*</span>}
            </label>
            <input
              type={type}
              name={name}
              value={formData[name] || ''}
              onChange={handleChange}
              placeholder={placeholder}
              className={fieldClasses}
              required={required}
            />
            {errors[name] && <p className="mt-1 text-red-500 text-sm">{errors[name]}</p>}
          </div>
        );
    }
  };

  return (
    <div className={`py-16 ${bgClasses[backgroundStyle]}`}>
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          {/* Heading */}
          {heading && (
            <motion.h2
              className="text-3xl md:text-4xl font-bold text-center mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              {heading}
            </motion.h2>
          )}

          {/* Description */}
          {description && (
            <motion.p
              className="text-lg text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              {description}
            </motion.p>
          )}

          {/* Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            {isSubmitted ? (
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg p-6 text-center">
                <svg className="w-12 h-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <h3 className="text-xl font-bold mb-2">Thank You!</h3>
                <p>{successMessage}</p>
                <button
                  className="mt-6 px-6 py-2 bg-royalBlue text-white rounded-md hover:bg-blue-700 transition-colors"
                  onClick={() => setIsSubmitted(false)}
                >
                  Send Another Message
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {formFields.map(renderField)}

                {errors.form && (
                  <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6">
                    {errors.form}
                  </div>
                )}

                <div className="text-center">
                  <button
                    type="submit"
                    className={`px-8 py-3 rounded-full font-medium transition-colors ${
                      isSubmitting
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-royalBlue text-white hover:bg-blue-700'
                    }`}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Submitting...' : submitButtonText}
                  </button>
                </div>
              </form>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
