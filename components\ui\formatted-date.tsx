'use client';

import { useState, useEffect } from 'react';
import { formatDate, formatRelativeTime, formatDateRange } from '@/lib/utils/dateUtils';

interface FormattedDateProps {
  date: string | Date | undefined | null;
  format?: string;
  fallback?: string;
  className?: string;
}

/**
 * A component that safely formats dates and prevents hydration mismatches
 */
export function FormattedDate({
  date,
  format = 'MMMM d, yyyy',
  fallback = 'Invalid date',
  className,
}: FormattedDateProps) {
  const [formattedDate, setFormattedDate] = useState<string>('');

  // Format the date on the client side to avoid hydration mismatches
  useEffect(() => {
    setFormattedDate(formatDate(date, format, fallback));
  }, [date, format, fallback]);

  // Return a placeholder during SSR to avoid hydration mismatches
  return (
    <time dateTime={date?.toString()} className={className} suppressHydrationWarning>
      {formattedDate || fallback}
    </time>
  );
}

interface FormattedRelativeTimeProps {
  date: string | Date | undefined | null;
  fallback?: string;
  className?: string;
}

/**
 * A component that safely formats relative times (e.g., "2 days ago")
 */
export function FormattedRelativeTime({
  date,
  fallback = 'Invalid date',
  className,
}: FormattedRelativeTimeProps) {
  const [formattedTime, setFormattedTime] = useState<string>('');

  // Format the relative time on the client side
  useEffect(() => {
    setFormattedTime(formatRelativeTime(date, fallback));

    // Update the relative time every minute
    const intervalId = setInterval(() => {
      setFormattedTime(formatRelativeTime(date, fallback));
    }, 60000);

    return () => clearInterval(intervalId);
  }, [date, fallback]);

  return (
    <time dateTime={date?.toString()} className={className} suppressHydrationWarning>
      {formattedTime || fallback}
    </time>
  );
}

interface FormattedDateRangeProps {
  startDate: string | Date | undefined | null;
  endDate?: string | Date | undefined | null;
  fallback?: string;
  className?: string;
}

/**
 * A component that safely formats date ranges
 */
export function FormattedDateRange({
  startDate,
  endDate,
  fallback = 'Invalid date range',
  className,
}: FormattedDateRangeProps) {
  const [formattedRange, setFormattedRange] = useState<string>('');

  // Format the date range on the client side
  useEffect(() => {
    setFormattedRange(formatDateRange(startDate, endDate, fallback));
  }, [startDate, endDate, fallback]);

  return (
    <time dateTime={startDate?.toString()} className={className} suppressHydrationWarning>
      {formattedRange || fallback}
    </time>
  );
}
