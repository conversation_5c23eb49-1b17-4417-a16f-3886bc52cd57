// Simple static file server for local testing
const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = process.env.PORT || 3000;

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Create a simple home page that shows the actual site content
app.get('/', (req, res) => {
  // Read the components we need to display
  const headerPath = path.join(__dirname, 'components', 'Header.tsx');
  const heroPath = path.join(__dirname, 'components', 'Hero.tsx');
  const footerPath = path.join(__dirname, 'components', 'Footer.tsx');
  
  let headerContent = 'Header component not found';
  let heroContent = 'Hero component not found';
  let footerContent = 'Footer component not found';
  
  try {
    if (fs.existsSync(headerPath)) {
      headerContent = fs.readFileSync(headerPath, 'utf8');
    }
    if (fs.existsSync(heroPath)) {
      heroContent = fs.readFileSync(heroPath, 'utf8');
    }
    if (fs.existsSync(footerPath)) {
      footerContent = fs.readFileSync(footerPath, 'utf8');
    }
  } catch (err) {
    console.error('Error reading component files:', err);
  }
  
  // Send a simplified HTML page that shows the site structure
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Kingdom of Adukrom - Local Preview</title>
      <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
      <style>
        body {
          font-family: 'Montserrat', sans-serif;
          margin: 0;
          padding: 0;
          background-color: #F5F5DC;
          color: #002366;
        }
        header {
          background: linear-gradient(135deg, #002366 0%, #001233 100%);
          color: white;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .logo {
          font-size: 1.5rem;
          font-weight: bold;
          color: #D4AF37;
        }
        nav ul {
          display: flex;
          list-style: none;
          gap: 1.5rem;
        }
        nav a {
          color: white;
          text-decoration: none;
          font-weight: 500;
        }
        .hero {
          min-height: 80vh;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          background: url('/images/hero-bg.jpg') center/cover no-repeat;
          position: relative;
        }
        .hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
        }
        .hero-content {
          position: relative;
          z-index: 1;
          max-width: 800px;
          padding: 2rem;
        }
        h1 {
          font-size: 3rem;
          color: #D4AF37;
          margin-bottom: 1rem;
        }
        .subtitle {
          font-size: 1.5rem;
          color: white;
          margin-bottom: 2rem;
        }
        .btn {
          display: inline-block;
          padding: 0.8rem 1.5rem;
          background-color: #D4AF37;
          color: #002366;
          text-decoration: none;
          border-radius: 4px;
          font-weight: 600;
          transition: all 0.3s ease;
        }
        .btn:hover {
          background-color: #c49c31;
          transform: translateY(-2px);
        }
        footer {
          background: linear-gradient(135deg, #002366 0%, #001233 100%);
          color: white;
          padding: 3rem 2rem;
          text-align: center;
        }
        .component-preview {
          background-color: white;
          border-radius: 8px;
          padding: 1rem;
          margin: 2rem 0;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .component-preview h3 {
          margin-top: 0;
          border-bottom: 1px solid #eee;
          padding-bottom: 0.5rem;
        }
        pre {
          background-color: #f5f5f5;
          padding: 1rem;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 0.9rem;
        }
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
        }
        .countdown {
          display: flex;
          justify-content: center;
          gap: 1rem;
          margin: 2rem 0;
        }
        .countdown-item {
          background-color: rgba(0, 35, 102, 0.8);
          color: white;
          padding: 1rem;
          border-radius: 8px;
          min-width: 80px;
          text-align: center;
        }
        .countdown-number {
          font-size: 2rem;
          font-weight: bold;
          color: #D4AF37;
        }
        .countdown-label {
          font-size: 0.8rem;
          text-transform: uppercase;
        }
      </style>
    </head>
    <body>
      <header>
        <div class="logo">Kingdom of Adukrom</div>
        <nav>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#about">About</a></li>
            <li><a href="#coronation">Coronation</a></li>
            <li><a href="#events">Events</a></li>
            <li><a href="#initiatives">Initiatives</a></li>
            <li><a href="#gallery">Gallery</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </nav>
      </header>
      
      <section class="hero" id="home">
        <div class="hero-content">
          <h1>The Royal Court of Adukrom Kingdom</h1>
          <p class="subtitle">Led by His Royal Majesty King Allen Ellison</p>
          
          <div class="countdown">
            <div class="countdown-item">
              <div class="countdown-number">365</div>
              <div class="countdown-label">Days</div>
            </div>
            <div class="countdown-item">
              <div class="countdown-number">24</div>
              <div class="countdown-label">Hours</div>
            </div>
            <div class="countdown-item">
              <div class="countdown-number">60</div>
              <div class="countdown-label">Minutes</div>
            </div>
            <div class="countdown-item">
              <div class="countdown-number">60</div>
              <div class="countdown-label">Seconds</div>
            </div>
          </div>
          
          <a href="#coronation" class="btn">Learn About the Coronation</a>
        </div>
      </section>
      
      <div class="container">
        <h2>Local Development Preview</h2>
        <p>This is a simplified preview of your Kingdom of Adukrom website. The actual Next.js application will have full functionality and styling.</p>
        
        <div class="component-preview">
          <h3>Header Component</h3>
          <pre>${headerContent.replace(/</g, '&lt;').replace(/>/g, '&gt;').substring(0, 500)}...</pre>
        </div>
        
        <div class="component-preview">
          <h3>Hero Component</h3>
          <pre>${heroContent.replace(/</g, '&lt;').replace(/>/g, '&gt;').substring(0, 500)}...</pre>
        </div>
        
        <div class="component-preview">
          <h3>Footer Component</h3>
          <pre>${footerContent.replace(/</g, '&lt;').replace(/>/g, '&gt;').substring(0, 500)}...</pre>
        </div>
      </div>
      
      <footer>
        <p>&copy; ${new Date().getFullYear()} Kingdom of Adukrom. All rights reserved.</p>
      </footer>
    </body>
    </html>
  `);
});

// Start the server
app.listen(port, () => {
  console.log(`Local preview server running at http://localhost:${port}`);
  console.log(`Open your browser to http://localhost:${port} to see the preview`);
});
