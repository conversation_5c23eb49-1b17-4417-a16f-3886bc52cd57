# Deployment Checklist

This document outlines the necessary steps and configurations required for deploying the Kingdom Adukrom website to production.

## Environment Variables

Ensure the following environment variables are set in your production environment:

### Sanity Configuration
```
NEXT_PUBLIC_SANITY_PROJECT_ID=n32kgamt
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=v2025-05-09
```

### Sanity API Tokens
```
# Read-only token for client-side queries (can be public)
NEXT_PUBLIC_SANITY_API_TOKEN="your-read-only-token"

# Full-access token for server-side operations (keep this secret)
SANITY_API_TOKEN="your-full-access-token"
```

### Application URLs
```
# Set these to your production URL
NEXT_PUBLIC_APP_URL="https://your-production-domain.com"
NEXTAUTH_URL="https://your-production-domain.com"
NEXTAUTH_URL_INTERNAL="https://your-production-domain.com"
```

### NextAuth Configuration
```
# Generate a new secret for production using: openssl rand -hex 32
NEXTAUTH_SECRET="generate-a-new-strong-secret-for-production"

# Admin credentials (use strong passwords in production)
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="strong-password-here"
SUPER_ADMIN_USERNAME="superadmin"
SUPER_ADMIN_PASSWORD="strong-password-here"
```

## Pre-Deployment Tasks

1. **Sync Users to Sanity**
   - Run the user sync script to ensure all users are in Sanity CMS:
   ```
   node scripts/sync-users-to-sanity.js
   ```

2. **Update Passwords**
   - Ensure all admin users have strong passwords set in both Sanity CMS and the local JSON file.
   - Use the admin interface to update passwords before deployment.

3. **Test Authentication**
   - Test login functionality with Sanity CMS credentials.
   - Verify that password changes are properly saved to Sanity CMS.

4. **Check API Routes**
   - Ensure all API routes are properly handling dynamic parameters.
   - Verify that API routes are using proper authentication checks.

## Post-Deployment Verification

1. **Verify Login**
   - Test login with admin credentials.
   - Test login with super admin credentials.
   - Verify that sessions persist correctly.

2. **Check Admin Dashboard**
   - Verify that all admin users are visible in the user management page.
   - Test user management functionality (create, edit, delete).

3. **Test Password Changes**
   - Change a user's password and verify that it works for subsequent logins.
   - Verify that password changes are saved to Sanity CMS.

## Troubleshooting

If login issues occur in production:

1. **Check Environment Variables**
   - Verify that all required environment variables are set correctly.

2. **Check Sanity Connection**
   - Verify that the application can connect to Sanity CMS.
   - Check that the Sanity API tokens are valid.

3. **Check User Data**
   - Run the user sync script again to ensure all users are in Sanity CMS.
   - Verify that user data is consistent between Sanity CMS and the local JSON file.

4. **Check Logs**
   - Review server logs for authentication errors.
   - Look for any API route errors related to user management.

## Security Considerations

1. **Remove Development Fallbacks**
   - Ensure all development fallbacks and hardcoded credentials are removed.

2. **Use HTTPS**
   - Ensure the site is served over HTTPS to protect authentication data.

3. **Set Secure Cookies**
   - Ensure cookies are set with secure and httpOnly flags in production.

4. **Rate Limiting**
   - Consider implementing rate limiting for login attempts to prevent brute force attacks.
