'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { Edit, Save, X, Settings } from '@/components/icons';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { getWriteClient } from '@/lib/sanity.client';

interface AdminControlsProps {
  pageId?: string;
}

export default function AdminControls({ pageId: propPageId }: AdminControlsProps) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const [isEditMode, setIsEditMode] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [pageId, setPageId] = useState<string | undefined>(propPageId);

  // Listen for pageId updates from sections
  useEffect(() => {
    const handlePageIdUpdate = (e: CustomEvent) => {
      if (e.detail && e.detail.pageId) {
        setPageId(e.detail.pageId);
      }
    };

    // Add event listener
    document.addEventListener('pageIdUpdate', handlePageIdUpdate as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('pageIdUpdate', handlePageIdUpdate as EventListener);
    };
  }, []);

  // Check if user is authenticated and has admin role
  const isAdmin = session?.user?.role === 'admin' || session?.user?.role === 'superadmin';

  // Don't show controls in admin section
  if (pathname.startsWith('/admin') || !isAdmin) {
    return null;
  }

  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);

    // Broadcast edit mode change to all sections
    if (!isEditMode) {
      document.dispatchEvent(new CustomEvent('enableEditMode'));

      // Check if we're on the home page
      if (pathname === '/') {
        toast.info('Home page editing is available in the admin dashboard. Redirecting you there...');
        setTimeout(() => {
          router.push('/admin/pages');
        }, 2000);
        return;
      }

      toast.info('Edit mode enabled. Click on any section to edit.');
    } else {
      document.dispatchEvent(new CustomEvent('disableEditMode'));
      toast.info('Edit mode disabled.');
    }
  };

  const handleSaveChanges = async () => {
    // This will be implemented to save all changes
    setIsSaving(true);

    try {
      // Collect all changes from sections
      const changes = document.querySelectorAll('[data-section-changes]');

      if (changes.length === 0) {
        toast.info('No changes to save');
        setIsSaving(false);
        return;
      }

      // Get the current page
      const client = getWriteClient();
      const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id: pageId });

      if (!page) {
        toast.error('Page not found');
        setIsSaving(false);
        return;
      }

      // Update the page builder with the changes
      const pageBuilder = [...(page.pageBuilder || [])];

      changes.forEach((change) => {
        const index = parseInt(change.getAttribute('data-section-index') || '0');
        const sectionData = JSON.parse(change.getAttribute('data-section-changes') || '{}');

        if (index >= 0 && index < pageBuilder.length) {
          pageBuilder[index] = {
            ...pageBuilder[index],
            ...sectionData
          };
        }
      });

      // Save the changes
      await client.patch(pageId)
        .set({ pageBuilder })
        .commit();

      toast.success('Changes saved successfully');

      // Disable edit mode
      setIsEditMode(false);
      document.dispatchEvent(new CustomEvent('disableEditMode'));

      // Refresh the page to show the changes
      router.refresh();
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setIsSaving(false);
    }
  };

  const goToAdminDashboard = () => {
    router.push('/admin');
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isExpanded ? (
        <div className="bg-white rounded-lg shadow-lg p-4 flex flex-col space-y-2">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium">Admin Controls</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <Button
            variant={isEditMode ? "default" : "outline"}
            size="sm"
            onClick={toggleEditMode}
            className="justify-start"
          >
            <Edit className="mr-2 h-4 w-4" />
            {pathname === '/'
              ? 'Edit Home Page (Admin)'
              : isEditMode
                ? 'Editing Mode (On)'
                : 'Enable Editing'}
          </Button>

          {isEditMode && (
            <Button
              variant="default"
              size="sm"
              onClick={handleSaveChanges}
              disabled={isSaving}
              className="justify-start"
            >
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // If we're on a dynamic page, try to find the page in the admin
              if (pathname !== '/' && pathname.split('/').length === 2) {
                const slug = pathname.substring(1);
                router.push(`/admin/pages?slug=${slug}`);
              } else {
                goToAdminDashboard();
              }
            }}
            className="justify-start"
          >
            <Settings className="mr-2 h-4 w-4" />
            {pathname === '/' ? 'Admin Dashboard' : 'Edit in Admin'}
          </Button>
        </div>
      ) : (
        <Button
          variant="default"
          size="icon"
          onClick={() => setIsExpanded(true)}
          className="h-12 w-12 rounded-full shadow-lg"
        >
          <Edit className="h-6 w-6" />
        </Button>
      )}
    </div>
  );
}
