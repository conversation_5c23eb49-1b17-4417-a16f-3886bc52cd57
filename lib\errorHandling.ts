/**
 * Centralized error handling utility
 *
 * This module provides functions for consistent error logging and handling
 * across the application.
 */

/**
 * Log an error with optional context
 */
export function logError(error: unknown, context?: string) {
  // Always log to console
  console.error(`Error${context ? ` in ${context}` : ''}:`, error);

  // In production, you could send to an error tracking service here
  // This is a placeholder for future error tracking implementation
}

/**
 * Format an error message for display
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return 'An unknown error occurred';
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: unknown, res: any) {
  logError(error, 'API');

  const message = formatErrorMessage(error);
  return res.status(500).json({ error: message });
}

/**
 * Create a user-friendly error object
 */
export function createErrorObject(error: unknown, defaultMessage = 'An error occurred'): {
  message: string;
  code?: string;
  details?: any;
} {
  // Handle Error objects
  if (error instanceof Error) {
    return {
      message: error.message || defaultMessage,
      code: (error as any).code,
      details: (error as any).details,
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return { message: error };
  }

  // Handle unknown errors
  return { message: defaultMessage };
}

/**
 * Safely execute a function and handle any errors
 */
export async function tryCatch<T>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => void
): Promise<{ data: T | null; error: Error | null }> {
  try {
    const data = await fn();
    return { data, error: null };
  } catch (error) {
    if (errorHandler) {
      errorHandler(error);
    } else {
      logError(error);
    }
    return { data: null, error: error instanceof Error ? error : new Error(String(error)) };
  }
}

/**
 * Rate limiting error
 */
export class RateLimitError extends Error {
  constructor(message = 'Too many requests', public retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}

/**
 * Authentication error
 */
export class AuthError extends Error {
  constructor(message = 'Authentication required') {
    super(message);
    this.name = 'AuthError';
  }
}

/**
 * Not found error
 */
export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

/**
 * Validation error
 */
export class ValidationError extends Error {
  constructor(message = 'Validation failed', public fields?: Record<string, string>) {
    super(message);
    this.name = 'ValidationError';
  }
}
