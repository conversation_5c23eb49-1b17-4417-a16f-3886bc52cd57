'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Save, ArrowLeft, Eye } from 'lucide-react';

import { urlFor } from '@/lib/sanity';
import { getWriteClient } from '@/lib/sanity.client';
import RichTextEditor from '@/app/(admin)/admin/components/RichTextEditor';
import SeoForm from '@/components/admin/SeoForm';
import { createClient } from '@sanity/client';

// Define types
interface Category {
  _id: string;
  title: string;
  description?: string;
  color?: string;
}

interface NewsArticle {
  _id: string;
  title: string;
  excerpt: string;
  body?: any;
  mainImage?: any;
  gallery?: Array<{
    image: any;
    alt: string;
    caption?: string;
  }>;
  category?: {
    _type: string;
    _ref: string;
  };
  publishedAt: string;
  status: string;
  slug: {
    current: string;
  };
  featured?: boolean;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    seoKeywords?: string[];
    nofollowAttributes?: boolean;
    openGraph?: {
      title?: string;
      description?: string;
      image?: any;
    };
    twitter?: {
      title?: string;
      description?: string;
      cardType?: string;
    };
  };
}

export default function EditNewsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  // Use React.use to unwrap params (fixing the warning)
  const unwrappedParams = React.use(params as any) as { id: string };
  const id = unwrappedParams.id;
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [addToGallery, setAddToGallery] = useState(false);
  const [galleryImages, setGalleryImages] = useState<Array<{
    file?: File;
    preview: string;
    alt: string;
    caption: string;
    existing?: boolean;
    _key?: string;
  }>>([]);

  // News article data
  const [newsData, setNewsData] = useState<NewsArticle>({
    _id: id,
    title: '',
    excerpt: '',
    body: [],
    mainImage: null,
    category: undefined,
    publishedAt: new Date().toISOString(),
    status: 'draft',
    slug: { current: '' },
    featured: false,
    seo: {
      metaTitle: '',
      metaDescription: '',
      seoKeywords: [],
      nofollowAttributes: false,
      openGraph: {
        title: '',
        description: '',
        image: null
      },
      twitter: {
        title: '',
        description: '',
        cardType: 'summary_large_image'
      }
    }
  });

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/categories');
        const data = await response.json();

        if (data.success && data.categories) {
          setCategories(data.categories);
        } else {
          throw new Error(data.error || 'Failed to fetch categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error('Failed to load categories');
      }
    };

    fetchCategories();
  }, []);

  // Fetch news article data
  useEffect(() => {
    const fetchNewsArticle = async () => {
      try {
        setIsLoading(true);

        // Check if SANITY_API_TOKEN is available
        console.log('SANITY_API_TOKEN available:', process.env.SANITY_API_TOKEN ? 'Yes' : 'No');

        // Try to get the Sanity client with error handling
        let client;
        try {
          client = getWriteClient();
          console.log('Successfully created Sanity write client');
        } catch (error) {
          console.error('Error creating Sanity write client:', error);

          // Use hardcoded token as fallback
          const hardcodedToken = "skZvPBz8yZn4iRP2UemnqcRDAx7bEknpdNoUhwSpL0mkffSi7B5n83PBrPUVvxww6QgsXbFDETUDESuxJmmb0l51Hzl3NNIC6YdJ3C9lEKii2Ydn3yQN4pu7se2ZwayyOSKzsjYJLp295ypq1xvM9fidravifu92uhZAx7sjYXKcfCAyGGrb";

          console.log('Creating client directly with hardcoded token');
          client = createClient({
            projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt',
            dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
            token: hardcodedToken,
            apiVersion: '2023-05-03',
            useCdn: false,
          });
        }

        // Fetch the news article by ID
        const article = await client.fetch(
          `*[_type == "news" && _id == $id][0]{
            _id,
            title,
            excerpt,
            body,
            mainImage,
            gallery[] {
              image,
              alt,
              caption
            },
            category->{_id, title},
            publishedAt,
            status,
            slug,
            featured,
            seo
          }`,
          { id }
        );

        if (!article) {
          throw new Error('News article not found');
        }

        console.log('Fetched article:', article);

        // Set image preview if article has a main image
        if (article.mainImage && article.mainImage.asset) {
          const imageUrl = urlFor(article.mainImage).url();
          if (imageUrl) {
            setImagePreview(imageUrl);
          }
        }

        // Initialize gallery images if they exist
        if (article.gallery && Array.isArray(article.gallery)) {
          const existingGalleryImages = article.gallery.map((item, index) => ({
            preview: urlFor(item.image).url(),
            alt: item.alt || '',
            caption: item.caption || '',
            existing: true,
            _key: `existing-${index}`
          }));
          setGalleryImages(existingGalleryImages);
        }

        // Handle body content - ensure it's in the correct format for the editor
        let bodyContent = article.body;

        console.log('Original body content type:', typeof article.body);
        console.log('Is array?', Array.isArray(article.body));

        if (article.body) {
          console.log('Body content sample:', JSON.stringify(article.body).substring(0, 100) + '...');
        }

        // If body is a string (from older articles created with the simple editor),
        // convert it to Portable Text format
        if (typeof article.body === 'string') {
          console.log('Converting string body to Portable Text format');
          bodyContent = [{
            _type: 'block',
            _key: `body-${Date.now()}`,
            style: 'normal',
            markDefs: [],
            children: [
              {
                _type: 'span',
                _key: `text-${Date.now()}`,
                text: article.body,
                marks: []
              }
            ]
          }];
        } else if (!article.body || !Array.isArray(article.body)) {
          // If body is missing or not in expected format, initialize with empty array
          console.log('Creating empty body array');
          bodyContent = [];
        } else if (Array.isArray(article.body) && article.body.length === 0) {
          // If body is an empty array, create a default block
          console.log('Creating default body block for empty array');
          bodyContent = [{
            _type: 'block',
            _key: `body-${Date.now()}`,
            style: 'normal',
            markDefs: [],
            children: [
              {
                _type: 'span',
                _key: `text-${Date.now()}`,
                text: article.excerpt || '',
                marks: []
              }
            ]
          }];
        }

        // Update the news data state
        setNewsData({
          ...article,
          body: bodyContent,
          category: article.category ? {
            _type: 'reference',
            _ref: article.category._id
          } : undefined
        });

      } catch (error) {
        console.error('Error fetching news article:', error);
        toast.error('Failed to load news article');
        router.push('/admin/news');
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsArticle();
  }, [id, router]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewsData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle category change
  const handleCategoryChange = (value: string) => {
    setNewsData(prev => ({
      ...prev,
      category: (value && value !== 'none') ? {
        _type: 'reference',
        _ref: value
      } : undefined
    }));
  };

  // Handle featured toggle
  const handleFeaturedToggle = (checked: boolean) => {
    setNewsData(prev => ({
      ...prev,
      featured: checked
    }));
  };

  // Handle status change
  const handleStatusChange = (value: string) => {
    setNewsData(prev => ({
      ...prev,
      status: value
    }));
  };

  // Handle SEO data changes
  const handleSeoChange = (seoData: any) => {
    setNewsData(prev => ({
      ...prev,
      seo: seoData
    }));
  };

  // Handle image change
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target) {
          setImagePreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle gallery image upload
  const handleGalleryImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);

      files.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target) {
            const newGalleryImage = {
              file,
              preview: event.target.result as string,
              alt: '',
              caption: '',
              existing: false,
              _key: `new-${Date.now()}-${Math.random()}`
            };
            setGalleryImages(prev => [...prev, newGalleryImage]);
          }
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Update gallery image metadata
  const updateGalleryImage = (index: number, field: 'alt' | 'caption', value: string) => {
    setGalleryImages(prev => prev.map((img, i) =>
      i === index ? { ...img, [field]: value } : img
    ));
  };

  // Remove gallery image
  const removeGalleryImage = (index: number) => {
    setGalleryImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);


      // Note: We're using the API endpoint instead of direct Sanity client
      // This avoids client-side token issues and provides better error handling

      // Prepare the news data for saving
      const articleData: any = {
        title: newsData.title,
        excerpt: newsData.excerpt,
        category: newsData.category,
        status: newsData.status,
        featured: newsData.featured,
        slug: newsData.slug,
        publishedAt: newsData.publishedAt,
        seo: newsData.seo
      };

      // Ensure body is in the correct Portable Text format
      console.log('Preparing body for save, type:', typeof newsData.body);
      console.log('Is array?', Array.isArray(newsData.body));

      if (newsData.body) {
        // If it's already an array (Portable Text format), use it directly
        if (Array.isArray(newsData.body)) {
          console.log('Using array body directly');

          // Make sure each block has required properties
          articleData.body = newsData.body.map(block => {
            // If it's not a proper block, create one
            if (!block._type || !block._key) {
              return {
                _type: 'block',
                _key: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                style: 'normal',
                markDefs: [],
                children: [
                  {
                    _type: 'span',
                    _key: `span-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    text: typeof block === 'string' ? block : (block.text || ''),
                    marks: []
                  }
                ]
              };
            }

            // If it's a proper block but missing children, add them
            if (!block.children || !Array.isArray(block.children) || block.children.length === 0) {
              return {
                ...block,
                children: [
                  {
                    _type: 'span',
                    _key: `span-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    text: '',
                    marks: []
                  }
                ]
              };
            }

            return block;
          });
        }
        // If it's a string, convert it to Portable Text format
        else if (typeof newsData.body === 'string') {
          console.log('Converting string body to Portable Text format');
          articleData.body = [{
            _type: 'block',
            _key: `body-${Date.now()}`,
            style: 'normal',
            markDefs: [],
            children: [
              {
                _type: 'span',
                _key: `text-${Date.now()}`,
                text: newsData.body,
                marks: []
              }
            ]
          }];
        }
      } else {
        // If body is missing, initialize with empty array
        console.log('Creating empty body array');
        articleData.body = [];
      }

      // If body is still empty, create a default block with the excerpt
      if (!articleData.body || (Array.isArray(articleData.body) && articleData.body.length === 0)) {
        console.log('Creating default body with excerpt');
        articleData.body = [{
          _type: 'block',
          _key: `body-${Date.now()}`,
          style: 'normal',
          markDefs: [],
          children: [
            {
              _type: 'span',
              _key: `text-${Date.now()}`,
              text: newsData.excerpt || 'No content available for this article.',
              marks: []
            }
          ]
        }];
      }

      // Include mainImage if it exists and no new image is being uploaded
      if (newsData.mainImage && !imageFile) {
        articleData.mainImage = newsData.mainImage;
      }

      console.log('Saving article to API:', articleData);

      // Handle image upload if there's a new image
      if (imageFile) {
        console.log('Uploading new image to Sanity...');

        // Create FormData for image upload
        const imageFormData = new FormData();
        imageFormData.append('image', imageFile);

        // Upload the image first
        try {
          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: imageFormData,
          });

          if (!uploadResponse.ok) {
            throw new Error('Failed to upload image');
          }

          const uploadResult = await uploadResponse.json();

          if (uploadResult.success && uploadResult.imageAsset) {
            console.log('Image uploaded successfully:', uploadResult.imageAsset);

            // Add the image asset to the article data
            articleData.mainImage = {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: uploadResult.imageAsset._id
              }
            };
          }
        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast.error('Failed to upload image. Saving article without new image.');
        }
      }

      // Update the article using the API
      const response = await fetch(`/api/news/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(articleData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to update news article');
      }

      console.log('Article saved successfully');

      // If addToGallery is checked and we have an image, add it to the gallery
      if (addToGallery && (imageFile || (newsData.mainImage && newsData.mainImage.asset))) {
        try {
          console.log('Adding image to gallery...');

          // Prepare the gallery item data
          const galleryData: any = {
            title: newsData.title,
            description: newsData.excerpt,
            category: newsData.category,
            mainImage: newsData.mainImage,
            fromArticle: {
              _type: 'reference',
              _ref: newsData._id
            }
          };

          // If we have a new image file, we need to use the uploaded image reference
          if (imageFile && articleData.mainImage) {
            galleryData.mainImage = articleData.mainImage;
          }

          // Add the image to the gallery
          const galleryResponse = await fetch('/api/gallery/from-article', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(galleryData),
          });

          const galleryResult = await galleryResponse.json();

          if (galleryResponse.ok && galleryResult.success) {
            console.log('Image added to gallery successfully');
            toast.success('News article saved and image added to gallery');
          } else {
            console.error('Failed to add image to gallery:', galleryResult.message);
            toast.error('News article saved but failed to add image to gallery');
          }
        } catch (galleryError) {
          console.error('Error adding image to gallery:', galleryError);
          toast.error('News article saved but failed to add image to gallery');
        }
      } else {
        toast.success('News article saved successfully');
      }

      router.push('/admin/news');
    } catch (error) {
      console.error('Error saving news article:', error);
      toast.error('Failed to save news article: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="ml-3 text-lg">Loading article...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Edit News Article</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <a href={`/news/${newsData.slug.current}`} target="_blank" rel="noopener noreferrer">
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </a>
          </Button>
        </div>
      </div>

      <Separator />

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Article Details</CardTitle>
            <CardDescription>
              Edit the details for your news article.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="seo">SEO & Social Sharing</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="Enter article title"
                    value={newsData.title}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    name="excerpt"
                    placeholder="Brief summary of the article"
                    value={newsData.excerpt}
                    onChange={handleChange}
                    rows={2}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={newsData.category?._ref || 'none'}
                    onValueChange={handleCategoryChange}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category._id} value={category._id}>
                          {category.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={newsData.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={newsData.featured || false}
                    onCheckedChange={handleFeaturedToggle}
                  />
                  <Label htmlFor="featured">Featured Article</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="addToGallery"
                    checked={addToGallery}
                    onCheckedChange={setAddToGallery}
                  />
                  <Label htmlFor="addToGallery">Add Image to Gallery</Label>
                  <span className="text-xs text-muted-foreground ml-2">(Image will be added to the gallery when saved)</span>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">Featured Image</Label>
                  <div className="flex flex-col gap-4">
                    {imagePreview && (
                      <div className="relative w-full h-48 bg-gray-100 rounded-md overflow-hidden">
                        <Image
                          src={imagePreview}
                          alt="Featured image preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <Input
                      id="image"
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </div>
                </div>

                <div className="space-y-2 mt-6">
                  <RichTextEditor
                    value={newsData.body || []}
                    onChange={(newBody) => {
                      console.log('RichTextEditor onChange:', newBody);
                      setNewsData(prev => ({
                        ...prev,
                        body: newBody
                      }));
                    }}
                    label="Article Content"
                    placeholder="Write your article content here..."
                  />
                </div>
              </TabsContent>

              <TabsContent value="seo" className="pt-4">
                <SeoForm
                  title={newsData.title}
                  description={newsData.excerpt}
                  slug={newsData.slug?.current || ''}
                  image={imagePreview || newsData.mainImage}
                  contentType="article"
                  seoData={newsData.seo}
                  onSeoChange={handleSeoChange}
                  onImageUpload={async (file) => {
                    try {
                      // Create FormData for image upload
                      const imageFormData = new FormData();
                      imageFormData.append('image', file);

                      // Upload the image
                      const uploadResponse = await fetch('/api/upload', {
                        method: 'POST',
                        body: imageFormData,
                      });

                      if (!uploadResponse.ok) {
                        throw new Error('Failed to upload image');
                      }

                      const uploadResult = await uploadResponse.json();

                      if (uploadResult.success && (uploadResult.asset || uploadResult.imageAsset)) {
                        const asset = uploadResult.asset || uploadResult.imageAsset;
                        console.log('Image uploaded successfully:', asset);

                        // Return the image asset reference
                        return {
                          _type: 'image',
                          asset: {
                            _type: 'reference',
                            _ref: asset._id
                          }
                        };
                      } else {
                        throw new Error('Failed to get image asset');
                      }
                    } catch (error) {
                      console.error('Error uploading SEO image:', error);
                      throw error;
                    }
                  }}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Article
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
