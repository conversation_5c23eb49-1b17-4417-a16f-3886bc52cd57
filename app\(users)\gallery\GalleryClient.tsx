'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { urlFor, fileUrlFor, isVideoFile } from '@/lib/sanity.client';
import Image from 'next/image';
import { FileVideo, Play, Pause } from 'lucide-react';
import VideoPlayer from '@/components/VideoPlayer';

interface Category {
  id: string;
  name: string;
}

interface MediaItem {
  mediaType?: 'image' | 'video';
  image?: any;
  video?: any;
  thumbnail?: any;
  alt?: string;
  caption?: string;
}

interface GalleryItem {
  _id: string;
  title: string;
  description: string;
  image?: any;
  images?: MediaItem[];
  category?: {
    title: string;
    slug: {
      current: string;
    };
  };
  tags?: string[];
}

interface GalleryClientProps {
  galleryItems: GalleryItem[];
  categories: Category[];
}

// Direct video preview component
function DirectVideoPreview({
  src,
  poster,
  title,
  onClick
}: {
  src: string;
  poster: string;
  title: string;
  onClick: () => void;
}) {
  // Log the video source for debugging
  useEffect(() => {
    console.log('DirectVideoPreview: Video source URL:', src);
    console.log('DirectVideoPreview: Poster URL:', poster);
  }, [src, poster]);

  // Handle click to open the lightbox
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onClick();
  };

  return (
    <div className="relative w-full h-full bg-black overflow-hidden">
      {/* Video container with fixed aspect ratio */}
      <div className="relative w-full h-full flex items-center justify-center">
        <video
          className="w-full h-full object-contain"
          src={src}
          poster={poster || '/images/video-thumbnail.png'}
          controls
          preload="auto"
          onError={(e) => {
            console.error('Video error:', e);
          }}
        />
      </div>

      {/* Video badge */}
      <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-md z-10">
        Video
      </div>

      {/* Fullscreen button */}
      <div
        className="absolute bottom-2 right-2 bg-black/50 text-white p-2 rounded-full cursor-pointer z-10 hover:bg-black/70"
        onClick={handleClick}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"></path>
        </svg>
      </div>
    </div>
  );
}

export default function GalleryClient({ galleryItems, categories }: GalleryClientProps) {
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);
  const [selectedMediaAlt, setSelectedMediaAlt] = useState<string>('');
  const [selectedMediaType, setSelectedMediaType] = useState<'image' | 'video'>('image');
  const [filter, setFilter] = useState('all');

  // Filter items based on selected category
  const filteredItems = filter === 'all'
    ? galleryItems
    : galleryItems.filter((item: any) => {
        if (item.category?.slug?.current) {
          return item.category.slug.current === filter;
        }
        // If filter is not 'all' and item has no category, don't include it
        return false;
      });

  // Log the number of items for debugging
  console.log(`Displaying ${filteredItems.length} gallery items out of ${galleryItems.length} total items`);
  console.log('Current filter:', filter);

  return (
    <>
      <motion.div
        className="flex flex-wrap justify-center gap-4 mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {categories.map((category) => (
          <motion.button
            key={category.id}
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
              filter === category.id
                ? 'bg-royalGold text-royalBlue'
                : 'bg-white text-royalBlue border border-royalBlue hover:bg-royalBlue hover:text-white'
            }`}
            onClick={() => setFilter(category.id)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {category.name}
          </motion.button>
        ))}
      </motion.div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        layout
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <AnimatePresence>
          {filteredItems.map((item: any, index: number) => {
            // Handle Sanity media (images or videos)
            let mediaUrl = '';
            let mediaAlt = item.title;
            let mediaType: 'image' | 'video' = 'image';
            let thumbnailUrl = '';

            try {
              // First, check if this item has images array with media
              if (item.images && item.images.length > 0) {
                const mediaItem = item.images[0];

                // Check if this is a video
                if (mediaItem.mediaType === 'video' && mediaItem.video) {
                  mediaType = 'video';

                  // Try to get the video URL using different methods
                  try {
                    // First try using fileUrlFor
                    mediaUrl = fileUrlFor(mediaItem.video);
                    console.log('Video URL generated with fileUrlFor:', mediaUrl);

                    // If the video has a direct URL property, use that instead
                    if (mediaItem.video.asset && mediaItem.video.asset.url) {
                      mediaUrl = mediaItem.video.asset.url;
                      console.log('Using direct video URL from asset:', mediaUrl);
                    }

                    // If we have a _ref, try to construct a direct CDN URL
                    if (mediaItem.video.asset && mediaItem.video.asset._ref) {
                      const ref = mediaItem.video.asset._ref;
                      if (typeof ref === 'string' && ref.startsWith('file-')) {
                        // Format: file-{fileId}-{format}
                        const parts = ref.split('-');
                        if (parts.length >= 3) {
                          const fileId = parts[1];
                          const format = parts[2];
                          const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'n32kgamt';
                          const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production';
                          const directUrl = `https://cdn.sanity.io/files/${projectId}/${dataset}/${fileId}.${format}`;
                          console.log('Constructed direct CDN URL:', directUrl);
                          mediaUrl = directUrl;
                        }
                      }
                    }
                  } catch (error) {
                    console.error('Error generating video URL:', error);
                    // Fallback to a placeholder or error state
                    mediaUrl = '';
                  }

                  mediaAlt = mediaItem.alt || item.title;

                  // Always use a fixed thumbnail for videos to ensure consistency
                  thumbnailUrl = '/images/video-thumbnail.png';

                  // If we have a thumbnail in the data, use that instead
                  if (mediaItem.thumbnail) {
                    try {
                      thumbnailUrl = urlFor(mediaItem.thumbnail).url();
                      console.log('Using custom thumbnail:', thumbnailUrl);
                    } catch (e) {
                      console.error('Error getting thumbnail URL:', e);
                    }
                  }
                }
                // Otherwise treat as image
                else if (mediaItem.image) {
                  mediaType = 'image';
                  mediaUrl = urlFor(mediaItem.image).url();
                  mediaAlt = mediaItem.alt || item.title;
                }
              }
              // Check if this is a legacy format with direct image
              else if (item.image && item.image.asset) {
                mediaType = 'image';
                mediaUrl = urlFor(item.image).url();
                mediaAlt = item.image.alt || item.title;
              }
              // Fallback to placeholder if no valid media found
              else {
                console.warn(`No valid media found for gallery item: ${item.title}`);
                mediaType = 'image';
                mediaUrl = '/images/ai-logo1a.png';
              }
            } catch (error) {
              console.error('Error generating Sanity media URL:', error);
              mediaType = 'image';
              mediaUrl = '/images/ai-logo1a.png';
            }

            return (
              <motion.div
                key={item._id}
                className="gallery-item rounded-lg overflow-hidden shadow-lg cursor-pointer"
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px rgba(0, 0, 0, 0.2)",
                  transition: { duration: 0.3 }
                }}
                onClick={() => {
                  setSelectedMedia(mediaUrl);
                  setSelectedMediaAlt(mediaAlt);
                  setSelectedMediaType(mediaType);
                }}
              >
                <div className="relative aspect-[4/3] overflow-hidden">
                  {mediaType === 'image' ? (
                    <Image
                      src={mediaUrl}
                      alt={mediaAlt}
                      width={400}
                      height={300}
                      className="w-full h-full object-contain bg-gray-100"
                      onError={(e) => {
                        // If image fails to load, use placeholder
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/ai-logo1a.png';
                      }}
                    />
                  ) : (
                    <DirectVideoPreview
                      src={mediaUrl}
                      poster={thumbnailUrl}
                      title={mediaAlt}
                      onClick={() => {
                        setSelectedMedia(mediaUrl);
                        setSelectedMediaAlt(mediaAlt);
                        setSelectedMediaType(mediaType);
                      }}
                    />
                  )}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-t from-black/90 via-royalBlue/60 to-transparent"
                    initial={{ opacity: 0.7 }}
                    whileHover={{ opacity: 0.8 }}
                  />
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <h3 className="text-lg font-bold text-white drop-shadow-md">{item.title}</h3>
                    <p className="text-sm text-royalGold font-medium drop-shadow-md">{item.description}</p>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {item.category && (
                        <span className="inline-block px-3 py-1 bg-royalGold/50 rounded-full text-xs text-white font-medium drop-shadow-md">
                          {item.category.title}
                        </span>
                      )}
                      {mediaType === 'video' && (
                        <span className="inline-block px-3 py-1 bg-red-500/50 rounded-full text-xs text-white font-medium drop-shadow-md">
                          Video
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </motion.div>

      {/* Lightbox */}
      <AnimatePresence>
        {selectedMedia && (
          <motion.div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedMedia(null)}
          >
            <motion.div
              className="relative max-w-5xl max-h-[90vh]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 25 }}
              onClick={(e) => e.stopPropagation()}
            >
              {selectedMediaType === 'image' ? (
                <Image
                  src={selectedMedia}
                  alt={selectedMediaAlt}
                  width={1200}
                  height={800}
                  className="max-h-[90vh] max-w-full object-contain"
                  onError={(e) => {
                    // If image fails to load, use placeholder
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/ai-logo1a.png';
                  }}
                />
              ) : (
                <div className="relative max-h-[90vh] max-w-[90vw] bg-black rounded-lg overflow-hidden">
                  {selectedMedia ? (
                    <div className="relative w-full max-w-[90vw] flex items-center justify-center" style={{ height: '80vh' }}>
                      <video
                        key={selectedMedia} // Force re-render when source changes
                        className="max-w-full h-auto max-h-[80vh] object-contain"
                        src={selectedMedia}
                        controls
                        autoPlay
                        playsInline
                        onError={(e) => {
                          console.error('Lightbox video error:', e);
                        }}
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-[50vh] w-[80vw]">
                      <p className="text-white">No video source available</p>
                    </div>
                  )}
                </div>
              )}
              <motion.button
                className="absolute top-4 right-4 w-10 h-10 rounded-full bg-royalGold text-royalBlue flex items-center justify-center"
                onClick={() => setSelectedMedia(null)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                ✕
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
