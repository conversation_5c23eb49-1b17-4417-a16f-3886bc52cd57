'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Save, X, Image as ImageIcon } from 'lucide-react';
import { getWriteClient } from '@/lib/sanity.client';

interface SectionEditorProps {
  pageId: string;
  section: any;
  sectionIndex: number;
  onSave: () => void;
  onCancel: () => void;
}

export default function SectionEditor({ pageId, section, sectionIndex, onSave, onCancel }: SectionEditorProps) {
  const [sectionData, setSectionData] = useState<any>(section || {});
  const [isSaving, setIsSaving] = useState(false);

  // Initialize section data if it's empty
  useEffect(() => {
    if (!section || Object.keys(section).length === 0) {
      // Set default section data based on type
      setSectionData({
        _type: 'textSection',
        heading: '',
        text: '',
        animation: {
          duration: 0.6,
          delay: 0,
          stagger: 0.2,
          type: 'spring'
        }
      });
    } else {
      // Ensure animation settings exist
      const sectionWithDefaults = {
        ...section,
        animation: section.animation || {
          duration: 0.6,
          delay: 0,
          stagger: 0.2,
          type: 'spring'
        }
      };
      setSectionData(sectionWithDefaults);
    }
  }, [section]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSectionData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setSectionData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setSectionData((prev: any) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle section type change
  const handleSectionTypeChange = (type: string) => {
    // Create a new section data object based on the selected type
    let newSectionData: any = {
      _type: type,
    };

    // Preserve animation settings if they exist
    if (sectionData.animation) {
      newSectionData.animation = sectionData.animation;
    } else {
      // Add default animation settings
      newSectionData.animation = {
        duration: 0.6,
        delay: 0,
        stagger: 0.2,
        type: 'spring'
      };
    }

    // Add default fields based on section type
    switch (type) {
      case 'hero':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          tagline: sectionData.tagline || '',
          ctas: sectionData.ctas || [],
        };
        break;
      case 'textSection':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          backgroundStyle: sectionData.backgroundStyle || 'none',
        };
        break;
      case 'imageGallery':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          images: sectionData.images || [],
        };
        break;
      case 'featuredContent':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          items: sectionData.items || [],
          layout: sectionData.layout || 'cards',
          backgroundStyle: sectionData.backgroundStyle || 'none',
        };
        break;
      case 'contactForm':
        newSectionData = {
          ...newSectionData,
          heading: sectionData.heading || '',
          text: sectionData.text || '',
          submitButtonText: sectionData.submitButtonText || 'Submit',
          successMessage: sectionData.successMessage || 'Thank you for your message. We will get back to you soon.',
          backgroundStyle: sectionData.backgroundStyle || 'none',
        };
        break;
      default:
        break;
    }

    setSectionData(newSectionData);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Get the Sanity client
      const client = getWriteClient();

      // Get the current page
      const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id: pageId });

      if (!page) {
        toast.error('Page not found');
        setIsSaving(false);
        return;
      }

      // Ensure animation settings are properly set
      if (!sectionData.animation) {
        sectionData.animation = {
          duration: 0.6,
          delay: 0,
          stagger: 0.2,
          type: 'spring'
        };
      }

      // Get the current page builder array
      const pageBuilder = page.pageBuilder || [];

      // Update or add the section
      if (sectionIndex >= 0 && sectionIndex < pageBuilder.length) {
        // Update existing section
        pageBuilder[sectionIndex] = sectionData;
      } else {
        // Add new section
        pageBuilder.push(sectionData);
      }

      // Update the page
      await client.patch(pageId)
        .set({ pageBuilder })
        .commit();

      toast.success('Section saved successfully');
      onSave();
    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section');
    } finally {
      setIsSaving(false);
    }
  };

  // Render animation settings
  const renderAnimationSettings = () => {
    return (
      <div className="space-y-4 mt-6 p-4 border rounded-md bg-gray-50">
        <h3 className="font-medium">Animation Settings</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="animation.duration">Duration</Label>
            <Input
              id="animation.duration"
              type="number"
              step="0.1"
              value={sectionData.animation?.duration || 0.6}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                setSectionData((prev: any) => ({
                  ...prev,
                  animation: {
                    ...prev.animation,
                    duration: value,
                  },
                }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="animation.delay">Delay</Label>
            <Input
              id="animation.delay"
              type="number"
              step="0.1"
              value={sectionData.animation?.delay || 0}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                setSectionData((prev: any) => ({
                  ...prev,
                  animation: {
                    ...prev.animation,
                    delay: value,
                  },
                }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="animation.stagger">Stagger</Label>
            <Input
              id="animation.stagger"
              type="number"
              step="0.1"
              value={sectionData.animation?.stagger || 0.2}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                setSectionData((prev: any) => ({
                  ...prev,
                  animation: {
                    ...prev.animation,
                    stagger: value,
                  },
                }));
              }}
            />
          </div>
          <div>
            <Label htmlFor="animation.type">Type</Label>
            <Select
              value={sectionData.animation?.type || 'spring'}
              onValueChange={(value) => {
                setSectionData((prev: any) => ({
                  ...prev,
                  animation: {
                    ...prev.animation,
                    type: value,
                  },
                }));
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Animation type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="spring">Spring</SelectItem>
                <SelectItem value="tween">Tween</SelectItem>
                <SelectItem value="inertia">Inertia</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    );
  };

  // Render form fields based on section type
  const renderSectionFields = () => {
    switch (sectionData._type) {
      case 'hero':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={sectionData.tagline || ''}
                onChange={handleInputChange}
                placeholder="Enter tagline"
              />
            </div>
            {renderAnimationSettings()}
          </div>
        );
      case 'textSection':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Text Content</Label>
              <Textarea
                id="text"
                name="text"
                value={typeof sectionData.text === 'string'
                  ? sectionData.text
                  : Array.isArray(sectionData.text)
                    ? JSON.stringify(sectionData.text)
                    : ''}
                onChange={(e) => {
                  try {
                    // Try to parse as JSON if it looks like JSON
                    if (e.target.value.trim().startsWith('[') && e.target.value.trim().endsWith(']')) {
                      const parsedValue = JSON.parse(e.target.value);
                      setSectionData((prev: any) => ({
                        ...prev,
                        text: parsedValue,
                      }));
                    } else {
                      // Otherwise treat as plain text
                      setSectionData((prev: any) => ({
                        ...prev,
                        text: e.target.value,
                      }));
                    }
                  } catch (error) {
                    // If JSON parsing fails, just use the raw text
                    setSectionData((prev: any) => ({
                      ...prev,
                      text: e.target.value,
                    }));
                  }
                }}
                placeholder="Enter text content"
                rows={8}
              />
            </div>
            <div>
              <Label htmlFor="backgroundStyle">Background Style</Label>
              <Select
                value={sectionData.backgroundStyle || 'none'}
                onValueChange={(value) => handleSelectChange('backgroundStyle', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select background style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="royalBlue">Royal Blue</SelectItem>
                  <SelectItem value="royalGold">Royal Gold</SelectItem>
                  <SelectItem value="ivory">Ivory</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {renderAnimationSettings()}
          </div>
        );
      case 'imageGallery':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Description</Label>
              <Textarea
                id="text"
                name="text"
                value={sectionData.text || ''}
                onChange={handleInputChange}
                placeholder="Enter gallery description"
                rows={4}
              />
            </div>
            {renderAnimationSettings()}
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="heading">Heading</Label>
              <Input
                id="heading"
                name="heading"
                value={sectionData.heading || ''}
                onChange={handleInputChange}
                placeholder="Enter heading"
              />
            </div>
            <div>
              <Label htmlFor="text">Content</Label>
              <Textarea
                id="text"
                name="text"
                value={sectionData.text || ''}
                onChange={handleInputChange}
                placeholder="Enter content"
                rows={6}
              />
            </div>
            {renderAnimationSettings()}
          </div>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {sectionIndex >= 0 ? 'Edit Section' : 'Add New Section'}
          </CardTitle>
          <CardDescription>
            Customize this section of your page
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="sectionType">Section Type</Label>
              <Select
                value={sectionData._type}
                onValueChange={(value) => handleSectionTypeChange(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select section type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hero">Hero Section</SelectItem>
                  <SelectItem value="textSection">Text Section</SelectItem>
                  <SelectItem value="imageGallery">Image Gallery</SelectItem>
                  <SelectItem value="featuredContent">Featured Content</SelectItem>
                  <SelectItem value="contactForm">Contact Form</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {renderSectionFields()}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" type="button" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>Saving...</>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Section
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
