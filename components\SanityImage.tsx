'use client';

import { useState } from 'react';
import Image from 'next/image';
import { urlFor } from '@/lib/sanity';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';
import { logError } from '@/lib/errorHandling';

interface SanityImageProps {
  image: SanityImageSource;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  sizes?: string;
  fill?: boolean;
  fallbackSrc?: string;
  onError?: (error: Error) => void;
}

export default function SanityImage({
  image,
  alt,
  width,
  height,
  className,
  priority = false,
  sizes,
  fill = false,
  fallbackSrc = '/placeholder.png',
  onError,
}: SanityImageProps) {
  const [imgSrc, setImgSrc] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // If no image is provided, use fallback
  if (!image) {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`} style={{ width: width || 800, height: height || 600 }}>
        <span className="text-gray-400 text-sm">No image available</span>
      </div>
    );
  }

  try {
    // Generate the image URL using Sanity's URL builder
    const imageUrl = imgSrc || urlFor(image).url();

    // Common props for the Image component
    const commonProps = {
      src: imageUrl,
      alt: alt || 'Image',
      className,
      priority,
      sizes,
      onError: (e: any) => {
        const err = new Error(`Failed to load image: ${imageUrl}`);
        logError(err, 'SanityImage');
        if (onError) onError(err);
        setError(err);
        setImgSrc(fallbackSrc);
      },
    };

    // If fill is true, use fill mode
    if (fill) {
      return <Image {...commonProps} fill />;
    }

    // Otherwise, use explicit width and height
    return (
      <Image
        {...commonProps}
        width={width || 800}
        height={height || 600}
      />
    );
  } catch (err) {
    // Handle any errors that occur during URL generation
    const error = err instanceof Error ? err : new Error('Failed to generate image URL');
    logError(error, 'SanityImage');
    if (onError) onError(error);

    // Return a fallback image
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`} style={{ width: width || 800, height: height || 600 }}>
        <span className="text-gray-400 text-sm">Image unavailable</span>
      </div>
    );
  }
}
