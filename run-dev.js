const { spawn } = require('child_process');
const path = require('path');

console.log('Starting Next.js development server...');

// Run the Next.js development server
const nextDev = spawn('npx', ['next', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_OPTIONS: '--no-warnings'
  }
});

nextDev.on('error', (error) => {
  console.error('Failed to start Next.js development server:', error);
});

nextDev.on('close', (code) => {
  if (code !== 0) {
    console.error(`Next.js development server exited with code ${code}`);
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Stopping Next.js development server...');
  nextDev.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Stopping Next.js development server...');
  nextDev.kill('SIGTERM');
  process.exit(0);
});
