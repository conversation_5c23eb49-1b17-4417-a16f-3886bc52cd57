import jwt from 'jsonwebtoken';

// Secret key for signing tokens
const TOKEN_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Token types
type TokenType = 'register' | 'reset-password';

// Token payload interface
interface TokenPayload {
  type: TokenType;
  email: string;
  userId?: string;
  role?: string;
  [key: string]: any; // Allow additional properties
}

/**
 * Generate a JWT token
 * @param payload - The data to include in the token
 * @param expiresIn - Token expiration time (e.g., '1h', '7d')
 * @returns The generated token
 */
export function generateToken(payload: TokenPayload, expiresIn: string = '1h'): string {
  return jwt.sign(payload, TOKEN_SECRET, { expiresIn });
}

/**
 * Verify and decode a JWT token
 * @param token - The token to verify
 * @returns The decoded token payload
 * @throws Error if token is invalid or expired
 */
export function verifyToken(token: string): TokenPayload {
  try {
    return jwt.verify(token, TOKEN_SECRET) as TokenPayload;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}
