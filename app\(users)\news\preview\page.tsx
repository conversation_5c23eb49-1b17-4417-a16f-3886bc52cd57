import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';
import { getNews, getCategories } from '@/lib/sanity';
import PreviewProvider from '@/components/PreviewProvider';
import LiveNews from '@/components/LiveNews';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default async function NewsPreviewPage() {
  const { isEnabled } = draftMode();
  
  // If draft mode is not enabled, redirect to the regular news page
  if (!isEnabled) {
    redirect('/news');
  }
  
  // Fetch initial data
  const newsArticles = await getNews();
  const categories = await getCategories();
  
  return (
    <>
      <Header />
      <main>
        <section className="royal-gradient py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">
                Royal News & Updates (Preview Mode)
              </h1>
              <p className="text-xl text-royalGold mb-8">
                You are viewing the news page in preview mode. Changes in Sanity will appear in real-time.
              </p>
              <div className="mt-4">
                <a 
                  href="/api/exit-preview?redirectTo=/news"
                  className="bg-white text-royalBlue px-6 py-3 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Exit Preview Mode
                </a>
              </div>
            </div>
          </div>
        </section>
        
        <section className="py-16 bg-ivory">
          <div className="container mx-auto px-4">
            <PreviewProvider token={process.env.SANITY_API_TOKEN || ''}>
              <LiveNews initialData={newsArticles} />
            </PreviewProvider>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
