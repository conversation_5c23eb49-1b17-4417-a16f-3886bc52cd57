import { Metadata } from 'next';
import { generateDynamicMetadata } from '@/lib/metadata-generator';

// Generate dynamic metadata for this page
export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata({
    title: 'Royal Events',
    description: 'Upcoming royal events and ceremonies in the Kingdom of Adukrom. Join us for these special occasions.',
    url: '/events',
    keywords: ['Royal Events', 'Adukrom Ceremonies', 'Royal Calendar', 'Kingdom Events'],
  });
}

export default function EventsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <div>{children}</div>;
}
