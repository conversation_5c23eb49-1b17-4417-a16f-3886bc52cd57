'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface EditSectionButtonProps {
  pageId: string;
  sectionIndex: number;
  sectionType?: string;
  sectionTitle?: string;
  className?: string;
}

export default function EditSectionButton({
  pageId,
  sectionIndex,
  sectionType,
  sectionTitle,
  className = ''
}: EditSectionButtonProps) {
  return (
    <Link href={`/admin/pages/sections/${pageId}/${sectionIndex}`}>
      <Button
        variant="outline"
        size="sm"
        className={className}
      >
        Edit Section Content
      </Button>
    </Link>
  );
}
