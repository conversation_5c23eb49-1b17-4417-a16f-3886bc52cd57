import { NextRequest, NextResponse } from 'next/server';
import { getWriteClient } from '@/lib/sanity.client';

// GET /api/pages/[id]/sections/[index] - Get a specific section
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; index: string } }
) {
  try {
    const { id, index } = params;
    const sectionIndex = parseInt(index);
    
    if (isNaN(sectionIndex) || sectionIndex < 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid section index' },
        { status: 400 }
      );
    }
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Fetch the page document
    const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });
    
    if (!page) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }
    
    // Get the section
    if (!page.pageBuilder || !page.pageBuilder[sectionIndex]) {
      return NextResponse.json(
        { success: false, message: 'Section not found' },
        { status: 404 }
      );
    }
    
    const section = page.pageBuilder[sectionIndex];
    
    return NextResponse.json({
      success: true,
      data: {
        section,
        pageTitle: page.title,
        pageId: id,
        sectionIndex
      }
    });
  } catch (error) {
    console.error('Error fetching section:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch section',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/pages/[id]/sections/[index] - Update a specific section
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; index: string } }
) {
  try {
    const { id, index } = params;
    const sectionIndex = parseInt(index);
    
    if (isNaN(sectionIndex) || sectionIndex < 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid section index' },
        { status: 400 }
      );
    }
    
    // Get the request body
    const body = await request.json();
    
    if (!body.section) {
      return NextResponse.json(
        { success: false, message: 'Missing section data' },
        { status: 400 }
      );
    }
    
    // Get the Sanity client
    const client = getWriteClient();
    
    // Fetch the page document
    const page = await client.fetch(`*[_type == "page" && _id == $id][0]`, { id });
    
    if (!page) {
      return NextResponse.json(
        { success: false, message: 'Page not found' },
        { status: 404 }
      );
    }
    
    // Check if the page has a pageBuilder array
    if (!page.pageBuilder) {
      return NextResponse.json(
        { success: false, message: 'Page has no sections' },
        { status: 404 }
      );
    }
    
    // Create a copy of the pageBuilder array
    const pageBuilder = [...page.pageBuilder];
    
    // Update the section
    if (sectionIndex >= pageBuilder.length) {
      // Add a new section
      pageBuilder.push(body.section);
    } else {
      // Update existing section
      pageBuilder[sectionIndex] = body.section;
    }
    
    // Update the page
    await client.patch(id)
      .set({ pageBuilder })
      .commit();
    
    return NextResponse.json({
      success: true,
      message: 'Section updated successfully',
      data: {
        section: body.section,
        pageId: id,
        sectionIndex
      }
    });
  } catch (error) {
    console.error('Error updating section:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update section',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
