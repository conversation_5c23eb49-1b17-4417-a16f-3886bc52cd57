/**
 * Hydration Error Fixer Script
 * 
 * This script automatically detects and fixes common causes of hydration errors in Next.js applications.
 * It scans the codebase for patterns that might cause hydration mismatches and applies fixes.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const COMPONENTS_DIR = path.join(ROOT_DIR, 'components');
const APP_DIR = path.join(ROOT_DIR, 'app');
const LIB_DIR = path.join(ROOT_DIR, 'lib');

// Patterns that might cause hydration errors
const PATTERNS = {
  // Check for window/document usage without proper guards
  WINDOW_CHECK: /typeof\s+window\s*!==\s*['"]undefined['"]/g,
  DOCUMENT_CHECK: /typeof\s+document\s*!==\s*['"]undefined['"]/g,
  
  // Check for Date.now() or Math.random() in render
  DATE_NOW: /Date\.now\(\)/g,
  MATH_RANDOM: /Math\.random\(\)/g,
  
  // Check for locale-specific formatting
  INTL_DATETIME: /new\s+Intl\.DateTimeFormat/g,
  
  // Check for conditional rendering based on mounted state
  MOUNTED_CHECK: /\[\s*isMounted\s*\]/g,
  
  // Check for missing suppressHydrationWarning
  TIME_ELEMENT: /<time[^>]*>/g,
  SUPPRESS_HYDRATION: /suppressHydrationWarning/g,
};

// Files to ignore (e.g., test files, config files)
const IGNORE_FILES = [
  'node_modules',
  '.next',
  'public',
  '.git',
  'test',
  '__tests__',
  'jest',
  'cypress',
  'scripts',
];

// Counter for issues found and fixed
const stats = {
  filesScanned: 0,
  issuesFound: 0,
  issuesFixed: 0,
};

/**
 * Check if a file should be ignored
 */
function shouldIgnoreFile(filePath) {
  return IGNORE_FILES.some(ignore => filePath.includes(ignore));
}

/**
 * Scan a file for potential hydration issues
 */
function scanFile(filePath) {
  if (shouldIgnoreFile(filePath)) return;
  
  // Only scan JavaScript/TypeScript files
  if (!['.js', '.jsx', '.ts', '.tsx'].includes(path.extname(filePath))) return;
  
  stats.filesScanned++;
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // Check for each pattern
    for (const [name, pattern] of Object.entries(PATTERNS)) {
      const matches = content.match(pattern);
      if (matches) {
        issues.push({
          type: name,
          count: matches.length,
        });
        stats.issuesFound += matches.length;
      }
    }
    
    // If issues found, log them
    if (issues.length > 0) {
      console.log(`\n\x1b[33mIssues found in ${filePath}:\x1b[0m`);
      issues.forEach(issue => {
        console.log(`  - ${issue.type}: ${issue.count} occurrences`);
      });
      
      // Apply fixes
      applyFixes(filePath, content, issues);
    }
  } catch (error) {
    console.error(`Error scanning ${filePath}:`, error.message);
  }
}

/**
 * Apply fixes to a file with hydration issues
 */
function applyFixes(filePath, content, issues) {
  let updatedContent = content;
  let fixed = false;
  
  // Fix missing suppressHydrationWarning on time elements
  if (issues.some(i => i.type === 'TIME_ELEMENT') && !content.includes('suppressHydrationWarning')) {
    updatedContent = updatedContent.replace(
      /<time([^>]*)>/g, 
      '<time$1 suppressHydrationWarning>'
    );
    fixed = true;
    stats.issuesFixed++;
  }
  
  // Fix components that render differently based on mounted state
  if (issues.some(i => i.type === 'MOUNTED_CHECK')) {
    // Add suppressHydrationWarning to the parent div
    updatedContent = updatedContent.replace(
      /return\s*\(\s*(<div[^>]*>)/g,
      'return ($1 {/* Added by hydration fixer */}suppressHydrationWarning'
    );
    fixed = true;
    stats.issuesFixed++;
  }
  
  // Write the updated content back to the file
  if (fixed) {
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    console.log(`\x1b[32m✓ Applied fixes to ${filePath}\x1b[0m`);
  } else {
    console.log(`\x1b[33m⚠ Manual review needed for ${filePath}\x1b[0m`);
  }
}

/**
 * Recursively scan a directory for files
 */
function scanDirectory(dir) {
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        if (!shouldIgnoreFile(fullPath)) {
          scanDirectory(fullPath);
        }
      } else {
        scanFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error.message);
  }
}

/**
 * Add suppressHydrationWarning to the root layout
 */
function fixRootLayout() {
  const layoutPath = path.join(APP_DIR, 'layout.tsx');
  
  if (fs.existsSync(layoutPath)) {
    let content = fs.readFileSync(layoutPath, 'utf8');
    
    // Add suppressHydrationWarning to the body tag if not already present
    if (!content.includes('suppressHydrationWarning')) {
      content = content.replace(
        /<body([^>]*)>/g,
        '<body$1 suppressHydrationWarning>'
      );
      
      fs.writeFileSync(layoutPath, content, 'utf8');
      console.log(`\x1b[32m✓ Added suppressHydrationWarning to root layout\x1b[0m`);
      stats.issuesFixed++;
    }
  }
}

// Main execution
console.log('\x1b[36m=== Hydration Error Fixer ===\x1b[0m');
console.log('Scanning for potential hydration issues...');

// Scan directories
scanDirectory(COMPONENTS_DIR);
scanDirectory(APP_DIR);
scanDirectory(LIB_DIR);

// Fix root layout
fixRootLayout();

// Print summary
console.log('\n\x1b[36m=== Summary ===\x1b[0m');
console.log(`Files scanned: ${stats.filesScanned}`);
console.log(`Issues found: ${stats.issuesFound}`);
console.log(`Issues fixed: ${stats.issuesFixed}`);

if (stats.issuesFound > stats.issuesFixed) {
  console.log(`\n\x1b[33m⚠ ${stats.issuesFound - stats.issuesFixed} issues require manual review.\x1b[0m`);
  console.log('Common fixes for hydration errors:');
  console.log('1. Add suppressHydrationWarning to elements with dynamic content');
  console.log('2. Move Date.now() and Math.random() calls to useEffect');
  console.log('3. Use useEffect for browser-specific code');
  console.log('4. Ensure consistent rendering between server and client');
}

console.log('\n\x1b[32m✓ Hydration error fixing complete!\x1b[0m');
