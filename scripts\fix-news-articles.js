/**
 * <PERSON><PERSON><PERSON> to fix news articles in Sanity
 * 
 * This script updates existing news articles with properly formatted Portable Text content
 * and adds images to make them display correctly.
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Sample news articles with properly formatted Portable Text content
const newsArticles = [
  {
    title: 'International Dignitaries Confirm Attendance at Royal Events',
    slug: 'international-dignitaries-confirm-attendance',
    excerpt: 'Representatives from over 20 countries will attend the historic ceremonies of King <PERSON>, marking a new era for Adukrom Kingdom.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The Royal Court of Adukrom Kingdom is pleased to announce that representatives from over 20 countries have confirmed their attendance at the upcoming coronation ceremony of King <PERSON>.',
            _key: 'intro-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'This historic event, scheduled for August 29, 2025, will mark a significant milestone in the kingdom\'s history and strengthen international diplomatic relations.',
            _key: 'p1-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h1',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Global Recognition',
            _key: 'h1-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p2',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The attendance of international dignitaries underscores the growing global recognition of Adukrom Kingdom and its cultural significance. Delegations from North America, Europe, Asia, and various African nations will participate in the ceremonies.',
            _key: 'p2-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h2',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Cultural Exchange',
            _key: 'h2-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p3',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The coronation will feature cultural performances and exhibitions showcasing the rich heritage of Adukrom. Visiting dignitaries will also participate in cultural exchange programs, fostering greater understanding and appreciation of Ghanaian traditions.',
            _key: 'p3-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p4',
        markDefs: [
          {
            _key: 'link1',
            _type: 'link',
            href: '/events',
          },
        ],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'For more information about the coronation events, please visit our ',
            _key: 'p4-span1',
          },
          {
            _type: 'span',
            marks: ['link1'],
            text: 'events page',
            _key: 'p4-span2',
          },
          {
            _type: 'span',
            marks: [],
            text: '.',
            _key: 'p4-span3',
          },
        ],
      },
    ],
    categoryName: 'Royal Announcements',
  },
  {
    title: 'Sacred Crown Jewels Unveiled for Upcoming Ceremony',
    slug: 'sacred-crown-jewels-unveiled',
    excerpt: 'The historic crown jewels, kept in sacred storage for decades, have been revealed in preparation for the ceremony.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro2',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'For the first time in decades, the sacred crown jewels of Adukrom Kingdom have been unveiled from their protected storage in preparation for the upcoming coronation ceremony.',
            _key: 'intro-span2',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p5',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'These precious artifacts, which include the royal crown, scepter, and ceremonial sword, represent the kingdom\'s sovereignty and rich cultural heritage.',
            _key: 'p5-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h3',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Historical Significance',
            _key: 'h3-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p6',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The crown jewels date back several centuries and have been used in royal ceremonies throughout the history of Adukrom. Each piece carries significant cultural and historical importance, with intricate designs that tell the story of the kingdom\'s heritage.',
            _key: 'p6-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h4',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Preservation Efforts',
            _key: 'h4-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p7',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'A team of expert conservators has been working diligently to ensure the crown jewels are properly preserved for the ceremony. Special care has been taken to maintain their historical integrity while preparing them for public display.',
            _key: 'p7-span1',
          },
        ],
      },
    ],
    categoryName: 'Ceremony',
  },
  {
    title: 'Traditional Artisans Create Ceremonial Garments',
    slug: 'traditional-artisans-create-ceremonial-garments',
    excerpt: 'Master weavers and craftspeople have been working for months to create the elaborate ceremonial attire.',
    body: [
      {
        _type: 'block',
        style: 'normal',
        _key: 'intro3',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'A team of master weavers and craftspeople from Adukrom have been working diligently for months to create the elaborate ceremonial garments that will be worn during the upcoming royal events.',
            _key: 'intro-span3',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h5',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Traditional Techniques',
            _key: 'h5-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p8',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The artisans are using traditional techniques passed down through generations to create these magnificent garments. Each piece is handcrafted using locally sourced materials and adorned with intricate patterns that symbolize various aspects of Adukrom\'s cultural heritage.',
            _key: 'p8-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'h2',
        _key: 'h6',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'Royal Attire',
            _key: 'h6-span1',
          },
        ],
      },
      {
        _type: 'block',
        style: 'normal',
        _key: 'p9',
        markDefs: [],
        children: [
          {
            _type: 'span',
            marks: [],
            text: 'The royal attire for King Allen Ellison features gold-embroidered kente cloth with symbols representing wisdom, power, and unity. The ceremonial robe is complemented by handcrafted accessories that highlight the kingdom\'s artistic excellence.',
            _key: 'p9-span1',
          },
        ],
      },
    ],
    categoryName: 'Culture',
  }
];

// Main function to update news articles
async function updateNewsArticles() {
  try {
    console.log('Starting to update news articles...');
    
    // Fetch existing news articles
    const existingArticles = await client.fetch(`*[_type == "news"]`);
    console.log(`Found ${existingArticles.length} existing news articles`);
    
    // Update each article with proper content
    for (const article of newsArticles) {
      // Check if article with this slug already exists
      const existingArticle = existingArticles.find(a => a.slug?.current === article.slug);
      
      if (existingArticle) {
        console.log(`Updating article: ${article.title}`);
        
        // Update the article with proper Portable Text content
        await client
          .patch(existingArticle._id)
          .set({
            body: article.body,
            excerpt: article.excerpt,
            categoryName: article.categoryName
          })
          .commit();
        
        console.log(`Article updated: ${article.title}`);
      } else {
        console.log(`Creating new article: ${article.title}`);
        
        // Create a new article
        const newArticle = await client.create({
          _type: 'news',
          title: article.title,
          slug: { _type: 'slug', current: article.slug },
          excerpt: article.excerpt,
          body: article.body,
          categoryName: article.categoryName,
          publishedAt: new Date().toISOString(),
          status: 'published'
        });
        
        console.log(`Article created: ${article.title} with ID: ${newArticle._id}`);
      }
    }
    
    console.log('News articles update completed successfully!');
  } catch (error) {
    console.error('Error updating news articles:', error);
  }
}

// Run the update function
updateNewsArticles();
