'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getCountdownTargetEvent } from '@/lib/sanity';
import { Event } from '@/lib/types/sanity';
import { USE_LOCAL_DATA, DEFAULT_CORONATION_DATE, DEFAULT_CORONATION_TITLE } from '@/lib/config';

interface CountdownTimerProps {
  className?: string;
  showTitle?: boolean;
  showLabels?: boolean;
  size?: 'sm' | 'md' | 'lg';
  textColor?: string;
  numberColor?: string;
}

export default function CountdownTimer({
  className = '',
  showTitle = true,
  showLabels = true,
  size = 'md',
  textColor = 'text-white',
  numberColor = 'text-royalGold',
}: CountdownTimerProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [countdown, setCountdown] = useState({
    days: '00',
    hours: '00',
    minutes: '00',
    seconds: '00',
  });
  const [hasEnded, setHasEnded] = useState(false);
  const [eventTitle, setEventTitle] = useState('');
  const [eventDate, setEventDate] = useState<Date | null>(null);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const fetchEvent = async () => {
      // If using local data only, skip the API call
      if (USE_LOCAL_DATA) {
        console.log('CountdownTimer: Using local data (Sanity API disabled)');
        setEventTitle(DEFAULT_CORONATION_TITLE);
        setEventDate(new Date(DEFAULT_CORONATION_DATE));
        return;
      }

      try {
        console.log('CountdownTimer: Attempting to fetch countdown target event...');

        // Use a timeout to prevent hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 5000);
        });

        // Try to fetch the event with a timeout
        const eventPromise = getCountdownTargetEvent();
        const event = await Promise.race([eventPromise, timeoutPromise]) as Event | null;

        if (event) {
          console.log('CountdownTimer: Successfully fetched event:', event.title);
          setEventTitle(event.title);
          // Parse the event date from Sanity
          if (event.date) {
            setEventDate(new Date(event.date));
          }
        } else {
          console.log('CountdownTimer: No event returned, using default values');
          // If no event is returned, use default values
          setEventTitle(DEFAULT_CORONATION_TITLE);
          setEventDate(new Date(DEFAULT_CORONATION_DATE));
        }
      } catch (error) {
        console.error('CountdownTimer: Failed to fetch event:', error);
        // Use default values on error
        setEventTitle(DEFAULT_CORONATION_TITLE);
        setEventDate(new Date(DEFAULT_CORONATION_DATE));
      }
    };

    fetchEvent();
  }, [isMounted]);

  useEffect(() => {
    if (!isMounted || typeof window === 'undefined') return;

    const updateCountdown = () => {
      try {
        // Use the official coronation date from the Coronation component
        // August 29, 2025 at 7:30 AM Ghana time (GMT)
        const coronationDate = new Date('2025-08-29T07:30:00.000Z');

        // Use the event date from Sanity, or fall back to the coronation date
        const targetDate = eventDate || coronationDate;
        const event = targetDate.getTime();
        const now = new Date().getTime();
        const distance = event - now;

        // Force the event to be in the future for the Royal Coronation
        if (distance < 0 && (eventTitle.includes('Royal Coronation') || eventTitle.includes('Coronation'))) {
          // Use the fixed future date for the Royal Coronation
          const futureDistance = coronationDate.getTime() - now;

          // Calculate days, hours, minutes, seconds from the future distance
          const days = Math.floor(futureDistance / (1000 * 60 * 60 * 24));
          const hours = Math.floor((futureDistance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((futureDistance % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((futureDistance % (1000 * 60)) / 1000);

          setHasEnded(false);
          setCountdown({
            days: days.toString().padStart(2, '0'),
            hours: hours.toString().padStart(2, '0'),
            minutes: minutes.toString().padStart(2, '0'),
            seconds: seconds.toString().padStart(2, '0'),
          });
          return;
        } else if (distance < 0) {
          setHasEnded(true);
          setCountdown({
            days: '00',
            hours: '00',
            minutes: '00',
            seconds: '00',
          });
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        setCountdown({
          days: days.toString().padStart(2, '0'),
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0'),
        });
      } catch (error) {
        console.error('Error updating countdown:', error);
        // Set default values on error
        setCountdown({
          days: '00',
          hours: '00',
          minutes: '00',
          seconds: '00',
        });
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [isMounted, eventDate, eventTitle]);

  if (!isMounted) {
    return null; // Return null on server-side and first render
  }

  const sizeClasses = {
    sm: {
      container: 'gap-2',
      number: 'text-2xl',
      label: 'text-xs',
    },
    md: {
      container: 'gap-4',
      number: 'text-4xl',
      label: 'text-sm',
    },
    lg: {
      container: 'gap-6',
      number: 'text-6xl',
      label: 'text-base',
    },
  };

  return (<div className={`flex flex-col items-center ${className}`} suppressHydrationWarning>
      <div className={`flex justify-center ${sizeClasses[size].container} mb-2`}>
        {['days', 'hours', 'minutes', 'seconds'].map((unit, index) => (
          <motion.div
            key={unit}
            className="flex flex-col items-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.1 * index }}
            whileHover={{ scale: 1.1 }}
          >
            <motion.div
              className={`font-bold ${numberColor} ${sizeClasses[size].number}`}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}

            >
              {countdown[unit as keyof typeof countdown]}
            </motion.div>
            {showLabels && (
              <div className={`${textColor} ${sizeClasses[size].label}`}>
                {unit.charAt(0).toUpperCase() + unit.slice(1)}
              </div>
            )}
          </motion.div>
        ))}
      </div>
      {showTitle && (
        <motion.p
          className={textColor}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}

        >
          {hasEnded ? `${eventTitle} Has Concluded` : `Until ${eventTitle}`}
        </motion.p>
      )}
    </div>
  );
}
