import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getAllUsers, createUser } from '@/lib/users';

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin
    const userRole = session.user.role;
    if (userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    // Get all users
    const users = getAllUsers().map(user => {
      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    return NextResponse.json({ success: true, users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: You must be logged in' },
        { status: 401 }
      );
    }

    // Check if user is a super admin
    const userRole = session.user.role;
    if (userRole !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Super admin access required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, email, password, role } = body;

    // Validate required fields
    if (!name || !email || !password || !role) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    try {
      // Create new user
      const newUser = await createUser({
        name,
        email,
        password,
        role,
      });

      return NextResponse.json({
        success: true,
        message: 'User created successfully',
        user: newUser
      });
    } catch (error: any) {
      return NextResponse.json(
        { success: false, message: error.message || 'Failed to create user' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create user' },
      { status: 500 }
    );
  }
}
