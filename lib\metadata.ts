import { Metadata } from 'next';
import { env } from './env';

/**
 * SEO metadata configuration
 *
 * This utility helps generate consistent metadata for all pages
 * to improve SEO and social sharing.
 */

// Valid OpenGraph types as defined by Next.js
type OpenGraphType = 'article' | 'book' | 'music.song' | 'music.album' | 'music.playlist' |
  'music.radio_station' | 'profile' | 'website' | 'video.tv_show' | 'video.other' |
  'video.movie' | 'video.episode';

interface SEOProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: OpenGraphType;
  keywords?: string[];
  noIndex?: boolean;
}

/**
 * Generate metadata for a page
 */
export function generateMetadata({
  title = 'The Royal Family of Africa',
  description = 'The Crown of Africa. The Rise of a New Era.',
  image = '/images/og-image.jpg',
  url = '',
  type = 'website',
  keywords = ['royalty', 'crown', 'africa', 'rise', 'new era', 'heritage', 'culture', 'Ghana', 'Royalty'],
  noIndex = false,
}: SEOProps): Metadata {
  const baseUrl = env.NEXT_PUBLIC_APP_URL || 'https://kingdomadukrom.com';
  const fullUrl = url ? `${baseUrl}${url}` : baseUrl;
  const imageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  return {
    title: {
      default: title,
      template: `%s | The Crown of Africa`,
    },
    description,
    keywords,
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: fullUrl,
    },
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName: 'The Royal Family of Africa',
      images: [{ url: imageUrl, alt: title }],
      type,
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: '@KingdomAdukrom',
    },
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    icons: {
      icon: '/favicon.ico',
      apple: '/apple-touch-icon.png',
    },
  };
}

/**
 * Default metadata for the site
 */
export const defaultMetadata = generateMetadata({
  title: 'The Royal Family of Africa',
  description: 'The Crown of Africa. The Rise of a New Era.',
});

/**
 * Generate event metadata
 */
export function generateEventMetadata(event: any): Metadata {
  return generateMetadata({
    title: event.title,
    description: event.description,
    image: event.imageUrl || '/images/events-og.jpg',
    url: `/events/${event.slug?.current}`,
    type: 'website', // Changed from 'event' to 'website' as 'event' is not a valid OpenGraph type
    keywords: ['Adukrom Event', event.title, event.eventType, 'Royal Event'],
  });
}

/**
 * Generate news metadata
 */
export function generateNewsMetadata(news: any): Metadata {
  return generateMetadata({
    title: news.title,
    description: news.excerpt,
    image: news.mainImage?.asset?.url || '/images/news-og.jpg',
    url: `/news/${news.slug?.current}`,
    type: 'article',
    keywords: ['Adukrom News', news.title, news.category?.title, 'Royal News'],
  });
}

/**
 * Generate gallery metadata
 */
export function generateGalleryMetadata(gallery?: any): Metadata {
  return generateMetadata({
    title: gallery ? gallery.title : 'Royal Gallery',
    description: gallery ? gallery.description : 'Explore the royal gallery of the Kingdom of Adukrom',
    image: gallery?.image?.asset?.url || '/images/gallery-og.jpg',
    url: gallery ? `/gallery/${gallery.slug?.current}` : '/gallery',
    type: 'website',
    keywords: ['Adukrom Gallery', 'Royal Photos', 'Kingdom Gallery'],
  });
}
