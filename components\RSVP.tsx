'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import RsvpConfirmationModal from '@/components/RsvpConfirmationModal';

export default function RSVP() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [submittedData, setSubmittedData] = useState({
    firstName: '',
    reminder: [] as string[]
  });
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    events: [] as string[],
    attendanceType: '',
    reminder: [] as string[],
    notes: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;

    if (name === 'events') {
      setFormData(prev => ({
        ...prev,
        events: checked
          ? [...prev.events, value]
          : prev.events.filter(event => event !== value)
      }));
    } else if (name === 'reminder') {
      setFormData(prev => ({
        ...prev,
        reminder: checked
          ? [...prev.reminder, value]
          : prev.reminder.filter(r => r !== value)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Submit the form data to our API route
      const response = await fetch('/api/rsvp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Failed to submit RSVP');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to submit RSVP');
      }

      toast.success('RSVP submitted successfully!');
      console.log('RSVP submitted with ID:', data.id);

      // Save the submitted data for the confirmation modal
      setSubmittedData({
        firstName: formData.firstName,
        reminder: formData.reminder
      });

      // Show confirmation modal
      setShowConfirmation(true);

      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        country: '',
        events: [],
        attendanceType: '',
        reminder: [],
        notes: ''
      });
    } catch (error) {
      console.error('Error submitting RSVP:', error);
      toast.error('Failed to submit RSVP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="rsvp" className="py-20 bg-ivory">
      {/* RSVP Confirmation Modal */}
      <RsvpConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        firstName={submittedData.firstName || 'Guest'}
        reminderPreference={submittedData.reminder}
      />

      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-royalBlue text-center mb-16 section-title">
          RSVP for Royal Events
        </h2>

        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-xl p-8 border border-royalGold/30">
          <form id="rsvp-form" className="space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="firstName" className="block text-charcoal font-medium mb-2">
                  First Name*
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                  className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                />
              </div>
              <div>
                <label htmlFor="lastName" className="block text-charcoal font-medium mb-2">
                  Last Name*
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                  className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-charcoal font-medium mb-2">
                Email Address*
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-charcoal font-medium mb-2">
                Phone Number*
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
              />
            </div>

            <div>
              <label htmlFor="country" className="block text-charcoal font-medium mb-2">
                Country*
              </label>
              <select
                id="country"
                name="country"
                value={formData.country}
                onChange={handleChange}
                required
                className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
              >
                <option value="">Select your country</option>
                {['Ghana', 'Nigeria', 'South Africa', 'United States', 'United Kingdom', 'Canada', 'Other'].map(
                  (country) => (
                    <option key={country} value={country}>
                      {country}
                    </option>
                  )
                )}
              </select>
            </div>

            <div>
              <label className="block text-charcoal font-medium mb-2">Which events will you attend?*</label>
              <div className="space-y-2">
                {[
                  { id: 'event1', value: 'coronation', label: 'Royal Coronation Ceremony (Aug 29)' },
                  { id: 'event2', value: 'gala', label: 'Royal Gala Dinner (Aug 30)' },
                  { id: 'event3', value: 'forum', label: 'Global Economic Forum (Aug 31)' },
                ].map((event) => (
                  <div key={event.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={event.id}
                      name="events"
                      value={event.value}
                      checked={formData.events.includes(event.value)}
                      onChange={handleCheckboxChange}
                      className="w-4 h-4 text-royalGold"
                    />
                    <label htmlFor={event.id} className="ml-2 text-charcoal">
                      {event.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-charcoal font-medium mb-2">Attendance Type*</label>
              <div className="flex space-x-6">
                {[
                  { id: 'physical', value: 'physical', label: 'Physical Attendance' },
                  { id: 'online', value: 'online', label: 'Online Attendance' },
                ].map((type) => (
                  <div key={type.id} className="flex items-center">
                    <input
                      type="radio"
                      id={type.id}
                      name="attendanceType"
                      value={type.value}
                      checked={formData.attendanceType === type.value}
                      onChange={handleChange}
                      className="w-4 h-4 text-royalGold"
                      required
                    />
                    <label htmlFor={type.id} className="ml-2 text-charcoal">
                      {type.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-charcoal font-medium mb-2">Reminder Preference</label>
              <div className="flex space-x-6">
                {[
                  { id: 'emailReminder', value: 'email', label: 'Email Reminder' },
                  { id: 'smsReminder', value: 'sms', label: 'SMS Reminder' },
                ].map((reminder) => (
                  <div key={reminder.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={reminder.id}
                      name="reminder"
                      value={reminder.value}
                      checked={formData.reminder.includes(reminder.value)}
                      onChange={handleCheckboxChange}
                      className="w-4 h-4 text-royalGold"
                    />
                    <label htmlFor={reminder.id} className="ml-2 text-charcoal">
                      {reminder.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label htmlFor="notes" className="block text-charcoal font-medium mb-2">
                Additional Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="form-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
              />
            </div>

            <div className="text-center">
              <button
                type="submit"
                className="royal-button bg-royalGold text-royalBlue font-bold py-3 px-8 rounded-full hover:bg-yellow-500 transition-colors shadow-lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit RSVP"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}
