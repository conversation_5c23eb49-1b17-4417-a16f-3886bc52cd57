// Full development server script
const express = require('express');
const path = require('path');
const { spawn } = require('child_process');
const app = express();
const port = process.env.PORT || 3000;
const nextPort = 4000;

console.log('Starting development environment...');

// Try to start Next.js development server
const nextDev = spawn('npx', ['next', 'dev', '-p', nextPort], {
  stdio: 'pipe',
  shell: true
});

let nextServerRunning = false;

nextDev.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(`Next.js: ${output}`);

  if (output.includes('ready') || output.includes('started server')) {
    nextServerRunning = true;
    console.log(`Next.js server is running at http://localhost:${nextPort}`);
  }
});

nextDev.stderr.on('data', (data) => {
  console.error(`Next.js Error: ${data.toString()}`);
});

nextDev.on('close', (code) => {
  console.log(`Next.js process exited with code ${code}`);
  nextServerRunning = false;
});

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve the test.html file
app.get('/test', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'test.html'));
});

// Default route to show server status
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Kingdom of Adukrom - Development Server</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          background-color: #f5f5dc;
          color: #002366;
        }
        .container {
          text-align: center;
          padding: 2rem;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          max-width: 600px;
        }
        h1 {
          color: #002366;
        }
        .gold {
          color: #D4AF37;
        }
        .status {
          margin: 1rem 0;
          padding: 1rem;
          border-radius: 4px;
        }
        .status.success {
          background-color: #e6ffe6;
          border: 1px solid #99cc99;
        }
        .status.error {
          background-color: #ffe6e6;
          border: 1px solid #cc9999;
        }
        .links {
          margin-top: 2rem;
        }
        .links a {
          display: inline-block;
          margin: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #002366;
          color: white;
          text-decoration: none;
          border-radius: 4px;
          transition: background-color 0.3s;
        }
        .links a:hover {
          background-color: #D4AF37;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Kingdom of <span class="gold">Adukrom</span></h1>
        <p>Development Environment Status</p>

        <div class="status ${nextServerRunning ? 'success' : 'error'}">
          <h3>Next.js Server</h3>
          <p>${nextServerRunning
            ? `Running at <a href="http://localhost:${nextPort}" target="_blank">http://localhost:${nextPort}</a>`
            : 'Not running. Check console for errors.'}</p>
        </div>

        <div class="status success">
          <h3>Express Server</h3>
          <p>Running at <a href="http://localhost:${port}" target="_blank">http://localhost:${port}</a></p>
        </div>

        <div class="links">
          <a href="/test" target="_blank">View Test Page</a>
          ${nextServerRunning ? `<a href="http://localhost:${nextPort}" target="_blank">Open Next.js App</a>` : ''}
        </div>
      </div>
    </body>
    </html>
  `);
});

// Start the Express server
app.listen(port, () => {
  console.log(`Express server running at http://localhost:${port}`);
  console.log(`Open your browser to http://localhost:${port} to see the development dashboard`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Stopping development environment...');
  nextDev.kill('SIGINT');
  process.exit(0);
});
