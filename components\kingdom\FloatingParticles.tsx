'use client';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  duration: number;
  delay: number;
  xOffset: number; // Store the random x offset for animation
}

interface FloatingParticlesProps {
  count?: number;
  colors?: string[];
  minSize?: number;
  maxSize?: number;
  minDuration?: number;
  maxDuration?: number;
}

export default function FloatingParticles({
  count = 20,
  colors = ['#D4AF37', '#002366', '#FFFFFF'],
  minSize = 3,
  maxSize = 8,
  minDuration = 10,
  maxDuration = 25,
}: FloatingParticlesProps) {
  const [particles, setParticles] = useState<Particle[]>([]);

  useEffect(() => {
    const newParticles: Particle[] = [];

    for (let i = 0; i < count; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * (maxSize - minSize) + minSize,
        color: colors[Math.floor(Math.random() * colors.length)],
        duration: Math.random() * (maxDuration - minDuration) + minDuration,
        delay: Math.random() * 5,
        xOffset: Math.random() > 0.5 ? 15 : -15, // Pre-calculate the random x offset
      });
    }

    setParticles(newParticles);
  }, [count, colors, minSize, maxSize, minDuration, maxDuration]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none z-0" suppressHydrationWarning>
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full opacity-30"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, particle.xOffset, 0],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
