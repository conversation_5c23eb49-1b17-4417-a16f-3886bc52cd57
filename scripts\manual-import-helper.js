/**
 * Manual Import Helper <PERSON>t
 * 
 * This script helps you import blog content that you've manually downloaded.
 * It processes HTML files and images in a specified directory and imports them into Sanity.
 * 
 * Usage:
 * 1. Download the blog articles as HTML files
 * 2. Save them in the 'manual-import/html' directory
 * 3. Save the images in the 'manual-import/images' directory
 * 4. Run this script
 * 
 * Requirements:
 * - cheerio: for parsing HTML
 * - dotenv: for loading environment variables
 * 
 * Install dependencies:
 * npm install cheerio dotenv
 */

require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const cheerio = require('cheerio');
const { createClient } = require('@sanity/client');

// Create a Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_API_TOKEN, // Need a token with write access
  apiVersion: '2023-05-03',
  useCdn: false,
});

// Configuration
const config = {
  htmlDir: path.join(__dirname, '../manual-import/html'),
  imageDir: path.join(__dirname, '../manual-import/images'),
};

// Create directories if they don't exist
if (!fs.existsSync(config.htmlDir)) {
  fs.mkdirSync(config.htmlDir, { recursive: true });
}
if (!fs.existsSync(config.imageDir)) {
  fs.mkdirSync(config.imageDir, { recursive: true });
}

/**
 * Extract article data from an HTML file
 * @param {string} htmlContent - HTML content
 * @param {string} filename - HTML filename
 * @returns {Object} - Article data
 */
function extractArticleData(htmlContent, filename) {
  const $ = cheerio.load(htmlContent);
  
  // These selectors might need to be adjusted based on the actual HTML structure
  const title = $('.post-title, .entry-title').first().text().trim() || path.basename(filename, '.html');
  const date = $('.post-date, .entry-date').first().text().trim();
  const content = $('.post-content, .entry-content').html() || $('body').html();
  
  // Extract excerpt
  let excerpt = '';
  $('.post-content p, .entry-content p').each((i, el) => {
    if (i === 0) {
      excerpt = $(el).text().trim();
    }
  });
  
  if (!excerpt) {
    $('p').each((i, el) => {
      if (i === 0) {
        excerpt = $(el).text().trim();
      }
    });
  }
  
  // Generate slug from title or filename
  const slug = title
    ? title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim()
    : path.basename(filename, '.html');
  
  return {
    title,
    slug,
    date: date ? new Date(date) : new Date(),
    content,
    excerpt: excerpt || title,
  };
}

/**
 * Upload an image to Sanity
 * @param {string} filePath - Path to the image file
 * @param {string} filename - Original filename
 * @returns {Promise<Object>} - Uploaded image asset
 */
async function uploadImageToSanity(filePath, filename) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const asset = await client.assets.upload('image', fileBuffer, {
      filename,
    });
    return asset;
  } catch (error) {
    console.error(`Error uploading image ${filename} to Sanity:`, error.message);
    return null;
  }
}

/**
 * Convert HTML content to Portable Text format
 * @param {string} html - HTML content
 * @param {Array<Object>} imageAssets - Array of image assets
 * @returns {Array<Object>} - Portable Text blocks
 */
function htmlToPortableText(html, imageAssets) {
  const $ = cheerio.load(html);
  const blocks = [];
  let blockKey = 1;
  
  // Process each element in the content
  $('body > *').each((i, el) => {
    const tagName = el.tagName.toLowerCase();
    
    // Handle paragraphs
    if (tagName === 'p') {
      blocks.push({
        _type: 'block',
        _key: `block-${blockKey++}`,
        style: 'normal',
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: `span-${blockKey}`,
            text: $(el).text().trim(),
            marks: [],
          },
        ],
      });
    }
    
    // Handle headings
    else if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      blocks.push({
        _type: 'block',
        _key: `block-${blockKey++}`,
        style: tagName,
        markDefs: [],
        children: [
          {
            _type: 'span',
            _key: `span-${blockKey}`,
            text: $(el).text().trim(),
            marks: [],
          },
        ],
      });
    }
    
    // Handle images
    else if (tagName === 'img') {
      const src = $(el).attr('src');
      const alt = $(el).attr('alt') || '';
      const filename = path.basename(src);
      
      // Find the matching image asset
      const imageAsset = imageAssets.find(asset => 
        asset.originalFilename === filename || 
        asset.originalFilename === path.basename(filename)
      );
      
      if (imageAsset) {
        blocks.push({
          _type: 'image',
          _key: `image-${blockKey++}`,
          asset: {
            _type: 'reference',
            _ref: imageAsset._id,
          },
          alt,
        });
      }
    }
  });
  
  return blocks;
}

/**
 * Import articles from HTML files
 */
async function importArticles() {
  try {
    console.log('Starting manual blog content import...');
    
    // Get list of HTML files
    const htmlFiles = fs.readdirSync(config.htmlDir)
      .filter(file => file.endsWith('.html'));
    
    console.log(`Found ${htmlFiles.length} HTML files`);
    
    // Get list of image files
    const imageFiles = fs.readdirSync(config.imageDir)
      .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file));
    
    console.log(`Found ${imageFiles.length} image files`);
    
    // Upload all images to Sanity
    const imageAssets = [];
    for (let i = 0; i < imageFiles.length; i++) {
      const filename = imageFiles[i];
      const filePath = path.join(config.imageDir, filename);
      
      console.log(`Uploading image ${i + 1}/${imageFiles.length}: ${filename}`);
      const asset = await uploadImageToSanity(filePath, filename);
      
      if (asset) {
        imageAssets.push(asset);
      }
    }
    
    // Process each HTML file
    for (let i = 0; i < htmlFiles.length; i++) {
      const filename = htmlFiles[i];
      const filePath = path.join(config.htmlDir, filename);
      
      console.log(`Processing HTML file ${i + 1}/${htmlFiles.length}: ${filename}`);
      
      // Read HTML file
      const htmlContent = fs.readFileSync(filePath, 'utf8');
      
      // Extract article data
      const articleData = extractArticleData(htmlContent, filename);
      console.log(`Extracted article: ${articleData.title}`);
      
      // Convert HTML content to Portable Text
      const portableTextContent = htmlToPortableText(articleData.content, imageAssets);
      
      // Check if article already exists in Sanity
      const existingArticle = await client.fetch(
        `*[_type == "news" && slug.current == $slug][0]`,
        { slug: articleData.slug }
      );
      
      if (existingArticle) {
        console.log(`Article already exists: ${articleData.title}. Updating...`);
        
        // Update existing article
        await client
          .patch(existingArticle._id)
          .set({
            title: articleData.title,
            excerpt: articleData.excerpt,
            body: portableTextContent,
            publishedAt: articleData.date.toISOString(),
          })
          .commit();
        
        console.log(`Article updated: ${articleData.title}`);
      } else {
        console.log(`Creating new article: ${articleData.title}`);
        
        // Create new article
        const newArticle = await client.create({
          _type: 'news',
          title: articleData.title,
          slug: { _type: 'slug', current: articleData.slug },
          excerpt: articleData.excerpt,
          body: portableTextContent,
          publishedAt: articleData.date.toISOString(),
          status: 'published',
        });
        
        console.log(`Article created: ${articleData.title} with ID: ${newArticle._id}`);
      }
    }
    
    console.log('Manual blog content import completed successfully!');
  } catch (error) {
    console.error('Error importing blog content:', error);
  }
}

// Run the import function
importArticles();
